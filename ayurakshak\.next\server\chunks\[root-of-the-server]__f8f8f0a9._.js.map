{"version": 3, "sources": ["turbopack:///[project]/src/lib/mongodb.ts", "turbopack:///[project]/src/lib/models/Donation.ts", "turbopack:///[project]/src/app/api/donations/route.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI!, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n\n// Type declaration for global mongoose\ndeclare global {\n  var mongoose: {\n    conn: typeof mongoose | null;\n    promise: Promise<typeof mongoose> | null;\n  };\n}\n", "import mongoose, { Schema } from 'mongoose';\nimport { IDonation } from '@/types';\n\nconst DonationSchema = new Schema<IDonation>(\n  {\n    donorName: {\n      type: String,\n      required: [true, 'Donor name is required'],\n      trim: true,\n      maxlength: [100, 'Name cannot exceed 100 characters'],\n    },\n    donorEmail: {\n      type: String,\n      required: [true, 'Email is required'],\n      trim: true,\n      lowercase: true,\n      match: [\n        /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n        'Please enter a valid email address',\n      ],\n    },\n    donorPhone: {\n      type: String,\n      trim: true,\n      match: [\n        /^[\\+]?[1-9][\\d]{0,15}$/,\n        'Please enter a valid phone number',\n      ],\n    },\n    amount: {\n      type: Number,\n      required: [true, 'Donation amount is required'],\n      min: [1, 'Amount must be at least ₹1'],\n      max: [********, 'Amount cannot exceed ₹1 crore'],\n    },\n    currency: {\n      type: String,\n      default: 'INR',\n      enum: ['INR', 'USD', 'EUR'],\n    },\n    purpose: {\n      type: String,\n      required: [true, 'Purpose is required'],\n      enum: ['general', 'health', 'education', 'emergency', 'livelihoods'],\n      default: 'general',\n    },\n    paymentMethod: {\n      type: String,\n      required: [true, 'Payment method is required'],\n      enum: ['online', 'bank_transfer', 'cash', 'cheque'],\n      default: 'online',\n    },\n    paymentStatus: {\n      type: String,\n      required: [true, 'Payment status is required'],\n      enum: ['pending', 'completed', 'failed', 'refunded'],\n      default: 'pending',\n    },\n    transactionId: {\n      type: String,\n      trim: true,\n      sparse: true, // Allows multiple null values\n    },\n    paymentGatewayResponse: {\n      type: Schema.Types.Mixed,\n    },\n    isAnonymous: {\n      type: Boolean,\n      default: false,\n    },\n    address: {\n      street: {\n        type: String,\n        trim: true,\n        maxlength: [200, 'Street address cannot exceed 200 characters'],\n      },\n      city: {\n        type: String,\n        trim: true,\n        maxlength: [50, 'City cannot exceed 50 characters'],\n      },\n      state: {\n        type: String,\n        trim: true,\n        maxlength: [50, 'State cannot exceed 50 characters'],\n      },\n      pincode: {\n        type: String,\n        trim: true,\n        match: [/^[1-9][0-9]{5}$/, 'Please enter a valid pincode'],\n      },\n      country: {\n        type: String,\n        trim: true,\n        default: 'India',\n        maxlength: [50, 'Country cannot exceed 50 characters'],\n      },\n    },\n    panNumber: {\n      type: String,\n      trim: true,\n      uppercase: true,\n      match: [/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Please enter a valid PAN number'],\n    },\n    receiptNumber: {\n      type: String,\n      trim: true,\n      unique: true,\n      sparse: true,\n    },\n  },\n  {\n    timestamps: true,\n    toJSON: { virtuals: true },\n    toObject: { virtuals: true },\n  }\n);\n\n// Indexes for better query performance\nDonationSchema.index({ donorEmail: 1 });\nDonationSchema.index({ paymentStatus: 1 });\nDonationSchema.index({ purpose: 1 });\nDonationSchema.index({ createdAt: -1 });\nDonationSchema.index({ amount: -1 });\nDonationSchema.index({ transactionId: 1 }, { sparse: true });\n\n// Virtual for formatted amount\nDonationSchema.virtual('formattedAmount').get(function () {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: this.currency,\n  }).format(this.amount);\n});\n\n// Virtual for formatted date\nDonationSchema.virtual('formattedDate').get(function () {\n  return this.createdAt.toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n});\n\n// Virtual for purpose display name\nDonationSchema.virtual('purposeDisplayName').get(function () {\n  const purposeMap = {\n    general: 'General Fund',\n    health: 'Health Camps',\n    education: 'Education Programs',\n    emergency: 'Emergency Relief',\n    livelihoods: 'Women Livelihoods',\n  };\n  return purposeMap[this.purpose] || this.purpose;\n});\n\n// Pre-save middleware\nDonationSchema.pre('save', function (next) {\n  // Generate receipt number for completed donations\n  if (this.paymentStatus === 'completed' && !this.receiptNumber) {\n    const year = new Date().getFullYear();\n    const month = String(new Date().getMonth() + 1).padStart(2, '0');\n    const random = Math.random().toString(36).substr(2, 6).toUpperCase();\n    this.receiptNumber = `AYU${year}${month}${random}`;\n  }\n  \n  // Sanitize text fields\n  this.donorName = this.donorName.replace(/<[^>]*>?/gm, '');\n  \n  next();\n});\n\n// Static method to get total donations\nDonationSchema.statics.getTotalAmount = function (purpose?: string) {\n  const match = purpose ? { purpose, paymentStatus: 'completed' } : { paymentStatus: 'completed' };\n  return this.aggregate([\n    { $match: match },\n    { $group: { _id: null, total: { $sum: '$amount' } } },\n  ]);\n};\n\n// Static method to get donation statistics\nDonationSchema.statics.getStats = function () {\n  return this.aggregate([\n    { $match: { paymentStatus: 'completed' } },\n    {\n      $group: {\n        _id: '$purpose',\n        totalAmount: { $sum: '$amount' },\n        count: { $sum: 1 },\n        avgAmount: { $avg: '$amount' },\n      },\n    },\n  ]);\n};\n\n// Static method to get recent donations\nDonationSchema.statics.getRecent = function (limit: number = 10) {\n  return this.find({ paymentStatus: 'completed' })\n    .sort({ createdAt: -1 })\n    .limit(limit)\n    .select('donorName amount purpose createdAt isAnonymous');\n};\n\n// Static method to get top donors\nDonationSchema.statics.getTopDonors = function (limit: number = 10) {\n  return this.aggregate([\n    { $match: { paymentStatus: 'completed', isAnonymous: false } },\n    {\n      $group: {\n        _id: '$donorEmail',\n        donorName: { $first: '$donorName' },\n        totalAmount: { $sum: '$amount' },\n        donationCount: { $sum: 1 },\n        lastDonation: { $max: '$createdAt' },\n      },\n    },\n    { $sort: { totalAmount: -1 } },\n    { $limit: limit },\n  ]);\n};\n\nconst Donation = mongoose.models.Donation || mongoose.model<IDonation>('Donation', DonationSchema);\n\nexport default Donation;\n", "import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Donation from '@/lib/models/Donation';\nimport { donationFormSchema } from '@/utils/validation';\nimport { formatSuccessResponse, formatErrorResponse, createRateLimiter } from '@/utils/validation';\n\n// Rate limiter: 3 requests per 10 minutes per IP\nconst rateLimiter = createRateLimiter(10 * 60 * 1000, 3);\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Rate limiting\n    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';\n    if (!rateLimiter(clientIP)) {\n      return NextResponse.json(\n        formatErrorResponse('Too many requests. Please try again later.'),\n        { status: 429 }\n      );\n    }\n\n    // Parse request body\n    const body = await request.json();\n\n    // Validate input data\n    const validationResult = donationFormSchema.safeParse(body);\n    if (!validationResult.success) {\n      return NextResponse.json(\n        formatErrorResponse('Validation failed', validationResult.error.errors),\n        { status: 400 }\n      );\n    }\n\n    const donationData = validationResult.data;\n\n    // Connect to database\n    await connectDB();\n\n    // Create new donation record\n    const donation = new Donation({\n      ...donationData,\n      paymentStatus: 'pending',\n      paymentMethod: 'online', // Default to online, can be updated based on payment gateway\n    });\n\n    await donation.save();\n\n    // TODO: Integrate with payment gateway (Razorpay, Stripe, etc.)\n    // TODO: Send confirmation email\n    // TODO: Generate tax receipt for completed donations\n\n    return NextResponse.json(\n      formatSuccessResponse(\n        { \n          id: donation._id,\n          amount: donation.amount,\n          purpose: donation.purpose,\n          // Return payment gateway URL or order ID here\n        },\n        'Donation initiated successfully. Please complete the payment.'\n      ),\n      { status: 201 }\n    );\n\n  } catch (error) {\n    console.error('Donation creation error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while processing your donation. Please try again later.'),\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const purpose = searchParams.get('purpose');\n    const status = searchParams.get('status');\n    const isPublic = searchParams.get('public') === 'true';\n\n    await connectDB();\n\n    // Build query\n    const query: any = {};\n    if (purpose) query.purpose = purpose;\n    if (status) query.paymentStatus = status;\n    if (isPublic) {\n      query.paymentStatus = 'completed';\n      query.isAnonymous = false;\n    }\n\n    // Get total count\n    const total = await Donation.countDocuments(query);\n\n    // Select fields based on public/admin access\n    const selectFields = isPublic \n      ? 'donorName amount purpose createdAt'\n      : 'donorName donorEmail amount purpose paymentStatus createdAt isAnonymous';\n\n    // Get paginated results\n    const donations = await Donation.find(query)\n      .sort({ createdAt: -1 })\n      .skip((page - 1) * limit)\n      .limit(limit)\n      .select(selectFields);\n\n    // Get statistics\n    const stats = await Donation.aggregate([\n      { $match: { paymentStatus: 'completed' } },\n      {\n        $group: {\n          _id: '$purpose',\n          totalAmount: { $sum: '$amount' },\n          count: { $sum: 1 },\n        },\n      },\n    ]);\n\n    const totalRaised = await Donation.aggregate([\n      { $match: { paymentStatus: 'completed' } },\n      { $group: { _id: null, total: { $sum: '$amount' } } },\n    ]);\n\n    return NextResponse.json(\n      formatSuccessResponse({\n        donations,\n        stats: {\n          byPurpose: stats,\n          totalRaised: totalRaised[0]?.total || 0,\n          totalDonors: await Donation.distinct('donorEmail', { paymentStatus: 'completed' }).then(emails => emails.length),\n        },\n        pagination: {\n          page,\n          limit,\n          total,\n          pages: Math.ceil(total / limit),\n        },\n      }),\n      { status: 200 }\n    );\n\n  } catch (error) {\n    console.error('Donations fetch error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while fetching donations.'),\n      { status: 500 }\n    );\n  }\n}\n\nexport async function PATCH(request: NextRequest) {\n  try {\n    // This endpoint is for payment gateway webhooks and admin updates\n    const body = await request.json();\n    const { id, paymentStatus, transactionId, paymentGatewayResponse } = body;\n\n    if (!id || !paymentStatus) {\n      return NextResponse.json(\n        formatErrorResponse('Invalid request data'),\n        { status: 400 }\n      );\n    }\n\n    await connectDB();\n\n    const updateData: any = { paymentStatus };\n    if (transactionId) updateData.transactionId = transactionId;\n    if (paymentGatewayResponse) updateData.paymentGatewayResponse = paymentGatewayResponse;\n\n    const updatedDonation = await Donation.findByIdAndUpdate(\n      id,\n      updateData,\n      { new: true }\n    );\n\n    if (!updatedDonation) {\n      return NextResponse.json(\n        formatErrorResponse('Donation not found'),\n        { status: 404 }\n      );\n    }\n\n    // TODO: Send receipt email for completed donations\n    // TODO: Update donor records\n    // TODO: Trigger thank you email\n\n    return NextResponse.json(\n      formatSuccessResponse(updatedDonation, 'Donation updated successfully'),\n      { status: 200 }\n    );\n\n  } catch (error) {\n    console.error('Donation update error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while updating the donation.'),\n      { status: 500 }\n    );\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/donations/route\",\n        pathname: \"/api/donations\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/donations/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/donations/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "u6CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAc,QAAQ,GAAG,CAAC,WAAW,CAE3C,GAAI,CAAC,EACH,MAAM,AAAI,KADM,CAEd,kEASJ,IAAI,EAAS,EAAA,CAAA,CAAO,GAAP,KAAe,AAExB,CAAC,IACH,EAAS,EADE,AACF,CAAA,CAAO,GAAP,KAAe,CAAG,CAAE,KAAM,KAAM,QAAS,IAAK,SAGzD,eAAe,EACb,GAAI,EAAO,IAAI,CACb,CADe,GAwBJ,GAvBJ,EAAO,IAAI,CAGf,EAAO,OAAO,EAAE,CAKnB,EAAO,OAAO,CAAG,EAAA,OAAQ,CAAC,OAAO,CAAC,EAJrB,CACX,UAG8C,MAH9B,CAClB,GAEsD,IAAI,CAAC,AAAC,GACnD,EACT,EAGF,GAAI,CACF,EAAO,IAAI,CAAG,MAAM,EAAO,OAC7B,AADoC,CAClC,MAAO,EAAG,CAEV,MADA,EAAO,OAAO,CAAG,KACX,CACR,CAEA,OAAO,EAAO,IAAI,AACpB,gDC5CA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAiB,IAAI,EAAA,MAAM,CAC/B,CACE,UAAW,CACT,KAAM,OACN,SAAU,EAAC,EAAM,yBAAyB,CAC1C,MAAM,EACN,UAAW,CAAC,IAAK,oCAAoC,AACvD,EACA,WAAY,CACV,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,MAAM,EACN,WAAW,EACX,MAAO,CACL,8CACA,qCACD,AACH,EACA,WAAY,CACV,KAAM,OACN,MAAM,EACN,MAAO,CACL,yBACA,oCACD,AACH,EACA,OAAQ,CACN,KAAM,OACN,SAAU,CAAC,GAAM,8BAA8B,CAC/C,IAAK,CAAC,EAAG,6BAA6B,CACtC,IAAK,CAAC,IAAU,gCAAgC,AAClD,EACA,SAAU,CACR,KAAM,OACN,QAAS,MACT,KAAM,CAAC,MAAO,MAAO,MAAM,AAC7B,EACA,QAAS,CACP,KAAM,OACN,SAAU,EAAC,EAAM,sBAAsB,CACvC,KAAM,CAAC,UAAW,SAAU,YAAa,YAAa,cAAc,CACpE,QAAS,SACX,EACA,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,6BAA6B,CAC9C,KAAM,CAAC,SAAU,gBAAiB,OAAQ,SAAS,CACnD,QAAS,QACX,EACA,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,6BAA6B,CAC9C,KAAM,CAAC,UAAW,YAAa,SAAU,WAAW,CACpD,QAAS,SACX,EACA,cAAe,CACb,KAAM,OACN,MAAM,EACN,QAAQ,CACV,EACA,uBAAwB,CACtB,KAAM,EAAA,MAAM,CAAC,KAAK,CAAC,KACrB,AAD0B,EAE1B,YAAa,CACX,KAAM,QACN,SAAS,CACX,EACA,QAAS,CACP,OAAQ,CACN,KAAM,OACN,MAAM,EACN,UAAW,CAAC,IAAK,8CAA8C,AACjE,EACA,KAAM,CACJ,KAAM,OACN,MAAM,EACN,UAAW,CAAC,GAAI,mCAAmC,AACrD,EACA,MAAO,CACL,KAAM,OACN,MAAM,EACN,UAAW,CAAC,GAAI,oCAAoC,AACtD,EACA,QAAS,CACP,KAAM,OACN,MAAM,EACN,MAAO,CAAC,kBAAmB,+BAA+B,AAC5D,EACA,QAAS,CACP,KAAM,OACN,MAAM,EACN,QAAS,QACT,UAAW,CAAC,GAAI,sCAAsC,AACxD,CACF,EACA,UAAW,CACT,KAAM,OACN,KAAM,GACN,WAAW,EACX,MAAO,CAAC,6BAA8B,kCAAkC,AAC1E,EACA,cAAe,CACb,KAAM,OACN,MAAM,EACN,QAAQ,EACR,QAAQ,CACV,CACF,EACA,CACE,YAAY,EACZ,OAAQ,CAAE,SAAU,EAAK,EACzB,SAAU,CAAE,UAAU,CAAK,CAC7B,GAIF,EAAe,KAAK,CAAC,CAAE,WAAY,CAAE,GACrC,EAAe,KAAK,CAAC,CAAE,cAAe,CAAE,GACxC,EAAe,KAAK,CAAC,CAAE,QAAS,CAAE,GAClC,EAAe,KAAK,CAAC,CAAE,UAAW,CAAC,CAAE,GACrC,EAAe,KAAK,CAAC,CAAE,OAAQ,CAAC,CAAE,GAClC,EAAe,KAAK,CAAC,CAAE,cAAe,CAAE,EAAG,CAAE,OAAQ,EAAK,GAG1D,EAAe,OAAO,CAAC,mBAAmB,GAAG,CAAC,WAC5C,OAAO,IAAI,KAAK,YAAY,CAAC,QAAS,CACpC,MAAO,WACP,SAAU,IAAI,CAAC,QACjB,AADyB,GACtB,MAAM,CAAC,IAAI,CAAC,MAAM,CACvB,GAGA,EAAe,OAAO,CAAC,iBAAiB,GAAG,CAAC,WAC1C,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAS,CAChD,KAAM,UACN,MAAO,OACP,IAAK,SACP,EACF,GAGA,EAAe,OAAO,CAAC,sBAAsB,GAAG,CAAC,WAQ/C,MAAO,CAPY,CACjB,QAAS,eACT,OAAQ,eACR,UAAW,qBACX,UAAW,mBACX,YAAa,oBACf,CACiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAI,IAAI,CAAC,OAAO,AACjD,GAGA,EAAe,GAAG,CAAC,OAAQ,SAAU,CAAI,EAEvC,GAA2B,cAAvB,IAAI,CAAC,aAAa,EAAoB,CAAC,IAAI,CAAC,aAAa,CAAE,CAC7D,IAAM,EAAO,IAAI,OAAO,WAAW,GAC7B,EAAQ,OAAO,IAAI,OAAO,QAAQ,GAAK,GAAG,QAAQ,CAAC,EAAG,KACtD,EAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,EAAG,GAAG,WAAW,GAClE,IAAI,CAAC,aAAa,CAAG,CAAC,GAAG,EAAE,EAAA,EAAO,EAAA,EAAQ,EAAA,CAAQ,AACpD,CAGA,IAAI,CAAC,SAAS,CAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAc,IAEtD,GACF,GAGA,EAAe,OAAO,CAAC,cAAc,CAAG,SAAU,CAAgB,EAEhE,OAAO,IAAI,CAAC,SAAS,CAAC,CACpB,CAAE,OAFU,CAEF,CAFY,SAAE,EAAS,cAAe,WAAY,EAAI,CAAE,cAAe,WAAY,CAE7E,EAChB,CAAE,OAAQ,CAAE,IAAK,KAAM,MAAO,CAAE,KAAM,SAAU,CAAE,CAAE,EACrD,CACH,EAGA,EAAe,OAAO,CAAC,QAAQ,CAAG,WAChC,OAAO,IAAI,CAAC,SAAS,CAAC,CACpB,CAAE,OAAQ,CAAE,cAAe,WAAY,CAAE,EACzC,CACE,OAAQ,CACN,IAAK,WACL,YAAa,CAAE,KAAM,SAAU,EAC/B,MAAO,CAAE,KAAM,CAAE,EACjB,UAAW,CAAE,KAAM,SAAU,CAC/B,CACF,EACD,CACH,EAGA,EAAe,OAAO,CAAC,SAAS,CAAG,SAAU,EAAgB,EAAE,EAC7D,OAAO,IAAI,CAAC,IAAI,CAAC,CAAE,cAAe,WAAY,GAC3C,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,KAAK,CAAC,GACN,MAAM,CAAC,iDACZ,EAGA,EAAe,OAAO,CAAC,YAAY,CAAG,SAAU,EAAgB,EAAE,EAChE,OAAO,IAAI,CAAC,SAAS,CAAC,CACpB,CAAE,OAAQ,CAAE,cAAe,YAAa,aAAa,CAAM,CAAE,EAC7D,CACE,OAAQ,CACN,IAAK,cACL,UAAW,CAAE,OAAQ,YAAa,EAClC,YAAa,CAAE,KAAM,SAAU,EAC/B,cAAe,CAAE,KAAM,CAAE,EACzB,aAAc,CAAE,KAAM,YAAa,CACrC,CACF,EACA,CAAE,MAAO,CAAE,YAAa,CAAC,CAAE,CAAE,EAC7B,CAAE,OAAQ,CAAM,EACjB,CACH,QAEiB,EAAA,OAAQ,CAAC,MAAM,CAAC,QAAQ,EAAI,EAAA,EAE9B,KAFsC,CAAC,KAAK,CAAY,WAAY,2LE7NnF,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,yDDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,CAAA,EAAA,EAAA,iBAAA,AAAiB,EAAC,IAAgB,CAAX,EAEpC,GAFyC,YAE1B,EAAK,CAAoB,EAC7C,GAAI,CAEF,IAAM,EAAW,EAAQ,EAAE,EAAI,EAAQ,OAAO,CAAC,GAAG,CAAC,oBAAsB,UACzE,GAAI,CAAC,EAAY,GACf,OAAO,CADmB,CACnB,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,8CACpB,CAAE,OAAQ,GAAI,GAKlB,IAAM,EAAO,MAAM,EAAQ,IAAI,GAGzB,EAAmB,EAAA,kBAAkB,CAAC,SAAS,CAAC,GACtD,GAAI,CAAC,EAAiB,OAAO,CAC3B,CAD6B,MACtB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,oBAAqB,EAAiB,KAAK,CAAC,MAAM,EACtE,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAe,EAAiB,IAAI,AAG1C,OAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAGf,IAAM,EAAW,IAAI,EAAA,OAAQ,CAAC,CAC5B,GAAG,CAAY,CACf,cAAe,UACf,cAAe,QACjB,GAQA,OANA,MAAM,EAAS,IAAI,GAMZ,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EACnB,CACE,GAAI,EAAS,GAAG,CAChB,OAAQ,EAAS,MAAM,CACvB,QAAS,EAAS,OAAO,AAE3B,EACA,iEAEF,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,2BAA4B,GACnC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,6EACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EACtC,EAAO,SAAS,EAAa,GAAG,CAAC,SAAW,KAC5C,EAAQ,SAAS,EAAa,GAAG,CAAC,UAAY,MAC9C,EAAU,EAAa,GAAG,CAAC,WAC3B,EAAS,EAAa,GAAG,CAAC,UAC1B,EAA0C,SAA/B,EAAa,GAAG,CAAC,SAElC,OAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAGf,IAAM,EAAa,CAAC,EAChB,IAAS,EAAM,OAAO,CAAG,CAAA,EACzB,IAAQ,EAAM,aAAa,CAAG,CAAA,EAC9B,IACF,EAAM,IADM,SACO,CAAG,YACtB,EAAM,WAAW,EAAG,GAItB,IAAM,EAAQ,MAAM,EAAA,OAAQ,CAAC,cAAc,CAAC,GAQtC,EAAY,MAAM,EAAA,OAAQ,CAAC,IAAI,CAAC,GACnC,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,IAAI,CAAC,CAAC,GAAO,CAAC,CAAI,GAClB,KAAK,CAAC,GACN,MAAM,CAAC,AATW,EACjB,qCACA,2EAUE,EAAQ,MAAM,EAAA,OAAQ,CAAC,SAAS,CAAC,CACrC,CAAE,OAAQ,CAAE,cAAe,WAAY,CAAE,EACzC,CACE,OAAQ,CACN,IAAK,WACL,YAAa,CAAE,KAAM,SAAU,EAC/B,MAAO,CAAE,KAAM,CAAE,CACnB,CACF,EACD,EAEK,EAAc,MAAM,EAAA,OAAQ,CAAC,SAAS,CAAC,CAC3C,CAAE,OAAQ,CAAE,cAAe,WAAY,CAAE,EACzC,CAAE,OAAQ,CAAE,IAAK,KAAM,MAAO,CAAE,KAAM,SAAU,CAAE,CAAE,EACrD,EAED,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,WACpB,EACA,MAAO,CACL,UAAW,EACX,YAAa,CAAW,CAAC,EAAE,EAAE,OAAS,EACtC,YAAa,MAAM,EAAA,OAAQ,CAAC,QAAQ,CAAC,aAAc,CAAE,cAAe,WAAY,GAAG,IAAI,CAAC,GAAU,EAAO,MAAM,CACjH,EACA,WAAY,MACV,QACA,QACA,EACA,MAAO,KAAK,IAAI,CAAC,EAAQ,EAC3B,CACF,GACA,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,yBAA0B,GACjC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,+CACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAM,CAAoB,EAC9C,GAAI,CAGF,GAAM,IAAE,CAAE,eAAE,CAAa,eAAE,CAAa,wBAAE,CAAsB,CAAE,CADrD,EACwD,IADlD,EAAQ,IAAI,GAG/B,GAAI,CAAC,GAAM,CAAC,EACV,OAAO,EAAA,IADkB,QACN,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,wBACpB,CAAE,OAAQ,GAAI,EAIlB,OAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAEf,IAAM,EAAkB,eAAE,CAAc,EACpC,IAAe,EAAW,aAAa,CAAG,CAAA,EAC1C,IAAwB,EAAW,sBAAsB,CAAG,CAAA,EAEhE,IAAM,EAAkB,MAAM,EAAA,OAAQ,CAAC,iBAAiB,CACtD,EACA,EACA,CAAE,KAAK,CAAK,GAGd,GAAI,CAAC,EACH,OAAO,EAAA,MADa,MACD,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,sBACpB,CAAE,OAAQ,GAAI,GAQlB,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAiB,iCACvC,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,yBAA0B,GACjC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,kDACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CCvLA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,uBACN,SAAU,iBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,2CAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,uBAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,CACtD,UACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,CAAE,mBAAiB,CAAE,qBAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAiB,AAAjB,EACnG,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,EAAgB,EAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,GAC+B,KAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,IAC7C,GAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,oBACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,EACA,0BACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAA2E,AAAxD,OAAC,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAU,AAAD,IACL,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,iBAAkB,OAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,EAAmB,QAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAc,AAAd,GAAiB,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,CACV,oBACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,aAAc,EAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAAQ,AADqC,GAAG,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [3]}