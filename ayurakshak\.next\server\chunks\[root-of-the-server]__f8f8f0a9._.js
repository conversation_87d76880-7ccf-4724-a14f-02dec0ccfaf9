module.exports=[61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},26046,(e,t,r)=>{t.exports=e.x("mongoose",()=>require("mongoose"))},49991,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(26046);let r=process.env.MONGODB_URI;if(!r)throw Error("Please define the MONGODB_URI environment variable inside .env");let a=e.g.mongoose;a||(a=e.g.mongoose={conn:null,promise:null});let n=async function(){if(a.conn)return a.conn;a.promise||(a.promise=t.default.connect(r,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},4770,e=>{"use strict";e.s(["default",()=>a]);var t=e.i(26046);let r=new t.Schema({donorName:{type:String,required:[!0,"Donor name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},donorEmail:{type:String,required:[!0,"Email is required"],trim:!0,lowercase:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email address"]},donorPhone:{type:String,trim:!0,match:[/^[\+]?[1-9][\d]{0,15}$/,"Please enter a valid phone number"]},amount:{type:Number,required:[!0,"Donation amount is required"],min:[1,"Amount must be at least ₹1"],max:[1e7,"Amount cannot exceed ₹1 crore"]},currency:{type:String,default:"INR",enum:["INR","USD","EUR"]},purpose:{type:String,required:[!0,"Purpose is required"],enum:["general","health","education","emergency","livelihoods"],default:"general"},paymentMethod:{type:String,required:[!0,"Payment method is required"],enum:["online","bank_transfer","cash","cheque"],default:"online"},paymentStatus:{type:String,required:[!0,"Payment status is required"],enum:["pending","completed","failed","refunded"],default:"pending"},transactionId:{type:String,trim:!0,sparse:!0},paymentGatewayResponse:{type:t.Schema.Types.Mixed},isAnonymous:{type:Boolean,default:!1},address:{street:{type:String,trim:!0,maxlength:[200,"Street address cannot exceed 200 characters"]},city:{type:String,trim:!0,maxlength:[50,"City cannot exceed 50 characters"]},state:{type:String,trim:!0,maxlength:[50,"State cannot exceed 50 characters"]},pincode:{type:String,trim:!0,match:[/^[1-9][0-9]{5}$/,"Please enter a valid pincode"]},country:{type:String,trim:!0,default:"India",maxlength:[50,"Country cannot exceed 50 characters"]}},panNumber:{type:String,trim:!0,uppercase:!0,match:[/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,"Please enter a valid PAN number"]},receiptNumber:{type:String,trim:!0,unique:!0,sparse:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({donorEmail:1}),r.index({paymentStatus:1}),r.index({purpose:1}),r.index({createdAt:-1}),r.index({amount:-1}),r.index({transactionId:1},{sparse:!0}),r.virtual("formattedAmount").get(function(){return new Intl.NumberFormat("en-IN",{style:"currency",currency:this.currency}).format(this.amount)}),r.virtual("formattedDate").get(function(){return this.createdAt.toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})}),r.virtual("purposeDisplayName").get(function(){return({general:"General Fund",health:"Health Camps",education:"Education Programs",emergency:"Emergency Relief",livelihoods:"Women Livelihoods"})[this.purpose]||this.purpose}),r.pre("save",function(e){if("completed"===this.paymentStatus&&!this.receiptNumber){let e=new Date().getFullYear(),t=String(new Date().getMonth()+1).padStart(2,"0"),r=Math.random().toString(36).substr(2,6).toUpperCase();this.receiptNumber=`AYU${e}${t}${r}`}this.donorName=this.donorName.replace(/<[^>]*>?/gm,""),e()}),r.statics.getTotalAmount=function(e){return this.aggregate([{$match:e?{purpose:e,paymentStatus:"completed"}:{paymentStatus:"completed"}},{$group:{_id:null,total:{$sum:"$amount"}}}])},r.statics.getStats=function(){return this.aggregate([{$match:{paymentStatus:"completed"}},{$group:{_id:"$purpose",totalAmount:{$sum:"$amount"},count:{$sum:1},avgAmount:{$avg:"$amount"}}}])},r.statics.getRecent=function(e=10){return this.find({paymentStatus:"completed"}).sort({createdAt:-1}).limit(e).select("donorName amount purpose createdAt isAnonymous")},r.statics.getTopDonors=function(e=10){return this.aggregate([{$match:{paymentStatus:"completed",isAnonymous:!1}},{$group:{_id:"$donorEmail",donorName:{$first:"$donorName"},totalAmount:{$sum:"$amount"},donationCount:{$sum:1},lastDonation:{$max:"$createdAt"}}},{$sort:{totalAmount:-1}},{$limit:e}])};let a=t.default.models.Donation||t.default.model("Donation",r)},53575,(e,t,r)=>{},18118,e=>{"use strict";e.s(["handler",()=>j,"patchFetch",()=>q,"routeModule",()=>$,"serverHooks",()=>P,"workAsyncStorage",()=>b,"workUnitAsyncStorage",()=>C],18118);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),o=e.i(61916),s=e.i(69741),i=e.i(16795),u=e.i(87718),l=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),m=e.i(26937),h=e.i(10372),g=e.i(93695);e.i(52474);var f=e.i(220);e.s(["GET",()=>A,"PATCH",()=>E,"POST",()=>S],67368);var y=e.i(89171),x=e.i(49991),R=e.i(4770),v=e.i(78044);let w=(0,v.createRateLimiter)(6e5,3);async function S(e){try{let t=e.ip||e.headers.get("x-forwarded-for")||"unknown";if(!w(t))return y.NextResponse.json((0,v.formatErrorResponse)("Too many requests. Please try again later."),{status:429});let r=await e.json(),a=v.donationFormSchema.safeParse(r);if(!a.success)return y.NextResponse.json((0,v.formatErrorResponse)("Validation failed",a.error.errors),{status:400});let n=a.data;await (0,x.default)();let o=new R.default({...n,paymentStatus:"pending",paymentMethod:"online"});return await o.save(),y.NextResponse.json((0,v.formatSuccessResponse)({id:o._id,amount:o.amount,purpose:o.purpose},"Donation initiated successfully. Please complete the payment."),{status:201})}catch(e){return console.error("Donation creation error:",e),y.NextResponse.json((0,v.formatErrorResponse)("An error occurred while processing your donation. Please try again later."),{status:500})}}async function A(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),n=t.get("purpose"),o=t.get("status"),s="true"===t.get("public");await (0,x.default)();let i={};n&&(i.purpose=n),o&&(i.paymentStatus=o),s&&(i.paymentStatus="completed",i.isAnonymous=!1);let u=await R.default.countDocuments(i),l=await R.default.find(i).sort({createdAt:-1}).skip((r-1)*a).limit(a).select(s?"donorName amount purpose createdAt":"donorName donorEmail amount purpose paymentStatus createdAt isAnonymous"),d=await R.default.aggregate([{$match:{paymentStatus:"completed"}},{$group:{_id:"$purpose",totalAmount:{$sum:"$amount"},count:{$sum:1}}}]),p=await R.default.aggregate([{$match:{paymentStatus:"completed"}},{$group:{_id:null,total:{$sum:"$amount"}}}]);return y.NextResponse.json((0,v.formatSuccessResponse)({donations:l,stats:{byPurpose:d,totalRaised:p[0]?.total||0,totalDonors:await R.default.distinct("donorEmail",{paymentStatus:"completed"}).then(e=>e.length)},pagination:{page:r,limit:a,total:u,pages:Math.ceil(u/a)}}),{status:200})}catch(e){return console.error("Donations fetch error:",e),y.NextResponse.json((0,v.formatErrorResponse)("An error occurred while fetching donations."),{status:500})}}async function E(e){try{let{id:t,paymentStatus:r,transactionId:a,paymentGatewayResponse:n}=await e.json();if(!t||!r)return y.NextResponse.json((0,v.formatErrorResponse)("Invalid request data"),{status:400});await (0,x.default)();let o={paymentStatus:r};a&&(o.transactionId=a),n&&(o.paymentGatewayResponse=n);let s=await R.default.findByIdAndUpdate(t,o,{new:!0});if(!s)return y.NextResponse.json((0,v.formatErrorResponse)("Donation not found"),{status:404});return y.NextResponse.json((0,v.formatSuccessResponse)(s,"Donation updated successfully"),{status:200})}catch(e){return console.error("Donation update error:",e),y.NextResponse.json((0,v.formatErrorResponse)("An error occurred while updating the donation."),{status:500})}}var N=e.i(67368);let $=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/donations/route",pathname:"/api/donations",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/donations/route.ts",nextConfigOutput:"",userland:N}),{workAsyncStorage:b,workUnitAsyncStorage:C,serverHooks:P}=$;function q(){return(0,a.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:C})}async function j(e,t,a){var y;let x="/api/donations/route";x=x.replace(/\/index$/,"")||"/";let R=await $.prepare(e,t,{srcPage:x,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:v,params:w,nextConfig:S,isDraftMode:A,prerenderManifest:E,routerServerContext:N,isOnDemandRevalidate:b,revalidateOnlyGenerated:C,resolvedPathname:P}=R,q=(0,s.normalizeAppPath)(x),j=!!(E.dynamicRoutes[q]||E.routes[P]);if(j&&!A){let e=!!E.routes[P],t=E.dynamicRoutes[q];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let D=null;!j||$.isDev||A||(D="/index"===(D=P)?"/":D);let I=!0===$.isDev||!j,T=j&&!I,_=e.method||"GET",O=(0,o.getTracer)(),k=O.getActiveScopeSpan(),U={params:w,prerenderManifest:E,renderOpts:{experimental:{cacheComponents:!!S.experimental.cacheComponents,authInterrupts:!!S.experimental.authInterrupts},supportsDynamicResponse:I,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=S.experimental)?void 0:y.cacheLife,isRevalidate:T,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>$.onRequestError(e,t,a,N)},sharedContext:{buildId:v}},M=new i.NodeNextRequest(e),H=new i.NodeNextResponse(t),F=u.NextRequestAdapter.fromNodeNextRequest(M,(0,u.signalFromNodeResponse)(t));try{let s=async r=>$.handle(F,U).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=O.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${_} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),i=async o=>{var i,u;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&b&&C&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=U.renderOpts.fetchMetrics;let u=U.renderOpts.pendingWaitUntil;u&&a.waitUntil&&(a.waitUntil(u),u=void 0);let l=U.renderOpts.collectedTags;if(!j)return await (0,p.sendResponse)(M,H,i,U.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==U.renderOpts.collectedRevalidate&&!(U.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&U.renderOpts.collectedRevalidate,a=void 0===U.renderOpts.collectedExpire||U.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:U.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await $.onRequestError(e,t,{routerKind:"App Router",routePath:x,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:T,isOnDemandRevalidate:b})},N),t}},g=await $.handleResponse({req:e,nextConfig:S,cacheKey:D,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:E,isRoutePPREnabled:!1,isOnDemandRevalidate:b,revalidateOnlyGenerated:C,responseGenerator:l,waitUntil:a.waitUntil});if(!j)return null;if((null==g||null==(i=g.value)?void 0:i.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(u=g.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",b?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,c.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&j||y.delete(h.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,m.getCacheControlHeader)(g.cacheControl)),await (0,p.sendResponse)(M,H,new Response(g.value.body,{headers:y,status:g.value.status||200})),null};k?await i(k):await O.withPropagatedContext(e.headers,()=>O.trace(l.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},i))}catch(t){if(k||t instanceof g.NoFallbackError||await $.onRequestError(e,t,{routerKind:"App Router",routePath:q,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:T,isOnDemandRevalidate:b})}),j)throw t;return await (0,p.sendResponse)(M,H,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__f8f8f0a9._.js.map