// Products Page JavaScript
document.addEventListener("DOMContentLoaded", function () {
  // Initialize AOS
  AOS.init({
    duration: 800,
    easing: "ease-in-out",
    once: true,
  });

  // Product data
  const products = {
    "daily-pain-oil": {
      name: "Daily Pain Oil",
      price: "₹299",
      image: "products/DailyPainOil.jpeg",
      description: "Natural pain relief oil made from traditional Ayurvedic herbs. Perfect for daily use to relieve muscle and joint pain.",
      ingredients: ["Eucalyptus Oil", "Wintergreen Oil", "Sesame Oil", "Camphor", "Menthol"],
      benefits: [
        "Provides long-lasting pain relief",
        "Reduces inflammation and swelling",
        "Improves blood circulation",
        "Safe for daily use",
        "No side effects"
      ],
      usage: "Apply gently on affected area and massage for 2-3 minutes. Use 2-3 times daily for best results.",
      features: ["100% Natural", "Ayurvedic", "Pain Relief"]
    },
    "herbal-shampoo": {
      name: "Herbal Dry Hair Shampoo",
      price: "₹249",
      image: "products/HerbalDryHairShampoo.jpeg",
      description: "Natural herbal shampoo specially formulated for dry and damaged hair. Nourishes and strengthens hair naturally.",
      ingredients: ["Amla Extract", "Bhringraj", "Neem", "Coconut Oil", "Aloe Vera"],
      benefits: [
        "Deeply nourishes dry hair",
        "Strengthens hair roots",
        "Prevents hair fall",
        "Adds natural shine",
        "Chemical-free formula"
      ],
      usage: "Apply to wet hair, massage gently into scalp, leave for 2-3 minutes, then rinse thoroughly.",
      features: ["Herbal", "Chemical-Free", "Hair Care"]
    },
    "instant-pain-oil": {
      name: "Instant Pain Oil",
      price: "₹349",
      image: "products/InstantPainOil.jpeg",
      description: "Fast-acting pain relief oil for immediate relief from acute pain. Made with potent Ayurvedic ingredients for quick results.",
      ingredients: ["Clove Oil", "Cinnamon Oil", "Ginger Extract", "Turmeric Oil", "Mustard Oil"],
      benefits: [
        "Instant pain relief",
        "Fast absorption",
        "Reduces muscle stiffness",
        "Anti-inflammatory properties",
        "Long-lasting effect"
      ],
      usage: "Apply a small amount on the affected area and massage gently. Use as needed for pain relief.",
      features: ["Fast Acting", "Ayurvedic", "Instant Relief"]
    }
  };

  // Handle view details buttons
  document.querySelectorAll('.btn-view-details').forEach(button => {
    button.addEventListener('click', function() {
      const productId = this.getAttribute('data-product');
      showProductDetails(productId);
    });
  });

  // Handle order buttons
  document.querySelectorAll('.btn-order').forEach(button => {
    button.addEventListener('click', function() {
      const productId = this.getAttribute('data-product');
      showOrderForm(productId);
    });
  });

  // Show product details modal
  function showProductDetails(productId) {
    const product = products[productId];
    if (!product) return;

    const modalHTML = `
      <div class="modal fade" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${product.name}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                <div class="col-md-6">
                  <img src="${product.image}" class="img-fluid rounded" alt="${product.name}">
                </div>
                <div class="col-md-6">
                  <h4 class="text-primary mb-3">${product.price}</h4>
                  <p class="mb-3">${product.description}</p>
                  
                  <div class="mb-3">
                    ${product.features.map(feature => `<span class="badge bg-success me-1">${feature}</span>`).join('')}
                  </div>
                  
                  <h6>Key Ingredients:</h6>
                  <ul class="list-unstyled mb-3">
                    ${product.ingredients.map(ingredient => `<li><i class="fas fa-leaf text-success me-2"></i>${ingredient}</li>`).join('')}
                  </ul>
                  
                  <h6>Benefits:</h6>
                  <ul class="list-unstyled mb-3">
                    ${product.benefits.map(benefit => `<li><i class="fas fa-check text-success me-2"></i>${benefit}</li>`).join('')}
                  </ul>
                  
                  <h6>Usage:</h6>
                  <p class="text-muted">${product.usage}</p>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" onclick="showOrderForm('${productId}')">
                <i class="fas fa-shopping-cart me-2"></i>Order Now
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('productModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
  }

  // Show order form modal
  function showOrderForm(productId) {
    const product = products[productId];
    if (!product) return;

    const orderModalHTML = `
      <div class="modal fade" id="orderModal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Order ${product.name}</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="text-center mb-4">
                <img src="${product.image}" class="img-fluid rounded" style="max-height: 200px;" alt="${product.name}">
                <h6 class="mt-2">${product.name}</h6>
                <h5 class="text-primary">${product.price}</h5>
              </div>
              
              <form id="orderForm">
                <div class="mb-3">
                  <label for="customerName" class="form-label">Full Name *</label>
                  <input type="text" class="form-control" id="customerName" required>
                </div>
                
                <div class="mb-3">
                  <label for="customerPhone" class="form-label">Phone Number *</label>
                  <input type="tel" class="form-control" id="customerPhone" required>
                </div>
                
                <div class="mb-3">
                  <label for="customerEmail" class="form-label">Email</label>
                  <input type="email" class="form-control" id="customerEmail">
                </div>
                
                <div class="mb-3">
                  <label for="quantity" class="form-label">Quantity *</label>
                  <select class="form-select" id="quantity" required>
                    <option value="1">1 Bottle</option>
                    <option value="2">2 Bottles</option>
                    <option value="3">3 Bottles</option>
                    <option value="5">5 Bottles</option>
                  </select>
                </div>
                
                <div class="mb-3">
                  <label for="address" class="form-label">Delivery Address *</label>
                  <textarea class="form-control" id="address" rows="3" required></textarea>
                </div>
                
                <div class="mb-3">
                  <label for="pincode" class="form-label">Pincode *</label>
                  <input type="text" class="form-control" id="pincode" required>
                </div>
                
                <div class="alert alert-info">
                  <i class="fas fa-info-circle me-2"></i>
                  <strong>Payment:</strong> Cash on Delivery available. 
                  Free delivery for orders above ₹500.
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-success" onclick="submitOrder('${productId}')">
                <i class="fas fa-check me-2"></i>Place Order
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('orderModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Close product modal if open
    const productModal = document.getElementById('productModal');
    if (productModal) {
      const modal = bootstrap.Modal.getInstance(productModal);
      if (modal) modal.hide();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', orderModalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('orderModal'));
    modal.show();
  }

  // Submit order function
  window.showOrderForm = showOrderForm;
  
  window.submitOrder = function(productId) {
    const product = products[productId];
    const form = document.getElementById('orderForm');
    
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }

    const orderData = {
      product: product.name,
      price: product.price,
      customerName: document.getElementById('customerName').value,
      customerPhone: document.getElementById('customerPhone').value,
      customerEmail: document.getElementById('customerEmail').value,
      quantity: document.getElementById('quantity').value,
      address: document.getElementById('address').value,
      pincode: document.getElementById('pincode').value
    };

    // Here you would typically send the order to your backend
    // For now, we'll show a success message
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('orderModal'));
    modal.hide();
    
    // Show success message
    setTimeout(() => {
      alert(`Thank you ${orderData.customerName}! Your order for ${orderData.quantity} x ${orderData.product} has been placed successfully. We will contact you shortly to confirm your order.`);
    }, 500);
  };

  // Add smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
});
