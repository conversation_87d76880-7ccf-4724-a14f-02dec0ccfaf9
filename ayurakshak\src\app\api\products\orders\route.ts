import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import ProductOrder from '@/lib/models/ProductOrder';
import { productOrderSchema } from '@/utils/validation';
import { formatSuccessResponse, formatErrorResponse, createRateLimiter } from '@/utils/validation';

// Rate limiter: 5 requests per 15 minutes per IP
const rateLimiter = createRateLimiter(15 * 60 * 1000, 5);

// Sample products data (in a real app, this would come from a database)
const PRODUCTS = [
  {
    id: 'ayur-001',
    name: 'Ayurvedic Immunity Booster',
    price: 299,
    description: 'Natural immunity booster with herbs',
    image: '/products/immunity-booster.jpg',
    inStock: true,
  },
  {
    id: 'ayur-002',
    name: 'Herbal Digestive Tea',
    price: 199,
    description: 'Digestive tea blend with traditional herbs',
    image: '/products/digestive-tea.jpg',
    inStock: true,
  },
  {
    id: 'ayur-003',
    name: 'Natural Pain Relief Oil',
    price: 249,
    description: 'Ayurvedic oil for joint and muscle pain',
    image: '/products/pain-relief-oil.jpg',
    inStock: true,
  },
  {
    id: 'ayur-004',
    name: 'Stress Relief Capsules',
    price: 399,
    description: 'Natural stress relief with ashwagandha',
    image: '/products/stress-relief.jpg',
    inStock: true,
  },
];

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    if (!rateLimiter(clientIP)) {
      return NextResponse.json(
        formatErrorResponse('Too many requests. Please try again later.'),
        { status: 429 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate input data
    const validationResult = productOrderSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        formatErrorResponse('Validation failed', validationResult.error),
        { status: 400 }
      );
    }

    const orderData = validationResult.data;

    // Connect to database
    await connectDB();

    // Validate products and calculate total
    const orderProducts = [];
    let totalAmount = 0;

    for (const item of orderData.products) {
      const product = PRODUCTS.find(p => p.id === item.productId);
      if (!product) {
        return NextResponse.json(
          formatErrorResponse(`Product with ID ${item.productId} not found`),
          { status: 400 }
        );
      }

      if (!product.inStock) {
        return NextResponse.json(
          formatErrorResponse(`Product ${product.name} is currently out of stock`),
          { status: 400 }
        );
      }

      const productTotal = product.price * item.quantity;
      totalAmount += productTotal;

      orderProducts.push({
        productId: product.id,
        productName: product.name,
        quantity: item.quantity,
        price: product.price,
      });
    }

    // Create new order
    const order = new ProductOrder({
      customerName: orderData.customerName,
      customerEmail: orderData.customerEmail,
      customerPhone: orderData.customerPhone,
      products: orderProducts,
      totalAmount,
      shippingAddress: orderData.shippingAddress,
      paymentMethod: orderData.paymentMethod,
      notes: orderData.notes,
      orderStatus: 'pending',
      paymentStatus: orderData.paymentMethod === 'cod' ? 'pending' : 'pending',
    });

    await order.save();

    // TODO: Send order confirmation email
    // TODO: Integrate with payment gateway for online payments
    // TODO: Update inventory
    // TODO: Send notification to admin

    return NextResponse.json(
      formatSuccessResponse(
        {
          orderId: order._id,
          orderNumber: order.orderNumber,
          totalAmount: order.totalAmount,
          paymentMethod: order.paymentMethod,
        },
        'Order placed successfully! You will receive a confirmation email shortly.'
      ),
      { status: 201 }
    );

  } catch (error) {
    console.error('Product order creation error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while processing your order. Please try again later.'),
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const customerEmail = searchParams.get('customerEmail');

    await connectDB();

    // Build query
    const query: any = {};
    if (status) query.orderStatus = status;
    if (customerEmail) query.customerEmail = customerEmail;

    // Get total count
    const total = await ProductOrder.countDocuments(query);

    // Get paginated results
    const orders = await ProductOrder.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .select('customerName customerEmail totalAmount orderStatus paymentStatus createdAt products');

    // Get statistics
    const stats = await ProductOrder.aggregate([
      {
        $group: {
          _id: '$orderStatus',
          count: { $sum: 1 },
          totalAmount: { $sum: '$totalAmount' },
        },
      },
    ]);

    return NextResponse.json(
      formatSuccessResponse({
        orders,
        stats,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      }),
      { status: 200 }
    );

  } catch (error) {
    console.error('Orders fetch error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while fetching orders.'),
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // This endpoint is for admin updates
    const body = await request.json();
    const { id, orderStatus, paymentStatus, trackingNumber } = body;

    if (!id) {
      return NextResponse.json(
        formatErrorResponse('Order ID is required'),
        { status: 400 }
      );
    }

    await connectDB();

    const updateData: any = {};
    if (orderStatus) updateData.orderStatus = orderStatus;
    if (paymentStatus) updateData.paymentStatus = paymentStatus;
    if (trackingNumber) updateData.trackingNumber = trackingNumber;

    const updatedOrder = await ProductOrder.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );

    if (!updatedOrder) {
      return NextResponse.json(
        formatErrorResponse('Order not found'),
        { status: 404 }
      );
    }

    // TODO: Send status update email to customer
    // TODO: Send tracking information if order is shipped

    return NextResponse.json(
      formatSuccessResponse(updatedOrder, 'Order updated successfully'),
      { status: 200 }
    );

  } catch (error) {
    console.error('Order update error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while updating the order.'),
      { status: 500 }
    );
  }
}
