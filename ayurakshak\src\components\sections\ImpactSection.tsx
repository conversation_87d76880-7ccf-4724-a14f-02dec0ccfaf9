'use client';

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import Card, { CardContent } from '@/components/ui/Card';

const ImpactSection: React.FC = () => {
  const impactStories = [
    {
      id: 1,
      title: 'Transforming Rural Healthcare in Rajasthan',
      location: 'Udaipur District, Rajasthan',
      image: '/impact/rajasthan-health.jpg',
      description: 'Our mobile health camps have reached 45 villages in Udaipur district, providing free Ayurvedic treatment to over 3,000 families.',
      stats: { families: '3,000+', villages: '45', camps: '120' },
      quote: 'Ayurakshak brought hope to our village when modern medicine was too expensive.',
      author: '<PERSON><PERSON>, Village Elder',
    },
    {
      id: 2,
      title: 'Women Entrepreneurs Leading Change',
      location: 'Varanasi, Uttar Pradesh',
      image: '/impact/women-entrepreneurs.jpg',
      description: 'Through our livelihood program, 200 women have started their own herbal product businesses, earning sustainable income.',
      stats: { women: '200+', businesses: '150+', income: '₹50,000' },
      quote: 'I can now support my family and send my children to school with dignity.',
      author: '<PERSON><PERSON>, Entrepreneur',
    },
    {
      id: 3,
      title: 'Emergency Relief During COVID-19',
      location: 'Multiple States',
      image: '/impact/covid-relief.jpg',
      description: 'During the pandemic, we provided immunity boosters and healthcare support to 10,000 families across 8 states.',
      stats: { families: '10,000+', states: '8', kits: '25,000' },
      quote: 'When hospitals were full, Ayurakshak was there with natural healing.',
      author: 'Dr. Rajesh Kumar, Community Health Worker',
    },
  ];

  const achievements = [
    {
      icon: '🏆',
      title: 'National Recognition',
      description: 'Awarded Best NGO for Rural Healthcare by Ministry of Health',
      year: '2023',
    },
    {
      icon: '🌟',
      title: 'UN Partnership',
      description: 'Collaborated with UN Women for sustainable development goals',
      year: '2022',
    },
    {
      icon: '📜',
      title: 'ISO Certification',
      description: 'Certified for quality management in healthcare delivery',
      year: '2021',
    },
    {
      icon: '🎖️',
      title: 'State Award',
      description: 'Recognized by Rajasthan Government for community service',
      year: '2020',
    },
  ];

  const metrics = [
    { number: '15,000+', label: 'Lives Directly Impacted', icon: '👥', color: 'text-blue-600' },
    { number: '250+', label: 'Villages Reached', icon: '🏘️', color: 'text-green-600' },
    { number: '2,500+', label: 'Women Empowered', icon: '👩', color: 'text-purple-600' },
    { number: '180+', label: 'Health Camps Conducted', icon: '🏥', color: 'text-red-600' },
    { number: '50,000+', label: 'Medicinal Plants Grown', icon: '🌱', color: 'text-emerald-600' },
    { number: '8', label: 'States Covered', icon: '🗺️', color: 'text-orange-600' },
  ];

  return (
    <section id="impact" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Our <span className="text-primary-600">Impact</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Real stories of transformation, healing, and hope from communities across India
          </p>
        </motion.div>

        {/* Impact Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-20"
        >
          {metrics.map((metric, index) => (
            <motion.div
              key={metric.label}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              <div className="text-3xl mb-3">{metric.icon}</div>
              <div className={`text-2xl lg:text-3xl font-bold mb-2 ${metric.color}`}>
                {metric.number}
              </div>
              <div className="text-sm text-gray-600 leading-tight">{metric.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Impact Stories */}
        <div className="mb-20">
          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-center text-gray-900 mb-12"
          >
            Stories of Transformation
          </motion.h3>
          
          <div className="space-y-12">
            {impactStories.map((story, index) => (
              <motion.div
                key={story.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className={`grid lg:grid-cols-2 gap-8 items-center ${
                  index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
                }`}
              >
                {/* Image */}
                <div className={`relative ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                  <div className="relative h-64 lg:h-80 rounded-2xl overflow-hidden shadow-xl">
                    <Image
                      src={story.image}
                      alt={story.title}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />
                  </div>
                  
                  {/* Stats Overlay */}
                  <div className="absolute -bottom-6 left-6 right-6 bg-white rounded-xl shadow-lg p-4">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      {Object.entries(story.stats).map(([key, value]) => (
                        <div key={key}>
                          <div className="text-lg font-bold text-primary-600">{value}</div>
                          <div className="text-xs text-gray-500 capitalize">{key}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className={`${index % 2 === 1 ? 'lg:col-start-1' : ''} pt-8 lg:pt-0`}>
                  <div className="inline-flex items-center bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
                    📍 {story.location}
                  </div>
                  
                  <h4 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                    {story.title}
                  </h4>
                  
                  <p className="text-gray-600 leading-relaxed mb-6 text-lg">
                    {story.description}
                  </p>
                  
                  {/* Quote */}
                  <blockquote className="border-l-4 border-primary-500 pl-6 py-4 bg-primary-50 rounded-r-lg">
                    <p className="text-gray-700 italic mb-2">"{story.quote}"</p>
                    <cite className="text-sm font-medium text-primary-700">
                      — {story.author}
                    </cite>
                  </blockquote>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Achievements */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Recognition & Achievements
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card variant="elevated" hover className="text-center h-full">
                  <CardContent className="p-6">
                    <div className="text-4xl mb-4">{achievement.icon}</div>
                    <div className="text-sm font-medium text-primary-600 mb-2">
                      {achievement.year}
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-3">
                      {achievement.title}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {achievement.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ImpactSection;
