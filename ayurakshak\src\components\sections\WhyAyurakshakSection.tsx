'use client';

import React from 'react';
import { motion } from 'framer-motion';

const WhyAyurakshakSection: React.FC = () => {
  const benefits = [
    {
      icon: '💰',
      title: '100% Cashless & Reimbursement Facility',
      description: 'Easy insurance processing and cashless treatment options available'
    },
    {
      icon: '💳',
      title: 'Easy Pay In EMI With 0% Interest Facility',
      description: 'Flexible payment options to make treatment affordable for everyone'
    },
    {
      icon: '😊',
      title: '100% Patient Satisfaction',
      description: 'Our patients are our priority, ensuring complete satisfaction with treatment'
    },
    {
      icon: '🏥',
      title: 'Network of 25+ Centers PAN India',
      description: 'Widespread network ensuring accessible treatment across the country'
    },
    {
      icon: '👨‍⚕️',
      title: '50+ Ayurveda Doctors & Therapists',
      description: 'Experienced team of qualified Ayurvedic practitioners and therapists'
    },
    {
      icon: '👥',
      title: '10,000+ Patients Treated So Far',
      description: 'Proven track record of successful treatments and patient recovery'
    },
    {
      icon: '✅',
      title: 'Success In Chronic Disease Reversal',
      description: 'Specialized in reversing chronic conditions through natural healing'
    },
    {
      icon: '🌿',
      title: '100% Ayurvedic Treatment – Naturally Healing, Zero Side Effects',
      description: 'Pure traditional Ayurvedic approach with no harmful side effects'
    }
  ];

  const stats = [
    { number: '10,000+', label: 'Patients Treated', icon: '👥' },
    { number: '95%', label: 'Success Rate', icon: '📈' },
    { number: '25+', label: 'Treatment Centers', icon: '🏥' },
    { number: '15+', label: 'Years Experience', icon: '⏰' },
    { number: '50+', label: 'Expert Doctors', icon: '👨‍⚕️' },
    { number: '100%', label: 'Natural Treatment', icon: '🌿' }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-primary-50 via-sage-50 to-mint-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Why <span className="text-primary-600">Ayurakshak</span> Center
            </h2>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              We understand the toll a disease can take on you and your family. The endless cycle of medication and no relief can be overwhelming. It's a journey filled with challenges, and we've seen firsthand how it can impact your life. That's why we're here. Our team of experts is dedicated to turning your health around. Let us help you reclaim your well-being and happiness.
            </p>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-6 mb-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center bg-white rounded-xl p-4 shadow-soft hover:shadow-medium transition-shadow"
                >
                  <div className="text-2xl mb-2">{stat.icon}</div>
                  <div className="text-2xl md:text-3xl font-bold text-primary-600 mb-1">
                    {stat.number}
                  </div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Right Benefits */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4 bg-white rounded-xl p-6 shadow-soft hover:shadow-medium transition-all duration-300 hover:scale-105"
              >
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center text-xl">
                    {benefit.icon}
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Bottom CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-20 text-center"
        >
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Ready to Start Your <span className="text-primary-600">Healing Journey?</span>
            </h3>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              Join thousands of patients who have successfully recovered through our traditional Ayurvedic treatments. Take the first step towards natural healing today.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="/contact"
                className="inline-flex items-center px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
                Call Now: +91-98765-43210
              </a>
              <a
                href="/contact"
                className="inline-flex items-center px-8 py-4 bg-sage-600 hover:bg-sage-700 text-white font-semibold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                Book Consultation
              </a>
            </div>

            {/* Trust Indicators */}
            <div className="mt-8 flex flex-wrap justify-center items-center gap-8 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Free Consultation</span>
              </div>
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>No Side Effects</span>
              </div>
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>100% Natural</span>
              </div>
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span>Proven Results</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyAyurakshakSection;
