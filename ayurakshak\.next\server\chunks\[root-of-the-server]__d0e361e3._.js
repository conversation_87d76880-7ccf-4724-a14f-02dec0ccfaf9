module.exports=[61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},26046,(e,t,r)=>{t.exports=e.x("mongoose",()=>require("mongoose"))},49991,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(26046);let r=process.env.MONGODB_URI;if(!r)throw Error("Please define the MONGODB_URI environment variable inside .env");let s=e.g.mongoose;s||(s=e.g.mongoose={conn:null,promise:null});let n=async function(){if(s.conn)return s.conn;s.promise||(s.promise=t.default.connect(r,{bufferCommands:!1}).then(e=>e));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},92095,e=>{"use strict";e.s(["default",()=>s]);var t=e.i(26046);let r=new t.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email address"]},name:{type:String,trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},isActive:{type:Boolean,default:!0},subscribedAt:{type:Date,default:Date.now},unsubscribedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({email:1},{unique:!0}),r.index({isActive:1}),r.index({subscribedAt:-1}),r.virtual("subscriptionStatus").get(function(){return this.isActive?"Active":"Unsubscribed"}),r.virtual("formattedSubscriptionDate").get(function(){return this.subscribedAt.toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})}),r.pre("save",function(e){this.isActive||this.unsubscribedAt||(this.unsubscribedAt=new Date),this.isActive&&this.unsubscribedAt&&(this.unsubscribedAt=void 0),e()}),r.statics.getActiveCount=function(){return this.countDocuments({isActive:!0})},r.statics.unsubscribe=function(e){return this.findOneAndUpdate({email:e},{isActive:!1,unsubscribedAt:new Date},{new:!0})},r.statics.resubscribe=function(e){return this.findOneAndUpdate({email:e},{isActive:!0,$unset:{unsubscribedAt:1}},{new:!0,upsert:!0})},r.statics.getRecent=function(e=10){return this.find({isActive:!0}).sort({subscribedAt:-1}).limit(e).select("email name subscribedAt")},r.statics.bulkUnsubscribe=function(e){return this.updateMany({email:{$in:e}},{isActive:!1,unsubscribedAt:new Date})};let s=t.default.models.Newsletter||t.default.model("Newsletter",r)},99062,(e,t,r)=>{},81233,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>O,"routeModule",()=>C,"serverHooks",()=>S,"workAsyncStorage",()=>j,"workUnitAsyncStorage",()=>D],81233);var t=e.i(47909),r=e.i(74017),s=e.i(96250),n=e.i(59756),a=e.i(61916),i=e.i(69741),o=e.i(16795),u=e.i(87718),l=e.i(95169),c=e.i(47587),d=e.i(66012),p=e.i(70101),m=e.i(26937),f=e.i(10372),b=e.i(93695);e.i(52474);var h=e.i(220);e.s(["DELETE",()=>y,"GET",()=>E,"POST",()=>g],17988);var v=e.i(89171),x=e.i(49991),w=e.i(92095),R=e.i(78044);let A=(0,R.createRateLimiter)(6e5,3);async function g(e){try{let t=e.ip||e.headers.get("x-forwarded-for")||"unknown";if(!A(t))return v.NextResponse.json((0,R.formatErrorResponse)("Too many requests. Please try again later."),{status:429});let r=await e.json(),s=R.newsletterSchema.safeParse(r);if(!s.success)return v.NextResponse.json((0,R.formatErrorResponse)("Validation failed",s.error.errors),{status:400});let{email:n,name:a}=s.data;await (0,x.default)();let i=await w.default.findOne({email:n});if(i)if(i.isActive)return v.NextResponse.json((0,R.formatErrorResponse)("This email is already subscribed to our newsletter."),{status:409});else return i.isActive=!0,i.subscribedAt=new Date,i.unsubscribedAt=void 0,a&&(i.name=a),await i.save(),v.NextResponse.json((0,R.formatSuccessResponse)({id:i._id},"Welcome back! Your newsletter subscription has been reactivated."),{status:200});let o=new w.default({email:n,name:a,isActive:!0,subscribedAt:new Date});return await o.save(),v.NextResponse.json((0,R.formatSuccessResponse)({id:o._id},"Thank you for subscribing! You will receive our latest updates and news."),{status:201})}catch(e){return console.error("Newsletter subscription error:",e),v.NextResponse.json((0,R.formatErrorResponse)("An error occurred while processing your subscription. Please try again later."),{status:500})}}async function y(e){try{let{searchParams:t}=new URL(e.url),r=t.get("email");if(t.get("token"),!r)return v.NextResponse.json((0,R.formatErrorResponse)("Email is required"),{status:400});await (0,x.default)();let s=await w.default.findOne({email:r});if(!s)return v.NextResponse.json((0,R.formatErrorResponse)("Email not found in our newsletter list."),{status:404});if(!s.isActive)return v.NextResponse.json((0,R.formatErrorResponse)("This email is already unsubscribed."),{status:409});return s.isActive=!1,s.unsubscribedAt=new Date,await s.save(),v.NextResponse.json((0,R.formatSuccessResponse)(null,"You have been successfully unsubscribed from our newsletter."),{status:200})}catch(e){return console.error("Newsletter unsubscribe error:",e),v.NextResponse.json((0,R.formatErrorResponse)("An error occurred while processing your unsubscribe request."),{status:500})}}async function E(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"10"),n=t.get("isActive");await (0,x.default)();let a={};null!==n&&(a.isActive="true"===n);let i=await w.default.countDocuments(a),o=await w.default.find(a).sort({subscribedAt:-1}).skip((r-1)*s).limit(s).select("email name isActive subscribedAt unsubscribedAt"),u={totalSubscribers:await w.default.countDocuments({isActive:!0}),totalUnsubscribed:await w.default.countDocuments({isActive:!1}),recentSubscriptions:await w.default.countDocuments({isActive:!0,subscribedAt:{$gte:new Date(Date.now()-2592e6)}})};return v.NextResponse.json((0,R.formatSuccessResponse)({subscriptions:o,stats:u,pagination:{page:r,limit:s,total:i,pages:Math.ceil(i/s)}}),{status:200})}catch(e){return console.error("Newsletter fetch error:",e),v.NextResponse.json((0,R.formatErrorResponse)("An error occurred while fetching newsletter data."),{status:500})}}var N=e.i(17988);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/newsletter/route",pathname:"/api/newsletter",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/newsletter/route.ts",nextConfigOutput:"",userland:N}),{workAsyncStorage:j,workUnitAsyncStorage:D,serverHooks:S}=C;function O(){return(0,s.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:D})}async function q(e,t,s){var v;let x="/api/newsletter/route";x=x.replace(/\/index$/,"")||"/";let w=await C.prepare(e,t,{srcPage:x,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==s.waitUntil||s.waitUntil.call(s,Promise.resolve()),null;let{buildId:R,params:A,nextConfig:g,isDraftMode:y,prerenderManifest:E,routerServerContext:N,isOnDemandRevalidate:j,revalidateOnlyGenerated:D,resolvedPathname:S}=w,O=(0,i.normalizeAppPath)(x),q=!!(E.dynamicRoutes[O]||E.routes[S]);if(q&&!y){let e=!!E.routes[S],t=E.dynamicRoutes[O];if(t&&!1===t.fallback&&!e)throw new b.NoFallbackError}let T=null;!q||C.isDev||y||(T="/index"===(T=S)?"/":T);let P=!0===C.isDev||!q,k=q&&!P,U=e.method||"GET",_=(0,a.getTracer)(),I=_.getActiveScopeSpan(),M={params:A,prerenderManifest:E,renderOpts:{experimental:{cacheComponents:!!g.experimental.cacheComponents,authInterrupts:!!g.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=g.experimental)?void 0:v.cacheLife,isRevalidate:k,waitUntil:s.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,s)=>C.onRequestError(e,t,s,N)},sharedContext:{buildId:R}},H=new o.NodeNextRequest(e),$=new o.NodeNextResponse(t),L=u.NextRequestAdapter.fromNodeNextRequest(H,(0,u.signalFromNodeResponse)(t));try{let i=async r=>C.handle(L,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let s=_.getRootSpanAttributes();if(!s)return;if(s.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${s.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=s.get("next.route");if(n){let e=`${U} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),o=async a=>{var o,u;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&j&&D&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(a);e.fetchMetrics=M.renderOpts.fetchMetrics;let u=M.renderOpts.pendingWaitUntil;u&&s.waitUntil&&(s.waitUntil(u),u=void 0);let l=M.renderOpts.collectedTags;if(!q)return await (0,d.sendResponse)(H,$,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[f.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,s=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:h.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:s}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:x,routeType:"route",revalidateReason:(0,c.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:j})},N),t}},b=await C.handleResponse({req:e,nextConfig:g,cacheKey:T,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:E,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:D,responseGenerator:l,waitUntil:s.waitUntil});if(!q)return null;if((null==b||null==(o=b.value)?void 0:o.kind)!==h.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==b||null==(u=b.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":b.isMiss?"MISS":b.isStale?"STALE":"HIT"),y&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,p.fromNodeOutgoingHttpHeaders)(b.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&q||v.delete(f.NEXT_CACHE_TAGS_HEADER),!b.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,m.getCacheControlHeader)(b.cacheControl)),await (0,d.sendResponse)(H,$,new Response(b.value.body,{headers:v,status:b.value.status||200})),null};I?await o(I):await _.withPropagatedContext(e.headers,()=>_.trace(l.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:a.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},o))}catch(t){if(I||t instanceof b.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:O,routeType:"route",revalidateReason:(0,c.getRevalidateReason)({isRevalidate:k,isOnDemandRevalidate:j})}),q)throw t;return await (0,d.sendResponse)(H,$,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d0e361e3._.js.map