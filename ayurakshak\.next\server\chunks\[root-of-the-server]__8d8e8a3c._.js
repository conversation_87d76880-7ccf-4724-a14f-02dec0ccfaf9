module.exports=[61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},26046,(e,t,r)=>{t.exports=e.x("mongoose",()=>require("mongoose"))},49991,e=>{"use strict";e.s(["default",()=>a]);var t=e.i(26046);let r=process.env.MONGODB_URI;if(!r)throw Error("Please define the MONGODB_URI environment variable inside .env");let n=e.g.mongoose;n||(n=e.g.mongoose={conn:null,promise:null});let a=async function(){if(n.conn)return n.conn;n.promise||(n.promise=t.default.connect(r,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},38335,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(26046);let r=new t.Schema({name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},email:{type:String,required:[!0,"Email is required"],trim:!0,lowercase:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email address"]},phone:{type:String,trim:!0,match:[/^[\+]?[1-9][\d]{0,15}$/,"Please enter a valid phone number"]},subject:{type:String,required:[!0,"Subject is required"],trim:!0,maxlength:[200,"Subject cannot exceed 200 characters"]},message:{type:String,required:[!0,"Message is required"],trim:!0,maxlength:[2e3,"Message cannot exceed 2000 characters"]},isRead:{type:Boolean,default:!1}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({email:1}),r.index({createdAt:-1}),r.index({isRead:1}),r.virtual("formattedDate").get(function(){return this.createdAt.toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}),r.pre("save",function(e){this.name=this.name.replace(/<[^>]*>?/gm,""),this.subject=this.subject.replace(/<[^>]*>?/gm,""),this.message=this.message.replace(/<[^>]*>?/gm,""),e()}),r.statics.getUnreadCount=function(){return this.countDocuments({isRead:!1})},r.statics.markAsRead=function(e){return this.findByIdAndUpdate(e,{isRead:!0},{new:!0})},r.statics.getRecent=function(e=10){return this.find().sort({createdAt:-1}).limit(e).select("name email subject createdAt isRead")};let n=t.default.models.ContactForm||t.default.model("ContactForm",r)},92095,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(26046);let r=new t.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email address"]},name:{type:String,trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},isActive:{type:Boolean,default:!0},subscribedAt:{type:Date,default:Date.now},unsubscribedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({email:1},{unique:!0}),r.index({isActive:1}),r.index({subscribedAt:-1}),r.virtual("subscriptionStatus").get(function(){return this.isActive?"Active":"Unsubscribed"}),r.virtual("formattedSubscriptionDate").get(function(){return this.subscribedAt.toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})}),r.pre("save",function(e){this.isActive||this.unsubscribedAt||(this.unsubscribedAt=new Date),this.isActive&&this.unsubscribedAt&&(this.unsubscribedAt=void 0),e()}),r.statics.getActiveCount=function(){return this.countDocuments({isActive:!0})},r.statics.unsubscribe=function(e){return this.findOneAndUpdate({email:e},{isActive:!1,unsubscribedAt:new Date},{new:!0})},r.statics.resubscribe=function(e){return this.findOneAndUpdate({email:e},{isActive:!0,$unset:{unsubscribedAt:1}},{new:!0,upsert:!0})},r.statics.getRecent=function(e=10){return this.find({isActive:!0}).sort({subscribedAt:-1}).limit(e).select("email name subscribedAt")},r.statics.bulkUnsubscribe=function(e){return this.updateMany({email:{$in:e}},{isActive:!1,unsubscribedAt:new Date})};let n=t.default.models.Newsletter||t.default.model("Newsletter",r)},4770,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(26046);let r=new t.Schema({donorName:{type:String,required:[!0,"Donor name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},donorEmail:{type:String,required:[!0,"Email is required"],trim:!0,lowercase:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email address"]},donorPhone:{type:String,trim:!0,match:[/^[\+]?[1-9][\d]{0,15}$/,"Please enter a valid phone number"]},amount:{type:Number,required:[!0,"Donation amount is required"],min:[1,"Amount must be at least ₹1"],max:[1e7,"Amount cannot exceed ₹1 crore"]},currency:{type:String,default:"INR",enum:["INR","USD","EUR"]},purpose:{type:String,required:[!0,"Purpose is required"],enum:["general","health","education","emergency","livelihoods"],default:"general"},paymentMethod:{type:String,required:[!0,"Payment method is required"],enum:["online","bank_transfer","cash","cheque"],default:"online"},paymentStatus:{type:String,required:[!0,"Payment status is required"],enum:["pending","completed","failed","refunded"],default:"pending"},transactionId:{type:String,trim:!0,sparse:!0},paymentGatewayResponse:{type:t.Schema.Types.Mixed},isAnonymous:{type:Boolean,default:!1},address:{street:{type:String,trim:!0,maxlength:[200,"Street address cannot exceed 200 characters"]},city:{type:String,trim:!0,maxlength:[50,"City cannot exceed 50 characters"]},state:{type:String,trim:!0,maxlength:[50,"State cannot exceed 50 characters"]},pincode:{type:String,trim:!0,match:[/^[1-9][0-9]{5}$/,"Please enter a valid pincode"]},country:{type:String,trim:!0,default:"India",maxlength:[50,"Country cannot exceed 50 characters"]}},panNumber:{type:String,trim:!0,uppercase:!0,match:[/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,"Please enter a valid PAN number"]},receiptNumber:{type:String,trim:!0,unique:!0,sparse:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({donorEmail:1}),r.index({paymentStatus:1}),r.index({purpose:1}),r.index({createdAt:-1}),r.index({amount:-1}),r.index({transactionId:1},{sparse:!0}),r.virtual("formattedAmount").get(function(){return new Intl.NumberFormat("en-IN",{style:"currency",currency:this.currency}).format(this.amount)}),r.virtual("formattedDate").get(function(){return this.createdAt.toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})}),r.virtual("purposeDisplayName").get(function(){return({general:"General Fund",health:"Health Camps",education:"Education Programs",emergency:"Emergency Relief",livelihoods:"Women Livelihoods"})[this.purpose]||this.purpose}),r.pre("save",function(e){if("completed"===this.paymentStatus&&!this.receiptNumber){let e=new Date().getFullYear(),t=String(new Date().getMonth()+1).padStart(2,"0"),r=Math.random().toString(36).substr(2,6).toUpperCase();this.receiptNumber=`AYU${e}${t}${r}`}this.donorName=this.donorName.replace(/<[^>]*>?/gm,""),e()}),r.statics.getTotalAmount=function(e){return this.aggregate([{$match:e?{purpose:e,paymentStatus:"completed"}:{paymentStatus:"completed"}},{$group:{_id:null,total:{$sum:"$amount"}}}])},r.statics.getStats=function(){return this.aggregate([{$match:{paymentStatus:"completed"}},{$group:{_id:"$purpose",totalAmount:{$sum:"$amount"},count:{$sum:1},avgAmount:{$avg:"$amount"}}}])},r.statics.getRecent=function(e=10){return this.find({paymentStatus:"completed"}).sort({createdAt:-1}).limit(e).select("donorName amount purpose createdAt isAnonymous")},r.statics.getTopDonors=function(e=10){return this.aggregate([{$match:{paymentStatus:"completed",isAnonymous:!1}},{$group:{_id:"$donorEmail",donorName:{$first:"$donorName"},totalAmount:{$sum:"$amount"},donationCount:{$sum:1},lastDonation:{$max:"$createdAt"}}},{$sort:{totalAmount:-1}},{$limit:e}])};let n=t.default.models.Donation||t.default.model("Donation",r)},25425,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(26046);let r=new t.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},customerEmail:{type:String,required:[!0,"Email is required"],trim:!0,lowercase:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email address"]},customerPhone:{type:String,required:[!0,"Phone number is required"],trim:!0,match:[/^[\+]?[1-9][\d]{0,15}$/,"Please enter a valid phone number"]},products:[{productId:{type:String,required:[!0,"Product ID is required"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],max:[100,"Quantity cannot exceed 100"]},price:{type:Number,required:[!0,"Price is required"],min:[0,"Price cannot be negative"]}}],totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},shippingAddress:{street:{type:String,required:[!0,"Street address is required"],trim:!0,maxlength:[200,"Street address cannot exceed 200 characters"]},city:{type:String,required:[!0,"City is required"],trim:!0,maxlength:[50,"City cannot exceed 50 characters"]},state:{type:String,required:[!0,"State is required"],trim:!0,maxlength:[50,"State cannot exceed 50 characters"]},pincode:{type:String,required:[!0,"Pincode is required"],trim:!0,match:[/^[1-9][0-9]{5}$/,"Please enter a valid pincode"]},country:{type:String,trim:!0,default:"India",maxlength:[50,"Country cannot exceed 50 characters"]}},orderStatus:{type:String,required:[!0,"Order status is required"],enum:["pending","confirmed","processing","shipped","delivered","cancelled"],default:"pending"},paymentStatus:{type:String,required:[!0,"Payment status is required"],enum:["pending","completed","failed","refunded"],default:"pending"},paymentMethod:{type:String,required:[!0,"Payment method is required"],enum:["cod","online","bank_transfer"],default:"cod"},trackingNumber:{type:String,trim:!0,sparse:!0},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"]}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({customerEmail:1}),r.index({orderStatus:1}),r.index({paymentStatus:1}),r.index({createdAt:-1}),r.index({trackingNumber:1},{sparse:!0}),r.virtual("orderNumber").get(function(){let e=this.createdAt.getFullYear(),t=String(this.createdAt.getMonth()+1).padStart(2,"0"),r=this._id.toString().slice(-6).toUpperCase();return`ORD${e}${t}${r}`}),r.virtual("formattedTotalAmount").get(function(){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR"}).format(this.totalAmount)}),r.virtual("formattedOrderDate").get(function(){return this.createdAt.toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})}),r.virtual("fullShippingAddress").get(function(){let e=this.shippingAddress;return`${e.street}, ${e.city}, ${e.state} - ${e.pincode}, ${e.country}`}),r.virtual("orderStatusDisplayName").get(function(){return({pending:"Pending",confirmed:"Confirmed",processing:"Processing",shipped:"Shipped",delivered:"Delivered",cancelled:"Cancelled"})[this.orderStatus]||this.orderStatus}),r.virtual("paymentStatusDisplayName").get(function(){return({pending:"Pending",completed:"Completed",failed:"Failed",refunded:"Refunded"})[this.paymentStatus]||this.paymentStatus}),r.pre("save",function(e){if(this.products&&this.products.length>0&&(this.totalAmount=this.products.reduce((e,t)=>e+t.price*t.quantity,0)),"shipped"===this.orderStatus&&!this.trackingNumber){let e=Math.random().toString(36).substr(2,10).toUpperCase();this.trackingNumber=`TRK${e}`}this.customerName=this.customerName.replace(/<[^>]*>?/gm,""),this.notes&&(this.notes=this.notes.replace(/<[^>]*>?/gm,"")),e()}),r.statics.getStats=function(){return this.aggregate([{$group:{_id:"$orderStatus",count:{$sum:1},totalAmount:{$sum:"$totalAmount"}}}])},r.statics.getRecent=function(e=10){return this.find().sort({createdAt:-1}).limit(e).select("customerName totalAmount orderStatus paymentStatus createdAt")},r.statics.getByCustomer=function(e){return this.find({customerEmail:e}).sort({createdAt:-1}).select("-customerEmail")},r.statics.updateStatus=function(e,t){return this.findByIdAndUpdate(e,{orderStatus:t},{new:!0})};let n=t.default.models.ProductOrder||t.default.model("ProductOrder",r)},21894,(e,t,r)=>{},18816,e=>{"use strict";e.s(["handler",()=>C,"patchFetch",()=>P,"routeModule",()=>q,"serverHooks",()=>E,"workAsyncStorage",()=>N,"workUnitAsyncStorage",()=>D],18816);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),i=e.i(61916),s=e.i(69741),o=e.i(16795),u=e.i(87718),d=e.i(95169),c=e.i(47587),l=e.i(66012),m=e.i(70101),p=e.i(26937),h=e.i(10372),g=e.i(93695);e.i(52474);var f=e.i(220);e.s(["GET",()=>$],43895);var y=e.i(89171),x=e.i(49991),v=e.i(38335),S=e.i(92095),A=e.i(4770),b=e.i(25425),w=e.i(78044);async function $(e){try{await (0,x.default)();let e=new Date,t=new Date(e.getTime()-2592e6);e.getTime();let[r,n,a,i,s,o,u,d,c,l,m,p]=await Promise.all([A.default.aggregate([{$match:{paymentStatus:"completed"}},{$group:{_id:null,total:{$sum:"$amount"},count:{$sum:1}}}]),A.default.distinct("donorEmail",{paymentStatus:"completed"}),A.default.countDocuments({paymentStatus:"completed",createdAt:{$gte:t}}),A.default.aggregate([{$match:{paymentStatus:"completed"}},{$group:{_id:"$purpose",totalAmount:{$sum:"$amount"},count:{$sum:1}}}]),S.default.countDocuments({isActive:!0}),S.default.countDocuments({isActive:!0,subscribedAt:{$gte:t}}),v.default.countDocuments(),v.default.countDocuments({isRead:!1}),v.default.countDocuments({createdAt:{$gte:t}}),b.default.countDocuments(),b.default.countDocuments({createdAt:{$gte:t}}),b.default.aggregate([{$group:{_id:"$orderStatus",count:{$sum:1},totalAmount:{$sum:"$totalAmount"}}}])]),h=new Date(e.getTime()-5184e6),[g,f,$,R]=await Promise.all([A.default.countDocuments({paymentStatus:"completed",createdAt:{$gte:h,$lt:t}}),S.default.countDocuments({isActive:!0,subscribedAt:{$gte:h,$lt:t}}),v.default.countDocuments({createdAt:{$gte:h,$lt:t}}),b.default.countDocuments({createdAt:{$gte:h,$lt:t}})]),q=(e,t)=>0===t?100*(e>0):Math.round((e-t)/t*100),N={donations:{total:r[0]?.total||0,count:r[0]?.count||0,uniqueDonors:n.length,recent:a,growth:q(a,g),byPurpose:i.map(e=>({purpose:e._id,amount:e.totalAmount,count:e.count,percentage:r[0]?.total?Math.round(e.totalAmount/r[0].total*100):0}))},newsletter:{totalSubscribers:s,recentSubscribers:o,growth:q(o,f)},contacts:{total:u,unread:d,recent:c,growth:q(c,$)},orders:{total:l,recent:m,growth:q(m,R),byStatus:p.map(e=>({status:e._id,count:e.count,totalAmount:e.totalAmount}))},impact:{livesImpacted:15e3,villagesReached:250,healthCamps:180,activePrograms:12,volunteers:85},recentActivity:{donations:a,subscribers:o,contacts:c,orders:m},performance:{donationGrowth:q(a,g),subscriberGrowth:q(o,f),contactGrowth:q(c,$),orderGrowth:q(m,R)}};return y.NextResponse.json((0,w.formatSuccessResponse)(N,"Statistics retrieved successfully"),{status:200})}catch(e){return console.error("Statistics fetch error:",e),y.NextResponse.json((0,w.formatErrorResponse)("An error occurred while fetching statistics."),{status:500})}}var R=e.i(43895);let q=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/stats/route",pathname:"/api/stats",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/stats/route.ts",nextConfigOutput:"",userland:R}),{workAsyncStorage:N,workUnitAsyncStorage:D,serverHooks:E}=q;function P(){return(0,n.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:D})}async function C(e,t,n){var y;let x="/api/stats/route";x=x.replace(/\/index$/,"")||"/";let v=await q.prepare(e,t,{srcPage:x,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:S,params:A,nextConfig:b,isDraftMode:w,prerenderManifest:$,routerServerContext:R,isOnDemandRevalidate:N,revalidateOnlyGenerated:D,resolvedPathname:E}=v,P=(0,s.normalizeAppPath)(x),C=!!($.dynamicRoutes[P]||$.routes[E]);if(C&&!w){let e=!!$.routes[E],t=$.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let O=null;!C||q.isDev||w||(O="/index"===(O=E)?"/":O);let I=!0===q.isDev||!C,_=C&&!I,j=e.method||"GET",T=(0,i.getTracer)(),U=T.getActiveScopeSpan(),k={params:A,prerenderManifest:$,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:I,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=b.experimental)?void 0:y.cacheLife,isRevalidate:_,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>q.onRequestError(e,t,n,R)},sharedContext:{buildId:S}},M=new o.NodeNextRequest(e),H=new o.NodeNextResponse(t),F=u.NextRequestAdapter.fromNodeNextRequest(M,(0,u.signalFromNodeResponse)(t));try{let s=async r=>q.handle(F,k).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=T.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${j} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${j} ${e.url}`)}),o=async i=>{var o,u;let d=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&N&&D&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await s(i);e.fetchMetrics=k.renderOpts.fetchMetrics;let u=k.renderOpts.pendingWaitUntil;u&&n.waitUntil&&(n.waitUntil(u),u=void 0);let d=k.renderOpts.collectedTags;if(!C)return await (0,l.sendResponse)(M,H,o,k.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,m.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==k.renderOpts.collectedRevalidate&&!(k.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&k.renderOpts.collectedRevalidate,n=void 0===k.renderOpts.collectedExpire||k.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:k.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await q.onRequestError(e,t,{routerKind:"App Router",routePath:x,routeType:"route",revalidateReason:(0,c.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})},R),t}},g=await q.handleResponse({req:e,nextConfig:b,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:$,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:D,responseGenerator:d,waitUntil:n.waitUntil});if(!C)return null;if((null==g||null==(o=g.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(u=g.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),w&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,m.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&C||y.delete(h.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,p.getCacheControlHeader)(g.cacheControl)),await (0,l.sendResponse)(M,H,new Response(g.value.body,{headers:y,status:g.value.status||200})),null};U?await o(U):await T.withPropagatedContext(e.headers,()=>T.trace(d.BaseServerSpan.handleRequest,{spanName:`${j} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":j,"http.target":e.url}},o))}catch(t){if(U||t instanceof g.NoFallbackError||await q.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,c.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})}),C)throw t;return await (0,l.sendResponse)(M,H,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__8d8e8a3c._.js.map