/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M22 12A10 10 0 1 1 12 2", key: "1fm58d" }],
  ["path", { d: "M22 2 12 12", key: "yg2myt" }],
  ["path", { d: "M16 2h6v6", key: "zan5cs" }]
];
const CircleArrowOutUpRight = createLucideIcon("circle-arrow-out-up-right", __iconNode);

export { __iconNode, CircleArrowOutUpRight as default };
//# sourceMappingURL=circle-arrow-out-up-right.js.map
