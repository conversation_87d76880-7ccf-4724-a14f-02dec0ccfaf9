'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Card, { <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const DonationSection: React.FC = () => {
  const [selectedAmount, setSelectedAmount] = useState(500);
  const [customAmount, setCustomAmount] = useState('');
  const [selectedPurpose, setSelectedPurpose] = useState('general');

  const predefinedAmounts = [100, 250, 500, 1000, 2500, 5000];

  const purposes = [
    {
      id: 'general',
      title: 'General Fund',
      description: 'Support all our programs and initiatives',
      icon: '🌟',
      impact: 'Helps us reach more communities',
    },
    {
      id: 'health',
      title: 'Health Camps',
      description: 'Mobile healthcare for remote villages',
      icon: '🏥',
      impact: '₹500 can provide treatment for 5 families',
    },
    {
      id: 'education',
      title: 'Health Education',
      description: 'Awareness programs and workshops',
      icon: '📚',
      impact: '₹1000 can educate 50 people',
    },
    {
      id: 'livelihoods',
      title: 'Women Empowerment',
      description: 'Skill development and microfinance',
      icon: '👩‍💼',
      impact: '₹2500 can train 10 women entrepreneurs',
    },
    {
      id: 'emergency',
      title: 'Emergency Relief',
      description: 'Disaster response and urgent care',
      icon: '🚨',
      impact: '₹1000 can provide emergency kit for 1 family',
    },
  ];

  const impactCalculator = (amount: number, purpose: string) => {
    const impacts = {
      general: Math.floor(amount / 100),
      health: Math.floor(amount / 100),
      education: Math.floor(amount / 20),
      livelihoods: Math.floor(amount / 250),
      emergency: Math.floor(amount / 1000),
    };
    return impacts[purpose as keyof typeof impacts] || 0;
  };

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    setSelectedAmount(0);
  };

  const getCurrentAmount = () => {
    return customAmount ? parseInt(customAmount) || 0 : selectedAmount;
  };

  return (
    <section className="py-20 bg-gradient-to-br from-primary-600 to-primary-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Make a <span className="text-yellow-300">Difference</span>
          </h2>
          <p className="text-xl text-primary-100 max-w-3xl mx-auto leading-relaxed">
            Your contribution can transform lives and heal communities. Every rupee counts towards building a healthier India.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Donation Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card variant="glass" className="bg-white/10 backdrop-blur-md border-white/20">
              <CardHeader>
                <CardTitle className="text-2xl text-white text-center">
                  Choose Your Contribution
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Purpose Selection */}
                <div>
                  <label className="block text-white font-medium mb-3">
                    Select Purpose
                  </label>
                  <div className="grid grid-cols-1 gap-3">
                    {purposes.map((purpose) => (
                      <button
                        key={purpose.id}
                        onClick={() => setSelectedPurpose(purpose.id)}
                        className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                          selectedPurpose === purpose.id
                            ? 'border-yellow-300 bg-white/20'
                            : 'border-white/30 hover:border-white/50 bg-white/10'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{purpose.icon}</span>
                          <div className="flex-1">
                            <h4 className="font-semibold text-white">{purpose.title}</h4>
                            <p className="text-sm text-white/80">{purpose.description}</p>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Amount Selection */}
                <div>
                  <label className="block text-white font-medium mb-3">
                    Select Amount (₹)
                  </label>
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    {predefinedAmounts.map((amount) => (
                      <button
                        key={amount}
                        onClick={() => handleAmountSelect(amount)}
                        className={`p-3 rounded-lg border-2 transition-all duration-200 font-semibold ${
                          selectedAmount === amount
                            ? 'border-yellow-300 bg-yellow-300 text-primary-800'
                            : 'border-white/30 text-white hover:border-white/50'
                        }`}
                      >
                        ₹{amount}
                      </button>
                    ))}
                  </div>
                  
                  <input
                    type="number"
                    placeholder="Enter custom amount"
                    value={customAmount}
                    onChange={(e) => handleCustomAmountChange(e.target.value)}
                    className="w-full p-3 rounded-lg bg-white/10 border-2 border-white/30 text-white placeholder-white/60 focus:border-yellow-300 focus:outline-none"
                  />
                </div>

                {/* Impact Display */}
                {getCurrentAmount() > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-white/20 rounded-lg p-4"
                  >
                    <h4 className="font-semibold text-white mb-2">Your Impact:</h4>
                    <p className="text-white/90">
                      ₹{getCurrentAmount()} can help{' '}
                      <span className="font-bold text-yellow-300">
                        {impactCalculator(getCurrentAmount(), selectedPurpose)}
                      </span>{' '}
                      {selectedPurpose === 'general' && 'people through our programs'}
                      {selectedPurpose === 'health' && 'families receive healthcare'}
                      {selectedPurpose === 'education' && 'people get health education'}
                      {selectedPurpose === 'livelihoods' && 'women start businesses'}
                      {selectedPurpose === 'emergency' && 'families in emergencies'}
                    </p>
                  </motion.div>
                )}

                {/* Donate Button */}
                <Button
                  variant="secondary"
                  size="lg"
                  className="w-full bg-yellow-400 text-primary-800 hover:bg-yellow-300 font-bold text-lg py-4"
                  disabled={getCurrentAmount() === 0}
                >
                  Donate ₹{getCurrentAmount()} Now
                </Button>

                {/* Security Note */}
                <p className="text-xs text-white/70 text-center">
                  🔒 Secure payment • 80G Tax exemption available • 100% transparent usage
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Impact Information */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Why Donate */}
            <div>
              <h3 className="text-3xl font-bold text-white mb-6">
                Why Your Donation Matters
              </h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="bg-yellow-400 rounded-full p-2 mt-1">
                    <svg className="w-4 h-4 text-primary-800" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-white">Direct Impact</h4>
                    <p className="text-primary-100">100% of your donation goes directly to programs, not administrative costs</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-yellow-400 rounded-full p-2 mt-1">
                    <svg className="w-4 h-4 text-primary-800" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-white">Tax Benefits</h4>
                    <p className="text-primary-100">Get 80G tax exemption on your donations as per Indian tax laws</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-yellow-400 rounded-full p-2 mt-1">
                    <svg className="w-4 h-4 text-primary-800" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-white">Transparency</h4>
                    <p className="text-primary-100">Regular updates on how your contribution is making a difference</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Impact */}
            <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
              <h4 className="text-xl font-bold text-white mb-4">Recent Impact</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-primary-100">Health camps this month</span>
                  <span className="font-bold text-yellow-300">15</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-primary-100">Families helped</span>
                  <span className="font-bold text-yellow-300">1,250</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-primary-100">Women trained</span>
                  <span className="font-bold text-yellow-300">180</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-primary-100">Villages reached</span>
                  <span className="font-bold text-yellow-300">25</span>
                </div>
              </div>
            </div>

            {/* Other Ways to Help */}
            <div>
              <h4 className="text-xl font-bold text-white mb-4">Other Ways to Help</h4>
              <div className="space-y-3">
                <Button variant="outline" className="w-full border-white text-white hover:bg-white hover:text-primary-800">
                  Become a Volunteer
                </Button>
                <Button variant="outline" className="w-full border-white text-white hover:bg-white hover:text-primary-800">
                  Corporate Partnership
                </Button>
                <Button variant="outline" className="w-full border-white text-white hover:bg-white hover:text-primary-800">
                  Sponsor a Program
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default DonationSection;
