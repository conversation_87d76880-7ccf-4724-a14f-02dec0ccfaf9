(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,98183,(e,t,i)=>{"use strict";function r(e){let t={};for(let[i,r]of e.entries()){let e=t[i];void 0===e?t[i]=r:Array.isArray(e)?e.push(r):t[i]=[e,r]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function s(e){let t=new URLSearchParams;for(let[i,r]of Object.entries(e))if(Array.isArray(r))for(let e of r)t.append(i,n(e));else t.set(i,n(r));return t}function a(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];for(let t of i){for(let i of t.keys())e.delete(i);for(let[i,r]of t.entries())e.append(i,r)}return e}Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return s}})},95057,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{formatUrl:function(){return s},formatWithValidation:function(){return o},urlObjectKeys:function(){return a}});let r=e.r(90809)._(e.r(98183)),n=/https?|ftp|gopher|file/;function s(e){let{auth:t,hostname:i}=e,s=e.protocol||"",a=e.pathname||"",o=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:i&&(u=t+(~i.indexOf(":")?"["+i+"]":i),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),e.slashes||(!s||n.test(s))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),o&&"#"!==o[0]&&(o="#"+o),c&&"?"!==c[0]&&(c="?"+c),""+s+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+o}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return s(e)}},18581,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"useMergedRef",{enumerable:!0,get:function(){return n}});let r=e.r(71645);function n(e,t){let i=(0,r.useRef)(null),n=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=i.current;e&&(i.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(i.current=s(e,r)),t&&(n.current=s(t,r))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},18967,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return y},NormalizeError:function(){return g},PageNotFoundError:function(){return v},SP:function(){return m},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return o},getURL:function(){return l},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,i=!1;return function(){for(var r=arguments.length,n=Array(r),s=0;s<r;s++)n[s]=arguments[s];return i||(i=!0,t=e(...n)),t}}let s=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>s.test(e);function o(){let{protocol:e,hostname:t,port:i}=window.location;return e+"//"+t+(i?":"+i:"")}function l(){let{href:e}=window.location,t=o();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let i=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(i&&c(i))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let m="undefined"!=typeof performance,p=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class g extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"isLocalURL",{enumerable:!0,get:function(){return s}});let r=e.r(18967),n=e.r(52817);function s(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),i=new URL(e,t);return i.origin===t&&(0,n.hasBasePath)(i.pathname)}catch(e){return!1}}},84508,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},22016,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{default:function(){return g},useLinkStatus:function(){return y}});let r=e.r(90809),n=e.r(43476),s=r._(e.r(71645)),a=e.r(95057),o=e.r(8372),l=e.r(18581),u=e.r(18967),c=e.r(5550);e.r(33525);let d=e.r(91949),h=e.r(73668),m=e.r(99781);e.r(84508);let p=e.r(65165);function f(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function g(e){var t;let i,r,a,[g,y]=(0,s.useOptimistic)(d.IDLE_LINK_STATUS),x=(0,s.useRef)(null),{href:b,as:w,children:j,prefetch:k=null,passHref:P,replace:T,shallow:S,scroll:C,onClick:M,onMouseEnter:A,onTouchStart:N,legacyBehavior:E=!1,onNavigate:R,ref:D,unstable_dynamicOnHover:V,...L}=e;i=j,E&&("string"==typeof i||"number"==typeof i)&&(i=(0,n.jsx)("a",{children:i}));let O=s.default.useContext(o.AppRouterContext),I=!1!==k,F=!1!==k?null===(t=k)||"auto"===t?p.FetchStrategy.PPR:p.FetchStrategy.Full:p.FetchStrategy.PPR,{href:z,as:B}=s.default.useMemo(()=>{let e=f(b);return{href:e,as:w?f(w):e}},[b,w]);E&&(r=s.default.Children.only(i));let _=E?r&&"object"==typeof r&&r.ref:D,U=s.default.useCallback(e=>(null!==O&&(x.current=(0,d.mountLinkInstance)(e,z,O,F,I,y)),()=>{x.current&&((0,d.unmountLinkForCurrentNavigation)(x.current),x.current=null),(0,d.unmountPrefetchableInstance)(e)}),[I,z,O,F,y]),W={ref:(0,l.useMergedRef)(U,_),onClick(e){E||"function"!=typeof M||M(e),E&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),O&&(e.defaultPrevented||function(e,t,i,r,n,a,o){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,h.isLocalURL)(t)){n&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}s.default.startTransition(()=>{(0,m.dispatchNavigateAction)(i||t,n?"replace":"push",null==a||a,r.current)})}}(e,z,B,x,T,C,R))},onMouseEnter(e){E||"function"!=typeof A||A(e),E&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),O&&I&&(0,d.onNavigationIntent)(e.currentTarget,!0===V)},onTouchStart:function(e){E||"function"!=typeof N||N(e),E&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),O&&I&&(0,d.onNavigationIntent)(e.currentTarget,!0===V)}};return(0,u.isAbsoluteUrl)(B)?W.href=B:E&&!P&&("a"!==r.type||"href"in r.props)||(W.href=(0,c.addBasePath)(B)),a=E?s.default.cloneElement(r,W):(0,n.jsx)("a",{...L,...W,children:i}),(0,n.jsx)(v.Provider,{value:g,children:a})}let v=(0,s.createContext)(d.IDLE_LINK_STATUS),y=()=>(0,s.useContext)(v);("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},88143,(e,t,i)=>{"use strict";function r(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:n,blurDataURL:s,objectFit:a}=e,o=r?40*r:t,l=n?40*n:i,u=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},87690,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},8927,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"getImgProps",{enumerable:!0,get:function(){return l}}),e.r(33525);let r=e.r(88143),n=e.r(87690),s=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let u,c,d,{src:h,sizes:m,unoptimized:p=!1,priority:f=!1,loading:g,className:v,quality:y,width:x,height:b,fill:w=!1,style:j,overrideSrc:k,onLoad:P,onLoadingComplete:T,placeholder:S="empty",blurDataURL:C,fetchPriority:M,decoding:A="async",layout:N,objectFit:E,objectPosition:R,lazyBoundary:D,lazyRoot:V,...L}=e,{imgConf:O,showAltText:I,blurComplete:F,defaultLoader:z}=t,B=O||n.imageConfigDefault;if("allSizes"in B)u=B;else{let e=[...B.deviceSizes,...B.imageSizes].sort((e,t)=>e-t),t=B.deviceSizes.sort((e,t)=>e-t),r=null==(i=B.qualities)?void 0:i.sort((e,t)=>e-t);u={...B,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===z)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let _=L.loader||z;delete L.loader,delete L.srcSet;let U="__next_img_default"in _;if(U){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=_;_=t=>{let{config:i,...r}=t;return e(r)}}if(N){"fill"===N&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[N];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[N];t&&!m&&(m=t)}let W="",H=o(x),Y=o(b);if((l=h)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(h)?h.default:h;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,d=e.blurHeight,C=C||e.blurDataURL,W=e.src,!w)if(H||Y){if(H&&!Y){let t=H/e.width;Y=Math.round(e.height*t)}else if(!H&&Y){let t=Y/e.height;H=Math.round(e.width*t)}}else H=e.width,Y=e.height}let G=!f&&("lazy"===g||void 0===g);(!(h="string"==typeof h?h:W)||h.startsWith("data:")||h.startsWith("blob:"))&&(p=!0,G=!1),u.unoptimized&&(p=!0),U&&!u.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(p=!0);let X=o(y),K=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:E,objectPosition:R}:{},I?{}:{color:"transparent"},j),q=F||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:H,heightInt:Y,blurWidth:c,blurHeight:d,blurDataURL:C||"",objectFit:K.objectFit})+'")':'url("'+S+'")',$=s.includes(K.objectFit)?"fill"===K.objectFit?"100% 100%":"cover":K.objectFit,Z=q?{backgroundSize:$,backgroundPosition:K.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:q}:{},J=function(e){let{config:t,src:i,unoptimized:r,width:n,quality:s,sizes:a,loader:o}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,i){let{deviceSizes:r,allSizes:n}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,a),c=l.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:l.map((e,r)=>o({config:t,src:i,quality:s,width:e})+" "+("w"===u?e:r+1)+u).join(", "),src:o({config:t,src:i,quality:s,width:l[c]})}}({config:u,src:h,unoptimized:p,width:H,quality:X,sizes:m,loader:_});return{props:{...L,loading:G?"lazy":g,fetchPriority:M,width:H,height:Y,decoding:A,className:v,style:{...K,...Z},sizes:J.sizes,srcSet:J.srcSet,src:k||J.src},meta:{unoptimized:p,priority:f,placeholder:S,fill:w}}}},98879,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"default",{enumerable:!0,get:function(){return o}});let r=e.r(71645),n="undefined"==typeof window,s=n?()=>{}:r.useLayoutEffect,a=n?()=>{}:r.useEffect;function o(e){let{headManager:t,reduceComponentsToState:i}=e;function o(){if(t&&t.mountedInstances){let n=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(i(n,e))}}if(n){var l;null==t||null==(l=t.mountedInstances)||l.add(e.children),o()}return s(()=>{var i;return null==t||null==(i=t.mountedInstances)||i.add(e.children),()=>{var i;null==t||null==(i=t.mountedInstances)||i.delete(e.children)}}),s(()=>(t&&(t._pendingUpdate=o),()=>{t&&(t._pendingUpdate=o)})),a(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},58908,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=e.r(55682)._(e.r(71645)).default.createContext({})},15986,(e,t,i)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||i&&r}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"isInAmpMode",{enumerable:!0,get:function(){return r}})},25633,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{default:function(){return f},defaultHead:function(){return d}});let r=e.r(55682),n=e.r(90809),s=e.r(43476),a=n._(e.r(71645)),o=r._(e.r(98879)),l=e.r(58908),u=e.r(42732),c=e.r(15986);function d(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function h(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}e.r(33525);let m=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:i}=t;return e.reduce(h,[]).reverse().concat(d(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,r={};return n=>{let s=!0,a=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){a=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?s=!1:t.add(n.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(n.props.hasOwnProperty(t))if("charSet"===t)i.has(t)?s=!1:i.add(t);else{let e=n.props[t],i=r[t]||new Set;("name"!==t||!a)&&i.has(e)?s=!1:(i.add(e),r[t]=i)}}}return s}}()).reverse().map((e,t)=>{let i=e.key||t;return a.default.cloneElement(e,{key:i})})}let f=function(e){let{children:t}=e,i=(0,a.useContext)(l.AmpStateContext),r=(0,a.useContext)(u.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:p,headManager:r,inAmpMode:(0,c.isInAmpMode)(i),children:t})};("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},18556,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"ImageConfigContext",{enumerable:!0,get:function(){return s}});let r=e.r(55682)._(e.r(71645)),n=e.r(87690),s=r.default.createContext(n.imageConfigDefault)},65856,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"RouterContext",{enumerable:!0,get:function(){return r}});let r=e.r(55682)._(e.r(71645)).default.createContext(null)},1948,(e,t,i)=>{"use strict";function r(e){var t;let{config:i,src:r,width:n,quality:s}=e,a=s||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+a+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},85437,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"Image",{enumerable:!0,get:function(){return b}});let r=e.r(55682),n=e.r(90809),s=e.r(43476),a=n._(e.r(71645)),o=r._(e.r(74080)),l=r._(e.r(25633)),u=e.r(8927),c=e.r(87690),d=e.r(18556);e.r(33525);let h=e.r(65856),m=r._(e.r(1948)),p=e.r(18581),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,i,r,n,s,a){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,n=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function v(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let y=(0,a.forwardRef)((e,t)=>{let{src:i,srcSet:r,sizes:n,height:o,width:l,decoding:u,className:c,style:d,fetchPriority:h,placeholder:m,loading:f,unoptimized:y,fill:x,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:j,setShowAltText:k,sizesInput:P,onLoad:T,onError:S,...C}=e,M=(0,a.useCallback)(e=>{e&&(S&&(e.src=e.src),e.complete&&g(e,m,b,w,j,y,P))},[i,m,b,w,j,S,y,P]),A=(0,p.useMergedRef)(t,M);return(0,s.jsx)("img",{...C,...v(h),loading:f,width:l,height:o,decoding:u,"data-nimg":x?"fill":"1",className:c,style:d,sizes:n,srcSet:r,src:i,ref:A,onLoad:e=>{g(e.currentTarget,m,b,w,j,y,P)},onError:e=>{k(!0),"empty"!==m&&j(!0),S&&S(e)}})});function x(e){let{isAppRouter:t,imgAttributes:i}=e,r={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...v(i.fetchPriority)};return t&&o.default.preload?(o.default.preload(i.src,r),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...r},"__nimg-"+i.src+i.srcSet+i.sizes)})}let b=(0,a.forwardRef)((e,t)=>{let i=(0,a.useContext)(h.RouterContext),r=(0,a.useContext)(d.ImageConfigContext),n=(0,a.useMemo)(()=>{var e;let t=f||r||c.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:n,qualities:s}},[r]),{onLoad:o,onLoadingComplete:l}=e,p=(0,a.useRef)(o);(0,a.useEffect)(()=>{p.current=o},[o]);let g=(0,a.useRef)(l);(0,a.useEffect)(()=>{g.current=l},[l]);let[v,b]=(0,a.useState)(!1),[w,j]=(0,a.useState)(!1),{props:k,meta:P}=(0,u.getImgProps)(e,{defaultLoader:m.default,imgConf:n,blurComplete:v,showAltText:w});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y,{...k,unoptimized:P.unoptimized,placeholder:P.placeholder,fill:P.fill,onLoadRef:p,onLoadingCompleteRef:g,setBlurComplete:b,setShowAltText:j,sizesInput:e.sizes,ref:t}),P.priority?(0,s.jsx)(x,{isAppRouter:!i,imgAttributes:k}):null]})});("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},94909,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{default:function(){return l},getImageProps:function(){return o}});let r=e.r(55682),n=e.r(8927),s=e.r(85437),a=r._(e.r(1948));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=s.Image},57688,(e,t,i)=>{t.exports=e.r(94909)},46932,31178,37806,21476,47414,74008,64978,72846,e=>{"use strict";let t;e.s(["motion",()=>ss],46932);var i=e.i(71645);let r=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],n=new Set(r),s=e=>180*e/Math.PI,a=e=>l(s(Math.atan2(e[1],e[0]))),o={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:a,rotateZ:a,skewX:e=>s(Math.atan(e[1])),skewY:e=>s(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},l=e=>((e%=360)<0&&(e+=360),e),u=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),c=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),d={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:u,scaleY:c,scale:e=>(u(e)+c(e))/2,rotateX:e=>l(s(Math.atan2(e[6],e[5]))),rotateY:e=>l(s(Math.atan2(-e[2],e[0]))),rotateZ:a,rotate:a,skewX:e=>s(Math.atan(e[4])),skewY:e=>s(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function h(e){return+!!e.includes("scale")}function m(e,t){let i,r;if(!e||"none"===e)return h(t);let n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=d,r=n;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=o,r=t}if(!r)return h(t);let s=i[t],a=r[1].split(",").map(p);return"function"==typeof s?s(a):a[s]}function p(e){return parseFloat(e.trim())}let f=e=>t=>"string"==typeof t&&t.startsWith(e),g=f("--"),v=f("var(--"),y=e=>!!v(e)&&x.test(e.split("/*")[0].trim()),x=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function b(e){let{top:t,left:i,right:r,bottom:n}=e;return{x:{min:i,max:r},y:{min:t,max:n}}}let w=(e,t,i)=>e+(t-e)*i;function j(e){return void 0===e||1===e}function k(e){let{scale:t,scaleX:i,scaleY:r}=e;return!j(t)||!j(i)||!j(r)}function P(e){return k(e)||T(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function T(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function S(e,t,i,r,n){return void 0!==n&&(e=r+n*(e-r)),r+i*(e-r)+t}function C(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,n=arguments.length>4?arguments[4]:void 0;e.min=S(e.min,t,i,r,n),e.max=S(e.max,t,i,r,n)}function M(e,t){let{x:i,y:r}=t;C(e.x,i.translate,i.scale,i.originPoint),C(e.y,r.translate,r.scale,r.originPoint)}function A(e,t){e.min=e.min+t,e.max=e.max+t}function N(e,t,i,r){let n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5,s=w(e.min,e.max,n);C(e,t,i,s,r)}function E(e,t){N(e.x,t.x,t.scaleX,t.scale,t.originX),N(e.y,t.y,t.scaleY,t.scale,t.originY)}function R(e,t){return b(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let D=new Set(["width","height","top","left","right","bottom",...r]),V=(e,t,i)=>i>t?t:i<e?e:i,L={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},O={...L,transform:e=>V(0,1,e)},I={...L,default:1},F=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),z=F("deg"),B=F("%"),_=F("px"),U=F("vh"),W=F("vw"),H={...B,parse:e=>B.parse(e)/100,transform:e=>B.transform(100*e)},Y=e=>t=>t.test(e),G=[L,_,B,z,W,U,{test:e=>"auto"===e,parse:e=>e}],X=e=>G.find(Y(e));e.i(47167);let K=()=>{},q=()=>{},$=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Z=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,J=e=>e===L||e===_,Q=new Set(["x","y","z"]),ee=r.filter(e=>!Q.has(e)),et={width:(e,t)=>{let{x:i}=e,{paddingLeft:r="0",paddingRight:n="0"}=t;return i.max-i.min-parseFloat(r)-parseFloat(n)},height:(e,t)=>{let{y:i}=e,{paddingTop:r="0",paddingBottom:n="0"}=t;return i.max-i.min-parseFloat(r)-parseFloat(n)},top:(e,t)=>{let{top:i}=t;return parseFloat(i)},left:(e,t)=>{let{left:i}=t;return parseFloat(i)},bottom:(e,t)=>{let{y:i}=e,{top:r}=t;return parseFloat(r)+(i.max-i.min)},right:(e,t)=>{let{x:i}=e,{left:r}=t;return parseFloat(r)+(i.max-i.min)},x:(e,t)=>{let{transform:i}=t;return m(i,"x")},y:(e,t)=>{let{transform:i}=t;return m(i,"y")}};et.translateX=et.x,et.translateY=et.y;let ei=e=>e,er={},en=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],es={value:null,addProjectionMetrics:null};function ea(e,t){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=en.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,r=new Set,n=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){a.has(t)&&(c.schedule(t),e()),l++,t(o)}let c={schedule:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=s&&n?i:r;return t&&a.add(e),o.has(e)||o.add(e),e},cancel:e=>{r.delete(e),a.delete(e)},process:e=>{if(o=e,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(u),t&&es.value&&es.value.frameloop[t].push(l),l=0,i.clear(),n=!1,s&&(s=!1,c.process(e))}};return c}(s,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:c,update:d,preRender:h,render:m,postRender:p}=a,f=()=>{let s=er.useManualTiming?n.timestamp:performance.now();i=!1,er.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,o.process(n),l.process(n),u.process(n),c.process(n),d.process(n),h.process(n),m.process(n),p.process(n),n.isProcessing=!1,i&&t&&(r=!1,e(f))};return{schedule:en.reduce((t,s)=>{let o=a[s];return t[s]=function(t){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return!i&&(i=!0,r=!0,n.isProcessing||e(f)),o.schedule(t,s,a)},t},{}),cancel:e=>{for(let t=0;t<en.length;t++)a[en[t]].cancel(e)},state:n,steps:a}}let{schedule:eo,cancel:el,state:eu,steps:ec}=ea("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ei,!0),ed=new Set,eh=!1,em=!1,ep=!1;function ef(){if(em){let e=Array.from(ed).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return ee.forEach(i=>{let r=e.getValue(i);void 0!==r&&(t.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(t=>{var i;let[r,n]=t;null==(i=e.getValue(r))||i.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}em=!1,eh=!1,ed.forEach(e=>e.complete(ep)),ed.clear()}function eg(){ed.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(em=!0)})}class ev{scheduleResolve(){this.state="scheduled",this.isAsync?(ed.add(this),eh||(eh=!0,eo.read(eg),eo.resolveKeyframes(ef))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:r}=this;if(null===e[0]){let n=null==r?void 0:r.get(),s=e[e.length-1];if(void 0!==n)e[0]=n;else if(i&&t){let r=i.readValue(t,s);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=s),r&&void 0===n&&r.set(e[0])}for(let t=1;t<e.length;t++)null!=e[t]||(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ed.delete(this)}cancel(){"scheduled"===this.state&&(ed.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}constructor(e,t,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}}let ey=e=>/^0[^.\s]+$/u.test(e),ex=e=>Math.round(1e5*e)/1e5,eb=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ew=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ej=(e,t)=>i=>!!("string"==typeof i&&ew.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),ek=(e,t,i)=>r=>{if("string"!=typeof r)return r;let[n,s,a,o]=r.match(eb);return{[e]:parseFloat(n),[t]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},eP={...L,transform:e=>Math.round(V(0,255,e))},eT={test:ej("rgb","red"),parse:ek("red","green","blue"),transform:e=>{let{red:t,green:i,blue:r,alpha:n=1}=e;return"rgba("+eP.transform(t)+", "+eP.transform(i)+", "+eP.transform(r)+", "+ex(O.transform(n))+")"}},eS={test:ej("#"),parse:function(e){let t="",i="",r="",n="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),r=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),r=e.substring(3,4),n=e.substring(4,5),t+=t,i+=i,r+=r,n+=n),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:eT.transform},eC={test:ej("hsl","hue"),parse:ek("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:i,lightness:r,alpha:n=1}=e;return"hsla("+Math.round(t)+", "+B.transform(ex(i))+", "+B.transform(ex(r))+", "+ex(O.transform(n))+")"}},eM={test:e=>eT.test(e)||eS.test(e)||eC.test(e),parse:e=>eT.test(e)?eT.parse(e):eC.test(e)?eC.parse(e):eS.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eT.transform(e):eC.transform(e),getAnimatableNone:e=>{let t=eM.parse(e);return t.alpha=0,eM.transform(t)}},eA=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eN="number",eE="color",eR=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eD(e){let t=e.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,a=t.replace(eR,e=>(eM.test(e)?(r.color.push(s),n.push(eE),i.push(eM.parse(e))):e.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(e)):(r.number.push(s),n.push(eN),i.push(parseFloat(e))),++s,"${}")).split("${}");return{values:i,split:a,indexes:r,types:n}}function eV(e){return eD(e).values}function eL(e){let{split:t,types:i}=eD(e),r=t.length;return e=>{let n="";for(let s=0;s<r;s++)if(n+=t[s],void 0!==e[s]){let t=i[s];t===eN?n+=ex(e[s]):t===eE?n+=eM.transform(e[s]):n+=e[s]}return n}}let eO=e=>"number"==typeof e?0:eM.test(e)?eM.getAnimatableNone(e):e,eI={test:function(e){var t,i;return isNaN(e)&&"string"==typeof e&&((null==(t=e.match(eb))?void 0:t.length)||0)+((null==(i=e.match(eA))?void 0:i.length)||0)>0},parse:eV,createTransformer:eL,getAnimatableNone:function(e){let t=eV(e);return eL(e)(t.map(eO))}},eF=new Set(["brightness","contrast","saturate","opacity"]);function ez(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=i.match(eb)||[];if(!r)return e;let n=i.replace(r,""),s=+!!eF.has(t);return r!==i&&(s*=100),t+"("+s+n+")"}let eB=/\b([a-z-]*)\(.*?\)/gu,e_={...eI,getAnimatableNone:e=>{let t=e.match(eB);return t?t.map(ez).join(" "):e}},eU={...L,transform:Math.round},eW={borderWidth:_,borderTopWidth:_,borderRightWidth:_,borderBottomWidth:_,borderLeftWidth:_,borderRadius:_,radius:_,borderTopLeftRadius:_,borderTopRightRadius:_,borderBottomRightRadius:_,borderBottomLeftRadius:_,width:_,maxWidth:_,height:_,maxHeight:_,top:_,right:_,bottom:_,left:_,padding:_,paddingTop:_,paddingRight:_,paddingBottom:_,paddingLeft:_,margin:_,marginTop:_,marginRight:_,marginBottom:_,marginLeft:_,backgroundPositionX:_,backgroundPositionY:_,rotate:z,rotateX:z,rotateY:z,rotateZ:z,scale:I,scaleX:I,scaleY:I,scaleZ:I,skew:z,skewX:z,skewY:z,distance:_,translateX:_,translateY:_,translateZ:_,x:_,y:_,z:_,perspective:_,transformPerspective:_,opacity:O,originX:H,originY:H,originZ:_,zIndex:eU,fillOpacity:O,strokeOpacity:O,numOctaves:eU},eH={...eW,color:eM,backgroundColor:eM,outlineColor:eM,fill:eM,stroke:eM,borderColor:eM,borderTopColor:eM,borderRightColor:eM,borderBottomColor:eM,borderLeftColor:eM,filter:e_,WebkitFilter:e_},eY=e=>eH[e];function eG(e,t){let i=eY(e);return i!==e_&&(i=eI),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let eX=new Set(["auto","none","0"]);class eK extends ev{readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let r=e[i];if("string"==typeof r&&y(r=r.trim())){let n=function e(t,i){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;q(r<=4,'Max CSS variable fallback depth detected in property "'.concat(t,'". This may indicate a circular fallback dependency.'),"max-css-var-depth");let[n,s]=function(e){let t=Z.exec(e);if(!t)return[,];let[,i,r,n]=t;return["--".concat(null!=i?i:r),n]}(t);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let e=a.trim();return $(e)?parseFloat(e):e}return y(s)?e(s,i,r+1):s}(r,t.current);void 0!==n&&(e[i]=n),i===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!D.has(i)||2!==e.length)return;let[r,n]=e,s=X(r),a=X(n);if(s!==a)if(J(s)&&J(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else et[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||ey(r)))&&i.push(t)}i.length&&function(e,t,i){let r,n=0;for(;n<e.length&&!r;){let t=e[n];"string"==typeof t&&!eX.has(t)&&eD(t).values.length&&(r=e[n]),n++}if(r&&i)for(let n of t)e[n]=eG(i,r)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=et[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(i,r).jump(r,!1)}measureEndState(){var e;let{element:t,name:i,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let n=t.getValue(i);n&&n.jump(this.measuredOrigin,!1);let s=r.length-1,a=r[s];r[s]=et[i](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),(null==(e=this.removedTransforms)?void 0:e.length)&&this.removedTransforms.forEach(e=>{let[i,r]=e;t.getValue(i).set(r)}),this.resolveNoneKeyframes()}constructor(e,t,i,r,n){super(e,t,i,r,n,!0)}}let eq=e=>!!(e&&e.getVelocity);function e$(){t=void 0}let eZ={now:()=>(void 0===t&&eZ.set(eu.isProcessing||er.useManualTiming?eu.timestamp:performance.now()),t),set:e=>{t=e,queueMicrotask(e$)}};function eJ(e,t){-1===e.indexOf(t)&&e.push(t)}function eQ(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class e0{add(e){return eJ(this.subscriptions,e),()=>eQ(this.subscriptions,e)}notify(e,t,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}constructor(){this.subscriptions=[]}}let e1={current:void 0};class e2{setCurrent(e){this.current=e,this.updatedAt=eZ.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.current;this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new e0);let i=this.events[e].add(t);return"change"===e?()=>{i(),eo.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;null==(e=this.events.change)||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return e1.current&&e1.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=eZ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,t;null==(e=this.dependents)||e.clear(),null==(t=this.events.destroy)||t.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=e=>{let t=eZ.now();if(this.updatedAt!==t&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev){var i;if(null==(i=this.events.change)||i.notify(this.current),this.dependents)for(let e of this.dependents)e.dirty()}},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}}function e4(e,t){return new e2(e,t)}let e3=[...G,eM,eI],{schedule:e5}=ea(queueMicrotask,!1),e6={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},e8={};for(let e in e6)e8[e]={isEnabled:t=>e6[e].some(e=>!!t[e])};let e7=()=>({translate:0,scale:1,origin:0,originPoint:0}),e9=()=>({x:e7(),y:e7()}),te=()=>({min:0,max:0}),tt=()=>({x:te(),y:te()}),ti="undefined"!=typeof window,tr={current:null},tn={current:!1},ts=new WeakMap;function ta(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function to(e){return"string"==typeof e||Array.isArray(e)}let tl=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tu=["initial",...tl];function tc(e){return ta(e.animate)||tu.some(t=>to(e[t]))}function td(e){return!!(tc(e)||e.variants)}function th(e){let t=[{},{}];return null==e||e.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function tm(e,t,i,r){if("function"==typeof t){let[n,s]=th(r);t=t(void 0!==i?i:e.custom,n,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[n,s]=th(r);t=t(void 0!==i?i:e.custom,n,s)}return t}let tp=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tf{scrapeMotionValuesFromProps(e,t,i){return{}}mount(e){var t;this.current=e,ts.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),tn.current||function(){if(tn.current=!0,ti)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>tr.current=e.matches;e.addEventListener("change",t),t()}else tr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||tr.current),null==(t=this.parent)||t.addChild(this),this.update(this.props,this.presenceContext)}unmount(){var e;for(let t in this.projection&&this.projection.unmount(),el(this.notifyUpdate),el(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),null==(e=this.parent)||e.removeChild(this),this.events)this.events[t].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),null!=this.enteringChildren||(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=n.has(e);r&&this.onBindTransform&&this.onBindTransform();let s=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eo.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{s(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in e8){let t=e8[e];if(!t)continue;let{isEnabled:i,Feature:r}=t;if(!this.features[e]&&r&&i(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):tt()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<tp.length;t++){let i=tp[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(e,t,i){for(let r in t){let n=t[r],s=i[r];if(eq(n))e.addValue(r,n);else if(eq(s))e.addValue(r,e4(n,{owner:e}));else if(s!==n)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(n):t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(r);e.addValue(r,e4(void 0!==t?t:n,{owner:e}))}}for(let r in i)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=e4(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){var i;let r=void 0===this.latestValues[e]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,e))?i:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];if(null!=r){if("string"==typeof r&&($(r)||ey(r)))r=parseFloat(r);else{let i;i=r,!e3.find(Y(i))&&eI.test(t)&&(r=eG(e,t))}this.setBaseTarget(e,eq(r)?r.get():r)}return eq(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){var r;let n=tm(this.props,i,null==(r=this.presenceContext)?void 0:r.custom);n&&(t=n[e])}if(i&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||eq(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new e0),this.events[e].add(t)}notify(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];this.events[e]&&this.events[e].notify(...i)}scheduleRenderMicrotask(){e5.render(this.render)}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ev,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=eZ.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,eo.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=tc(t),this.isVariantNode=td(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==o[e]&&eq(t)&&t.set(o[e])}}}class tg extends tf{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:i,style:r}=t;delete i[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eq(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent="".concat(e))}))}constructor(){super(...arguments),this.KeyframeResolver=eK}}let tv=(e,t)=>t&&"number"==typeof e?t.transform(e):e,ty={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tx=r.length;function tb(e,t,i){let{style:s,vars:a,transformOrigin:o}=e,l=!1,u=!1;for(let e in t){let i=t[e];if(n.has(e)){l=!0;continue}if(g(e)){a[e]=i;continue}{let t=tv(i,eW[e]);e.startsWith("origin")?(u=!0,o[e]=t):s[e]=t}}if(!t.transform&&(l||i?s.transform=function(e,t,i){let n="",s=!0;for(let a=0;a<tx;a++){let o=r[a],l=e[o];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!o.startsWith("scale"):0===parseFloat(l))||i){let e=tv(l,eW[o]);if(!u){s=!1;let t=ty[o]||o;n+="".concat(t,"(").concat(e,") ")}i&&(t[o]=e)}}return n=n.trim(),i?n=i(t,s?"":n):s&&(n="none"),n}(t,e.transform,i):s.transform&&(s.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:i=0}=o;s.transformOrigin="".concat(e," ").concat(t," ").concat(i)}}function tw(e,t,i,r){let n,{style:s,vars:a}=t,o=e.style;for(n in s)o[n]=s[n];for(n in null==r||r.applyProjectionStyles(o,i),a)o.setProperty(n,a[n])}let tj={};function tk(e,t){let{layout:i,layoutId:r}=t;return n.has(e)||e.startsWith("origin")||(i||void 0!==r)&&(!!tj[e]||"opacity"===e)}function tP(e,t,i){let{style:r}=e,n={};for(let a in r){var s;(eq(r[a])||t.style&&eq(t.style[a])||tk(a,e)||(null==i||null==(s=i.getValue(a))?void 0:s.liveStyle)!==void 0)&&(n[a]=r[a])}return n}class tT extends tg{readValueFromInstance(e,t){var i;if(n.has(t))return(null==(i=this.projection)?void 0:i.isProjecting)?h(t):((e,t)=>{let{transform:i="none"}=getComputedStyle(e);return m(i,t)})(e,t);{let i=window.getComputedStyle(e),r=(g(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,t){let{transformPagePoint:i}=t;return R(e,i)}build(e,t,i){tb(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return tP(e,t,i)}constructor(){super(...arguments),this.type="html",this.renderInstance=tw}}let tS=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tC={offset:"stroke-dashoffset",array:"stroke-dasharray"},tM={offset:"strokeDashoffset",array:"strokeDasharray"};function tA(e,t,i,r,n){var s,a;let{attrX:o,attrY:l,attrScale:u,pathLength:c,pathSpacing:d=1,pathOffset:h=0,...m}=t;if(tb(e,m,r),i){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:p,style:f}=e;p.transform&&(f.transform=p.transform,delete p.transform),(f.transform||p.transformOrigin)&&(f.transformOrigin=null!=(s=p.transformOrigin)?s:"50% 50%",delete p.transformOrigin),f.transform&&(f.transformBox=null!=(a=null==n?void 0:n.transformBox)?a:"fill-box",delete p.transformBox),void 0!==o&&(p.x=o),void 0!==l&&(p.y=l),void 0!==u&&(p.scale=u),void 0!==c&&function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=!(arguments.length>4)||void 0===arguments[4]||arguments[4];e.pathLength=1;let s=n?tC:tM;e[s.offset]=_.transform(-r);let a=_.transform(t),o=_.transform(i);e[s.array]="".concat(a," ").concat(o)}(p,c,d,h,!1)}let tN=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),tE=e=>"string"==typeof e&&"svg"===e.toLowerCase();function tR(e,t,i){let n=tP(e,t,i);for(let i in e)(eq(e[i])||eq(t[i]))&&(n[-1!==r.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return n}class tD extends tg{getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(n.has(t)){let e=eY(t);return e&&e.default||0}return t=tN.has(t)?t:tS(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return tR(e,t,i)}build(e,t,i){tA(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,r){for(let i in tw(e,t,void 0,r),t.attrs)e.setAttribute(tN.has(i)?i:tS(i),t.attrs[i])}mount(e){this.isSVGTag=tE(e.tagName),super.mount(e)}constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tt}}let tV=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tL(e){if("string"!=typeof e||e.includes("-"));else if(tV.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var tO=e.i(43476);e.s(["LayoutGroupContext",()=>tI],31178);let tI=(0,i.createContext)({}),tF=(0,i.createContext)({strict:!1});e.s(["MotionConfigContext",()=>tz],37806);let tz=(0,i.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),tB=(0,i.createContext)({});function t_(e){return Array.isArray(e)?e.join(" "):e}let tU=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tW(e,t,i){for(let r in t)eq(t[r])||tk(r,i)||(e[r]=t[r])}let tH=()=>({...tU(),attrs:{}}),tY=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tG(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||tY.has(e)}let tX=e=>!tG(e);try{!function(e){"function"==typeof e&&(tX=t=>t.startsWith("on")?!tG(t):e(t))}((()=>{let e=Error("Cannot find module '@emotion/is-prop-valid'");throw e.code="MODULE_NOT_FOUND",e})().default)}catch(e){}e.s(["PresenceContext",()=>tK],21476);let tK=(0,i.createContext)(null);function tq(e){let t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}function t$(e){return eq(e)?e.get():e}e.s(["useConstant",()=>tq],47414);let tZ=e=>(t,r)=>{let n=(0,i.useContext)(tB),s=(0,i.useContext)(tK),a=()=>(function(e,t,i,r){let{scrapeMotionValuesFromProps:n,createRenderState:s}=e;return{latestValues:function(e,t,i,r){let n={},s=r(e,{});for(let e in s)n[e]=t$(s[e]);let{initial:a,animate:o}=e,l=tc(e),u=td(e);t&&u&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===o&&(o=t.animate));let c=!!i&&!1===i.initial,d=(c=c||!1===a)?o:a;if(d&&"boolean"!=typeof d&&!ta(d)){let t=Array.isArray(d)?d:[d];for(let i=0;i<t.length;i++){let r=tm(e,t[i]);if(r){let{transitionEnd:e,transition:t,...i}=r;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(t,i,r,n),renderState:s()}})(e,t,n,s);return r?a():tq(a)},tJ=tZ({scrapeMotionValuesFromProps:tP,createRenderState:tU}),tQ=tZ({scrapeMotionValuesFromProps:tR,createRenderState:tH}),t0=Symbol.for("motionComponentSymbol");function t1(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let t2="data-"+tS("framerAppearId"),t4=(0,i.createContext)({});e.s(["useIsomorphicLayoutEffect",()=>t3],74008);let t3=ti?i.useLayoutEffect:i.useEffect;function t5(e){var t,r;let{forwardMotionProps:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;s&&function(e){for(let t in e)e8[t]={...e8[t],...e[t]}}(s);let o=tL(e)?tQ:tJ;function l(t,r){var s;let l,u={...(0,i.useContext)(tz),...t,layoutId:function(e){let{layoutId:t}=e,r=(0,i.useContext)(tI).id;return r&&void 0!==t?r+"-"+t:t}(t)},{isStatic:c}=u,d=function(e){let{initial:t,animate:r}=function(e,t){if(tc(e)){let{initial:t,animate:i}=e;return{initial:!1===t||to(t)?t:void 0,animate:to(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(tB));return(0,i.useMemo)(()=>({initial:t,animate:r}),[t_(t),t_(r)])}(t),h=o(t,c);if(!c&&ti){(0,i.useContext)(tF).strict;let t=function(e){let{drag:t,layout:i}=e8;if(!t&&!i)return{};let r={...t,...i};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==i?void 0:i.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);l=t.MeasureLayout,d.visualElement=function(e,t,r,n,s){var a,o,l,u;let{visualElement:c}=(0,i.useContext)(tB),d=(0,i.useContext)(tF),h=(0,i.useContext)(tK),m=(0,i.useContext)(tz).reducedMotion,p=(0,i.useRef)(null);n=n||d.renderer,!p.current&&n&&(p.current=n(e,{visualState:t,parent:c,props:r,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:m}));let f=p.current,g=(0,i.useContext)(t4);f&&!f.projection&&s&&("html"===f.type||"svg"===f.type)&&function(e,t,i,r){let{layoutId:n,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!a||o&&t1(o),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(p.current,r,s,g);let v=(0,i.useRef)(!1);(0,i.useInsertionEffect)(()=>{f&&v.current&&f.update(r,h)});let y=r[t2],x=(0,i.useRef)(!!y&&!(null==(a=(o=window).MotionHandoffIsComplete)?void 0:a.call(o,y))&&(null==(l=(u=window).MotionHasOptimisedAnimation)?void 0:l.call(u,y)));return t3(()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),f.scheduleRenderMicrotask(),x.current&&f.animationState&&f.animationState.animateChanges())}),(0,i.useEffect)(()=>{f&&(!x.current&&f.animationState&&f.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var e,t;null==(e=(t=window).MotionHandoffMarkAsComplete)||e.call(t,y)}),x.current=!1),f.enteringChildren=void 0)}),f}(e,h,u,a,t.ProjectionNode)}return(0,tO.jsxs)(tB.Provider,{value:d,children:[l&&d.visualElement?(0,tO.jsx)(l,{visualElement:d.visualElement,...u}):null,function(e,t,r,n,s){let{latestValues:a}=n,o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],l=(tL(e)?function(e,t,r,n){let s=(0,i.useMemo)(()=>{let i=tH();return tA(i,t,tE(n),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};tW(t,e.style,e),s.style={...t,...s.style}}return s}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return tW(n,r,e),Object.assign(n,function(e,t){let{transformTemplate:r}=e;return(0,i.useMemo)(()=>{let e=tU();return tb(e,t,r),Object.assign({},e.vars,e.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(t,a,s,e),u=function(e,t,i){let r={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(tX(n)||!0===i&&tG(n)||!t&&!tG(n)||e.draggable&&n.startsWith("onDrag"))&&(r[n]=e[n]);return r}(t,"string"==typeof e,o),c=e!==i.Fragment?{...u,...l,ref:r}:{},{children:d}=t,h=(0,i.useMemo)(()=>eq(d)?d.get():d,[d]);return(0,i.createElement)(e,{...c,children:h})}(e,t,(s=d.visualElement,(0,i.useCallback)(e=>{e&&h.onMount&&h.onMount(e),s&&(e?s.mount(e):s.unmount()),r&&("function"==typeof r?r(e):t1(r)&&(r.current=e))},[s,r])),h,c,n)]})}l.displayName="motion.".concat("string"==typeof e?e:"create(".concat(null!=(r=null!=(t=e.displayName)?t:e.name)?r:"",")"));let u=(0,i.forwardRef)(l);return u[t0]=e,u}function t6(e,t,i){let r=e.getProps();return tm(r,t,void 0!==i?i:r.custom,e)}function t8(e,t){var i,r;return null!=(r=null!=(i=null==e?void 0:e[t])?i:null==e?void 0:e.default)?r:e}let t7=e=>Array.isArray(e);function t9(e,t){let i=e.getValue("willChange");if(eq(i)&&i.add)return i.add(t);if(!i&&er.WillChange){let i=new er.WillChange("auto");e.addValue("willChange",i),i.add(t)}}function ie(e){e.duration=0,e.type}let it=(e,t)=>i=>t(e(i)),ii=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.reduce(it)},ir=e=>1e3*e,is={layout:0,mainThread:0,waapi:0};function ia(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function io(e,t){return i=>i>0?t:e}let il=(e,t,i)=>{let r=e*e,n=i*(t*t-r)+r;return n<0?0:Math.sqrt(n)},iu=[eS,eT,eC];function ic(e){let t=iu.find(t=>t.test(e));if(K(!!t,"'".concat(e,"' is not an animatable color. Use the equivalent color code instead."),"color-not-animatable"),!t)return!1;let i=t.parse(e);return t===eC&&(i=function(e){let{hue:t,saturation:i,lightness:r,alpha:n}=e;t/=360,r/=100;let s=0,a=0,o=0;if(i/=100){let e=r<.5?r*(1+i):r+i-r*i,n=2*r-e;s=ia(n,e,t+1/3),a=ia(n,e,t),o=ia(n,e,t-1/3)}else s=a=o=r;return{red:Math.round(255*s),green:Math.round(255*a),blue:Math.round(255*o),alpha:n}}(i)),i}let id=(e,t)=>{let i=ic(e),r=ic(t);if(!i||!r)return io(e,t);let n={...i};return e=>(n.red=il(i.red,r.red,e),n.green=il(i.green,r.green,e),n.blue=il(i.blue,r.blue,e),n.alpha=w(i.alpha,r.alpha,e),eT.transform(n))},ih=new Set(["none","hidden"]);function im(e,t){return i=>w(e,t,i)}function ip(e){return"number"==typeof e?im:"string"==typeof e?y(e)?io:eM.test(e)?id:iy:Array.isArray(e)?ig:"object"==typeof e?eM.test(e)?id:iv:io}function ig(e,t){let i=[...e],r=i.length,n=e.map((e,i)=>ip(e)(e,t[i]));return e=>{for(let t=0;t<r;t++)i[t]=n[t](e);return i}}function iv(e,t){let i={...e,...t},r={};for(let n in i)void 0!==e[n]&&void 0!==t[n]&&(r[n]=ip(e[n])(e[n],t[n]));return e=>{for(let t in r)i[t]=r[t](e);return i}}let iy=(e,t)=>{let i=eI.createTransformer(t),r=eD(e),n=eD(t);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?ih.has(e)&&!n.values.length||ih.has(t)&&!r.values.length?function(e,t){return ih.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):ii(ig(function(e,t){let i=[],r={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){var n;let a=t.types[s],o=e.indexes[a][r[a]],l=null!=(n=e.values[o])?n:0;i[s]=l,r[a]++}return i}(r,n),n.values),i):(K(!0,"Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition."),"complex-values-different"),io(e,t))};function ix(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?w(e,t,i):ip(e)(e,t)}let ib=e=>{let t=t=>{let{timestamp:i}=t;return e(i)};return{start:function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return eo.update(t,e)},stop:()=>el(t),now:()=>eu.isProcessing?eu.timestamp:eZ.now()}},iw=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r="",n=Math.max(Math.round(t/i),2);for(let t=0;t<n;t++)r+=Math.round(1e4*e(t/(n-1)))/1e4+", ";return"linear(".concat(r.substring(0,r.length-2),")")};function ij(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function ik(e,t,i){var r,n;let s=Math.max(t-5,0);return r=i-e(s),(n=t-s)?1e3/n*r:0}let iP={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iT(e,t){return e*Math.sqrt(1-t*t)}let iS=["duration","bounce"],iC=["stiffness","damping","mass"];function iM(e,t){return t.some(t=>void 0!==e[t])}function iA(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:iP.visualDuration,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:iP.bounce,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:i}:t,{restSpeed:n,restDelta:s}=r,a=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:d,duration:h,velocity:m,isResolvedFromDuration:p}=function(e){let t={velocity:iP.velocity,stiffness:iP.stiffness,damping:iP.damping,mass:iP.mass,isResolvedFromDuration:!1,...e};if(!iM(e,iC)&&iM(e,iS))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),r=i*i,n=2*V(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:iP.mass,stiffness:r,damping:n}}else{let i=function(e){let t,i,{duration:r=iP.duration,bounce:n=iP.bounce,velocity:s=iP.velocity,mass:a=iP.mass}=e;K(r<=ir(iP.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-n;o=V(iP.minDamping,iP.maxDamping,o),r=V(iP.minDuration,iP.maxDuration,r/1e3),o<1?(t=e=>{let t=e*o,i=t*r;return .001-(t-s)/iT(e,o)*Math.exp(-i)},i=e=>{let i=e*o*r,n=Math.pow(o,2)*Math.pow(e,2)*r,a=Math.exp(-i),l=iT(Math.pow(e,2),o);return(i*s+s-n)*a*(-t(e)+.001>0?-1:1)/l}):(t=e=>-.001+Math.exp(-e*r)*((e-s)*r+1),i=e=>r*r*(s-e)*Math.exp(-e*r));let l=function(e,t,i){let r=i;for(let i=1;i<12;i++)r-=e(r)/t(r);return r}(t,i,5/r);if(r=ir(r),isNaN(l))return{stiffness:iP.stiffness,damping:iP.damping,duration:r};{let e=Math.pow(l,2)*a;return{stiffness:e,damping:2*o*Math.sqrt(a*e),duration:r}}}(e);(t={...t,...i,mass:iP.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-((r.velocity||0)/1e3)}),f=m||0,g=c/(2*Math.sqrt(u*d)),v=o-a,y=Math.sqrt(u/d)/1e3,x=5>Math.abs(v);if(n||(n=x?iP.restSpeed.granular:iP.restSpeed.default),s||(s=x?iP.restDelta.granular:iP.restDelta.default),g<1){let t=iT(y,g);e=e=>o-Math.exp(-g*y*e)*((f+g*y*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)e=e=>o-Math.exp(-y*e)*(v+(f+y*v)*e);else{let t=y*Math.sqrt(g*g-1);e=e=>{let i=Math.exp(-g*y*e),r=Math.min(t*e,300);return o-i*((f+g*y*v)*Math.sinh(r)+t*v*Math.cosh(r))/t}}let b={calculatedDuration:p&&h||null,next:t=>{let i=e(t);if(p)l.done=t>=h;else{let r=0===t?f:0;g<1&&(r=0===t?ir(f):ik(e,t,i));let a=Math.abs(o-i)<=s;l.done=Math.abs(r)<=n&&a}return l.value=l.done?o:i,l},toString:()=>{let e=Math.min(ij(b),2e4),t=iw(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function iN(e){let t,i,{keyframes:r,velocity:n=0,power:s=.8,timeConstant:a=325,bounceDamping:o=10,bounceStiffness:l=500,modifyTarget:u,min:c,max:d,restDelta:h=.5,restSpeed:m}=e,p=r[0],f={done:!1,value:p},g=s*n,v=p+g,y=void 0===u?v:u(v);y!==v&&(g=y-p);let x=e=>-g*Math.exp(-e/a),b=e=>y+x(e),w=e=>{let t=x(e),i=b(e);f.done=Math.abs(t)<=h,f.value=f.done?y:i},j=e=>{let r;if(r=f.value,void 0!==c&&r<c||void 0!==d&&r>d){var n;t=e,i=iA({keyframes:[f.value,(n=f.value,void 0===c?d:void 0===d||Math.abs(c-n)<Math.abs(d-n)?c:d)],velocity:ik(b,e,f.value),damping:o,stiffness:l,restDelta:h,restSpeed:m})}};return j(0),{calculatedDuration:null,next:e=>{let r=!1;return(i||void 0!==t||(r=!0,w(e),j(e)),void 0!==t&&e>=t)?i.next(e-t):(r||w(e),f)}}}iA.applyToOptions=e=>{let t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,i=arguments.length>2?arguments[2]:void 0,r=i({...e,keyframes:[0,t]}),n=Math.min(ij(r),2e4);return{type:"keyframes",ease:e=>r.next(n*e).value/t,duration:n/1e3}}(e,100,iA);return e.ease=t.ease,e.duration=ir(t.duration),e.type="keyframes",e};let iE=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function iR(e,t,i,r){return e===t&&i===r?ei:n=>0===n||1===n?n:iE(function(e,t,i,r,n){let s,a,o=0;do(s=iE(a=t+(i-t)/2,r,n)-e)>0?i=a:t=a;while(Math.abs(s)>1e-7&&++o<12)return a}(n,0,1,e,i),t,r)}let iD=iR(.42,0,1,1),iV=iR(0,0,.58,1),iL=iR(.42,0,.58,1),iO=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,iI=e=>t=>1-e(1-t),iF=iR(.33,1.53,.69,.99),iz=iI(iF),iB=iO(iz),i_=e=>(e*=2)<1?.5*iz(e):.5*(2-Math.pow(2,-10*(e-1))),iU=e=>1-Math.sin(Math.acos(e)),iW=iI(iU),iH=iO(iU),iY=e=>Array.isArray(e)&&"number"==typeof e[0],iG={linear:ei,easeIn:iD,easeInOut:iL,easeOut:iV,circIn:iU,circInOut:iH,circOut:iW,backIn:iz,backInOut:iB,backOut:iF,anticipate:i_},iX=e=>{if(iY(e)){q(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,i,r,n]=e;return iR(t,i,r,n)}return"string"==typeof e?(q(void 0!==iG[e],"Invalid easing type '".concat(e,"'"),"invalid-easing-type"),iG[e]):e},iK=(e,t,i)=>{let r=t-e;return 0===r?1:(i-e)/r};function iq(e){var t;let{duration:i=300,keyframes:r,times:n,ease:s="easeInOut"}=e,a=Array.isArray(s)&&"number"!=typeof s[0]?s.map(iX):iX(s),o={done:!1,value:r[0]},l=function(e,t){let{clamp:i=!0,ease:r,mixer:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=e.length;if(q(s===t.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let r=[],n=i||er.mix||ix,s=e.length-1;for(let i=0;i<s;i++){let s=n(e[i],e[i+1]);t&&(s=ii(Array.isArray(t)?t[i]||ei:t,s)),r.push(s)}return r}(t,r,n),l=o.length,u=i=>{if(a&&i<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(i<e[r+1]);r++);let n=iK(e[r],e[r+1],i);return o[r](n)};return i?t=>u(V(e[0],e[s-1],t)):u}((t=n&&n.length===r.length?n:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let r=1;r<=t;r++){let n=iK(0,t,r);e.push(w(i,1,n))}}(t,e.length-1),t}(r),t.map(e=>e*i)),r,{ease:Array.isArray(a)?a:r.map(()=>a||iL).splice(0,r.length-1)});return{calculatedDuration:i,next:e=>(o.value=l(e),o.done=e>=i,o)}}let i$=e=>null!==e;function iZ(e,t,i){let{repeat:r,repeatType:n="loop"}=t,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,a=e.filter(i$),o=s<0||r&&"loop"!==n&&r%2==1?0:a.length-1;return o&&void 0!==i?i:a[o]}let iJ={decay:iN,inertia:iN,tween:iq,keyframes:iq,spring:iA};function iQ(e){"string"==typeof e.type&&(e.type=iJ[e.type])}class i0{get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}constructor(){this.updateFinished()}}let i1=e=>e/100;class i2 extends i0{initAnimation(){let{options:e}=this;iQ(e);let{type:t=iq,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=e,{keyframes:a}=e,o=t||iq;o!==iq&&"number"!=typeof a[0]&&(this.mixKeyframes=ii(i1,ix(a[0],a[1])),a=[0,100]);let l=o({...e,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=ij(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:m,onUpdate:p,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,x=i;if(c){let e=Math.min(this.currentTime,r)/a,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(i=1-i,h&&(i-=h/a)):"mirror"===d&&(x=s)),y=V(0,1,i)*a}let b=v?{done:!1,value:u[0]}:x.next(y);n&&(b.value=n(b.value));let{done:w}=b;v||null===o||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return j&&m!==iN&&(b.value=iZ(u,this.options,f,this.speed)),p&&p(b.value),j&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(e){var t;e=ir(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),null==(t=this.driver)||t.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(eZ.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=this.currentTime/1e3)}play(){var e,t;if(this.isStopped)return;let{driver:i=ib,startTime:r}=this.options;this.driver||(this.driver=i(e=>this.tick(e))),null==(e=(t=this.options).onPlay)||e.call(t);let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=null!=r?r:n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(eZ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,t;this.notifyFinished(),this.teardown(),this.state="finished",null==(e=(t=this.options).onComplete)||e.call(t)}cancel(){var e,t;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null==(e=(t=this.options).onCancel)||e.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,is.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var t;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null==(t=this.driver)||t.stop(),e.observe(this)}constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var e,t;let{motionValue:i}=this.options;i&&i.updatedAt!==eZ.now()&&this.tick(eZ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),null==(e=(t=this.options).onStop)||e.call(t))},is.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}}function i4(e){let t;return()=>(void 0===t&&(t=e()),t)}let i3=i4(()=>void 0!==window.ScrollTimeline),i5={},i6=function(e,t){let i=i4(e);return()=>{var e;return null!=(e=i5[t])?e:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),i8=e=>{let[t,i,r,n]=e;return"cubic-bezier(".concat(t,", ").concat(i,", ").concat(r,", ").concat(n,")")},i7={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i8([0,.65,.55,1]),circOut:i8([.55,0,1,.45]),backIn:i8([.31,.01,.66,-.59]),backOut:i8([.33,1.53,.69,.99])};function i9(e){return"function"==typeof e&&"applyToOptions"in e}class re extends i0{play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,t;null==(e=(t=this.animation).finish)||e.call(t)}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){if(!this.isPseudoElement){var e,t;null==(e=(t=this.animation).commitStyles)||e.call(t)}}get duration(){var e,t;return Number((null==(t=this.animation.effect)||null==(e=t.getComputedTiming)?void 0:e.call(t).duration)||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(e){this.finishedTime=null,this.animation.currentTime=ir(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline(e){let{timeline:t,observe:i}=e;if(this.allowFlatten){var r;null==(r=this.animation.effect)||r.updateTiming({easing:"linear"})}return(this.animation.onfinish=null,t&&i3())?(this.animation.timeline=t,ei):i(this)}constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=e,q("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function(e){let{type:t,...i}=e;return i9(t)&&i6()?t.applyToOptions(i):(null!=i.duration||(i.duration=300),null!=i.ease||(i.ease="easeOut"),i)}(e);this.animation=function(e,t,i){let{delay:r=0,duration:n=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:void 0,c={[t]:i};l&&(c.offset=l);let d=function e(t,i){if(t)return"function"==typeof t?i6()?iw(t,i):"ease-out":iY(t)?i8(t):Array.isArray(t)?t.map(t=>e(t,i)||i7.easeOut):i7[t]}(o,n);Array.isArray(d)&&(c.easing=d),es.value&&is.waapi++;let h={delay:r,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};u&&(h.pseudoElement=u);let m=e.animate(c,h);return es.value&&m.finished.finally(()=>{is.waapi--}),m}(t,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let e=iZ(r,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){t.startsWith("--")?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}null==o||o(),this.notifyFinished()}}}let rt={anticipate:i_,backInOut:iB,circInOut:iH};class ri extends re{updateMotionValue(e){var t;let{motionValue:i,onUpdate:r,onComplete:n,element:s,...a}=this.options;if(!i)return;if(void 0!==e)return void i.set(e);let o=new i2({...a,autoplay:!1}),l=ir(null!=(t=this.finishedTime)?t:this.time);i.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}constructor(e){!function(e){"string"==typeof e.ease&&e.ease in rt&&(e.ease=rt[e.ease])}(e),iQ(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}}let rr=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eI.test(e)||"0"===e)&&!e.startsWith("url(")),rn=new Set(["opacity","clipPath","filter","transform"]),rs=i4(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ra extends i0{onKeyframesResolved(e,t,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:u}=i;this.resolvedAt=eZ.now(),!function(e,t,i,r){let n=e[0];if(null===n)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],a=rr(n,t),o=rr(s,t);return K(a===o,"You are trying to animate ".concat(t,' from "').concat(n,'" to "').concat(s,'". "').concat(a?s:n,'" is not an animatable value.'),"value-not-animatable"),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||i9(i))&&r)}(e,n,s,a)&&((er.instantAnimations||!o)&&(null==u||u(iZ(e,i,t))),e[0]=e[e.length-1],ie(i),i.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},d=!l&&function(e){var t;let{motionValue:i,name:r,repeatDelay:n,repeatType:s,damping:a,type:o}=e;if(!((null==i||null==(t=i.owner)?void 0:t.current)instanceof HTMLElement))return!1;let{onUpdate:l,transformTemplate:u}=i.owner.getProps();return rs()&&r&&rn.has(r)&&("transform"!==r||!u)&&!l&&!n&&"mirror"!==s&&0!==a&&"inertia"!==o}(c)?new ri({...c,element:c.motionValue.owner.current}):new i2(c);d.finished.then(()=>this.notifyFinished()).catch(ei),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){if(!this._animation){var e;null==(e=this.keyframeResolver)||e.resume(),ep=!0,eg(),ef(),ep=!1}return this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),null==(e=this.keyframeResolver)||e.cancel()}constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:u,...c}){var d;super(),this.stop=()=>{var e,t;this._animation&&(this._animation.stop(),null==(t=this.stopTimeline)||t.call(this)),null==(e=this.keyframeResolver)||e.cancel()},this.createdAt=eZ.now();let h={autoplay:e,delay:t,type:i,repeat:r,repeatDelay:n,repeatType:s,name:o,motionValue:l,element:u,...c},m=(null==u?void 0:u.KeyframeResolver)||ev;this.keyframeResolver=new m(a,(e,t,i)=>this.onKeyframesResolved(e,t,h,!i),o,l,u),null==(d=this.keyframeResolver)||d.scheduleResolve()}}let ro=e=>null!==e,rl={type:"spring",stiffness:500,damping:25,restSpeed:10},ru={type:"keyframes",duration:.8},rc={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rd=function(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=arguments.length>4?arguments[4]:void 0,a=arguments.length>5?arguments[5]:void 0;return o=>{let l=t8(r,e)||{},u=l.delay||r.delay||0,{elapsed:c=0}=r;c-=ir(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-c,onUpdate:e=>{t.set(e),l.onUpdate&&l.onUpdate(e)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:a?void 0:s};!function(e){let{when:t,delay:i,delayChildren:r,staggerChildren:n,staggerDirection:s,repeat:a,repeatType:o,repeatDelay:l,from:u,elapsed:c,...d}=e;return!!Object.keys(d).length}(l)&&Object.assign(d,((e,t)=>{let{keyframes:i}=t;return i.length>2?ru:n.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===i[1]?2*Math.sqrt(550):30,restSpeed:10}:rl:rc})(e,d)),d.duration&&(d.duration=ir(d.duration)),d.repeatDelay&&(d.repeatDelay=ir(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let h=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(ie(d),0===d.delay&&(h=!0)),(er.instantAnimations||er.skipAnimations)&&(h=!0,ie(d),d.delay=0),d.allowFlatten=!l.type&&!l.ease,h&&!a&&void 0!==t.get()){let e=function(e,t,i){let{repeat:r,repeatType:n="loop"}=t,s=e.filter(ro),a=r&&"loop"!==n&&r%2==1?0:s.length-1;return s[a]}(d.keyframes,l);if(void 0!==e)return void eo.update(()=>{d.onUpdate(e),d.onComplete()})}return l.isSync?new i2(d):new ra(d)}};function rh(e,t){let{delay:i=0,transitionOverride:r,type:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:s=e.getDefaultTransition(),transitionEnd:a,...o}=t;r&&(s=r);let l=[],u=n&&e.animationState&&e.animationState.getState()[n];for(let t in o){var c;let r=e.getValue(t,null!=(c=e.latestValues[t])?c:null),n=o[t];if(void 0===n||u&&function(e,t){let{protectedKeys:i,needsAnimating:r}=e,n=i.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,n}(u,t))continue;let a={delay:i,...t8(s||{},t)},d=r.get();if(void 0!==d&&!r.isAnimating&&!Array.isArray(n)&&n===d&&!a.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let i=e.props[t2];if(i){let e=window.MotionHandoffAnimation(i,t,eo);null!==e&&(a.startTime=e,h=!0)}}t9(e,t),r.start(rd(t,r,n,e.shouldReduceMotion&&D.has(t)?{type:!1}:a,e,h));let m=r.animation;m&&l.push(m)}return a&&Promise.all(l).then(()=>{eo.update(()=>{a&&function(e,t){let{transitionEnd:i={},transition:r={},...n}=t6(e,t)||{};for(let t in n={...n,...i}){var s;let i=t7(s=n[t])?s[s.length-1]||0:s;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,e4(i))}}(e,a)})}),l}function rm(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=Array.from(e).sort((e,t)=>e.sortNodePosition(t)).indexOf(t),a=e.size,o=(a-1)*r;return"function"==typeof i?i(s,a):1===n?s*r:o-s*r}function rp(e,t){var i;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=t6(e,t,"exit"===r.type?null==(i=e.presenceContext)?void 0:i.custom:void 0),{transition:s=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(s=r.transitionOverride);let a=n?()=>Promise.all(rh(e,n,r)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?function(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=s;return function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,a=arguments.length>6?arguments[6]:void 0,o=[];for(let l of e.variantChildren)l.notify("AnimationStart",t),o.push(rp(l,t,{...a,delay:i+("function"==typeof r?0:r)+rm(e.variantChildren,l,r,n,s)}).then(()=>l.notify("AnimationComplete",t)));return Promise.all(o)}(e,t,i,n,a,o,r)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([a(),o(r.delay)]);{let[e,t]="beforeChildren"===l?[a,o]:[o,a];return e().then(()=>t())}}function rf(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let r=0;r<i;r++)if(t[r]!==e[r])return!1;return!0}let rg=tu.length,rv=[...tl].reverse(),ry=tl.length;function rx(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rb(){return{animate:rx(!0),whileInView:rx(),whileHover:rx(),whileTap:rx(),whileDrag:rx(),whileFocus:rx(),exit:rx()}}class rw{update(){}constructor(e){this.isMounted=!1,this.node=e}}let rj=0,rk={x:!1,y:!1};function rP(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,i,r),()=>e.removeEventListener(t,i)}let rT=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rS(e){return{point:{x:e.pageX,y:e.pageY}}}function rC(e,t,i,r){return rP(e,t,e=>rT(e)&&i(e,rS(e)),r)}function rM(e){return e.max-e.min}function rA(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=w(t.min,t.max,e.origin),e.scale=rM(i)/rM(t),e.translate=w(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rN(e,t,i,r){rA(e.x,t.x,i.x,r?r.originX:void 0),rA(e.y,t.y,i.y,r?r.originY:void 0)}function rE(e,t,i){e.min=i.min+t.min,e.max=e.min+rM(t)}function rR(e,t,i){e.min=t.min-i.min,e.max=e.min+rM(t)}function rD(e,t,i){rR(e.x,t.x,i.x),rR(e.y,t.y,i.y)}function rV(e){return[e("x"),e("y")]}let rL=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null},rO=(e,t)=>Math.abs(e-t);class rI{updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),el(this.updatePoint)}constructor(e,t,{transformPagePoint:i,contextWindow:r=window,dragSnapToOrigin:n=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rB(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(rO(e.x,t.x)**2+rO(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!i)return;let{point:r}=e,{timestamp:n}=eu;this.history.push({...r,timestamp:n});let{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rF(t,this.transformPagePoint),eo.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rB("pointercancel"===e.type?this.lastMoveEventInfo:rF(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,s),r&&r(e,s)},!rT(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=r||window;let a=rF(rS(e),this.transformPagePoint),{point:o}=a,{timestamp:l}=eu;this.history=[{...o,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,rB(a,this.history)),this.removeListeners=ii(rC(this.contextWindow,"pointermove",this.handlePointerMove),rC(this.contextWindow,"pointerup",this.handlePointerUp),rC(this.contextWindow,"pointercancel",this.handlePointerUp))}}function rF(e,t){return t?{point:t(e.point)}:e}function rz(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rB(e,t){let{point:i}=e;return{point:i,delta:rz(i,r_(t)),offset:rz(i,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,r=null,n=r_(e);for(;i>=0&&(r=e[i],!(n.timestamp-r.timestamp>ir(.1)));)i--;if(!r)return{x:0,y:0};let s=(n.timestamp-r.timestamp)/1e3;if(0===s)return{x:0,y:0};let a={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function r_(e){return e[e.length-1]}function rU(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function rW(e,t){let i=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,r]=[r,i]),{min:i,max:r}}function rH(e,t,i){return{min:rY(e,t),max:rY(e,i)}}function rY(e,t){return"number"==typeof e?e:e[t]||0}let rG=new WeakMap;class rX{start(e){let{snapToCursor:t=!1,distanceThreshold:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let n=e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rS(e).point)},s=(e,t)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(rk[e])return null;else return rk[e]=!0,()=>{rk[e]=!1};return rk.x||rk.y?null:(rk.x=rk.y=!0,()=>{rk.x=rk.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rV(e=>{let t=this.getAxisMotionValue(e).get()||0;if(B.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[e];r&&(t=rM(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),n&&eo.postRender(()=>n(e,t)),t9(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},a=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},o=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>rV(e=>{var t;return"paused"===this.getAnimationState(e)&&(null==(t=this.getAxisMotionValue(e).animation)?void 0:t.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new rI(e,{onSessionStart:n,onStart:s,onMove:a,onSessionEnd:o,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:i,contextWindow:rL(this.visualElement)})}stop(e,t){let i=e||this.latestPointerEvent,r=t||this.latestPanInfo,n=this.isDragging;if(this.cancel(),!n||!r||!i)return;let{velocity:s}=r;this.startAnimation(s);let{onDragEnd:a}=this.getProps();a&&eo.postRender(()=>a(i,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:r}=this.getProps();if(!i||!rK(e,r,this.currentDirection))return;let n=this.getAxisMotionValue(e),s=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(s=function(e,t,i){let{min:r,max:n}=t;return void 0!==r&&e<r?e=i?w(r,e,i.min):Math.max(e,r):void 0!==n&&e>n&&(e=i?w(n,e,i.max):Math.min(e,n)),e}(s,this.constraints[e],this.elastic[e])),n.set(s)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(e=this.visualElement.projection)?void 0:e.layout,n=this.constraints;t&&t1(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,t){let{top:i,left:r,bottom:n,right:s}=t;return{x:rU(e.x,r,s),y:rU(e.y,i,n)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===e?e=0:!0===e&&(e=.35),{x:rH(e,"left","right"),y:rH(e,"top","bottom")}}(i),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&rV(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!t1(t))return!1;let r=t.current;q(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(e,t,i){let r=R(e,i),{scroll:n}=t;return n&&(A(r.x,n.offset.x),A(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),a=(e=n.layout.layoutBox,{x:rW(e.x,s.x),y:rW(e.y,s.y)});if(i){let e=i(function(e){let{x:t,y:i}=e;return{top:i.min,right:t.max,bottom:i.max,left:t.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=b(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(rV(a=>{if(!rK(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?e[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return t9(this.visualElement,e),i.start(rd(e,i,0,t,this.visualElement,!1))}stopAnimation(){rV(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rV(e=>{var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.pause()})}getAnimationState(e){var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.state}getAxisMotionValue(e){let t="_drag".concat(e.toUpperCase()),i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){rV(t=>{let{drag:i}=this.getProps();if(!rK(t,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(t);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[t];n.set(e[t]-w(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!t1(t)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rV(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();r[e]=function(e,t){let i=.5,r=rM(e),n=rM(t);return n>r?i=iK(t.min,t.max-r,e.min):r>n&&(i=iK(e.min,e.max-n,t.min)),V(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),rV(t=>{if(!rK(t,e,null))return;let i=this.getAxisMotionValue(t),{min:n,max:s}=this.constraints[t];i.set(w(n,s,r[t]))})}addListeners(){if(!this.visualElement.current)return;rG.set(this.visualElement,this);let e=rC(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();t1(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),eo.read(t);let n=rP(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:i}=e;this.isDragging&&i&&(rV(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),e(),r(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:a}}constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=tt(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}}function rK(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}let rq=e=>(t,i)=>{e&&eo.postRender(()=>e(t,i))};var r$=i;function rZ(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,i.useContext)(tK);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:n,register:s}=t,a=(0,i.useId)();(0,i.useEffect)(()=>{if(e)return s(a)},[e]);let o=(0,i.useCallback)(()=>e&&n&&n(a),[a,n,e]);return!r&&n?[!1,o]:[!0]}e.s(["usePresence",()=>rZ],64978);let rJ={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rQ(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let r0={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!_.test(e))return e;else e=parseFloat(e);let i=rQ(e,t.target.x),r=rQ(e,t.target.y);return"".concat(i,"% ").concat(r,"%")}},r1=!1;class r2 extends r$.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=e;for(let e in r3)tj[e]=r3[e],g(e)&&(tj[e].isCSSVariable=!0);n&&(t.group&&t.group.add(n),i&&i.register&&r&&i.register(n),r1&&n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rJ.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r1=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==n?s.willUpdate():this.safeToRemove(),e.isPresent!==n&&(n?s.promote():s.relegate()||eo.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),e5.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:r}=e;r1=!0,r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function r4(e){let[t,i]=rZ(),r=(0,r$.useContext)(tI);return(0,tO.jsx)(r2,{...e,layoutGroup:r,switchLayoutGroup:(0,r$.useContext)(t4),isPresent:t,safeToRemove:i})}let r3={borderRadius:{...r0,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r0,borderTopRightRadius:r0,borderBottomLeftRadius:r0,borderBottomRightRadius:r0,boxShadow:{correct:(e,t)=>{let{treeScale:i,projectionDelta:r}=t,n=eI.parse(e);if(n.length>5)return e;let s=eI.createTransformer(e),a=+("number"!=typeof n[0]),o=r.x.scale*i.x,l=r.y.scale*i.y;n[0+a]/=o,n[1+a]/=l;let u=w(o,l,.5);return"number"==typeof n[2+a]&&(n[2+a]/=u),"number"==typeof n[3+a]&&(n[3+a]/=u),s(n)}}};function r5(e){return"object"==typeof e&&null!==e}function r6(e){return r5(e)&&"ownerSVGElement"in e}let r8=(e,t)=>e.depth-t.depth;class r7{add(e){eJ(this.children,e),this.isDirty=!0}remove(e){eQ(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(r8),this.isDirty=!1,this.children.forEach(e)}constructor(){this.children=[],this.isDirty=!1}}let r9=["TopLeft","TopRight","BottomLeft","BottomRight"],ne=r9.length,nt=e=>"string"==typeof e?parseFloat(e):e,ni=e=>"number"==typeof e||_.test(e);function nr(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nn=na(0,.5,iW),ns=na(.5,.95,ei);function na(e,t,i){return r=>r<e?0:r>t?1:i(iK(e,t,r))}function no(e,t){e.min=t.min,e.max=t.max}function nl(e,t){no(e.x,t.x),no(e.y,t.y)}function nu(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nc(e,t,i,r,n){return e-=t,e=r+1/i*(e-r),void 0!==n&&(e=r+1/n*(e-r)),e}function nd(e,t,i,r,n){let[s,a,o]=i;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,n=arguments.length>4?arguments[4]:void 0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;if(B.test(t)&&(t=parseFloat(t),t=w(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=w(s.min,s.max,r);e===s&&(o-=t),e.min=nc(e.min,t,i,o,n),e.max=nc(e.max,t,i,o,n)}(e,t[s],t[a],t[o],t.scale,r,n)}let nh=["x","scaleX","originX"],nm=["y","scaleY","originY"];function np(e,t,i,r){nd(e.x,t,nh,i?i.x:void 0,r?r.x:void 0),nd(e.y,t,nm,i?i.y:void 0,r?r.y:void 0)}function nf(e){return 0===e.translate&&1===e.scale}function ng(e){return nf(e.x)&&nf(e.y)}function nv(e,t){return e.min===t.min&&e.max===t.max}function ny(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nx(e,t){return ny(e.x,t.x)&&ny(e.y,t.y)}function nb(e){return rM(e.x)/rM(e.y)}function nw(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nj{add(e){eJ(this.members,e),e.scheduleRender()}remove(e){if(eQ(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}constructor(){this.members=[]}}let nk={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nP=["","X","Y","Z"],nT=0;function nS(e,t,i,r){let{latestValues:n}=t;n[e]&&(i[e]=n[e],t.setStaticValue(e,0),r&&(r[e]=0))}function nC(e){let{attachResizeListener:t,defaultParent:i,measureScroll:r,checkIsScrollRoot:n,resetTransform:s}=e;return class{addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new e0),this.eventHandlers.get(e).add(t)}notifyListeners(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];let n=this.eventHandlers.get(e);n&&n.notify(...i)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){if(this.instance)return;this.isSVG=r6(e)&&!(r6(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=0,n=()=>this.root.updateBlockedByResize=!1;eo.read(()=>{r=window.innerWidth}),t(e,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=eZ.now(),r=t=>{let{timestamp:n}=t,s=n-i;s>=250&&(el(r),e(s-250))};return eo.setup(r,!0),()=>el(r)}(n,250),rJ.hasAnimatedSinceResize&&(rJ.hasAnimatedSinceResize=!1,this.nodes.forEach(nO)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:i,hasRelativeLayoutChanged:r,layout:s}=e;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let a=this.options.transition||n.getDefaultTransition()||nH,{onLayoutAnimationStart:o,onLayoutAnimationComplete:l}=n.getProps(),u=!this.targetLayout||!nx(this.targetLayout,s),c=!i&&r;if(this.options.layoutRoot||this.resumeFrom||c||i&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...t8(a,"layout"),onPlay:o,onComplete:l};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,c)}else i||nO(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),el(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nz),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let r=i.props[t2];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",eo,!(e||i))}let{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&e(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nD);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nV);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nL),this.nodes.forEach(nM),this.nodes.forEach(nA)):this.nodes.forEach(nV),this.clearAllSnapshots();let e=eZ.now();eu.delta=V(0,1e3/60,e-eu.timestamp),eu.timestamp=e,eu.isProcessing=!0,ec.update.process(eu),ec.preRender.process(eu),ec.render.process(eu),eu.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,e5.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nR),this.sharedNodes.forEach(nB)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eo.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eo.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rM(this.snapshot.measuredBox.x)||rM(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=tt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!s)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!ng(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,n=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||P(this.latestValues)||n)&&(s(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){var e;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),nX((e=r).x),nX(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return tt();let i=t.measureViewportBox();if(!((null==(e=this.scroll)?void 0:e.wasRoot)||this.path.some(nq))){let{scroll:e}=this.root;e&&(A(i.x,e.offset.x),A(i.y,e.offset.y))}return i}removeElementScroll(e){var t;let i=tt();if(nl(i,e),null==(t=this.scroll)?void 0:t.wasRoot)return i;for(let t=0;t<this.path.length;t++){let r=this.path[t],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&nl(i,e),A(i.x,n.offset.x),A(i.y,n.offset.y))}return i}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=tt();nl(i,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&E(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),P(r.latestValues)&&E(i,r.latestValues)}return P(this.latestValues)&&E(i,this.latestValues),i}removeTransform(e){let t=tt();nl(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!P(i.latestValues))continue;k(i.latestValues)&&i.updateSnapshot();let r=tt();nl(r,i.measurePageBox()),np(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return P(this.latestValues)&&np(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eu.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var e,t,i,r;let n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==s;if(!(n||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:o,layoutId:l}=this.options;if(this.layout&&(o||l)){if(this.resolvedRelativeTargetAt=eu.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tt(),this.relativeTargetOrigin=tt(),rD(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nl(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=tt(),this.targetWithTransforms=tt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),t=this.target,i=this.relativeTarget,r=this.relativeParent.target,rE(t.x,i.x,r.x),rE(t.y,i.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nl(this.target,this.layout.layoutBox),M(this.target,this.targetDelta)):nl(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tt(),this.relativeTargetOrigin=tt(),rD(this.relativeTargetOrigin,this.target,e.target),nl(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}es.value&&nk.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||k(this.parent.latestValues)||T(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),i=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===eu.timestamp&&(r=!1),r)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;nl(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;!function(e,t,i){let r,n,s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){n=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&E(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,M(e,n)),s&&P(r.latestValues)&&E(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=tt());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nu(this.prevProjectionDelta.x,this.projectionDelta.x),nu(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rN(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===a&&this.treeScale.y===o&&nw(this.projectionDelta.x,this.prevProjectionDelta.x)&&nw(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),es.value&&nk.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var e;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=e9(),this.projectionDelta=e9(),this.projectionDeltaWithTransform=e9()}setAnimationOrigin(e){let t,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},a=e9();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!i;let o=tt(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(nW));this.animationProgress=0,this.mixTargetDelta=i=>{let r=i/1e3;if(n_(a.x,e.x,r),n_(a.y,e.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,m,p,f,g;rD(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,f=o,g=r,nU(m.x,p.x,f.x,g),nU(m.y,p.y,f.y,g),t&&(u=this.relativeTarget,h=t,nv(u.x,h.x)&&nv(u.y,h.y))&&(this.isProjectionDirty=!1),t||(t=tt()),nl(t,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,i,r,n,s){var a,o,l,u;n?(e.opacity=w(0,null!=(a=i.opacity)?a:1,nn(r)),e.opacityExit=w(null!=(o=t.opacity)?o:1,0,ns(r))):s&&(e.opacity=w(null!=(l=t.opacity)?l:1,null!=(u=i.opacity)?u:1,r));for(let n=0;n<ne;n++){let s="border".concat(r9[n],"Radius"),a=nr(t,s),o=nr(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||ni(a)===ni(o)?(e[s]=Math.max(w(nt(a),nt(o),r),0),(B.test(o)||B.test(a))&&(e[s]+="%")):e[s]=o)}(t.rotate||i.rotate)&&(e.rotate=w(t.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){var t,i,r;this.notifyListeners("animationStart"),null==(t=this.currentAnimation)||t.stop(),null==(r=this.resumingFrom)||null==(i=r.currentAnimation)||i.stop(),this.pendingAnimation&&(el(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eo.update(()=>{rJ.hasAnimatedSinceResize=!0,is.layout++,this.motionValue||(this.motionValue=e4(0)),this.currentAnimation=function(e,t,i){let r=eq(e)?e:e4(e);return r.start(rd("",r,t,i)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{is.layout--},onComplete:()=>{is.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:r,latestValues:n}=e;if(t&&i&&r){if(this!==e&&this.layout&&r&&nK(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||tt();let t=rM(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let r=rM(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+r}nl(t,i),E(t,n),rN(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nj),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null==(e=this.getStack())?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null==(e=this.getStack())?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=this.getStack();r&&r.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let r={};i.z&&nS("z",e,r,this.animationValues);for(let t=0;t<nP.length;t++)nS("rotate".concat(nP[t]),e,r,this.animationValues),nS("skew".concat(nP[t]),e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=t$(null==t?void 0:t.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=t$(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!P(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1);return}e.visibility="";let n=r.animationValues||r.latestValues;this.applyTransformsToTarget();let s=function(e,t,i){let r="",n=e.x.translate/t.x,s=e.y.translate/t.y,a=(null==i?void 0:i.z)||0;if((n||s||a)&&(r="translate3d(".concat(n,"px, ").concat(s,"px, ").concat(a,"px) ")),(1!==t.x||1!==t.y)&&(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),i){let{transformPerspective:e,rotate:t,rotateX:n,rotateY:s,skewX:a,skewY:o}=i;e&&(r="perspective(".concat(e,"px) ").concat(r)),t&&(r+="rotate(".concat(t,"deg) ")),n&&(r+="rotateX(".concat(n,"deg) ")),s&&(r+="rotateY(".concat(s,"deg) ")),a&&(r+="skewX(".concat(a,"deg) ")),o&&(r+="skewY(".concat(o,"deg) "))}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(r+="scale(".concat(o,", ").concat(l,")")),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n);i&&(s=i(n,s)),e.transform=s;let{x:a,y:o}=this.projectionDelta;if(e.transformOrigin="".concat(100*a.origin,"% ").concat(100*o.origin,"% 0"),r.animationValues){var l,u;e.opacity=r===this?null!=(u=null!=(l=n.opacity)?l:this.latestValues.opacity)?u:1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit}else e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0;for(let t in tj){if(void 0===n[t])continue;let{correct:i,applyTo:a,isCSSVariable:o}=tj[t],l="none"===s?n[t]:i(n[t],r);if(a){let t=a.length;for(let i=0;i<t;i++)e[a[i]]=l}else o?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?t$(null==t?void 0:t.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null==(t=e.currentAnimation)?void 0:t.stop()}),this.root.nodes.forEach(nD),this.root.sharedNodes.clear()}constructor(e={},t=null==i?void 0:i()){this.id=nT++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,es.value&&(nk.nodes=nk.calculatedTargetDeltas=nk.calculatedProjections=0),this.nodes.forEach(nN),this.nodes.forEach(nI),this.nodes.forEach(nF),this.nodes.forEach(nE),es.addProjectionMetrics&&es.addProjectionMetrics(nk)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new r7)}}}function nM(e){e.updateLayout()}function nA(e){var t;let i=(null==(t=e.resumeFrom)?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:n}=e.options,s=i.source!==e.layout.source;"size"===n?rV(e=>{let r=s?i.measuredBox[e]:i.layoutBox[e],n=rM(r);r.min=t[e].min,r.max=r.min+n}):nK(n,i.layoutBox,t)&&rV(r=>{let n=s?i.measuredBox[r]:i.layoutBox[r],a=rM(t[r]);n.max=n.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});let a=e9();rN(a,t,i.layoutBox);let o=e9();s?rN(o,e.applyTransform(r,!0),i.measuredBox):rN(o,t,i.layoutBox);let l=!ng(a),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let a=tt();rD(a,i.layoutBox,n.layoutBox);let o=tt();rD(o,t,s.layoutBox),nx(a,o)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:i,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function nN(e){es.value&&nk.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nE(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nR(e){e.clearSnapshot()}function nD(e){e.clearMeasurements()}function nV(e){e.isLayoutDirty=!1}function nL(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function nO(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function nI(e){e.resolveTargetDelta()}function nF(e){e.calcProjection()}function nz(e){e.resetSkewAndRotation()}function nB(e){e.removeLeadSnapshot()}function n_(e,t,i){e.translate=w(t.translate,0,i),e.scale=w(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function nU(e,t,i,r){e.min=w(t.min,i.min,r),e.max=w(t.max,i.max,r)}function nW(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let nH={duration:.45,ease:[.4,0,.1,1]},nY=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),nG=nY("applewebkit/")&&!nY("chrome/")?Math.round:ei;function nX(e){e.min=nG(e.min),e.max=nG(e.max)}function nK(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nb(t)-nb(i)))}function nq(e){var t;return e!==e.root&&(null==(t=e.scroll)?void 0:t.wasRoot)}let n$=nC({attachResizeListener:(e,t)=>rP(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nZ={current:void 0},nJ=nC({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!nZ.current){let e=new n$({});e.mount(window),e.setOptions({layoutScroll:!0}),nZ.current=e}return nZ.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function nQ(e,t){let i=function(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document.querySelectorAll(e);return t?Array.from(t):[]}return Array.from(e)}(e),r=new AbortController;return[i,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function n0(e){return!("touch"===e.pointerType||rk.x||rk.y)}function n1(e,t,i){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&eo.postRender(()=>n(t,rS(t)))}function n2(e){return r5(e)&&"offsetHeight"in e}e.s(["isHTMLElement",()=>n2],72846);let n4=(e,t)=>!!t&&(e===t||n4(e,t.parentElement)),n3=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),n5=new WeakSet;function n6(e){return t=>{"Enter"===t.key&&e(t)}}function n8(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function n7(e){return rT(e)&&!(rk.x||rk.y)}function n9(e,t,i){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&eo.postRender(()=>n(t,rS(t)))}let se=new WeakMap,st=new WeakMap,si=e=>{let t=se.get(e.target);t&&t(e)},sr=e=>{e.forEach(si)},sn={some:0,all:1},ss=function(e,t){if("undefined"==typeof Proxy)return t5;let i=new Map,r=(i,r)=>t5(i,r,e,t);return new Proxy((e,t)=>r(e,t),{get:(n,s)=>"create"===s?r:(i.has(s)||i.set(s,t5(s,void 0,e,t)),i.get(s))})}({animation:{Feature:class extends rw{updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();ta(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null==(e=this.unmountControls)||e.call(this)}constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(t=>{let{animation:i,options:r}=t;return function(e,t){let i,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t))i=Promise.all(t.map(t=>rp(e,t,r)));else if("string"==typeof t)i=rp(e,t,r);else{let n="function"==typeof t?t6(e,t,r.custom):t;i=Promise.all(rh(e,n,r))}return i.then(()=>{e.notify("AnimationComplete",t)})}(e,i,r)})),i=rb(),r=!0,n=t=>(i,r)=>{var n;let s=t6(e,r,"exit"===t?null==(n=e.presenceContext)?void 0:n.custom:void 0);if(s){let{transition:e,transitionEnd:t,...r}=s;i={...i,...r,...t}}return i};function s(s){let{props:a}=e,o=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<rg;e++){let r=tu[e],n=t.props[r];(to(n)||!1===n)&&(i[r]=n)}return i}(e.parent)||{},l=[],u=new Set,c={},d=1/0;for(let t=0;t<ry;t++){var h,m;let p=rv[t],f=i[p],g=void 0!==a[p]?a[p]:o[p],v=to(g),y=p===s?f.isActive:null;!1===y&&(d=t);let x=g===o[p]&&g!==a[p]&&v;if(x&&r&&e.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...c},!f.isActive&&null===y||!g&&!f.prevProp||ta(g)||"boolean"==typeof g)continue;let b=(h=f.prevProp,"string"==typeof(m=g)?m!==h:!!Array.isArray(m)&&!rf(m,h)),w=b||p===s&&f.isActive&&!x&&v||t>d&&v,j=!1,k=Array.isArray(g)?g:[g],P=k.reduce(n(p),{});!1===y&&(P={});let{prevResolvedValues:T={}}=f,S={...T,...P},C=t=>{w=!0,u.has(t)&&(j=!0,u.delete(t)),f.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in S){let t=P[e],i=T[e];if(!c.hasOwnProperty(e))(t7(t)&&t7(i)?rf(t,i):t===i)?void 0!==t&&u.has(e)?C(e):f.protectedKeys[e]=!0:null!=t?C(e):u.add(e)}f.prevProp=g,f.prevResolvedValues=P,f.isActive&&(c={...c,...P}),r&&e.blockInitialAnimation&&(w=!1);let M=x&&b,A=!M||j;w&&A&&l.push(...k.map(t=>{let i={type:p};if("string"==typeof t&&r&&!M&&e.manuallyAnimateOnMount&&e.parent){let{parent:r}=e,n=t6(r,t);if(r.enteringChildren&&n){let{delayChildren:t}=n.transition||{};i.delay=rm(r.enteringChildren,e,t)}}return{animation:t,options:i}}))}if(u.size){let t={};if("boolean"!=typeof a.initial){let i=t6(e,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(t.transition=i.transition)}u.forEach(i=>{let r=e.getBaseTarget(i),n=e.getValue(i);n&&(n.liveStyle=!0),t[i]=null!=r?r:null}),l.push({animation:t})}let p=!!l.length;return r&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(p=!1),r=!1,p?t(l):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){var n;if(i[t].isActive===r)return Promise.resolve();null==(n=e.variantChildren)||n.forEach(e=>{var i;return null==(i=e.animationState)?void 0:i.setActive(t,r)}),i[t].isActive=r;let a=s(t);for(let e in i)i[e].protectedKeys={};return a},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=rb(),r=!0}}}(e))}}},exit:{Feature:class extends rw{update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}constructor(){super(...arguments),this.id=rj++}}},inView:{Feature:class extends rw{startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:r="some",once:n}=e,s={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:sn[r]},a=e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=t?i:r;s&&s(e)};var o=this.node.current;let l=function(e){let{root:t,...i}=e,r=t||document;st.has(r)||st.set(r,{});let n=st.get(r),s=JSON.stringify(i);return n[s]||(n[s]=new IntersectionObserver(sr,{root:t,...i})),n[s]}(s);return se.set(o,a),l.observe(o),()=>{se.delete(o),l.unobserve(o)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:i={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==i[e]}(e,t))&&this.startObserver()}unmount(){}constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}}},tap:{Feature:class extends rw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[r,n,s]=nQ(e,i),a=e=>{let r=e.currentTarget;if(!n7(e))return;n5.add(r);let s=t(r,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),n5.has(r)&&n5.delete(r),n7(e)&&"function"==typeof s&&s(e,{success:t})},o=e=>{a(e,r===window||r===document||i.useGlobalTarget||n4(r,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return r.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",a,n),n2(e))&&(e.addEventListener("focus",e=>((e,t)=>{let i=e.currentTarget;if(!i)return;let r=n6(()=>{if(n5.has(i))return;n8(i,"down");let e=n6(()=>{n8(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>n8(i,"cancel"),t)});i.addEventListener("keydown",r,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),t)})(e,n)),n3.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(n9(this.node,t,"Start"),(e,t)=>{let{success:i}=t;return n9(this.node,e,i?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends rw{onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ii(rP(this.node.current,"focus",()=>this.onFocus()),rP(this.node.current,"blur",()=>this.onBlur()))}unmount(){}constructor(){super(...arguments),this.isActive=!1}}},hover:{Feature:class extends rw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[r,n,s]=nQ(e,i),a=e=>{if(!n0(e))return;let{target:i}=e,r=t(i,e);if("function"!=typeof r||!i)return;let s=e=>{n0(e)&&(r(e),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(e=>{e.addEventListener("pointerenter",a,n)}),s}(e,(e,t)=>(n1(this.node,t,"Start"),e=>n1(this.node,e,"End"))))}unmount(){}}},pan:{Feature:class extends rw{onPointerDown(e){this.session=new rI(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rL(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rq(e),onStart:rq(t),onMove:i,onEnd:(e,t)=>{delete this.session,r&&eo.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=rC(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}constructor(){super(...arguments),this.removePointerDownListener=ei}}},drag:{Feature:class extends rw{mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ei}unmount(){this.removeGroupControls(),this.removeListeners()}constructor(e){super(e),this.removeGroupControls=ei,this.removeListeners=ei,this.controls=new rX(e)}},ProjectionNode:nJ,MeasureLayout:r4},layout:{ProjectionNode:nJ,MeasureLayout:r4}},(e,t)=>tL(e)?new tD(t):new tT(t,{allowProjection:e!==i.Fragment}))},59544,51360,e=>{"use strict";e.s(["default",()=>en],59544);var t=e.i(43476),i=e.i(71645),r=e.i(46932);e.s(["cn",()=>ei],51360);let n=(e,t)=>{var i;if(0===e.length)return t.classGroupId;let r=e[0],s=t.nextPart.get(r),a=s?n(e.slice(1),s):void 0;if(a)return a;if(0===t.validators.length)return;let o=e.join("-");return null==(i=t.validators.find(e=>{let{validator:t}=e;return t(o)}))?void 0:i.classGroupId},s=/^\[(.+)\]$/,a=(e,t,i,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:o(t,e)).classGroupId=i;return}if("function"==typeof e)return l(e)?void a(e(r),t,i,r):void t.validators.push({validator:e,classGroupId:i});Object.entries(e).forEach(e=>{let[n,s]=e;a(s,o(t,n),i,r)})})},o=(e,t)=>{let i=e;return t.split("-").forEach(e=>{i.nextPart.has(e)||i.nextPart.set(e,{nextPart:new Map,validators:[]}),i=i.nextPart.get(e)}),i},l=e=>e.isThemeGetter,u=/\s+/;function c(){let e,t,i=0,r="";for(;i<arguments.length;)(e=arguments[i++])&&(t=d(e))&&(r&&(r+=" "),r+=t);return r}let d=e=>{let t;if("string"==typeof e)return e;let i="";for(let r=0;r<e.length;r++)e[r]&&(t=d(e[r]))&&(i&&(i+=" "),i+=t);return i},h=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},m=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,f=/^\d+\/\d+$/,g=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,v=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,y=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,x=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,w=e=>f.test(e),j=e=>!!e&&!Number.isNaN(Number(e)),k=e=>!!e&&Number.isInteger(Number(e)),P=e=>e.endsWith("%")&&j(e.slice(0,-1)),T=e=>g.test(e),S=()=>!0,C=e=>v.test(e)&&!y.test(e),M=()=>!1,A=e=>x.test(e),N=e=>b.test(e),E=e=>!D(e)&&!z(e),R=e=>G(e,$,M),D=e=>m.test(e),V=e=>G(e,Z,C),L=e=>G(e,J,j),O=e=>G(e,K,M),I=e=>G(e,q,N),F=e=>G(e,ee,A),z=e=>p.test(e),B=e=>X(e,Z),_=e=>X(e,Q),U=e=>X(e,K),W=e=>X(e,$),H=e=>X(e,q),Y=e=>X(e,ee,!0),G=(e,t,i)=>{let r=m.exec(e);return!!r&&(r[1]?t(r[1]):i(r[2]))},X=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=p.exec(e);return!!r&&(r[1]?t(r[1]):i)},K=e=>"position"===e||"percentage"===e,q=e=>"image"===e||"url"===e,$=e=>"length"===e||"size"===e||"bg-size"===e,Z=e=>"length"===e,J=e=>"number"===e,Q=e=>"family-name"===e,ee=e=>"shadow"===e;Symbol.toStringTag;let et=function(e){let t,i,r;for(var o=arguments.length,l=Array(o>1?o-1:0),d=1;d<o;d++)l[d-1]=arguments[d];let h=function(o){let u;return i=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,i=new Map,r=new Map,n=(n,s)=>{i.set(n,s),++t>e&&(t=0,r=i,i=new Map)};return{get(e){let t=i.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(n(e,t),t):void 0},set(e,t){i.has(e)?i.set(e,t):n(e,t)}}})((u=l.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:i}=e,r=e=>{let t,i,r=[],n=0,s=0,a=0;for(let i=0;i<e.length;i++){let o=e[i];if(0===n&&0===s){if(":"===o){r.push(e.slice(a,i)),a=i+1;continue}if("/"===o){t=i;continue}}"["===o?n++:"]"===o?n--:"("===o?s++:")"===o&&s--}let o=0===r.length?e:e.substring(a),l=(i=o).endsWith("!")?i.substring(0,i.length-1):i.startsWith("!")?i.substring(1):i;return{modifiers:r,hasImportantModifier:l!==o,baseClassName:l,maybePostfixModifierPosition:t&&t>a?t-a:void 0}};if(t){let e=t+":",i=r;r=t=>t.startsWith(e)?i(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(i){let e=r;r=t=>i({className:t,parseClassName:e})}return r})(u),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let i=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(i.push(...r.sort(),e),r=[]):r.push(e)}),i.push(...r.sort()),i}})(u),...(e=>{let t=(e=>{let{theme:t,classGroups:i}=e,r={nextPart:new Map,validators:[]};for(let e in i)a(i[e],r,e,t);return r})(e),{conflictingClassGroups:i,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let i=e.split("-");return""===i[0]&&1!==i.length&&i.shift(),n(i,t)||(e=>{if(s.test(e)){let t=s.exec(e)[1],i=null==t?void 0:t.substring(0,t.indexOf(":"));if(i)return"arbitrary.."+i}})(e)},getConflictingClassGroupIds:(e,t)=>{let n=i[e]||[];return t&&r[e]?[...n,...r[e]]:n}}})(u)}).cache.get,r=t.cache.set,h=m,m(o)};function m(e){let n=i(e);if(n)return n;let s=((e,t)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:s}=t,a=[],o=e.trim().split(u),l="";for(let e=o.length-1;e>=0;e-=1){let t=o[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:m}=i(t);if(u){l=t+(l.length>0?" "+l:l);continue}let p=!!m,f=r(p?h.substring(0,m):h);if(!f){if(!p||!(f=r(h))){l=t+(l.length>0?" "+l:l);continue}p=!1}let g=s(c).join(":"),v=d?g+"!":g,y=v+f;if(a.includes(y))continue;a.push(y);let x=n(f,p);for(let e=0;e<x.length;++e){let t=x[e];a.push(v+t)}l=t+(l.length>0?" "+l:l)}return l})(e,t);return r(e,s),s}return function(){return h(c.apply(null,arguments))}}(()=>{let e=h("color"),t=h("font"),i=h("text"),r=h("font-weight"),n=h("tracking"),s=h("leading"),a=h("breakpoint"),o=h("container"),l=h("spacing"),u=h("radius"),c=h("shadow"),d=h("inset-shadow"),m=h("text-shadow"),p=h("drop-shadow"),f=h("blur"),g=h("perspective"),v=h("aspect"),y=h("ease"),x=h("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],C=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],M=()=>[...C(),z,D],A=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],G=()=>[z,D,l],X=()=>[w,"full","auto",...G()],K=()=>[k,"none","subgrid",z,D],q=()=>["auto",{span:["full",k,z,D]},k,z,D],$=()=>[k,"auto",z,D],Z=()=>["auto","min","max","fr",z,D],J=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...G()],et=()=>[w,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],ei=()=>[e,z,D],er=()=>[...C(),U,O,{position:[z,D]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],es=()=>["auto","cover","contain",W,R,{size:[z,D]}],ea=()=>[P,B,V],eo=()=>["","none","full",u,z,D],el=()=>["",j,B,V],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[j,P,U,O],eh=()=>["","none",f,z,D],em=()=>["none",j,z,D],ep=()=>["none",j,z,D],ef=()=>[j,z,D],eg=()=>[w,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[T],breakpoint:[T],color:[S],container:[T],"drop-shadow":[T],ease:["in","out","in-out"],font:[E],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[T],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[T],shadow:[T],spacing:["px",j],text:[T],"text-shadow":[T],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",w,D,z,v]}],container:["container"],columns:[{columns:[j,D,z,o]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:M()}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:X()}],"inset-x":[{"inset-x":X()}],"inset-y":[{"inset-y":X()}],start:[{start:X()}],end:[{end:X()}],top:[{top:X()}],right:[{right:X()}],bottom:[{bottom:X()}],left:[{left:X()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",z,D]}],basis:[{basis:[w,"full","auto",o,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[j,w,"auto","initial","none",D]}],grow:[{grow:["",j,z,D]}],shrink:[{shrink:["",j,z,D]}],order:[{order:[k,"first","last","none",z,D]}],"grid-cols":[{"grid-cols":K()}],"col-start-end":[{col:q()}],"col-start":[{"col-start":$()}],"col-end":[{"col-end":$()}],"grid-rows":[{"grid-rows":K()}],"row-start-end":[{row:q()}],"row-start":[{"row-start":$()}],"row-end":[{"row-end":$()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...J(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...J()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":J()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[o,"screen",...et()]}],"min-w":[{"min-w":[o,"screen","none",...et()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",i,B,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,z,L]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",P,D]}],"font-family":[{font:[_,D,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,z,D]}],"line-clamp":[{"line-clamp":[j,"none",z,L]}],leading:[{leading:[s,...G()]}],"list-image":[{"list-image":["none",z,D]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",z,D]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ei()}],"text-color":[{text:ei()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[j,"from-font","auto",z,V]}],"text-decoration-color":[{decoration:ei()}],"underline-offset":[{"underline-offset":[j,"auto",z,D]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",z,D]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",z,D]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:es()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,z,D],radial:["",z,D],conic:[k,z,D]},H,I]}],"bg-color":[{bg:ei()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:ei()}],"gradient-via":[{via:ei()}],"gradient-to":[{to:ei()}],rounded:[{rounded:eo()}],"rounded-s":[{"rounded-s":eo()}],"rounded-e":[{"rounded-e":eo()}],"rounded-t":[{"rounded-t":eo()}],"rounded-r":[{"rounded-r":eo()}],"rounded-b":[{"rounded-b":eo()}],"rounded-l":[{"rounded-l":eo()}],"rounded-ss":[{"rounded-ss":eo()}],"rounded-se":[{"rounded-se":eo()}],"rounded-ee":[{"rounded-ee":eo()}],"rounded-es":[{"rounded-es":eo()}],"rounded-tl":[{"rounded-tl":eo()}],"rounded-tr":[{"rounded-tr":eo()}],"rounded-br":[{"rounded-br":eo()}],"rounded-bl":[{"rounded-bl":eo()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:ei()}],"border-color-x":[{"border-x":ei()}],"border-color-y":[{"border-y":ei()}],"border-color-s":[{"border-s":ei()}],"border-color-e":[{"border-e":ei()}],"border-color-t":[{"border-t":ei()}],"border-color-r":[{"border-r":ei()}],"border-color-b":[{"border-b":ei()}],"border-color-l":[{"border-l":ei()}],"divide-color":[{divide:ei()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[j,z,D]}],"outline-w":[{outline:["",j,B,V]}],"outline-color":[{outline:ei()}],shadow:[{shadow:["","none",c,Y,F]}],"shadow-color":[{shadow:ei()}],"inset-shadow":[{"inset-shadow":["none",d,Y,F]}],"inset-shadow-color":[{"inset-shadow":ei()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ei()}],"ring-offset-w":[{"ring-offset":[j,V]}],"ring-offset-color":[{"ring-offset":ei()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":ei()}],"text-shadow":[{"text-shadow":["none",m,Y,F]}],"text-shadow-color":[{"text-shadow":ei()}],opacity:[{opacity:[j,z,D]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[j]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":ei()}],"mask-image-linear-to-color":[{"mask-linear-to":ei()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":ei()}],"mask-image-t-to-color":[{"mask-t-to":ei()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":ei()}],"mask-image-r-to-color":[{"mask-r-to":ei()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":ei()}],"mask-image-b-to-color":[{"mask-b-to":ei()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":ei()}],"mask-image-l-to-color":[{"mask-l-to":ei()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":ei()}],"mask-image-x-to-color":[{"mask-x-to":ei()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":ei()}],"mask-image-y-to-color":[{"mask-y-to":ei()}],"mask-image-radial":[{"mask-radial":[z,D]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":ei()}],"mask-image-radial-to-color":[{"mask-radial-to":ei()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":C()}],"mask-image-conic-pos":[{"mask-conic":[j]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":ei()}],"mask-image-conic-to-color":[{"mask-conic-to":ei()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:es()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",z,D]}],filter:[{filter:["","none",z,D]}],blur:[{blur:eh()}],brightness:[{brightness:[j,z,D]}],contrast:[{contrast:[j,z,D]}],"drop-shadow":[{"drop-shadow":["","none",p,Y,F]}],"drop-shadow-color":[{"drop-shadow":ei()}],grayscale:[{grayscale:["",j,z,D]}],"hue-rotate":[{"hue-rotate":[j,z,D]}],invert:[{invert:["",j,z,D]}],saturate:[{saturate:[j,z,D]}],sepia:[{sepia:["",j,z,D]}],"backdrop-filter":[{"backdrop-filter":["","none",z,D]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[j,z,D]}],"backdrop-contrast":[{"backdrop-contrast":[j,z,D]}],"backdrop-grayscale":[{"backdrop-grayscale":["",j,z,D]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[j,z,D]}],"backdrop-invert":[{"backdrop-invert":["",j,z,D]}],"backdrop-opacity":[{"backdrop-opacity":[j,z,D]}],"backdrop-saturate":[{"backdrop-saturate":[j,z,D]}],"backdrop-sepia":[{"backdrop-sepia":["",j,z,D]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",z,D]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[j,"initial",z,D]}],ease:[{ease:["linear","initial",y,z,D]}],delay:[{delay:[j,z,D]}],animate:[{animate:["none",x,z,D]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,z,D]}],"perspective-origin":[{"perspective-origin":M()}],rotate:[{rotate:em()}],"rotate-x":[{"rotate-x":em()}],"rotate-y":[{"rotate-y":em()}],"rotate-z":[{"rotate-z":em()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[z,D,"","none","gpu","cpu"]}],"transform-origin":[{origin:M()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:ei()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ei()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",z,D]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",z,D]}],fill:[{fill:["none",...ei()]}],"stroke-w":[{stroke:[j,B,V,L]}],stroke:[{stroke:["none",...ei()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ei(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return et(function(){for(var e,t,i=0,r="",n=arguments.length;i<n;i++)(e=arguments[i])&&(t=function e(t){var i,r,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(i=0;i<s;i++)t[i]&&(r=e(t[i]))&&(n&&(n+=" "),n+=r)}else for(r in t)t[r]&&(n&&(n+=" "),n+=r);return n}(e))&&(r&&(r+=" "),r+=t);return r}(t))}let er=i.default.forwardRef((e,i)=>{let{className:n,variant:s="primary",size:a="md",isLoading:o=!1,leftIcon:l,rightIcon:u,children:c,disabled:d,...h}=e,m=ei(["inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed","relative overflow-hidden"],{primary:["bg-primary-600 text-white hover:bg-primary-700","focus:ring-primary-500 shadow-lg hover:shadow-xl","active:bg-primary-800"],secondary:["bg-sage-100 text-sage-800 hover:bg-sage-200","focus:ring-sage-500 border border-sage-200","active:bg-sage-300"],outline:["border-2 border-primary-600 text-primary-600 hover:bg-primary-50","focus:ring-primary-500 hover:border-primary-700","active:bg-primary-100"],ghost:["text-gray-700 hover:bg-gray-100","focus:ring-gray-500","active:bg-gray-200"],danger:["bg-red-600 text-white hover:bg-red-700","focus:ring-red-500 shadow-lg hover:shadow-xl","active:bg-red-800"]}[s],{sm:"px-3 py-1.5 text-sm gap-1.5",md:"px-4 py-2 text-base gap-2",lg:"px-6 py-3 text-lg gap-2.5",xl:"px-8 py-4 text-xl gap-3"}[a],n);return(0,t.jsxs)(r.motion.button,{ref:i,className:m,disabled:d||o,whileHover:{scale:d||o?1:1.02},whileTap:{scale:d||o?1:.98},transition:{duration:.1},...h,children:[o&&(0,t.jsx)(r.motion.div,{className:"absolute inset-0 bg-white/20 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.2},children:(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"})}),(0,t.jsxs)("div",{className:ei("flex items-center gap-inherit",o&&"opacity-0"),children:[l&&(0,t.jsx)("span",{className:"flex-shrink-0",children:l}),(0,t.jsx)("span",{children:c}),u&&(0,t.jsx)("span",{className:"flex-shrink-0",children:u})]})]})});er.displayName="Button";let en=er},58234,e=>{"use strict";e.s(["default",()=>o]);var t=e.i(43476),i=e.i(71645),r=e.i(22016),n=e.i(57688),s=e.i(46932),a=e.i(59544);let o=()=>{let e=[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Refund Policy",href:"/refund"},{name:"Cookie Policy",href:"/cookies"},{name:"Transparency",href:"/transparency"},{name:"Annual Reports",href:"/reports"}],o=[{name:"Facebook",href:"https://facebook.com/ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})},{name:"Twitter",href:"https://twitter.com/ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})},{name:"Instagram",href:"https://instagram.com/ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z"})})},{name:"LinkedIn",href:"https://linkedin.com/company/ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})},{name:"YouTube",href:"https://youtube.com/@ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"})})}];return(0,t.jsxs)("footer",{className:"bg-gray-900 text-white",children:[(0,t.jsx)("div",{className:"bg-primary-600",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Stay Connected with Our Mission"}),(0,t.jsx)("p",{className:"text-primary-100 mb-8 max-w-2xl mx-auto",children:"Get updates on our programs, impact stories, and ways you can make a difference in communities across India."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[(0,t.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white"}),(0,t.jsx)(a.default,{variant:"secondary",size:"md",className:"bg-white text-primary-600 hover:bg-gray-100",children:"Subscribe"})]})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,t.jsx)("div",{className:"relative w-12 h-12",children:(0,t.jsx)(n.default,{src:"/logo.jpeg",alt:"Ayurakshak Logo",fill:!0,className:"object-contain rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Ayurakshak"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Care · Restore · Protect"})]})]}),(0,t.jsx)("p",{className:"text-gray-300 mb-6 leading-relaxed",children:"Ayurakshak combines traditional Ayurveda with modern outreach to heal communities across India. Through health camps, sustainable livelihoods, and green initiatives, we're building a healthier, more resilient future for all."}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,t.jsx)("p",{children:"📍 123 Wellness Street, New Delhi, India 110001"}),(0,t.jsx)("p",{children:"📞 +91 98765 43210"}),(0,t.jsx)("p",{children:"✉️ <EMAIL>"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:"Quick Links"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{name:"About Us",href:"/about"},{name:"Our Programs",href:"/#programs"},{name:"Impact Stories",href:"/#impact"},{name:"Volunteer",href:"/volunteer"},{name:"Careers",href:"/careers"},{name:"News & Updates",href:"/news"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(r.default,{href:e.href,className:"text-gray-400 hover:text-white transition-colors duration-200",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:"Our Programs"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{name:"Health Camps",href:"/#programs"},{name:"Women Livelihoods",href:"/#programs"},{name:"Education Support",href:"/#programs"},{name:"Emergency Relief",href:"/#programs"},{name:"Environmental Care",href:"/#programs"},{name:"Community Development",href:"/#programs"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(r.default,{href:e.href,className:"text-gray-400 hover:text-white transition-colors duration-200",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:"Support Us"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{name:"Donate",href:"/donate"},{name:"Sponsor a Program",href:"/sponsor"},{name:"Corporate Partnership",href:"/partnership"},{name:"Volunteer",href:"/volunteer"},{name:"Fundraise",href:"/fundraise"},{name:"Gift a Smile",href:"/gift"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(r.default,{href:e.href,className:"text-gray-400 hover:text-white transition-colors duration-200",children:e.name})},e.name))})]})]}),(0,t.jsx)("div",{className:"mt-12 pt-8 border-t border-gray-800",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsx)("div",{className:"flex space-x-6 mb-6 md:mb-0",children:o.map(e=>(0,t.jsx)(s.motion.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors duration-200",whileHover:{scale:1.2},whileTap:{scale:.9},"aria-label":e.name,children:e.icon},e.name))}),(0,t.jsxs)("div",{className:"text-center md:text-right",children:[(0,t.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Registered NGO | 80G Tax Exemption Available"}),(0,t.jsx)("div",{className:"flex flex-wrap justify-center md:justify-end gap-4 text-xs text-gray-500",children:e.map((n,s)=>(0,t.jsxs)(i.default.Fragment,{children:[(0,t.jsx)(r.default,{href:n.href,className:"hover:text-gray-300 transition-colors duration-200",children:n.name}),s<e.length-1&&(0,t.jsx)("span",{children:"•"})]},n.name))})]})]})}),(0,t.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-800 text-center",children:(0,t.jsxs)("p",{className:"text-gray-400 text-sm",children:["© ",new Date().getFullYear()," Ayurakshak. All rights reserved.",(0,t.jsx)("span",{className:"mx-2",children:"•"}),"Developed with ❤️ by"," ",(0,t.jsx)("a",{href:"https://kush-personal-portfolio-my-portfolio.vercel.app/",target:"_blank",rel:"noopener noreferrer",className:"text-primary-400 hover:text-primary-300 transition-colors duration-200",children:"Kush Vardhan"})]})})]})]})}},29004,e=>{"use strict";e.s(["default",()=>w],29004);var t=e.i(43476),i=e.i(71645),r=e.i(22016),n=e.i(57688),s=e.i(46932);e.i(47167);var a=e.i(31178),o=e.i(47414),l=e.i(74008),u=e.i(21476),c=e.i(72846),d=i,h=e.i(37806);class m extends d.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,i=(0,c.isHTMLElement)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:i,isPresent:r,anchorX:n,root:s}=e,a=(0,d.useId)(),o=(0,d.useRef)(null),l=(0,d.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,d.useContext)(h.MotionConfigContext);return(0,d.useInsertionEffect)(()=>{let{width:e,height:t,top:i,left:c,right:d}=l.current;if(r||!o.current||!e||!t)return;o.current.dataset.motionPopId=a;let h=document.createElement("style");u&&(h.nonce=u);let m=null!=s?s:document.head;return m.appendChild(h),h.sheet&&h.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===n?"left: ".concat(c):"right: ".concat(d),"px !important;\n            top: ").concat(i,"px !important;\n          }\n        ")),()=>{m.contains(h)&&m.removeChild(h)}},[r]),(0,t.jsx)(m,{isPresent:r,childRef:o,sizeRef:l,children:d.cloneElement(i,{ref:o})})}let f=e=>{let{children:r,initial:n,isPresent:s,onExitComplete:a,custom:l,presenceAffectsLayout:c,mode:d,anchorX:h,root:m}=e,f=(0,o.useConstant)(g),v=(0,i.useId)(),y=!0,x=(0,i.useMemo)(()=>(y=!1,{id:v,initial:n,isPresent:s,custom:l,onExitComplete:e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;a&&a()},register:e=>(f.set(e,!1),()=>f.delete(e))}),[s,f,a]);return c&&y&&(x={...x}),(0,i.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[s]),i.useEffect(()=>{s||f.size||!a||a()},[s]),"popLayout"===d&&(r=(0,t.jsx)(p,{isPresent:s,anchorX:h,root:m,children:r})),(0,t.jsx)(u.PresenceContext.Provider,{value:x,children:r})};function g(){return new Map}var v=e.i(64978);let y=e=>e.key||"";function x(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let b=e=>{let{children:r,custom:n,initial:s=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:d="sync",propagate:h=!1,anchorX:m="left",root:p}=e,[g,b]=(0,v.usePresence)(h),w=(0,i.useMemo)(()=>x(r),[r]),j=h&&!g?[]:w.map(y),k=(0,i.useRef)(!0),P=(0,i.useRef)(w),T=(0,o.useConstant)(()=>new Map),[S,C]=(0,i.useState)(w),[M,A]=(0,i.useState)(w);(0,l.useIsomorphicLayoutEffect)(()=>{k.current=!1,P.current=w;for(let e=0;e<M.length;e++){let t=y(M[e]);j.includes(t)?T.delete(t):!0!==T.get(t)&&T.set(t,!1)}},[M,j.length,j.join("-")]);let N=[];if(w!==S){let e=[...w];for(let t=0;t<M.length;t++){let i=M[t],r=y(i);j.includes(r)||(e.splice(t,0,i),N.push(i))}return"wait"===d&&N.length&&(e=N),A(x(e)),C(w),null}let{forceRender:E}=(0,i.useContext)(a.LayoutGroupContext);return(0,t.jsx)(t.Fragment,{children:M.map(e=>{let i=y(e),r=(!h||!!g)&&(w===M||j.includes(i));return(0,t.jsx)(f,{isPresent:r,initial:(!k.current||!!s)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,root:p,onExitComplete:r?void 0:()=>{if(!T.has(i))return;T.set(i,!0);let e=!0;T.forEach(t=>{t||(e=!1)}),e&&(null==E||E(),A(P.current),h&&(null==b||b()),u&&u())},anchorX:m,children:e},i)})})},w=()=>{let[e,a]=(0,i.useState)(!1),[o,l]=(0,i.useState)(!1),[u,c]=(0,i.useState)(null);(0,i.useEffect)(()=>{let e=()=>{a(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let d=[{name:"Home",href:"/"},{name:"Services",href:"/services",dropdown:["Ayurvedic Treatment","Panchakarma Therapy","Herbal Medicine","Yoga & Meditation","Diet Consultation","Lifestyle Counseling"]},{name:"Diseases",href:"/diseases",dropdown:["Kidney Disease","Liver Disease","Cancer Disease","Heart Disease","Diabetes Type1/Type2","Blood Pressure","Joint Pain","Digestive Issues","Respiratory Problems","Skin Disorders"]},{name:"About Ayurakshak",href:"/about"},{name:"Contact Us",href:"/contact"},{name:"Patient Stories",href:"/patient-stories",dropdown:["Patient Testimonials","Case Studies","Recovery Stories","Success Stories"]},{name:"Camps",href:"/camps"}];return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"bg-primary-600 text-white py-2 text-sm",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})}),(0,t.jsx)("span",{children:"+91-98765-43210"})]}),(0,t.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,t.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,t.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}),(0,t.jsx)("span",{children:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,t.jsx)("span",{children:"Follow Us:"}),(0,t.jsx)("div",{className:"flex space-x-2",children:["facebook","instagram","twitter","youtube"].map(e=>(0,t.jsx)("a",{href:"#".concat(e),className:"w-6 h-6 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors",children:(0,t.jsx)("span",{className:"text-xs",children:"📱"})},e))})]})]})})}),(0,t.jsxs)(s.motion.header,{initial:{y:-100},animate:{y:0},transition:{duration:.6},className:"sticky top-0 z-50 transition-all duration-300 ".concat(e?"bg-white shadow-lg":"bg-white"),children:[(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,t.jsxs)(r.default,{href:"/",className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"relative w-12 h-12 lg:w-14 lg:h-14",children:(0,t.jsx)(n.default,{src:"/logo.jpeg",alt:"Ayurakshak Logo",fill:!0,className:"object-contain rounded-full",sizes:"56px"})}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"font-heading font-bold text-xl lg:text-2xl text-primary-600",children:"Ayurakshak"}),(0,t.jsx)("span",{className:"text-xs text-gray-600 hidden sm:block",children:"Traditional Ayurveda & Naturopathy"})]})]}),(0,t.jsx)("nav",{className:"hidden lg:flex items-center space-x-1",children:d.map(e=>(0,t.jsxs)("div",{className:"relative",onMouseEnter:()=>e.dropdown&&c(e.name),onMouseLeave:()=>c(null),children:[(0,t.jsxs)(r.default,{href:e.href,className:"flex items-center px-4 py-2 text-gray-700 hover:text-primary-600 font-medium transition-colors duration-300",children:[e.name,e.dropdown&&(0,t.jsx)("svg",{className:"w-4 h-4 ml-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),e.dropdown&&u===e.name&&(0,t.jsx)(s.motion.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},className:"absolute top-full left-0 mt-1 w-64 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50",children:e.dropdown.map(i=>(0,t.jsx)(r.default,{href:"".concat(e.href,"/").concat(i.toLowerCase().replace(/\s+/g,"-")),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors",children:i},i))})]},e.name))}),(0,t.jsxs)("div",{className:"hidden lg:flex items-center space-x-3",children:[(0,t.jsxs)(r.default,{href:"/shop",className:"bg-sage-500 hover:bg-sage-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-300 flex items-center space-x-2",children:[(0,t.jsx)("span",{children:"🛒"}),(0,t.jsx)("span",{children:"Shop Now"})]}),(0,t.jsx)(r.default,{href:"/contact",className:"bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-300",children:"+91-98765-43210"})]}),(0,t.jsx)("button",{onClick:()=>l(!o),className:"lg:hidden p-2 rounded-md text-gray-700 hover:text-primary-600 transition-colors duration-300","aria-label":"Toggle mobile menu",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o?(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]})}),(0,t.jsx)(b,{children:o&&(0,t.jsx)(s.motion.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"lg:hidden bg-white border-t border-gray-200",children:(0,t.jsxs)("div",{className:"px-4 py-6 space-y-4 max-h-96 overflow-y-auto",children:[d.map(e=>(0,t.jsxs)("div",{children:[(0,t.jsx)(r.default,{href:e.href,className:"block text-gray-700 hover:text-primary-600 font-medium transition-colors duration-300 py-2",onClick:()=>!e.dropdown&&l(!1),children:e.name}),e.dropdown&&(0,t.jsx)("div",{className:"ml-4 mt-2 space-y-2",children:e.dropdown.map(i=>(0,t.jsx)(r.default,{href:"".concat(e.href,"/").concat(i.toLowerCase().replace(/\s+/g,"-")),className:"block text-sm text-gray-600 hover:text-primary-600 transition-colors py-1",onClick:()=>l(!1),children:i},i))})]},e.name)),(0,t.jsxs)("div",{className:"pt-4 space-y-3",children:[(0,t.jsx)(r.default,{href:"/shop",className:"block bg-sage-500 hover:bg-sage-600 text-white px-6 py-3 rounded-lg font-medium text-center transition-colors duration-300",onClick:()=>l(!1),children:"🛒 Shop Now"}),(0,t.jsx)(r.default,{href:"/contact",className:"block bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-lg font-medium text-center transition-colors duration-300",onClick:()=>l(!1),children:"📞 +91-98765-43210"})]})]})})})]})]})}},41383,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(43476),i=e.i(46932),r=e.i(22016);let n=()=>(0,t.jsx)("section",{className:"py-20 bg-white",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(i.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,t.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["Diseases and ",(0,t.jsx)("span",{className:"text-primary-600",children:"Conditions We Treat"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Our traditional Ayurvedic approach has helped thousands of patients recover from chronic conditions naturally and effectively."})]}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12",children:[{id:"kidney",name:"Kidney Disease",description:"If left untreated, it can lead to kidney failure.",icon:"🫘",color:"from-red-500 to-red-600",bgColor:"bg-red-50",textColor:"text-red-600",stats:"85% Success Rate",treatments:["Ayurvedic Medicine","Panchakarma","Diet Therapy"]},{id:"liver",name:"Liver Disease",description:"Catching it early can prevent liver damage.",icon:"🫀",color:"from-orange-500 to-orange-600",bgColor:"bg-orange-50",textColor:"text-orange-600",stats:"90% Success Rate",treatments:["Herbal Medicine","Detox Therapy","Lifestyle Changes"]},{id:"cancer",name:"Cancer",description:"Early management can reverse cancer.",icon:"🎗️",color:"from-purple-500 to-purple-600",bgColor:"bg-purple-50",textColor:"text-purple-600",stats:"75% Success Rate",treatments:["Immunotherapy","Herbal Support","Nutrition Therapy"]},{id:"heart",name:"Heart Disease",description:"Manage your heart health to avoid failure.",icon:"❤️",color:"from-pink-500 to-pink-600",bgColor:"bg-pink-50",textColor:"text-pink-600",stats:"88% Success Rate",treatments:["Cardiac Care","Yoga Therapy","Stress Management"]},{id:"blood-pressure",name:"Blood Pressure",description:"Reverse BP & protect yourself.",icon:"🩺",color:"from-blue-500 to-blue-600",bgColor:"bg-blue-50",textColor:"text-blue-600",stats:"92% Success Rate",treatments:["Natural Medicine","Meditation","Diet Control"]},{id:"diabetes",name:"Diabetes",description:"Reverse diabetes to avoid serious problems.",icon:"🍯",color:"from-green-500 to-green-600",bgColor:"bg-green-50",textColor:"text-green-600",stats:"87% Success Rate",treatments:["Sugar Control","Panchakarma","Exercise Therapy"]}].map((e,n)=>(0,t.jsx)(i.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*n},viewport:{once:!0},className:"group",children:(0,t.jsx)(r.default,{href:"/diseases/".concat(e.id),children:(0,t.jsxs)("div",{className:"relative overflow-hidden rounded-2xl ".concat(e.bgColor," p-8 h-full transition-all duration-300 hover:shadow-xl hover:scale-105 cursor-pointer"),children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br ".concat(e.color," opacity-0 group-hover:opacity-10 transition-opacity duration-300")}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,t.jsx)("h3",{className:"text-2xl font-bold ".concat(e.textColor," mb-3 group-hover:text-gray-900 transition-colors"),children:e.name}),(0,t.jsx)("p",{className:"text-gray-600 mb-4 leading-relaxed",children:e.description}),(0,t.jsx)("div",{className:"inline-block px-3 py-1 rounded-full text-sm font-medium ".concat(e.bgColor," ").concat(e.textColor," mb-4"),children:e.stats}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-700",children:"Treatment Methods:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.treatments.map(e=>(0,t.jsx)("span",{className:"text-xs px-2 py-1 bg-white rounded-full text-gray-600 border",children:e},e))})]}),(0,t.jsxs)("div",{className:"mt-6 flex items-center text-sm font-medium text-gray-500 group-hover:text-primary-600 transition-colors",children:[(0,t.jsx)("span",{children:"Learn More"}),(0,t.jsx)("svg",{className:"w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]})})},e.id))}),(0,t.jsx)(i.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-primary-50 to-sage-50 rounded-2xl p-8 md:p-12",children:[(0,t.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"Other Diseases We Treat"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-6 max-w-2xl mx-auto",children:"We also provide effective treatment for joint pain, digestive issues, respiratory problems, skin disorders, thyroid conditions, and many other chronic health conditions."}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8",children:["Joint Pain","Digestive Issues","Respiratory Problems","Skin Disorders","Thyroid","Migraine","Obesity","Stress & Anxiety"].map(e=>(0,t.jsx)("div",{className:"bg-white rounded-lg p-4 shadow-soft hover:shadow-medium transition-shadow",children:(0,t.jsx)("div",{className:"text-sm font-medium text-gray-700",children:e})},e))}),(0,t.jsxs)(r.default,{href:"/diseases",className:"inline-flex items-center px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-300",children:["View All Conditions",(0,t.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})})]})})},18453,e=>{"use strict";e.s(["default",()=>o],18453);var t=e.i(43476),i=e.i(71645),r=e.i(46932),n=e.i(59544),s=e.i(51360);let a=i.default.forwardRef((e,i)=>{let{className:n,label:a,error:o,helperText:l,leftIcon:u,rightIcon:c,variant:d="default",inputSize:h="md",id:m,...p}=e,f=m||"input-".concat(Math.random().toString(36).substr(2,9)),g={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"},v=(0,s.cn)(["w-full transition-all duration-200 focus:outline-none","disabled:opacity-50 disabled:cursor-not-allowed"],{default:["border border-gray-300 rounded-lg bg-white","focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20",o&&"border-red-500 focus:border-red-500 focus:ring-red-500/20"],filled:["border-0 rounded-lg bg-gray-100","focus:bg-white focus:ring-2 focus:ring-primary-500/20",o&&"bg-red-50 focus:ring-red-500/20"],outlined:["border-2 border-gray-300 rounded-lg bg-transparent","focus:border-primary-500",o&&"border-red-500 focus:border-red-500"]}[d],{sm:"px-3 py-2 text-sm",md:"px-4 py-3 text-base",lg:"px-5 py-4 text-lg"}[h],u&&"pl-10",c&&"pr-10",n);return(0,t.jsxs)("div",{className:"w-full",children:[a&&(0,t.jsx)("label",{htmlFor:f,className:(0,s.cn)("block text-sm font-medium mb-2",o?"text-red-700":"text-gray-700"),children:a}),(0,t.jsxs)("div",{className:"relative",children:[u&&(0,t.jsx)("div",{className:(0,s.cn)("absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",g[h]),children:u}),(0,t.jsx)("input",{ref:i,id:f,className:v,...p}),c&&(0,t.jsx)("div",{className:(0,s.cn)("absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",g[h]),children:c})]}),(o||l)&&(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:-5},animate:{opacity:1,y:0},transition:{duration:.2},className:"mt-2",children:[o&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),o]}),l&&!o&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:l})]})]})});a.displayName="Input";let o=()=>{let[e,s]=(0,i.useState)({name:"",phone:"",disease:""}),[o,l]=(0,i.useState)(!1),u=async t=>{if(t.preventDefault(),e.name&&e.phone&&e.disease){l(!0);try{(await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,phone:e.phone,message:"Inquiry about ".concat(e.disease),subject:"Treatment Inquiry - ".concat(e.disease)})})).ok?(alert("Thank you for reaching out! Our representative will contact you soon."),s({name:"",phone:"",disease:""})):alert("Something went wrong. Please try again.")}catch(e){console.error("Form submission error:",e),alert("Something went wrong. Please try again.")}finally{l(!1)}}},c=(e,t)=>{s(i=>({...i,[e]:t}))};return(0,t.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 via-sage-50 to-mint-50 overflow-hidden",children:[(0,t.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,t.jsx)("div",{className:"absolute top-20 left-20 w-32 h-32 bg-primary-300 rounded-full blur-3xl"}),(0,t.jsx)("div",{className:"absolute top-40 right-32 w-48 h-48 bg-sage-300 rounded-full blur-3xl"}),(0,t.jsx)("div",{className:"absolute bottom-32 left-1/3 w-40 h-40 bg-mint-300 rounded-full blur-3xl"})]}),(0,t.jsx)("div",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,t.jsxs)(r.motion.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"text-center lg:text-left",children:[(0,t.jsxs)(r.motion.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight",children:["Ayurakshak: Center for"," ",(0,t.jsx)("span",{className:"text-primary-600",children:"Traditional Healing"})]}),(0,t.jsx)(r.motion.p,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"We understand the toll a disease can take on you and your family. The endless cycle of medication and no relief can be overwhelming. Let us help you reclaim your well-being through traditional Ayurveda and naturopathy."}),(0,t.jsx)(r.motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"grid grid-cols-2 md:grid-cols-4 gap-6 mb-8",children:[{number:"10,000+",label:"Patients Treated"},{number:"95%",label:"Success Rate"},{number:"15+",label:"Years Experience"},{number:"100%",label:"Natural Treatment"}].map((e,i)=>(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl md:text-3xl font-bold text-primary-600 mb-1",children:e.number}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:e.label})]},e.label))}),(0,t.jsx)(r.motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"flex flex-wrap justify-center lg:justify-start gap-6 mb-8",children:[{icon:"🏥",label:"Virtual Consultation"},{icon:"📍",label:"Center Near Me"},{icon:"🏠",label:"Home Treatment"},{icon:"📅",label:"Health Camps"}].map(e=>(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2 p-4 bg-white rounded-lg shadow-soft hover:shadow-medium transition-shadow",children:[(0,t.jsx)("div",{className:"text-2xl",children:e.icon}),(0,t.jsx)("div",{className:"text-sm font-medium text-gray-700 text-center",children:e.label})]},e.label))})]}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},className:"bg-white rounded-2xl shadow-xl p-8 lg:p-10",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Enquire Now"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Fill the form below to get started with your healing journey"})]}),(0,t.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Name *"}),(0,t.jsx)(a,{type:"text",placeholder:"Enter your full name",value:e.name,onChange:e=>c("name",e.target.value),required:!0,variant:"filled",inputSize:"lg"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Your Phone Number *"}),(0,t.jsx)(a,{type:"tel",placeholder:"Enter your phone number",value:e.phone,onChange:e=>c("phone",e.target.value),required:!0,variant:"filled",inputSize:"lg"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Disease/Condition *"}),(0,t.jsxs)("select",{value:e.disease,onChange:e=>c("disease",e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors bg-gray-50",children:[(0,t.jsx)("option",{value:"",children:"Select Disease"}),["Kidney Problem","Liver Problem","Cancer","Heart Disease","Thalassemia","Diabetes","Blood Pressure","Joint Pain","Gas/Acidity","Obesity","Thyroid","Others"].map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})]}),(0,t.jsx)(n.default,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:o,disabled:!e.name||!e.phone||!e.disease||o,children:o?"Submitting...":"Submit"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Our representative will contact you soon to discuss your treatment options."})}),(0,t.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-6 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-primary-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})}),(0,t.jsx)("span",{children:"+91-98765-43210"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("svg",{className:"w-4 h-4 text-primary-600",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,t.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,t.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}),(0,t.jsx)("span",{children:"<EMAIL>"})]})]})})]})]})})]})}},12644,e=>{"use strict";e.s(["default",()=>r]);var t=e.i(43476),i=e.i(46932);let r=()=>(0,t.jsx)("section",{className:"py-20 bg-gradient-to-br from-primary-50 via-sage-50 to-mint-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center",children:[(0,t.jsxs)(i.motion.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,t.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["Why ",(0,t.jsx)("span",{className:"text-primary-600",children:"Ayurakshak"})," Center"]}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-8 leading-relaxed",children:"We understand the toll a disease can take on you and your family. The endless cycle of medication and no relief can be overwhelming. It's a journey filled with challenges, and we've seen firsthand how it can impact your life. That's why we're here. Our team of experts is dedicated to turning your health around. Let us help you reclaim your well-being and happiness."}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-6 mb-8",children:[{number:"10,000+",label:"Patients Treated",icon:"👥"},{number:"95%",label:"Success Rate",icon:"📈"},{number:"25+",label:"Treatment Centers",icon:"🏥"},{number:"15+",label:"Years Experience",icon:"⏰"},{number:"50+",label:"Expert Doctors",icon:"👨‍⚕️"},{number:"100%",label:"Natural Treatment",icon:"🌿"}].map((e,r)=>(0,t.jsxs)(i.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*r},viewport:{once:!0},className:"text-center bg-white rounded-xl p-4 shadow-soft hover:shadow-medium transition-shadow",children:[(0,t.jsx)("div",{className:"text-2xl mb-2",children:e.icon}),(0,t.jsx)("div",{className:"text-2xl md:text-3xl font-bold text-primary-600 mb-1",children:e.number}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:e.label})]},e.label))})]}),(0,t.jsx)(i.motion.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-6",children:[{icon:"💰",title:"100% Cashless & Reimbursement Facility",description:"Easy insurance processing and cashless treatment options available"},{icon:"💳",title:"Easy Pay In EMI With 0% Interest Facility",description:"Flexible payment options to make treatment affordable for everyone"},{icon:"😊",title:"100% Patient Satisfaction",description:"Our patients are our priority, ensuring complete satisfaction with treatment"},{icon:"🏥",title:"Network of 25+ Centers PAN India",description:"Widespread network ensuring accessible treatment across the country"},{icon:"👨‍⚕️",title:"50+ Ayurveda Doctors & Therapists",description:"Experienced team of qualified Ayurvedic practitioners and therapists"},{icon:"👥",title:"10,000+ Patients Treated So Far",description:"Proven track record of successful treatments and patient recovery"},{icon:"✅",title:"Success In Chronic Disease Reversal",description:"Specialized in reversing chronic conditions through natural healing"},{icon:"🌿",title:"100% Ayurvedic Treatment – Naturally Healing, Zero Side Effects",description:"Pure traditional Ayurvedic approach with no harmful side effects"}].map((e,r)=>(0,t.jsxs)(i.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*r},viewport:{once:!0},className:"flex items-start space-x-4 bg-white rounded-xl p-6 shadow-soft hover:shadow-medium transition-all duration-300 hover:scale-105",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center text-xl",children:e.icon})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description})]})]},e.title))})]}),(0,t.jsx)(i.motion.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-20 text-center",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 md:p-12",children:[(0,t.jsxs)("h3",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:["Ready to Start Your ",(0,t.jsx)("span",{className:"text-primary-600",children:"Healing Journey?"})]}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-8 max-w-2xl mx-auto",children:"Join thousands of patients who have successfully recovered through our traditional Ayurvedic treatments. Take the first step towards natural healing today."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,t.jsxs)("a",{href:"/contact",className:"inline-flex items-center px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl",children:[(0,t.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})}),"Call Now: +91-98765-43210"]}),(0,t.jsxs)("a",{href:"/contact",className:"inline-flex items-center px-8 py-4 bg-sage-600 hover:bg-sage-700 text-white font-semibold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl",children:[(0,t.jsxs)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,t.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,t.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}),"Book Consultation"]})]}),(0,t.jsxs)("div",{className:"mt-8 flex flex-wrap justify-center items-center gap-8 text-sm text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,t.jsx)("span",{children:"Free Consultation"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,t.jsx)("span",{children:"No Side Effects"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,t.jsx)("span",{children:"100% Natural"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-5 h-5 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,t.jsx)("span",{children:"Proven Results"})]})]})]})})]})})}]);