(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,93510,(e,t,r)=>{"use strict";r._=function(e,t,r){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return r}},80329,(e,t,r)=>{"use strict";r._=function(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}},63340,(e,t,r)=>{"use strict";var n=e.r(80329);r._=function(e,t){n._(e,t),t.add(e)}},27873,(e,t,r)=>{"use strict";r._=function(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}},11071,(e,t,r)=>{"use strict";r._=function(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}},72866,(e,t,r)=>{"use strict";var n,a,o,i=e.r(93510),l=e.r(63340),s=e.r(27873),c=e.r(11071);function u(){let e=c._(["_"],["\\_"]);return u=function(){return e},e}var d=Object.defineProperty,f={};function p(e,t){let r=(null==t?void 0:t.from)?{file:t.from,code:e}:null;"\uFEFF"===e[0]&&(e=" "+e.slice(1));let n=[],a=[],o=[],i=null,l=null,s="",c="",u=0,d;for(let t=0;t<e.length;t++){let p=e.charCodeAt(t);if(13!==p||10!==(d=e.charCodeAt(t+1)))if(92===p)""===s&&(u=t),s+=e.slice(t,t+2),t+=1;else if(47===p&&42===e.charCodeAt(t+1)){let n=t;for(let r=t+2;r<e.length;r++)if(92===(d=e.charCodeAt(r)))r+=1;else if(42===d&&47===e.charCodeAt(r+1)){t=r+1;break}let o=e.slice(n,t+1);if(33===o.charCodeAt(2)){let e=W(o.slice(2,-2));a.push(e),r&&(e.src=[r,n,t+1],e.dst=[r,n,t+1])}}else if(39===p||34===p){let r=v(e,t,p);s+=e.slice(t,r+1),t=r}else{if((32===p||10===p||9===p)&&(d=e.charCodeAt(t+1))&&(32===d||10===d||9===d||13===d&&(d=e.charCodeAt(t+2))&&10==d))continue;if(10===p){if(0===s.length)continue;32!==(d=s.charCodeAt(s.length-1))&&10!==d&&9!==d&&(s+=" ")}else if(45===p&&45===e.charCodeAt(t+1)&&0===s.length){let a="",o=t,l=-1;for(let r=t+2;r<e.length;r++)if(92===(d=e.charCodeAt(r)))r+=1;else if(39===d||34===d)r=v(e,r,d);else if(47===d&&42===e.charCodeAt(r+1)){for(let t=r+2;t<e.length;t++)if(92===(d=e.charCodeAt(t)))t+=1;else if(42===d&&47===e.charCodeAt(t+1)){r=t+1;break}}else if(-1===l&&58===d)l=s.length+r-o;else if(59===d&&0===a.length){s+=e.slice(o,r),t=r;break}else if(40===d)a+=")";else if(91===d)a+="]";else if(123===d)a+="}";else if((125===d||e.length-1===r)&&0===a.length){t=r-1,s+=e.slice(o,r);break}else(41===d||93===d||125===d)&&a.length>0&&e[r]===a[a.length-1]&&(a=a.slice(0,-1));let c=m(s,l);if(!c)throw Error("Invalid custom property, expected a value");r&&(c.src=[r,o,t],c.dst=[r,o,t]),i?i.nodes.push(c):n.push(c),s=""}else if(59===p&&64===s.charCodeAt(0))l=h(s),r&&(l.src=[r,u,t],l.dst=[r,u,t]),i?i.nodes.push(l):n.push(l),s="",l=null;else if(59===p&&")"!==c[c.length-1]){let e=m(s);if(!e){if(0===s.length)continue;throw Error("Invalid declaration: `".concat(s.trim(),"`"))}r&&(e.src=[r,u,t],e.dst=[r,u,t]),i?i.nodes.push(e):n.push(e),s=""}else if(123===p&&")"!==c[c.length-1])c+="}",l=N(s.trim()),r&&(l.src=[r,u,t],l.dst=[r,u,t]),i&&i.nodes.push(l),o.push(i),i=l,s="",l=null;else if(125===p&&")"!==c[c.length-1]){var f;if(""===c)throw Error("Missing opening {");if(c=c.slice(0,-1),s.length>0)if(64===s.charCodeAt(0))l=h(s),r&&(l.src=[r,u,t],l.dst=[r,u,t]),i?i.nodes.push(l):n.push(l),s="",l=null;else{let e=s.indexOf(":");if(i){let n=m(s,e);if(!n)throw Error("Invalid declaration: `".concat(s.trim(),"`"));r&&(n.src=[r,u,t],n.dst=[r,u,t]),i.nodes.push(n)}}let e=null!=(f=o.pop())?f:null;null===e&&i&&n.push(i),i=e,s="",l=null}else if(40===p)c+=")",s+="(";else if(41===p){if(")"!==c[c.length-1])throw Error("Missing opening (");c=c.slice(0,-1),s+=")"}else{if(0===s.length&&(32===p||10===p||9===p))continue;""===s&&(u=t),s+=String.fromCharCode(p)}}}if(64===s.charCodeAt(0)){let t=h(s);r&&(t.src=[r,u,e.length],t.dst=[r,u,e.length]),n.push(t)}if(c.length>0&&i){if("rule"===i.kind)throw Error("Missing closing } at ".concat(i.selector));if("at-rule"===i.kind)throw Error("Missing closing } at ".concat(i.name," ").concat(i.params))}return a.length>0?a.concat(n):n}function h(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=e,n="";for(let t=5;t<e.length;t++){let a=e.charCodeAt(t);if(32===a||40===a){r=e.slice(0,t),n=e.slice(t);break}}return E(r.trim(),n.trim(),t)}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.indexOf(":");if(-1===t)return null;let r=e.indexOf("!important",t+1);return O(e.slice(0,t).trim(),e.slice(t+1,-1===r?e.length:r).trim(),-1!==r)}function v(e,t,r){let n;for(let a=t+1;a<e.length;a++)if(92===(n=e.charCodeAt(a)))a+=1;else{if(n===r)return a;if(59===n&&(10===e.charCodeAt(a+1)||13===e.charCodeAt(a+1)&&10===e.charCodeAt(a+2)))throw Error("Unterminated string: ".concat(e.slice(t,a+1)+String.fromCharCode(r)));if(10===n||13===n&&10===e.charCodeAt(a+1))throw Error("Unterminated string: ".concat(e.slice(t,a)+String.fromCharCode(r)))}return t}function g(e){if(0==arguments.length)throw TypeError("`CSS.escape` requires an argument.");let t=String(e),r=t.length,n=-1,a,o="",i=t.charCodeAt(0);if(1===r&&45===i)return"\\"+t;for(;++n<r;){if(0===(a=t.charCodeAt(n))){o+="�";continue}if(a>=1&&a<=31||127===a||0===n&&a>=48&&a<=57||1===n&&a>=48&&a<=57&&45===i){o+="\\"+a.toString(16)+" ";continue}if(a>=128||45===a||95===a||a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122){o+=t.charAt(n);continue}o+="\\"+t.charAt(n)}return o}function w(e){return e.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,e=>e.length>2?String.fromCodePoint(Number.parseInt(e.slice(1).trim(),16)):e[1])}((e,t)=>{for(var r in t)d(e,r,{get:t[r],enumerable:!0})})(f,{Features:()=>tB,Polyfills:()=>t_,__unstable__loadDesignSystem:()=>tP,compile:()=>tI,compileAst:()=>tM,default:()=>tR});var b=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-decoration-color","--text-decoration-thickness","--text-indent","--text-shadow","--text-underline-offset"]]]);function x(e,t){var r;return(null!=(r=b.get(t))?r:[]).some(t=>e===t||e.startsWith("".concat(t,"-")))}var A=(n=new WeakSet,a=new WeakSet,o=new WeakSet,class{get size(){return this.values.size}add(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3?arguments[3]:void 0;if(e.endsWith("-*")){if("initial"!==t)throw Error("Invalid theme value `".concat(t,"` for namespace `").concat(e,"`"));"--*"===e?this.values.clear():this.clearNamespace(e.slice(0,-2),0)}if(4&r){let t=this.values.get(e);if(t&&!(4&t.options))return}"initial"===t?this.values.delete(e):this.values.set(e,{value:t,options:r,src:n})}keysInNamespaces(e){let t=[];for(let r of e){let e="".concat(r,"-");for(let n of this.values.keys())n.startsWith(e)&&-1===n.indexOf("--",2)&&(x(n,r)||t.push(n.slice(e.length)))}return t}get(e){for(let t of e){let e=this.values.get(t);if(e)return e.value}return null}hasDefault(e){return(4&this.getOptions(e))==4}getOptions(e){var t,r;return e=w(i._(this,n,tq).call(this,e)),null!=(r=null==(t=this.values.get(e))?void 0:t.options)?r:0}entries(){return this.prefix?Array.from(this.values,e=>(e[0]=this.prefixKey(e[0]),e)):this.values.entries()}prefixKey(e){return this.prefix?"--".concat(this.prefix,"-").concat(e.slice(2)):e}clearNamespace(e,t){var r;let n=null!=(r=b.get(e))?r:[];e:for(let r of this.values.keys())if(r.startsWith(e)){if(0!==t&&(this.getOptions(r)&t)!==t)continue;for(let e of n)if(r.startsWith(e))continue e;this.values.delete(r)}}markUsedVariable(e){let t=w(i._(this,n,tq).call(this,e)),r=this.values.get(t);if(!r)return!1;let a=16&r.options;return r.options|=16,!a}resolve(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=i._(this,a,t$).call(this,e,t);if(!n)return null;let l=this.values.get(n);return(r|l.options)&1?l.value:i._(this,o,tH).call(this,n)}resolveValue(e,t){let r=i._(this,a,t$).call(this,e,t);return r?this.values.get(r).value:null}resolveWith(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=i._(this,a,t$).call(this,e,t);if(!n)return null;let l={};for(let e of r){let t="".concat(n).concat(e),r=this.values.get(t);r&&(1&r.options?l[e]=r.value:l[e]=i._(this,o,tH).call(this,t))}let s=this.values.get(n);return 1&s.options?[s.value,l]:[i._(this,o,tH).call(this,n),l]}namespace(e){let t=new Map,r="".concat(e,"-");for(let[n,a]of this.values)n===e?t.set(null,a.value):n.startsWith("".concat(r,"-"))?t.set(n.slice(e.length),a.value):n.startsWith(r)&&t.set(n.slice(r.length),a.value);return t}addKeyframes(e){this.keyframes.add(e)}getKeyframes(){return Array.from(this.keyframes)}constructor(e=new Map,t=new Set([])){l._(this,n),l._(this,a),l._(this,o),s._(this,"prefix",null),this.values=e,this.keyframes=t}}),z=class extends Map{get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e,this),this.set(e,t)),t}constructor(e){super(),this.factory=e}};function T(e){return{kind:"word",value:e}}function j(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;for(let a=0;a<e.length;a++){var n;let o=e[a],i=!1,l=0,s=null!=(n=t(o,{parent:r,replaceWith(t){i||(i=!0,Array.isArray(t)?0===t.length?(e.splice(a,1),l=0):1===t.length?(e[a]=t[0],l=1):(e.splice(a,1,...t),l=t.length):e[a]=t)}}))?n:0;if(i){0===s?a--:a+=l-1;continue}if(2===s||1!==s&&"function"===o.kind&&2===j(o.nodes,t,o))return 2}}function C(e){let t="";for(let r of e)switch(r.kind){case"word":case"separator":t+=r.value;break;case"function":t+=r.value+"("+C(r.nodes)+")"}return t}function K(e){e=e.replaceAll("\r\n","\n");let t=[],r=[],n=null,a="",o;for(let i=0;i<e.length;i++){let l=e.charCodeAt(i);switch(l){case 92:a+=e[i]+e[i+1],i++;break;case 58:case 44:case 61:case 62:case 60:case 10:case 47:case 32:case 9:{if(a.length>0){let e=T(a);n?n.nodes.push(e):t.push(e),a=""}let r=i,l=i+1;for(;l<e.length&&(58===(o=e.charCodeAt(l))||44===o||61===o||62===o||60===o||10===o||47===o||32===o||9===o);l++);i=l-1;let s={kind:"separator",value:e.slice(r,l)};n?n.nodes.push(s):t.push(s);break}case 39:case 34:{let t=i;for(let t=i+1;t<e.length;t++)if(92===(o=e.charCodeAt(t)))t+=1;else if(o===l){i=t;break}a+=e.slice(t,i+1);break}case 40:{let e={kind:"function",value:a,nodes:[]};a="",n?n.nodes.push(e):t.push(e),r.push(e),n=e;break}case 41:{let e=r.pop();if(a.length>0){let t=T(a);null==e||e.nodes.push(t),a=""}n=r.length>0?r[r.length-1]:null;break}default:a+=String.fromCharCode(l)}}return a.length>0&&t.push(T(a)),t}function S(e){let t=[];return j(K(e),e=>{if("function"===e.kind&&"var"===e.value)return j(e.nodes,e=>{"word"!==e.kind||"-"!==e.value[0]||"-"!==e.value[1]||t.push(e.value)}),1}),t}function V(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return{kind:"rule",selector:e,nodes:t}}function E(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return{kind:"at-rule",name:e,params:t,nodes:r}}function N(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return 64===e.charCodeAt(0)?h(e,t):V(e,t)}function O(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return{kind:"declaration",property:e,value:t,important:r}}function W(e){return{kind:"comment",value:e}}function F(e,t){return{kind:"context",context:e,nodes:t}}function _(e){return{kind:"at-root",nodes:e}}function U(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};for(let i=0;i<e.length;i++){var a,o;let l=e[i],s=null!=(a=r[r.length-1])?a:null;if("context"===l.kind){if(2===U(l.nodes,t,r,{...n,...l.context}))return 2;continue}r.push(l);let c=!1,u=0,d=null!=(o=t(l,{parent:s,context:n,path:r,replaceWith(t){c||(c=!0,Array.isArray(t)?0===t.length?(e.splice(i,1),u=0):1===t.length?(e[i]=t[0],u=1):(e.splice(i,1,...t),u=t.length):(e[i]=t,u=1))}}))?o:0;if(r.pop(),c){0===d?i--:i+=u-1;continue}if(2===d)return 2;if(1!==d&&"nodes"in l){r.push(l);let e=U(l.nodes,t,r,n);if(r.pop(),2===e)return 2}}}function D(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,n=[],a=new Set,o=new z(()=>new Set),i=new z(()=>new Set),l=new Set,s=new Set,c=[],u=[],d=new z(()=>new Set),f=[];for(let p of e)!function e(f,p){let h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},m=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;if("declaration"===f.kind){if("--tw-sort"===f.property||void 0===f.value||null===f.value)return;if(h.theme&&"-"===f.property[0]&&"-"===f.property[1]){if("initial"===f.value){f.value=void 0;return}h.keyframes||o.get(p).add(f)}if(f.value.includes("var("))if(h.theme&&"-"===f.property[0]&&"-"===f.property[1])for(let e of S(f.value))d.get(e).add(f.property);else t.trackUsedVariables(f.value);if("animation"===f.property)for(let e of L(f.value))s.add(e);2&r&&f.value.includes("color-mix(")&&i.get(p).add(f),p.push(f)}else if("rule"===f.kind){let t=[];for(let r of f.nodes)e(r,t,h,m+1);let r={},n=new Set;for(let e of t){if("declaration"!==e.kind)continue;let t="".concat(e.property,":").concat(e.value,":").concat(e.important);null!=r[t]||(r[t]=[]),r[t].push(e)}for(let e in r)for(let t=0;t<r[e].length-1;++t)n.add(r[e][t]);if(n.size>0&&(t=t.filter(e=>!n.has(e))),0===t.length)return;"&"===f.selector?p.push(...t):p.push({...f,nodes:t})}else if("at-rule"===f.kind&&"@property"===f.name&&0===m){if(a.has(f.params))return;if(1&r){let e=f.params,t=null,r=!1;for(let e of f.nodes)"declaration"===e.kind&&("initial-value"===e.property?t=e.value:"inherits"===e.property&&(r="true"===e.value));let n=O(e,null!=t?t:"initial");n.src=f.src,r?c.push(n):u.push(n)}a.add(f.params);let t={...f,nodes:[]};for(let r of f.nodes)e(r,t.nodes,h,m+1);p.push(t)}else if("at-rule"===f.kind){"@keyframes"===f.name&&(h={...h,keyframes:!0});let t={...f,nodes:[]};for(let r of f.nodes)e(r,t.nodes,h,m+1);"@keyframes"===f.name&&h.theme&&l.add(t),(t.nodes.length>0||"@layer"===t.name||"@charset"===t.name||"@custom-media"===t.name||"@namespace"===t.name||"@import"===t.name)&&p.push(t)}else if("at-root"===f.kind)for(let t of f.nodes){let r=[];for(let a of(e(t,r,h,0),r))n.push(a)}else if("context"===f.kind){if(f.context.reference)return;for(let t of f.nodes)e(t,p,{...h,...f.context},m)}else"comment"===f.kind&&p.push(f)}(p,f,{},0);e:for(let[e,r]of o)for(let n of r){if(function e(t,r,n){var a;let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:new Set;if(o.has(t)||(o.add(t),24&r.getOptions(t)))return!0;for(let i of null!=(a=n.get(t))?a:[])if(e(i,r,n,o))return!0;return!1}(n.property,t.theme,d)){if(n.property.startsWith(t.theme.prefixKey("--animate-")))for(let e of L(n.value))s.add(e);continue}let r=e.indexOf(n);if(e.splice(r,1),0===e.length){let t=function(e,t){let r=[];return U(e,(e,n)=>{let{path:a}=n;if(t(e))return r=[...a],2}),r}(f,t=>"rule"===t.kind&&t.nodes===e);if(!t||0===t.length)continue e;for(t.unshift({kind:"at-root",nodes:f});;){let e=t.pop();if(!e)break;let r=t[t.length-1];if(!r||"at-root"!==r.kind&&"at-rule"!==r.kind)break;let n=r.nodes.indexOf(e);if(-1===n)break;r.nodes.splice(n,1)}continue e}}for(let e of l)if(!s.has(e.params)){let t=n.indexOf(e);n.splice(t,1)}if(f=f.concat(n),2&r)for(let[e,r]of i)for(let n of r){let r=e.indexOf(n);if(-1===r||null==n.value)continue;let a=K(n.value),o=!1;if(j(a,(e,r)=>{let{replaceWith:n}=r;if("function"!==e.kind||"color-mix"!==e.value)return;let a=!1,i=!1;if(j(e.nodes,(e,r)=>{let{replaceWith:n}=r;if("word"==e.kind&&"currentcolor"===e.value.toLowerCase()){i=!0,o=!0;return}let l=e,s=null,c=new Set;do{if("function"!==l.kind||"var"!==l.value)return;let e=l.nodes[0];if(!e||"word"!==e.kind)return;let r=e.value;if(c.has(r)||(c.add(r),o=!0,!(s=t.theme.resolveValue(null,[e.value])))){a=!0;return}if("currentcolor"===s.toLowerCase()){i=!0;return}l=s.startsWith("var(")?K(s)[0]:null}while(l)n({kind:"word",value:s})}),a||i){let t=e.nodes.findIndex(e=>"separator"===e.kind&&e.value.trim().includes(","));if(-1===t)return;let r=e.nodes.length>t?e.nodes[t+1]:null;if(!r)return;n(r)}else if(o){let t=e.nodes[2];"word"===t.kind&&("oklab"===t.value||"oklch"===t.value||"lab"===t.value||"lch"===t.value)&&(t.value="srgb")}}),!o)continue;let i={...n,value:C(a)},l=N("@supports (color: color-mix(in lab, red, red))",[n]);l.src=n.src,e.splice(r,1,i,l)}if(1&r){let e=[];if(c.length>0){let t=N(":root, :host",c);t.src=c[0].src,e.push(t)}if(u.length>0){let t=N("*, ::before, ::after, ::backdrop",u);t.src=u[0].src,e.push(t)}if(e.length>0){let t=f.findIndex(e=>"comment"!==e.kind&&("at-rule"!==e.kind||"@charset"!==e.name&&"@import"!==e.name)),r=E("@layer","properties",[]);r.src=e[0].src,f.splice(t<0?f.length:t,0,r);let n=N("@layer properties",[E("@supports","((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))",e)]);n.src=e[0].src,n.nodes[0].src=e[0].src,f.push(n)}}return f}function B(e,t){let r=0,n={file:null,code:""},a="";for(let o of e)a+=function e(a){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i="",l="  ".repeat(o);if("declaration"===a.kind){if(i+="".concat(l).concat(a.property,": ").concat(a.value).concat(a.important?" !important":"",";\n"),t){var s,c;let e=r+=l.length;r+=a.property.length,r+=2,r+=null!=(c=null==(s=a.value)?void 0:s.length)?c:0,a.important&&(r+=11);let t=r;r+=2,a.dst=[n,e,t]}}else if("rule"===a.kind){if(i+="".concat(l).concat(a.selector," {\n"),t){let e=r+=l.length;r+=a.selector.length,a.dst=[n,e,r+=1],r+=2}for(let t of a.nodes)i+=e(t,o+1);i+="".concat(l,"}\n"),t&&(r+=l.length,r+=2)}else if("at-rule"===a.kind){if(0===a.nodes.length){let e="".concat(l).concat(a.name," ").concat(a.params,";\n");if(t){let e=r+=l.length;r+=a.name.length,r+=1;let t=r+=a.params.length;r+=2,a.dst=[n,e,t]}return e}if(i+="".concat(l).concat(a.name).concat(a.params?" ".concat(a.params," "):" ","{\n"),t){let e=r+=l.length;r+=a.name.length,a.params&&(r+=1,r+=a.params.length),a.dst=[n,e,r+=1],r+=2}for(let t of a.nodes)i+=e(t,o+1);i+="".concat(l,"}\n"),t&&(r+=l.length,r+=2)}else if("comment"===a.kind){if(i+="".concat(l,"/*").concat(a.value,"*/\n"),t){let e=r+=l.length,t=r+=2+a.value.length+2;a.dst=[n,e,t],r+=1}}else if("context"===a.kind||"at-root"===a.kind)return"";return i}(o,0);return n.code=a,a}function L(e){return e.split(/[\s,]+/)}var M=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"];function I(e){return -1!==e.indexOf("(")&&M.some(t=>e.includes("".concat(t,"(")))}function P(e){if(-1===e.indexOf("("))return R(e);let t=K(e);return function e(t){for(let n of t)switch(n.kind){case"function":if("url"===n.value||n.value.endsWith("_url")){n.value=R(n.value);break}if("var"===n.value||n.value.endsWith("_var")||"theme"===n.value||n.value.endsWith("_theme")){n.value=R(n.value);for(let t=0;t<n.nodes.length;t++){if(0==t&&"word"===n.nodes[t].kind){n.nodes[t].value=R(n.nodes[t].value,!0);continue}e([n.nodes[t]])}break}n.value=R(n.value),e(n.nodes);break;case"separator":case"word":n.value=R(n.value);break;default:var r=n;throw Error("Unexpected value: ".concat(r))}}(t),e=function(e){if(!M.some(t=>e.includes(t)))return e;let t="",r=[],n=null,a=null;for(let o=0;o<e.length;o++){let i=e.charCodeAt(o);if(i>=48&&i<=57||null!==n&&(37===i||i>=97&&i<=122||i>=65&&i<=90)?n=o:(a=n,n=null),40===i){t+=e[o];let n=o;for(let t=o-1;t>=0;t--){let r=e.charCodeAt(t);if(r>=48&&r<=57)n=t;else if(r>=97&&r<=122)n=t;else break}let a=e.slice(n,o);if(M.includes(a)||r[0]&&""===a){r.unshift(!0);continue}r.unshift(!1);continue}if(41===i)t+=e[o],r.shift();else if(44===i&&r[0]){t+=", ";continue}else{if(32===i&&r[0]&&32===t.charCodeAt(t.length-1))continue;if((43===i||42===i||47===i||45===i)&&r[0]){let r=t.trimEnd(),n=r.charCodeAt(r.length-1),i=r.charCodeAt(r.length-2),l=e.charCodeAt(o+1);if((101===n||69===n)&&i>=48&&i<=57){t+=e[o];continue}if(43===n||42===n||47===n||45===n){t+=e[o];continue}if(40===n||44===n){t+=e[o];continue}else 32===e.charCodeAt(o-1)?t+="".concat(e[o]," "):n>=48&&n<=57||l>=48&&l<=57||41===n||40===l||43===l||42===l||47===l||45===l||null!==a&&a===o-1?t+=" ".concat(e[o]," "):t+=e[o]}else t+=e[o]}}return t}(e=C(t))}function R(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r="";for(let n=0;n<e.length;n++){let a=e[n];"\\"===a&&"_"===e[n+1]?(r+="_",n+=1):"_"!==a||t?r+=a:r+=" "}return r}var q=new Uint8Array(256);function $(e){let t=0,r=e.length;for(let n=0;n<r;n++){let a=e.charCodeAt(n);switch(a){case 92:n+=1;break;case 39:case 34:for(;++n<r;){let t=e.charCodeAt(n);if(92===t){n+=1;continue}if(t===a)break}break;case 40:q[t]=41,t++;break;case 91:q[t]=93,t++;break;case 123:break;case 93:case 125:case 41:if(0===t)return!1;t>0&&a===q[t-1]&&t--;break;case 59:if(0===t)return!1}}return!0}var H=new Uint8Array(256);function Z(e,t){let r=0,n=[],a=0,o=e.length,i=t.charCodeAt(0);for(let t=0;t<o;t++){let l=e.charCodeAt(t);if(0===r&&l===i){n.push(e.slice(a,t)),a=t+1;continue}switch(l){case 92:t+=1;break;case 39:case 34:for(;++t<o;){let r=e.charCodeAt(t);if(92===r){t+=1;continue}if(r===l)break}break;case 40:H[r]=41,r++;break;case 91:H[r]=93,r++;break;case 123:H[r]=125,r++;break;case 93:case 125:case 41:r>0&&l===H[r-1]&&r--}}return n.push(e.slice(a)),n}function Y(e){if("["===e[0]&&"]"===e[e.length-1]){let t=P(e.slice(1,-1));return $(t)&&0!==t.length&&0!==t.trim().length?{kind:"arbitrary",value:t}:null}return"("===e[0]&&")"===e[e.length-1]?"-"===(e=e.slice(1,-1))[0]&&"-"===e[1]&&$(e)?{kind:"arbitrary",value:P(e="var(".concat(e,")"))}:null:{kind:"named",value:e}}function*J(e,t){t(e)&&(yield[e,null]);let r=e.lastIndexOf("-");for(;r>0;){let n=e.slice(0,r);if(t(n)){let a=[n,e.slice(r+1)];if(""===a[1]||"@"===a[0]&&t("@")&&"-"===e[r])break;yield a}r=e.lastIndexOf("-",r-1)}"@"===e[0]&&t("@")&&(yield["@",e.slice(1)])}function G(e){var t;if(null===e)return"";let r=(t=e.value,er.get(t)),n=r?e.value.slice(4,-1):e.value,[a,o]=r?["(",")"]:["[","]"];return"arbitrary"===e.kind?"/".concat(a).concat(ee(n)).concat(o):"named"===e.kind?"/".concat(e.value):""}function X(e){var t,r;if("static"===e.kind)return e.root;if("arbitrary"===e.kind){return"[".concat(ee((t=e.selector,et.get(t))),"]")}let n="";if("functional"===e.kind){n+=e.root;let t="@"!==e.root;if(e.value)if("arbitrary"===e.value.kind){let a=(r=e.value.value,er.get(r)),o=a?e.value.value.slice(4,-1):e.value.value,[i,l]=a?["(",")"]:["[","]"];n+="".concat(t?"-":"").concat(i).concat(ee(o)).concat(l)}else"named"===e.value.kind&&(n+="".concat(t?"-":"").concat(e.value.value))}return"compound"===e.kind&&(n+=e.root,n+="-",n+=X(e.variant)),("functional"===e.kind||"compound"===e.kind)&&(n+=G(e.modifier)),n}var Q=new z(e=>{let t=K(e),r=new Set;return j(t,(e,n)=>{var a,o;let{parent:i}=n,l=null===i?t:null!=(a=i.nodes)?a:[];if("word"===e.kind&&("+"===e.value||"-"===e.value||"*"===e.value||"/"===e.value)){let t=null!=(o=l.indexOf(e))?o:-1;if(-1===t)return;let n=l[t-1];if((null==n?void 0:n.kind)!=="separator"||" "!==n.value)return;let a=l[t+1];if((null==a?void 0:a.kind)!=="separator"||" "!==a.value)return;r.add(n),r.add(a)}else"separator"===e.kind&&"/"===e.value.trim()?e.value="/":"separator"===e.kind&&e.value.length>0&&""===e.value.trim()?(l[0]===e||l[l.length-1]===e)&&r.add(e):"separator"===e.kind&&","===e.value.trim()&&(e.value=",")}),r.size>0&&j(t,(e,t)=>{let{replaceWith:n}=t;r.has(e)&&(r.delete(e),n([]))}),function e(t){for(let n of t)switch(n.kind){case"function":if("url"===n.value||n.value.endsWith("_url")){n.value=en(n.value);break}if("var"===n.value||n.value.endsWith("_var")||"theme"===n.value||n.value.endsWith("_theme")){n.value=en(n.value);for(let t=0;t<n.nodes.length;t++)e([n.nodes[t]]);break}n.value=en(n.value),e(n.nodes);break;case"separator":n.value=en(n.value);break;case"word":("-"!==n.value[0]||"-"!==n.value[1])&&(n.value=en(n.value));break;default:var r=n;throw Error("Unexpected value: ".concat(r))}}(t),C(t)});function ee(e){return Q.get(e)}var et=new z(e=>{let t=K(e);return 3===t.length&&"word"===t[0].kind&&"&"===t[0].value&&"separator"===t[1].kind&&":"===t[1].value&&"function"===t[2].kind&&"is"===t[2].value?C(t[2].nodes):e}),er=new z(e=>{let t=K(e);return 1===t.length&&"function"===t[0].kind&&"var"===t[0].value});function en(e){return e.replaceAll("_",String.raw(u())).replaceAll(" ","_")}function ea(e,t,r){if(e===t)return 0;let n=e.indexOf("("),a=t.indexOf("("),o=-1===n?e.replace(/[\d.]+/g,""):e.slice(0,n),i=-1===a?t.replace(/[\d.]+/g,""):t.slice(0,a),l=(o===i?0:o<i?-1:1)||("asc"===r?parseInt(e)-parseInt(t):parseInt(t)-parseInt(e));return Number.isNaN(l)?e<t?-1:1:l}var eo=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),ei=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i,el={color:function(e){return 35===e.charCodeAt(0)||ei.test(e)||eo.has(e.toLowerCase())},length:eb,percentage:eg,ratio:function(e){return ew.test(e)||I(e)},number:em,integer:eA,url:eu,position:function(e){let t=0;for(let r of Z(e," ")){if("center"===r||"top"===r||"right"===r||"bottom"===r||"left"===r){t+=1;continue}if(!r.startsWith("var(")){if(eb(r)||eg(r)){t+=1;continue}return!1}}return t>0},"bg-size":function(e){let t=0;for(let r of Z(e,",")){if("cover"===r||"contain"===r){t+=1;continue}let e=Z(r," ");if(1!==e.length&&2!==e.length)return!1;if(e.every(e=>"auto"===e||eb(e)||eg(e))){t+=1;continue}}return t>0},"line-width":function(e){return Z(e," ").every(e=>eb(e)||em(e)||"thin"===e||"medium"===e||"thick"===e)},image:function(e){let t=0;for(let r of Z(e,","))if(!r.startsWith("var(")){if(eu(r)||ef.test(r)||ed.test(r)){t+=1;continue}return!1}return t>0},"family-name":function(e){let t=0;for(let r of Z(e,",")){let e=r.charCodeAt(0);if(e>=48&&e<=57)return!1;r.startsWith("var(")||(t+=1)}return t>0},"generic-name":function(e){return"serif"===e||"sans-serif"===e||"monospace"===e||"cursive"===e||"fantasy"===e||"system-ui"===e||"ui-serif"===e||"ui-sans-serif"===e||"ui-monospace"===e||"ui-rounded"===e||"math"===e||"emoji"===e||"fangsong"===e},"absolute-size":function(e){return"xx-small"===e||"x-small"===e||"small"===e||"medium"===e||"large"===e||"x-large"===e||"xx-large"===e||"xxx-large"===e},"relative-size":function(e){return"larger"===e||"smaller"===e},angle:function(e){return ey.test(e)},vector:function(e){return ex.test(e)}};function es(e,t){var r;if(e.startsWith("var("))return null;for(let n of t)if(null==(r=el[n])?void 0:r.call(el,e))return n;return null}var ec=/^url\(.*\)$/;function eu(e){return ec.test(e)}var ed=/^(?:element|image|cross-fade|image-set)\(/,ef=/^(repeating-)?(conic|linear|radial)-gradient\(/,ep=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,eh=new RegExp("^".concat(ep.source,"$"));function em(e){return eh.test(e)||I(e)}var ev=new RegExp("^".concat(ep.source,"%$"));function eg(e){return ev.test(e)||I(e)}var ew=new RegExp("^".concat(ep.source,"s*/s*").concat(ep.source,"$")),ek=new RegExp("^".concat(ep.source,"(").concat("cm|mm|Q|in|pc|pt|px|em|ex|ch|rem|lh|rlh|vw|vh|vmin|vmax|vb|vi|svw|svh|lvw|lvh|dvw|dvh|cqw|cqh|cqi|cqb|cqmin|cqmax",")$"));function eb(e){return ek.test(e)||I(e)}var ey=new RegExp("^".concat(ep.source,"(").concat("deg|rad|grad|turn",")$")),ex=new RegExp("^".concat(ep.source," +").concat(ep.source," +").concat(ep.source,"$"));function eA(e){let t=Number(e);return Number.isInteger(t)&&t>=0&&String(t)===String(e)}function ez(e){let t=Number(e);return Number.isInteger(t)&&t>0&&String(t)===String(e)}function eT(e){return eC(e,.25)}function ej(e){return eC(e,.25)}function eC(e,t){let r=Number(e);return r>=0&&r%t==0&&String(r)===String(e)}var eK=new Set(["inset","inherit","initial","revert","unset"]),eS=/^-?(\d+|\.\d+)(.*?)$/g;function eV(e,t){return Z(e,",").map(e=>{let r=Z(e=e.trim()," ").filter(e=>""!==e.trim()),n=null,a=null,o=null;for(let e of r)eK.has(e)||(eS.test(e)?(null===a?a=e:null===o&&(o=e),eS.lastIndex=0):null===n&&(n=e));if(null===a||null===o)return e;let i=t(null!=n?n:"currentcolor");return null!==n?e.replace(n,i):"".concat(e," ").concat(i)}).join(", ")}var eE=/^-?[a-z][a-zA-Z0-9/%._-]*$/,eN=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,eO=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],eW=class{static(e,t){this.utilities.get(e).push({kind:"static",compileFn:t})}functional(e,t,r){this.utilities.get(e).push({kind:"functional",compileFn:t,options:r})}has(e,t){return this.utilities.has(e)&&this.utilities.get(e).some(e=>e.kind===t)}get(e){return this.utilities.has(e)?this.utilities.get(e):[]}getCompletions(e){var t,r;return null!=(r=null==(t=this.completions.get(e))?void 0:t())?r:[]}suggest(e,t){this.completions.set(e,t)}keys(e){let t=[];for(let[r,n]of this.utilities.entries())for(let a of n)if(a.kind===e){t.push(r);break}return t}constructor(){s._(this,"utilities",new z(()=>[])),s._(this,"completions",new Map)}};function eF(e,t,r){return E("@property",e,[O("syntax",r?'"'.concat(r,'"'):'"*"'),O("inherits","false"),...t?[O("initial-value",t)]:[]])}function e_(e,t){if(null===t)return e;let r=Number(t);return Number.isNaN(r)||(t="".concat(100*r,"%")),"100%"===t?e:"color-mix(in oklab, ".concat(e," ").concat(t,", transparent)")}function eU(e,t){let r=Number(t);return Number.isNaN(r)||(t="".concat(100*r,"%")),"oklab(from ".concat(e," l a b / ").concat(t,")")}function eD(e,t,r){if(!t)return e;if("arbitrary"===t.kind)return e_(e,t.value);let n=r.resolve(t.value,["--opacity"]);return n?e_(e,n):ej(t.value)?e_(e,"".concat(t.value,"%")):null}function eB(e,t,r){let n=null;switch(e.value.value){case"inherit":n="inherit";break;case"transparent":n="transparent";break;case"current":n="currentcolor";break;default:n=t.resolve(e.value.value,r)}return n?eD(n,e.modifier,t):null}var eL=/(\d+)_(\d+)/g,eM=["number","integer","ratio","percentage"];function eI(e,t,r){for(let n of t.nodes){if("named"===e.kind&&"word"===n.kind&&("'"===n.value[0]||'"'===n.value[0])&&n.value[n.value.length-1]===n.value[0]&&n.value.slice(1,-1)===e.value)return{nodes:K(e.value)};if("named"===e.kind&&"word"===n.kind&&"-"===n.value[0]&&"-"===n.value[1]){let t=n.value;if(t.endsWith("-*")){t=t.slice(0,-2);let n=r.theme.resolve(e.value,[t]);if(n)return{nodes:K(n)}}else{let n=t.split("-*");if(n.length<=1)continue;let a=[n.shift()],o=r.theme.resolveWith(e.value,a,n);if(o){let[,e={}]=o;{let t=e[n.pop()];if(t)return{nodes:K(t)}}}}}else if("named"===e.kind&&"word"===n.kind){if(!eM.includes(n.value))continue;let t="ratio"===n.value&&"fraction"in e?e.fraction:e.value;if(!t)continue;let r=es(t,[n.value]);if(null===r)continue;if("ratio"===r){let[e,r]=Z(t,"/");if(!eA(e)||!eA(r))continue}else if("number"===r&&!eT(t)||"percentage"===r&&!eA(t.slice(0,-1)))continue;return{nodes:K(t),ratio:"ratio"===r}}else if("arbitrary"===e.kind&&"word"===n.kind&&"["===n.value[0]&&"]"===n.value[n.value.length-1]){let t=n.value.slice(1,-1);if("*"===t)return{nodes:K(e.value)};if("dataType"in e&&e.dataType&&e.dataType!==t)continue;if("dataType"in e&&e.dataType||null!==es(e.value,[t]))return{nodes:K(e.value)}}}}function eP(e,t,r,n){let a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",o=!1,i=eV(t,e=>null==r?n(e):e.startsWith("current")?n(e_(e,r)):((e.startsWith("var(")||r.startsWith("var("))&&(o=!0),n(eU(e,r))));function l(e){return a?Z(e,",").map(e=>a+e).join(","):e}return o?[O(e,l(eV(t,n))),N("@supports (color: lab(from red l a b))",[O(e,l(i))])]:[O(e,l(i))]}function eR(e,t,r,n){let a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",o=!1,i=Z(t,",").map(e=>eV(e,e=>null==r?n(e):e.startsWith("current")?n(e_(e,r)):((e.startsWith("var(")||r.startsWith("var("))&&(o=!0),n(eU(e,r))))).map(e=>"drop-shadow(".concat(e,")")).join(" ");return o?[O(e,a+Z(t,",").map(e=>"drop-shadow(".concat(eV(e,n),")")).join(" ")),N("@supports (color: lab(from red l a b))",[O(e,a+i)])]:[O(e,a+i)]}var eq={"--alpha":function(e,t,r){for(var n=arguments.length,a=Array(n>3?n-3:0),o=3;o<n;o++)a[o-3]=arguments[o];let[i,l]=Z(r,"/").map(e=>e.trim());if(!i||!l)throw Error("The --alpha(…) function requires a color and an alpha value, e.g.: `--alpha(".concat(i||"var(--my-color)"," / ").concat(l||"50%",")`"));if(a.length>0)throw Error("The --alpha(…) function only accepts one argument, e.g.: `--alpha(".concat(i||"var(--my-color)"," / ").concat(l||"50%",")`"));return e_(i,l)},"--spacing":function(e,t,r){for(var n=arguments.length,a=Array(n>3?n-3:0),o=3;o<n;o++)a[o-3]=arguments[o];if(!r)throw Error("The --spacing(…) function requires an argument, but received none.");if(a.length>0)throw Error("The --spacing(…) function only accepts a single argument, but received ".concat(a.length+1,"."));let i=e.theme.resolve(null,["--spacing"]);if(!i)throw Error("The --spacing(…) function requires that the `--spacing` theme variable exists, but it was not found.");return"calc(".concat(i," * ").concat(r,")")},"--theme":function(e,t,r){for(var n,a,o=arguments.length,i=Array(o>3?o-3:0),l=3;l<o;l++)i[l-3]=arguments[l];if(!r.startsWith("--"))throw Error("The --theme(…) function can only be used with CSS variables from your theme.");let s=!1;r.endsWith(" inline")&&(s=!0,r=r.slice(0,-7)),"at-rule"===t.kind&&(s=!0);let c=e.resolveThemeValue(r,s);if(!c){if(i.length>0)return i.join(", ");throw Error("Could not resolve value for theme function: `theme(".concat(r,")`. Consider checking if the variable name is correct or provide a fallback value to silence this error."))}if(0===i.length)return c;let u=i.join(", ");if("initial"===u)return c;if("initial"===c)return u;if(c.startsWith("var(")||c.startsWith("theme(")||c.startsWith("--theme(")){let e=K(c);return n=e,a=u,j(n,e=>{if("function"===e.kind&&("var"===e.value||"theme"===e.value||"--theme"===e.value))if(1===e.nodes.length)e.nodes.push({kind:"word",value:", ".concat(a)});else{let t=e.nodes[e.nodes.length-1];"word"===t.kind&&"initial"===t.value&&(t.value=a)}}),C(e)}return c},theme:function(e,t,r){for(var n=arguments.length,a=Array(n>3?n-3:0),o=3;o<n;o++)a[o-3]=arguments[o];r=function(e){if("'"!==e[0]&&'"'!==e[0])return e;let t="",r=e[0];for(let n=1;n<e.length-1;n++){let a=e[n],o=e[n+1];"\\"===a&&(o===r||"\\"===o)?(t+=o,n++):t+=a}return t}(r);let i=e.resolveThemeValue(r);if(!i&&a.length>0)return a.join(", ");if(!i)throw Error("Could not resolve value for theme function: `theme(".concat(r,")`. Consider checking if the path is correct or provide a fallback value to silence this error."));return i}},e$=new RegExp(Object.keys(eq).map(e=>"".concat(e,"\\(")).join("|"));function eH(e,t){let r=0;return U(e,e=>{if("declaration"===e.kind&&e.value&&e$.test(e.value)){r|=8,e.value=eZ(e.value,e,t);return}"at-rule"===e.kind&&("@media"===e.name||"@custom-media"===e.name||"@container"===e.name||"@supports"===e.name)&&e$.test(e.params)&&(r|=8,e.params=eZ(e.params,e,t))}),r}function eZ(e,t,r){let n=K(e);return j(n,(e,n)=>{let{replaceWith:a}=n;if("function"===e.kind&&e.value in eq){let n=Z(C(e.nodes).trim(),",").map(e=>e.trim());return a(K(eq[e.value](r,t,...n)))}}),C(n)}function eY(e,t){let r=e.length,n=t.length,a=r<n?r:n;for(let r=0;r<a;r++){let n=e.charCodeAt(r),a=t.charCodeAt(r);if(n>=48&&n<=57&&a>=48&&a<=57){let o=r,i=r+1,l=r,s=r+1;for(n=e.charCodeAt(i);n>=48&&n<=57;)n=e.charCodeAt(++i);for(a=t.charCodeAt(s);a>=48&&a<=57;)a=t.charCodeAt(++s);let c=e.slice(o,i),u=t.slice(l,s),d=Number(c)-Number(u);if(d)return d;if(c<u)return -1;if(c>u)return 1;continue}if(n!==a)return n-a}return e.length-t.length}var eJ=/^\d+\/\d+$/,eG=RegExp("^@?[a-z0-9][a-zA-Z0-9_-]*(?<![_-])$"),eX=class{static(e,t){let{compounds:r,order:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.set(e,{kind:"static",applyFn:t,compoundsWith:0,compounds:null!=r?r:2,order:n})}fromAst(e,t){let r=[];U(t,e=>{"rule"===e.kind?r.push(e.selector):"at-rule"===e.kind&&"@slot"!==e.name&&r.push("".concat(e.name," ").concat(e.params))}),this.static(e,e=>{let r=structuredClone(t);e1(r,e.nodes),e.nodes=r},{compounds:eQ(r)})}functional(e,t){let{compounds:r,order:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.set(e,{kind:"functional",applyFn:t,compoundsWith:0,compounds:null!=r?r:2,order:n})}compound(e,t,r){let{compounds:n,order:a}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};this.set(e,{kind:"compound",applyFn:r,compoundsWith:t,compounds:null!=n?n:2,order:a})}group(e,t){this.groupOrder=this.nextOrder(),t&&this.compareFns.set(this.groupOrder,t),e(),this.groupOrder=null}has(e){return this.variants.has(e)}get(e){return this.variants.get(e)}kind(e){var t;return null==(t=this.variants.get(e))?void 0:t.kind}compoundsWith(e,t){let r=this.variants.get(e),n="string"==typeof t?this.variants.get(t):"arbitrary"===t.kind?{compounds:eQ([t.selector])}:this.variants.get(t.root);return!(!r||!n||"compound"!==r.kind||0===n.compounds||0===r.compoundsWith||(r.compoundsWith&n.compounds)==0)}suggest(e,t){this.completions.set(e,t)}getCompletions(e){var t,r;return null!=(r=null==(t=this.completions.get(e))?void 0:t())?r:[]}compare(e,t){if(e===t)return 0;if(null===e)return -1;if(null===t)return 1;if("arbitrary"===e.kind&&"arbitrary"===t.kind)return e.selector<t.selector?-1:1;if("arbitrary"===e.kind)return 1;if("arbitrary"===t.kind)return -1;let r=this.variants.get(e.root).order,n=r-this.variants.get(t.root).order;if(0!==n)return n;if("compound"===e.kind&&"compound"===t.kind){let r=this.compare(e.variant,t.variant);return 0!==r?r:e.modifier&&t.modifier?e.modifier.value<t.modifier.value?-1:1:e.modifier?1:t.modifier?-1:0}let a=this.compareFns.get(r);if(void 0!==a)return a(e,t);if(e.root!==t.root)return e.root<t.root?-1:1;let o=e.value,i=t.value;return null===o?-1:null===i||"arbitrary"===o.kind&&"arbitrary"!==i.kind?1:"arbitrary"!==o.kind&&"arbitrary"===i.kind||o.value<i.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(e,t){let{kind:r,applyFn:n,compounds:a,compoundsWith:o,order:i}=t,l=this.variants.get(e);l?Object.assign(l,{kind:r,applyFn:n,compounds:a}):(void 0===i&&(this.lastOrder=this.nextOrder(),i=this.lastOrder),this.variants.set(e,{kind:r,applyFn:n,order:i,compoundsWith:o,compounds:a}))}nextOrder(){var e;return null!=(e=this.groupOrder)?e:this.lastOrder+1}constructor(){s._(this,"compareFns",new Map),s._(this,"variants",new Map),s._(this,"completions",new Map),s._(this,"groupOrder",null),s._(this,"lastOrder",0)}};function eQ(e){let t=0;for(let r of e){if("@"===r[0]){if(!r.startsWith("@media")&&!r.startsWith("@supports")&&!r.startsWith("@container"))return 0;t|=1;continue}if(r.includes("::"))return 0;t|=2}return t}function e0(e){if(e.includes("=")){let[t,...r]=Z(e,"="),n=r.join("=").trim();if("'"===n[0]||'"'===n[0])return e;if(n.length>1){let e=n[n.length-1];if(" "===n[n.length-2]&&("i"===e||"I"===e||"s"===e||"S"===e))return"".concat(t,'="').concat(n.slice(0,-2),'" ').concat(e)}return"".concat(t,'="').concat(n,'"')}return e}function e1(e,t){U(e,(e,r)=>{let{replaceWith:n}=r;if("at-rule"===e.kind&&"@slot"===e.name)n(t);else if("at-rule"===e.kind&&("@keyframes"===e.name||"@property"===e.name))return Object.assign(e,_([E(e.name,e.params,e.nodes)])),1})}var e2=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","mask-image","--tw-mask-top","--tw-mask-top-from-color","--tw-mask-top-from-position","--tw-mask-top-to-color","--tw-mask-top-to-position","--tw-mask-right","--tw-mask-right-from-color","--tw-mask-right-from-position","--tw-mask-right-to-color","--tw-mask-right-to-position","--tw-mask-bottom","--tw-mask-bottom-from-color","--tw-mask-bottom-from-position","--tw-mask-bottom-to-color","--tw-mask-bottom-to-position","--tw-mask-left","--tw-mask-left-from-color","--tw-mask-left-from-position","--tw-mask-left-to-color","--tw-mask-left-to-position","--tw-mask-linear","--tw-mask-linear-position","--tw-mask-linear-from-color","--tw-mask-linear-from-position","--tw-mask-linear-to-color","--tw-mask-linear-to-position","--tw-mask-radial","--tw-mask-radial-shape","--tw-mask-radial-size","--tw-mask-radial-position","--tw-mask-radial-from-color","--tw-mask-radial-from-position","--tw-mask-radial-to-color","--tw-mask-radial-to-position","--tw-mask-conic","--tw-mask-conic-position","--tw-mask-conic-from-color","--tw-mask-conic-from-position","--tw-mask-conic-to-color","--tw-mask-conic-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","mask-composite","mask-mode","mask-type","mask-size","mask-clip","mask-position","mask-repeat","mask-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function e5(e,t){let{onInvalidCandidate:r,respectImportant:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=new Map,o=[],i=new Map;for(let n of e){if(t.invalidCandidates.has(n)){null==r||r(n);continue}let e=t.parseCandidate(n);if(0===e.length){null==r||r(n);continue}i.set(n,e)}let l=0;(null==n||n)&&(l|=1);let s=t.getVariantOrder();for(let[e,n]of i){let i=!1;for(let r of n){let n=t.compileAstNodes(r,l);if(0!==n.length)for(let{node:t,propertySort:l}of(i=!0,n)){let n=0n;for(let e of r.variants)n|=1n<<BigInt(s.get(e));a.set(t,{properties:l,variants:n,candidate:e}),o.push(t)}}i||null==r||r(e)}return o.sort((e,t)=>{var r,n;let o=a.get(e),i=a.get(t);if(o.variants-i.variants!==0n)return Number(o.variants-i.variants);let l=0;for(;l<o.properties.order.length&&l<i.properties.order.length&&o.properties.order[l]===i.properties.order[l];)l+=1;return(null!=(r=o.properties.order[l])?r:1/0)-(null!=(n=i.properties.order[l])?n:1/0)||i.properties.count-o.properties.count||eY(o.candidate,i.candidate)}),{astNodes:o,nodeSorting:a}}function e3(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;if("arbitrary"===t.kind)return t.relative&&0===n?null:void(e.nodes=[N(t.selector,e.nodes)]);let{applyFn:a}=r.get(t.root);if("compound"===t.kind){let o=E("@slot");if(null===e3(o,t.variant,r,n+1)||"not"===t.root&&o.nodes.length>1)return null;for(let e of o.nodes)if("rule"!==e.kind&&"at-rule"!==e.kind||null===a(e,t))return null;return U(o.nodes,t=>{if(("rule"===t.kind||"at-rule"===t.kind)&&t.nodes.length<=0)return t.nodes=e.nodes,1}),void(e.nodes=o.nodes)}if(null===a(e,t))return null}function e6(e){var t,r;let n=null!=(r=null==(t=e.options)?void 0:t.types)?r:[];return n.length>1&&n.includes("any")}function e4(e,t){let r=0,n=N("&",e),a=new Set,o=new z(()=>new Set),i=new z(()=>new Set);U([n],(e,n)=>{let{parent:l,path:s}=n;if("at-rule"===e.kind){if("@keyframes"===e.name)return U(e.nodes,e=>{if("at-rule"===e.kind&&"@apply"===e.name)throw Error("You cannot use `@apply` inside `@keyframes`.")}),1;if("@utility"===e.name){let r=e.params.replace(/-\*$/,"");i.get(r).add(e),U(e.nodes,r=>{if("at-rule"===r.kind&&"@apply"===r.name)for(let n of(a.add(e),e9(r,t)))o.get(e).add(n)});return}if("@apply"===e.name){if(null===l)return;for(let n of(r|=1,a.add(l),e9(e,t)))for(let t of s)t!==e&&a.has(t)&&o.get(t).add(n)}}});let l=new Set,s=[],c=new Set;for(let e of a)!function e(r){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!l.has(r)){if(c.has(r)){let e=n[(n.indexOf(r)+1)%n.length];throw"at-rule"===r.kind&&"@utility"===r.name&&"at-rule"===e.kind&&"@utility"===e.name&&U(r.nodes,r=>{if("at-rule"===r.kind&&"@apply"===r.name)for(let n of r.params.split(/\s+/g))for(let r of t.parseCandidate(n))switch(r.kind){case"arbitrary":break;case"static":case"functional":if(e.params.replace(/-\*$/,"")===r.root)throw Error("You cannot `@apply` the `".concat(n,"` utility here because it creates a circular dependency."))}}),Error("Circular dependency detected:\n\n".concat(B([r]),"\nRelies on:\n\n").concat(B([e])))}for(let t of(c.add(r),o.get(r)))for(let a of i.get(t))n.push(r),e(a,n),n.pop();l.add(r),c.delete(r),s.push(r)}}(e);for(let e of s)"nodes"in e&&U(e.nodes,(e,r)=>{let{replaceWith:n}=r;if("at-rule"!==e.kind||"@apply"!==e.name)return;let a=e.params.split(/(\s+)/g),o={},i=0;for(let[e,t]of a.entries())e%2==0&&(o[t]=i),i+=t.length;{let r=e5(Object.keys(o),t,{respectImportant:!1,onInvalidCandidate:e=>{if(t.theme.prefix&&!e.startsWith(t.theme.prefix))throw Error("Cannot apply unprefixed utility class `".concat(e,"`. Did you mean `").concat(t.theme.prefix,":").concat(e,"`?"));if(t.invalidCandidates.has(e))throw Error("Cannot apply utility class `".concat(e,"` because it has been explicitly disabled: https://tailwindcss.com/docs/detecting-classes-in-source-files#explicitly-excluding-classes"));let r=Z(e,":");if(r.length>1){let n=r.pop();if(t.candidatesToCss([n])[0]){let n=t.candidatesToCss(r.map(e=>"".concat(e,":[--tw-variant-check:1]"))),a=r.filter((e,t)=>null===n[t]);if(a.length>0){if(1===a.length)throw Error("Cannot apply utility class `".concat(e,"` because the ").concat(a.map(e=>"`".concat(e,"`"))," variant does not exist."));{let t=new Intl.ListFormat("en",{style:"long",type:"conjunction"});throw Error("Cannot apply utility class `".concat(e,"` because the ").concat(t.format(a.map(e=>"`".concat(e,"`")))," variants do not exist."))}}}}throw 0===t.theme.size?Error("Cannot apply unknown utility class `".concat(e,"`. Are you using CSS modules or similar and missing `@reference`? https://tailwindcss.com/docs/functions-and-directives#reference-directive")):Error("Cannot apply unknown utility class `".concat(e,"`"))}}),a=e.src,i=r.astNodes.map(e=>{var t;let n=null==(t=r.nodeSorting.get(e))?void 0:t.candidate,i=n?o[n]:void 0;if(e=structuredClone(e),!a||!n||void 0===i)return U([e],e=>{e.src=a}),e;let l=[a[0],a[1],a[2]];return l[1]+=7+i,l[2]=l[1]+n.length,U([e],e=>{e.src=l}),e}),l=[];for(let e of i)if("rule"===e.kind)for(let t of e.nodes)l.push(t);else l.push(e);n(l)}});return r}function*e9(e,t){for(let r of e.params.split(/\s+/g))for(let e of t.parseCandidate(r))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root}}async function e7(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=0,i=[];return U(e,(e,l)=>{let{replaceWith:s}=l;if("at-rule"===e.kind&&("@import"===e.name||"@reference"===e.name)){let l=function(e){let t,r=null,n=null,a=null;for(let o=0;o<e.length;o++){let i=e[o];if("separator"!==i.kind){if("word"===i.kind&&!t){if(!i.value||'"'!==i.value[0]&&"'"!==i.value[0])return null;t=i.value.slice(1,-1);continue}if("function"===i.kind&&"url"===i.value.toLowerCase()||!t)return null;if(("word"===i.kind||"function"===i.kind)&&"layer"===i.value.toLowerCase()){if(r)return null;if(a)throw Error("`layer(…)` in an `@import` should come before any other functions or conditions");r="nodes"in i?C(i.nodes):"";continue}if("function"===i.kind&&"supports"===i.value.toLowerCase()){if(a)return null;a=C(i.nodes);continue}n=C(e.slice(o));break}}return t?{uri:t,layer:r,media:n,supports:a}:null}(K(e.params));if(null===l)return;"@reference"===e.name&&(l.media="reference"),o|=2;let{uri:c,layer:u,media:d,supports:f}=l;if(c.startsWith("data:")||c.startsWith("http://")||c.startsWith("https://"))return;let h=F({},[]);return i.push((async()=>{if(n>100)throw Error("Exceeded maximum recursion depth while resolving `".concat(c,"` in `").concat(t,"`)"));let o=await r(c,t),i=p(o.content,{from:a?o.path:void 0});await e7(i,o.base,r,n+1,a),h.nodes=function(e,t,r,n,a){let o=t;if(null!==r){let t=E("@layer",r,o);t.src=e.src,o=[t]}if(null!==n){let t=E("@media",n,o);t.src=e.src,o=[t]}if(null!==a){let t=E("@supports","("===a[0]?a:"(".concat(a,")"),o);t.src=e.src,o=[t]}return o}(e,[F({base:o.base},i)],u,d,f)})()),s(h),1}}),i.length>0&&await Promise.all(i),o}function e8(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return Array.isArray(e)&&2===e.length&&"object"==typeof e[1]&&(e[1],1)?r?null!=(t=e[1][r])?t:null:e[0]:Array.isArray(e)&&null===r?e.join(", "):"string"==typeof e&&null===r?e:null}var te=/^[a-zA-Z0-9-_%/\.]+$/;function tt(e){if("container"===e[0])return null;for(let t of("animation"===(e=structuredClone(e))[0]&&(e[0]="animate"),"aspectRatio"===e[0]&&(e[0]="aspect"),"borderRadius"===e[0]&&(e[0]="radius"),"boxShadow"===e[0]&&(e[0]="shadow"),"colors"===e[0]&&(e[0]="color"),"containers"===e[0]&&(e[0]="container"),"fontFamily"===e[0]&&(e[0]="font"),"fontSize"===e[0]&&(e[0]="text"),"letterSpacing"===e[0]&&(e[0]="tracking"),"lineHeight"===e[0]&&(e[0]="leading"),"maxWidth"===e[0]&&(e[0]="container"),"screens"===e[0]&&(e[0]="breakpoint"),"transitionTimingFunction"===e[0]&&(e[0]="ease"),e))if(!te.test(t))return null;return e.map((e,t,r)=>"1"===e&&t!==r.length-1?"":e).map(e=>e.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(e,t,r)=>"".concat(t,"-").concat(r.toLowerCase()))).filter((t,r)=>"DEFAULT"!==t||r!==e.length-1).join("-")}function tr(e){let t=[];for(let r of Z(e,".")){if(!r.includes("[")){t.push(r);continue}let e=0;for(;;){let n=r.indexOf("[",e),a=r.indexOf("]",n);if(-1===n||-1===a)break;n>e&&t.push(r.slice(e,n)),t.push(r.slice(n+1,a)),e=a+1}e<=r.length-1&&t.push(r.slice(e))}return t}function tn(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}function ta(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];for(let a of t)if(null!=a)for(let t of Reflect.ownKeys(a)){n.push(t);let o=r(e[t],a[t],n);void 0!==o?e[t]=o:tn(e[t])&&tn(a[t])?e[t]=ta({},[e[t],a[t]],r,n):e[t]=a[t],n.pop()}return e}function to(e,t,r){return function(n,a){let o=n.lastIndexOf("/"),i=null;-1!==o&&(i=n.slice(o+1).trim(),n=n.slice(0,o).trim());let l=(()=>{var a,o,i,l,s;let c=tr(n),[u,d]=function(e,t){var r,n,a;if(1===t.length&&t[0].startsWith("--"))return[e.get([t[0]]),e.getOptions(t[0])];let o=tt(t),i=new Map,l=new z(()=>new Map),s=e.namespace("--".concat(o));if(0===s.size)return[null,0];let c=new Map;for(let[t,r]of s){if(!t||!t.includes("--")){i.set(t,r),c.set(t,e.getOptions(t?"--".concat(o,"-").concat(t):"--".concat(o)));continue}let n=t.indexOf("--"),a=t.slice(0,n),s=t.slice(n+2);s=s.replace(/-([a-z])/g,(e,t)=>t.toUpperCase()),l.get(""===a?null:a).set(s,[r,e.getOptions("--".concat(o).concat(t))])}let u=e.getOptions("--".concat(o));for(let[e,t]of l){let r=i.get(e);if("string"!=typeof r)continue;let n={},a={};for(let[e,[r,o]]of t)n[e]=r,a[e]=o;i.set(e,[r,n]),c.set(e,[u,a])}let d={},f={};for(let[e,t]of i)tl(d,[null!=e?e:"DEFAULT"],t);for(let[e,t]of c)tl(f,[null!=e?e:"DEFAULT"],t);return"DEFAULT"===t[t.length-1]?[null!=(r=null==d?void 0:d.DEFAULT)?r:null,null!=(n=f.DEFAULT)?n:0]:"DEFAULT"in d&&1===Object.keys(d).length?[d.DEFAULT,null!=(a=f.DEFAULT)?a:0]:(d.__CSS_VALUES__=f,[d,f])}(e.theme,c),f=r(null!=(o=ti(null!=(a=t())?a:{},c))?o:null);if("string"==typeof f&&(f=f.replace("<alpha-value>","1")),"object"!=typeof u)return"object"!=typeof d&&4&d&&null!=f?f:u;if(null!==f&&"object"==typeof f&&!Array.isArray(f)){let e=ta({},[f],(e,t)=>t);if(null===u&&Object.hasOwn(f,"__CSS_VALUES__")){let t={};for(let r in f.__CSS_VALUES__)t[r]=f[r],delete e[r];u=t}for(let t in u)"__CSS_VALUES__"!==t&&((null==f||null==(i=f.__CSS_VALUES__)?void 0:i[t])&4&&void 0!==ti(e,t.split("-"))||(e[w(t)]=u[t]));return e}if(Array.isArray(u)&&Array.isArray(d)&&Array.isArray(f)){let e=u[0],t=u[1];for(let r of(4&d[0]&&(e=null!=(l=f[0])?l:e),Object.keys(t)))4&d[1][r]&&(t[r]=null!=(s=f[1][r])?s:t[r]);return[e,t]}return null!=u?u:f})();return i&&"string"==typeof l&&(l=e_(l,i)),null!=l?l:a}}function ti(e,t){for(let r=0;r<t.length;++r){let n=t[r];if((null==e?void 0:e[n])===void 0){if(void 0===t[r+1])return;t[r+1]="".concat(n,"-").concat(t[r+1]);continue}e=e[n]}return e}function tl(e,t,r){for(let r of t.slice(0,-1))void 0===e[r]&&(e[r]={}),e=e[r];e[t[t.length-1]]=r}function ts(e){return{kind:"selector",value:e}}function tc(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;for(let a=0;a<e.length;a++){var n;let o=e[a],i=!1,l=0,s=null!=(n=t(o,{parent:r,replaceWith(t){i||(i=!0,Array.isArray(t)?0===t.length?(e.splice(a,1),l=0):1===t.length?(e[a]=t[0],l=1):(e.splice(a,1,...t),l=t.length):(e[a]=t,l=1))}}))?n:0;if(i){0===s?a--:a+=l-1;continue}if(2===s||1!==s&&"function"===o.kind&&2===tc(o.nodes,t,o))return 2}}function tu(e){let t="";for(let r of e)switch(r.kind){case"combinator":case"selector":case"separator":case"value":t+=r.value;break;case"function":t+=r.value+"("+tu(r.nodes)+")"}return t}function td(e){e=e.replaceAll("\r\n","\n");let t=[],r=[],n=null,a="",o;for(let i=0;i<e.length;i++){let l=e.charCodeAt(i);switch(l){case 44:case 62:case 10:case 32:case 43:case 9:case 126:{if(a.length>0){let e=ts(a);n?n.nodes.push(e):t.push(e),a=""}let r=i,l=i+1;for(;l<e.length&&(44===(o=e.charCodeAt(l))||62===o||10===o||32===o||43===o||9===o||126===o);l++);i=l-1;let s=e.slice(r,l),c=","===s.trim()?{kind:"separator",value:s}:{kind:"combinator",value:s};n?n.nodes.push(c):t.push(c);break}case 40:{let l={kind:"function",value:a,nodes:[]};if(a="",":not"!==l.value&&":where"!==l.value&&":has"!==l.value&&":is"!==l.value){let r=i+1,s=0;for(let t=i+1;t<e.length;t++){if(40===(o=e.charCodeAt(t))){s++;continue}if(41===o){if(0===s){i=t;break}s--}}let c=i;l.nodes.push({kind:"value",value:e.slice(r,c)}),a="",i=c,n?n.nodes.push(l):t.push(l);break}n?n.nodes.push(l):t.push(l),r.push(l),n=l;break}case 41:{let e=r.pop();if(a.length>0){let t=ts(a);e.nodes.push(t),a=""}n=r.length>0?r[r.length-1]:null;break}case 46:case 58:case 35:if(a.length>0){let e=ts(a);n?n.nodes.push(e):t.push(e)}a=String.fromCharCode(l);break;case 91:{if(a.length>0){let e=ts(a);n?n.nodes.push(e):t.push(e)}a="";let r=i,l=0;for(let t=i+1;t<e.length;t++){if(91===(o=e.charCodeAt(t))){l++;continue}if(93===o){if(0===l){i=t;break}l--}}a+=e.slice(r,i+1);break}case 39:case 34:{let t=i;for(let t=i+1;t<e.length;t++)if(92===(o=e.charCodeAt(t)))t+=1;else if(o===l){i=t;break}a+=e.slice(t,i+1);break}case 92:{let t=e.charCodeAt(i+1);a+=String.fromCharCode(l)+String.fromCharCode(t),i+=1;break}default:a+=String.fromCharCode(l)}}return a.length>0&&t.push(ts(a)),t}var tf=/^[a-z@][a-zA-Z0-9/%._-]*$/;function tp(e){let{designSystem:t,ast:r,resolvedConfig:n,featuresRef:a,referenceMode:o,src:i}=e,l={addBase(e){if(o)return;let n=th(e);a.current|=eH(n,t);let l=E("@layer","base",n);U([l],e=>{e.src=i}),r.push(l)},addVariant(e,r){if(!eG.test(e))throw Error("`addVariant('".concat(e,"')` defines an invalid variant name. Variants should only contain alphanumeric, dashes, or underscore characters and start with a lowercase letter or number."));if("string"==typeof r){if(r.includes(":merge("))return}else if(Array.isArray(r)){if(r.some(e=>e.includes(":merge(")))return}else if("object"==typeof r){let e=function(t,r){return Object.entries(t).some(t=>{let[n,a]=t;return n.includes(r)||"object"==typeof a&&e(a,r)})};if(e(r,":merge("))return}"string"==typeof r||Array.isArray(r)?t.variants.static(e,e=>{e.nodes=tm(r,e.nodes)},{compounds:eQ("string"==typeof r?[r]:r)}):"object"==typeof r&&t.variants.fromAst(e,th(r))},matchVariant(e,r,n){var a;function o(e,t,n){var a;return tm(r(e,{modifier:null!=(a=null==t?void 0:t.value)?a:null}),n)}try{let e=r("a",{modifier:null});if("string"==typeof e&&e.includes(":merge(")||Array.isArray(e)&&e.some(e=>e.includes(":merge(")))return}catch(e){}let i=Object.keys(null!=(a=null==n?void 0:n.values)?a:{});t.variants.group(()=>{t.variants.functional(e,(e,t)=>{if(!t.value){if((null==n?void 0:n.values)&&"DEFAULT"in n.values){e.nodes=o(n.values.DEFAULT,t.modifier,e.nodes);return}return null}if("arbitrary"===t.value.kind)e.nodes=o(t.value.value,t.modifier,e.nodes);else{if("named"!==t.value.kind||null==n||!n.values)return null;let r=n.values[t.value.value];if("string"!=typeof r)return null;e.nodes=o(r,t.modifier,e.nodes)}})},(e,t)=>{var r,a,o,l,s,c,u,d;if("functional"!==e.kind||"functional"!==t.kind)return 0;let f=e.value?e.value.value:"DEFAULT",p=t.value?t.value.value:"DEFAULT",h=null!=(s=null==n||null==(r=n.values)?void 0:r[f])?s:f,m=null!=(c=null==n||null==(a=n.values)?void 0:a[p])?c:p;if(n&&"function"==typeof n.sort)return n.sort({value:h,modifier:null!=(u=null==(o=e.modifier)?void 0:o.value)?u:null},{value:m,modifier:null!=(d=null==(l=t.modifier)?void 0:l.value)?d:null});let v=i.indexOf(f),g=i.indexOf(p);return(v=-1===v?i.length:v)!==(g=-1===g?i.length:g)?v-g:h<m?-1:1}),t.variants.suggest(e,()=>{var e;return Object.keys(null!=(e=null==n?void 0:n.values)?e:{}).filter(e=>"DEFAULT"!==e)})},addUtilities(e){let n=(e=Array.isArray(e)?e:[e]).flatMap(e=>Object.entries(e));n=n.flatMap(e=>{let[t,r]=e;return Z(t,",").map(e=>[e.trim(),r])});let l=new z(()=>[]);for(let[e,t]of n){if(e.startsWith("@keyframes ")){if(!o){let n=N(e,th(t));U([n],e=>{e.src=i}),r.push(n)}continue}let n=td(e),a=!1;if(tc(n,e=>{if("selector"===e.kind&&"."===e.value[0]&&tf.test(e.value.slice(1))){let r=e.value;e.value="&";let o=tu(n),i=r.slice(1),s="&"===o?th(t):[N(o,th(t))];l.get(i).push(...s),a=!0,e.value=r;return}if("function"===e.kind&&":not"===e.value)return 1}),!a)throw Error("`addUtilities({ '".concat(e,"' : … })` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. `.scrollbar-none`."))}for(let[e,r]of l)t.theme.prefix&&U(r,e=>{if("rule"===e.kind){let r=td(e.selector);tc(r,e=>{"selector"===e.kind&&"."===e.value[0]&&(e.value=".".concat(t.theme.prefix,"\\:").concat(e.value.slice(1)))}),e.selector=tu(r)}}),t.utilities.static(e,n=>{let o=structuredClone(r);return tv(o,e,n.raw),a.current|=e4(o,t),o})},matchUtilities(e,r){let n=(null==r?void 0:r.type)?Array.isArray(null==r?void 0:r.type)?r.type:[r.type]:["any"];for(let[o,i]of Object.entries(e)){let e=function(e){let{negative:l}=e;return e=>{var s,c,u,d,f,p,h;let m;if((null==(s=e.value)?void 0:s.kind)==="arbitrary"&&n.length>0&&!n.includes("any")&&(e.value.dataType&&!n.includes(e.value.dataType)||!e.value.dataType&&!es(e.value.value,n)))return;let v=n.includes("color"),g=null,w=!1;{let t=null!=(u=null==r?void 0:r.values)?u:{};v&&(t=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentcolor"},t)),e.value?"arbitrary"===e.value.kind?g=e.value.value:e.value.fraction&&t[e.value.fraction]?(g=t[e.value.fraction],w=!0):t[e.value.value]?g=t[e.value.value]:t.__BARE_VALUE__&&(g=null!=(d=t.__BARE_VALUE__(e.value))?d:null,w=null!=(f=null!==e.value.fraction&&(null==g?void 0:g.includes("/")))&&f):g=null!=(p=t.DEFAULT)?p:null}if(null===g)return;{let t=null!=(h=null==r?void 0:r.modifiers)?h:null;m=e.modifier?"any"===t||"arbitrary"===e.modifier.kind?e.modifier.value:(null==t?void 0:t[e.modifier.value])?t[e.modifier.value]:v&&!Number.isNaN(Number(e.modifier.value))?"".concat(e.modifier.value,"%"):null:null}if(e.modifier&&null===m&&!w)return(null==(c=e.value)?void 0:c.kind)==="arbitrary"?null:void 0;v&&null!==m&&(g=e_(g,m)),l&&(g="calc(".concat(g," * -1)"));let b=th(i(g,{modifier:m}));return tv(b,o,e.raw),a.current|=e4(b,t),b}};if(!tf.test(o))throw Error("`matchUtilities({ '".concat(o,"' : … })` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. `scrollbar`."));(null==r?void 0:r.supportsNegativeValues)&&t.utilities.functional("-".concat(o),e({negative:!0}),{types:n}),t.utilities.functional(o,e({negative:!1}),{types:n}),t.utilities.suggest(o,()=>{var e,t,n;let a=new Set(Object.keys(null!=(e=null==r?void 0:r.values)?e:{}));a.delete("__BARE_VALUE__"),a.delete("__CSS_VALUES__"),a.has("DEFAULT")&&(a.delete("DEFAULT"),a.add(null));let o=null!=(t=null==r?void 0:r.modifiers)?t:{},i="any"===o?[]:Object.keys(o);return[{supportsNegative:null!=(n=null==r?void 0:r.supportsNegativeValues)&&n,values:Array.from(a),modifiers:i}]})}},addComponents(e,t){this.addUtilities(e,t)},matchComponents(e,t){this.matchUtilities(e,t)},theme:to(t,()=>{var e;return null!=(e=n.theme)?e:{}},e=>e),prefix:e=>e,config(e,t){let r=n;if(!e)return r;let a=tr(e);for(let e=0;e<a.length;++e){let n=a[e];if(void 0===r[n])return t;r=r[n]}return null!=r?r:t}};return l.addComponents=l.addComponents.bind(l),l.matchComponents=l.matchComponents.bind(l),l}function th(e){let t=[];for(let[r,n]of(e=Array.isArray(e)?e:[e]).flatMap(e=>Object.entries(e)))if(null!=n&&!1!==n)if("object"!=typeof n){if(!r.startsWith("--")){if("@slot"===n){t.push(N(r,[E("@slot")]));continue}r=r.replace(/([A-Z])/g,"-$1").toLowerCase()}t.push(O(r,String(n)))}else if(Array.isArray(n))for(let e of n)"string"==typeof e?t.push(O(r,e)):t.push(N(r,th(e)));else t.push(N(r,th(n)));return t}function tm(e,t){return("string"==typeof e?[e]:e).flatMap(e=>{if(!e.trim().endsWith("}"))return N(e,t);{let r=p(e.replace("}","{@slot}}"));return e1(r,t),r}})}function tv(e,t,r){U(e,e=>{if("rule"===e.kind){let n=td(e.selector);tc(n,e=>{"selector"===e.kind&&e.value===".".concat(t)&&(e.value=".".concat(g(r)))}),e.selector=tu(n)}})}var tg={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function tw(e){return{__BARE_VALUE__:e}}var tk=tw(e=>{if(eA(e.value))return e.value}),tb=tw(e=>{if(eA(e.value))return"".concat(e.value,"%")}),ty=tw(e=>{if(eA(e.value))return"".concat(e.value,"px")}),tx=tw(e=>{if(eA(e.value))return"".concat(e.value,"ms")}),tA=tw(e=>{if(eA(e.value))return"".concat(e.value,"deg")}),tz=tw(e=>{if(null===e.fraction)return;let[t,r]=Z(e.fraction,"/");if(!(!eA(t)||!eA(r)))return e.fraction}),tT=tw(e=>{if(eA(Number(e.value)))return"repeat(".concat(e.value,", minmax(0, 1fr))")}),tj={accentColor:e=>{let{theme:t}=e;return t("colors")},animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...tz},backdropBlur:e=>{let{theme:t}=e;return t("blur")},backdropBrightness:e=>{let{theme:t}=e;return{...t("brightness"),...tb}},backdropContrast:e=>{let{theme:t}=e;return{...t("contrast"),...tb}},backdropGrayscale:e=>{let{theme:t}=e;return{...t("grayscale"),...tb}},backdropHueRotate:e=>{let{theme:t}=e;return{...t("hueRotate"),...tA}},backdropInvert:e=>{let{theme:t}=e;return{...t("invert"),...tb}},backdropOpacity:e=>{let{theme:t}=e;return{...t("opacity"),...tb}},backdropSaturate:e=>{let{theme:t}=e;return{...t("saturate"),...tb}},backdropSepia:e=>{let{theme:t}=e;return{...t("sepia"),...tb}},backgroundColor:e=>{let{theme:t}=e;return t("colors")},backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:e=>{let{theme:t}=e;return t("opacity")},backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:e=>{let{theme:t}=e;return{DEFAULT:"currentcolor",...t("colors")}},borderOpacity:e=>{let{theme:t}=e;return t("opacity")},borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:e=>{let{theme:t}=e;return t("spacing")},borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...ty},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:e=>{let{theme:t}=e;return t("colors")},brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...tb},caretColor:e=>{let{theme:t}=e;return t("colors")},colors:()=>({...tg}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...tk},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...tb},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:e=>{let{theme:t}=e;return t("borderColor")},divideOpacity:e=>{let{theme:t}=e;return t("borderOpacity")},divideWidth:e=>{let{theme:t}=e;return{...t("borderWidth"),...ty}},dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:e=>{let{theme:t}=e;return t("colors")},flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...t("spacing")}},flexGrow:{0:"0",DEFAULT:"1",...tk},flexShrink:{0:"0",DEFAULT:"1",...tk},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:e=>{let{theme:t}=e;return t("spacing")},gradientColorStops:e=>{let{theme:t}=e;return t("colors")},gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...tb},grayscale:{0:"0",DEFAULT:"100%",...tb},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...tk},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...tk},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...tk},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...tk},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...tT},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...tT},height:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...tA},inset:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}},invert:{0:"0",DEFAULT:"100%",...tb},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:e=>{let{theme:t}=e;return{auto:"auto",...t("spacing")}},lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...tk},maxHeight:e=>{let{theme:t}=e;return{none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},maxWidth:e=>{let{theme:t}=e;return{none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...t("spacing")}},minHeight:e=>{let{theme:t}=e;return{full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},minWidth:e=>{let{theme:t}=e;return{full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...tb},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...tk},outlineColor:e=>{let{theme:t}=e;return t("colors")},outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ty},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ty},padding:e=>{let{theme:t}=e;return t("spacing")},placeholderColor:e=>{let{theme:t}=e;return t("colors")},placeholderOpacity:e=>{let{theme:t}=e;return t("opacity")},ringColor:e=>{let{theme:t}=e;return{DEFAULT:"currentcolor",...t("colors")}},ringOffsetColor:e=>{let{theme:t}=e;return t("colors")},ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ty},ringOpacity:e=>{let{theme:t}=e;return{DEFAULT:"0.5",...t("opacity")}},ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ty},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...tA},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...tb},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...tb},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:e=>{let{theme:t}=e;return t("spacing")},scrollPadding:e=>{let{theme:t}=e;return t("spacing")},sepia:{0:"0",DEFAULT:"100%",...tb},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...tA},space:e=>{let{theme:t}=e;return t("spacing")},spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:e=>{let{theme:t}=e;return{none:"none",...t("colors")}},strokeWidth:{0:"0",1:"1",2:"2",...tk},supports:{},data:{},textColor:e=>{let{theme:t}=e;return t("colors")},textDecorationColor:e=>{let{theme:t}=e;return t("colors")},textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ty},textIndent:e=>{let{theme:t}=e;return t("spacing")},textOpacity:e=>{let{theme:t}=e;return t("opacity")},textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...ty},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...tx},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...tx},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:e=>{let{theme:t}=e;return{"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}},size:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},width:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...tk}},tC={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function tK(e,t){var r,n,a,o;let i={design:e,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(tC)};for(let e of t)!function e(t,r){var n,a,o,i;let{config:l,base:s,path:c,reference:u,src:d}=r,f=[];for(let e of null!=(n=l.plugins)?n:[])"__isOptionsFunction"in e?f.push({...e(),reference:u,src:d}):"handler"in e?f.push({...e,reference:u,src:d}):f.push({handler:e,reference:u,src:d});if(Array.isArray(l.presets)&&0===l.presets.length)throw Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let r of null!=(a=l.presets)?a:[])e(t,{path:c,base:s,config:r,reference:u,src:d});for(let r of f)t.plugins.push(r),r.config&&e(t,{path:c,base:s,config:r.config,reference:!!r.reference,src:null!=(o=r.src)?o:d});let p=null!=(i=l.content)?i:[];for(let e of Array.isArray(p)?p:p.files)t.content.files.push("object"==typeof e?e:{base:s,pattern:e});t.configs.push(l)}(i,e);for(let e of i.configs)"darkMode"in e&&void 0!==e.darkMode&&(i.result.darkMode=null!=(r=e.darkMode)?r:null),"prefix"in e&&void 0!==e.prefix&&(i.result.prefix=null!=(n=e.prefix)?n:""),"blocklist"in e&&void 0!==e.blocklist&&(i.result.blocklist=null!=(a=e.blocklist)?a:[]),"important"in e&&void 0!==e.important&&(i.result.important=null!=(o=e.important)&&o);let l=function(e){let t=new Set,r=to(e.design,()=>e.theme,a),n=Object.assign(r,{theme:r,colors:tg});function a(e){var t;return"function"==typeof e?null!=(t=e(n))?t:null:null!=e?e:null}for(let r of e.configs){var o,i,l;let n=null!=(i=r.theme)?i:{},a=null!=(l=n.extend)?l:{};for(let e in n)"extend"!==e&&t.add(e);for(let t in Object.assign(e.theme,n),a)null!=(o=e.extend)[t]||(o[t]=[]),e.extend[t].push(a[t])}for(let t in delete e.theme.extend,e.extend){let r=[e.theme[t],...e.extend[t]];e.theme[t]=()=>ta({},r.map(a),tS)}for(let t in e.theme)e.theme[t]=a(e.theme[t]);if(e.theme.screens&&"object"==typeof e.theme.screens)for(let t of Object.keys(e.theme.screens)){let r=e.theme.screens[t];r&&"object"==typeof r&&("raw"in r||"max"in r||"min"in r&&(e.theme.screens[t]=r.min))}return t}(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:l}}function tS(e,t){return Array.isArray(e)&&tn(e[0])?e.concat(t):Array.isArray(t)&&tn(t[0])&&tn(e)?[e,...t]:Array.isArray(t)?t:void 0}function tV(e){let{addVariant:t,config:r}=e,n=r("darkMode",null),[a,o=".dark"]=Array.isArray(n)?n:[n];if("variant"===a){let e;if(Array.isArray(o)||"function"==typeof o?e=o:"string"==typeof o&&(e=[o]),Array.isArray(e))for(let t of e)".dark"===t?(a=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):t.includes("&")||(a=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));o=e}null===a||("selector"===a?t("dark","&:where(".concat(o,", ").concat(o," *)")):"media"===a?t("dark","@media (prefers-color-scheme: dark)"):"variant"===a?t("dark",o):"class"===a&&t("dark","&:is(".concat(o," *)")))}var tE=/^[a-z]+$/;async function tN(e){let{designSystem:t,base:r,ast:n,loadModule:a,sources:o}=e,i=0,l=[],s=[];U(n,(e,t)=>{let{parent:r,replaceWith:n,context:a}=t;if("at-rule"===e.kind){if("@plugin"===e.name){var o;if(null!==r)throw Error("`@plugin` cannot be nested.");let t=e.params.slice(1,-1);if(0===t.length)throw Error("`@plugin` must have a path.");let s={};for(let t of null!=(o=e.nodes)?o:[]){if("declaration"!==t.kind)throw Error("Unexpected `@plugin` option:\n\n".concat(B([t]),"\n\n`@plugin` options must be a flat list of declarations."));if(void 0===t.value)continue;let e=Z(t.value,",").map(e=>{if("null"===(e=e.trim()))return null;if("true"===e)return!0;if("false"===e)return!1;if(!Number.isNaN(Number(e)))return Number(e);if('"'===e[0]&&'"'===e[e.length-1]||"'"===e[0]&&"'"===e[e.length-1])return e.slice(1,-1);if("{"===e[0]&&"}"===e[e.length-1])throw Error("Unexpected `@plugin` option: Value of declaration `".concat(B([t]).trim(),"` is not supported.\n\nUsing an object as a plugin option is currently only supported in JavaScript configuration files."));return e});s[t.property]=1===e.length?e[0]:e}l.push([{id:t,base:a.base,reference:!!a.reference,src:e.src},Object.keys(s).length>0?s:null]),n([]),i|=4;return}if("@config"===e.name){if(e.nodes.length>0)throw Error("`@config` cannot have a body.");if(null!==r)throw Error("`@config` cannot be nested.");s.push({id:e.params.slice(1,-1),base:a.base,reference:!!a.reference,src:e.src}),n([]),i|=4;return}}}),function(e){for(let[t,r]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])e.utilities.static("bg-gradient-to-".concat(t),()=>[O("--tw-gradient-position","to ".concat(r," in oklab")),O("background-image","linear-gradient(var(--tw-gradient-stops))")]);e.utilities.static("bg-left-top",()=>[O("background-position","left top")]),e.utilities.static("bg-right-top",()=>[O("background-position","right top")]),e.utilities.static("bg-left-bottom",()=>[O("background-position","left bottom")]),e.utilities.static("bg-right-bottom",()=>[O("background-position","right bottom")]),e.utilities.static("object-left-top",()=>[O("object-position","left top")]),e.utilities.static("object-right-top",()=>[O("object-position","right top")]),e.utilities.static("object-left-bottom",()=>[O("object-position","left bottom")]),e.utilities.static("object-right-bottom",()=>[O("object-position","right bottom")]),e.utilities.functional("max-w-screen",t=>{if(!t.value||"arbitrary"===t.value.kind)return;let r=e.theme.resolve(t.value.value,["--breakpoint"]);if(r)return[O("max-width",r)]}),e.utilities.static("overflow-ellipsis",()=>[O("text-overflow","ellipsis")]),e.utilities.static("decoration-slice",()=>[O("-webkit-box-decoration-break","slice"),O("box-decoration-break","slice")]),e.utilities.static("decoration-clone",()=>[O("-webkit-box-decoration-break","clone"),O("box-decoration-break","clone")]),e.utilities.functional("flex-shrink",e=>{if(!e.modifier){if(!e.value)return[O("flex-shrink","1")];if("arbitrary"===e.value.kind||eA(e.value.value))return[O("flex-shrink",e.value.value)]}}),e.utilities.functional("flex-grow",e=>{if(!e.modifier){if(!e.value)return[O("flex-grow","1")];if("arbitrary"===e.value.kind||eA(e.value.value))return[O("flex-grow",e.value.value)]}}),e.utilities.static("order-none",()=>[O("order","0")])}(t);let c=t.resolveThemeValue;if(t.resolveThemeValue=function(e,a){return e.startsWith("--")?c(e,a):(i|=tO({designSystem:t,base:r,ast:n,sources:o,configs:[],pluginDetails:[]}),t.resolveThemeValue(e,a))},!l.length&&!s.length)return 0;let[u,d]=await Promise.all([Promise.all(s.map(async e=>{let{id:t,base:r,reference:n,src:o}=e,i=await a(t,r,"config");return{path:t,base:i.base,config:i.module,reference:n,src:o}})),Promise.all(l.map(async e=>{let[{id:t,base:r,reference:n,src:o},i]=e,l=await a(t,r,"plugin");return{path:t,base:l.base,plugin:l.module,options:i,reference:n,src:o}}))]);return i|=tO({designSystem:t,base:r,ast:n,sources:o,configs:u,pluginDetails:d})}function tO(e){var t,r,n;let{designSystem:a,base:o,ast:i,sources:l,configs:s,pluginDetails:c}=e,u=0,d=[...c.map(e=>{if(!e.options)return{config:{plugins:[e.plugin]},base:e.base,reference:e.reference,src:e.src};if("__isOptionsFunction"in e.plugin)return{config:{plugins:[e.plugin(e.options)]},base:e.base,reference:e.reference,src:e.src};throw Error('The plugin "'.concat(e.path,'" does not accept options'))}),...s],{resolvedConfig:f}=tK(a,[{config:(t=a.theme,{theme:{...tj,colors:e=>{let{theme:t}=e;return t("color",{})},extend:{fontSize:e=>{let{theme:t}=e;return{...t("text",{})}},boxShadow:e=>{let{theme:t}=e;return{...t("shadow",{})}},animation:e=>{let{theme:t}=e;return{...t("animate",{})}},aspectRatio:e=>{let{theme:t}=e;return{...t("aspect",{})}},borderRadius:e=>{let{theme:t}=e;return{...t("radius",{})}},screens:e=>{let{theme:t}=e;return{...t("breakpoint",{})}},letterSpacing:e=>{let{theme:t}=e;return{...t("tracking",{})}},lineHeight:e=>{let{theme:t}=e;return{...t("leading",{})}},transitionDuration:{DEFAULT:null!=(r=t.get(["--default-transition-duration"]))?r:null},transitionTimingFunction:{DEFAULT:null!=(n=t.get(["--default-transition-timing-function"]))?n:null},maxWidth:e=>{let{theme:t}=e;return{...t("container",{})}}}}}),base:o,reference:!0,src:void 0},...d,{config:{plugins:[tV]},base:o,reference:!0,src:void 0}]),{resolvedConfig:p,replacedThemeKeys:h}=tK(a,d),m={designSystem:a,ast:i,resolvedConfig:f,featuresRef:{set current(k){u|=k}}},v=tp({...m,referenceMode:!1,src:void 0}),g=a.resolveThemeValue;for(let{handler:e,reference:t,src:r}of(a.resolveThemeValue=function(e,t){if("-"===e[0]&&"-"===e[1])return g(e,t);let r=v.theme(e,void 0);return Array.isArray(r)&&2===r.length?r[0]:Array.isArray(r)?r.join(", "):"string"==typeof r?r:void 0},f.plugins))e(tp({...m,referenceMode:null!=t&&t,src:r}));if(function(e,t,r){let n,{theme:a}=t;for(let t of r){let r=tt([t]);r&&e.theme.clearNamespace("--".concat(r),4)}for(let[t,r]of(n=[],function e(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0;for(let o of Reflect.ownKeys(t)){var a;let i=t[o];if(null==i)continue;let l=[...r,o],s=null!=(a=n(i,l))?a:0;if(1!==s&&(2===s||!(!Array.isArray(i)&&"object"!=typeof i)&&2===e(i,l,n)))return 2}}(a,[],(e,t)=>{var r;if("number"==typeof(r=e)||"string"==typeof r)return n.push([t,e]),1;if(function(e){if(!Array.isArray(e)||2!==e.length||"string"!=typeof e[0]&&"number"!=typeof e[0]||void 0===e[1]||null===e[1]||"object"!=typeof e[1])return!1;for(let t of Reflect.ownKeys(e[1]))if("string"!=typeof t||"string"!=typeof e[1][t]&&"number"!=typeof e[1][t])return!1;return!0}(e)){for(let r of(n.push([t,e[0]]),Reflect.ownKeys(e[1])))n.push([[...t,"-".concat(r)],e[1][r]]);return 1}if(Array.isArray(e)&&e.every(e=>"string"==typeof e))return"fontSize"===t[0]?(n.push([t,e[0]]),e.length>=2&&n.push([[...t,"-line-height"],e[1]])):n.push([t,e.join(", ")]),1}),n)){if("string"!=typeof r&&"number"!=typeof r)continue;if("string"==typeof r&&(r=r.replace(/<alpha-value>/g,"1")),"opacity"===t[0]&&("number"==typeof r||"string"==typeof r)){let e="string"==typeof r?parseFloat(r):r;e>=0&&e<=1&&(r=100*e+"%")}let n=tt(t);n&&e.theme.add("--".concat(n),""+r,7)}if(Object.hasOwn(a,"fontFamily")){var o,i,l,s;{let t=e8(a.fontFamily.sans);t&&e.theme.hasDefault("--font-sans")&&(e.theme.add("--default-font-family",t,5),e.theme.add("--default-font-feature-settings",null!=(o=e8(a.fontFamily.sans,"fontFeatureSettings"))?o:"normal",5),e.theme.add("--default-font-variation-settings",null!=(i=e8(a.fontFamily.sans,"fontVariationSettings"))?i:"normal",5))}{let t=e8(a.fontFamily.mono);t&&e.theme.hasDefault("--font-mono")&&(e.theme.add("--default-mono-font-family",t,5),e.theme.add("--default-mono-font-feature-settings",null!=(l=e8(a.fontFamily.mono,"fontFeatureSettings"))?l:"normal",5),e.theme.add("--default-mono-font-variation-settings",null!=(s=e8(a.fontFamily.mono,"fontVariationSettings"))?s:"normal",5))}}}(a,p,h),function(e,t,r){for(let r of function(e){let t=[];if("keyframes"in e.theme)for(let[r,n]of Object.entries(e.theme.keyframes))t.push(E("@keyframes",r,th(n)));return t}(t))e.theme.addKeyframes(r)}(a,p,0),function(e,t){let r=e.theme.aria||{},n=e.theme.supports||{},a=e.theme.data||{};if(Object.keys(r).length>0){let e=t.variants.get("aria"),n=null==e?void 0:e.applyFn,a=null==e?void 0:e.compounds;t.variants.functional("aria",(e,t)=>{let a=t.value;return a&&"named"===a.kind&&a.value in r?null==n?void 0:n(e,{...t,value:{kind:"arbitrary",value:r[a.value]}}):null==n?void 0:n(e,t)},{compounds:a})}if(Object.keys(n).length>0){let e=t.variants.get("supports"),r=null==e?void 0:e.applyFn,a=null==e?void 0:e.compounds;t.variants.functional("supports",(e,t)=>{let a=t.value;return a&&"named"===a.kind&&a.value in n?null==r?void 0:r(e,{...t,value:{kind:"arbitrary",value:n[a.value]}}):null==r?void 0:r(e,t)},{compounds:a})}if(Object.keys(a).length>0){let e=t.variants.get("data"),r=null==e?void 0:e.applyFn,n=null==e?void 0:e.compounds;t.variants.functional("data",(e,t)=>{let n=t.value;return n&&"named"===n.kind&&n.value in a?null==r?void 0:r(e,{...t,value:{kind:"arbitrary",value:a[n.value]}}):null==r?void 0:r(e,t)},{compounds:n})}}(p,a),function(e,t){var r,n,a;let o=e.theme.screens||{},i=null!=(n=null==(r=t.variants.get("min"))?void 0:r.order)?n:0,l=[];for(let[e,r]of Object.entries(o)){let n=function(r){t.variants.static(e,e=>{e.nodes=[E("@media",u,e.nodes)]},{order:r})},o=t.variants.get(e),s=t.theme.resolveValue(e,["--breakpoint"]);if(o&&s&&!t.theme.hasDefault("--breakpoint-".concat(e)))continue;let c=!0;"string"==typeof r&&(c=!1);let u=(Array.isArray(a=r)?a:[a]).map(e=>"string"==typeof e?{min:e}:e&&"object"==typeof e?e:null).map(e=>{if(null===e)return null;if("raw"in e)return e.raw;let t="";return void 0!==e.max&&(t+="".concat(e.max," >= ")),t+="width",void 0!==e.min&&(t+=" >= ".concat(e.min)),"(".concat(t,")")}).filter(Boolean).join(", ");c?l.push(n):n(i)}if(0!==l.length){for(let[,e]of t.variants.variants)e.order>i&&(e.order+=l.length);for(let[e,r]of(t.variants.compareFns=new Map(Array.from(t.variants.compareFns).map(e=>{let[t,r]=e;return t>i&&(t+=l.length),[t,r]})),l.entries()))r(i+e+1)}}(p,a),function(e,t){let r=e.theme.container||{};if("object"!=typeof r||null===r)return;let n=function(e,t){let{center:r,padding:n,screens:a}=e,o=[],i=null;if(r&&o.push(O("margin-inline","auto")),("string"==typeof n||"object"==typeof n&&null!==n&&"DEFAULT"in n)&&o.push(O("padding-inline","string"==typeof n?n:n.DEFAULT)),"object"==typeof a&&null!==a){i=new Map;let e=Array.from(t.theme.namespace("--breakpoint").entries());if(e.sort((e,t)=>ea(e[1],t[1],"asc")),e.length>0){let[t]=e[0];o.push(E("@media","(width >= --theme(--breakpoint-".concat(t,"))"),[O("max-width","none")]))}for(let[e,t]of Object.entries(a)){if("object"==typeof t)if(!("min"in t))continue;else t=t.min;i.set(e,E("@media","(width >= ".concat(t,")"),[O("max-width",t)]))}}if("object"==typeof n&&null!==n){let e=Object.entries(n).filter(e=>{let[t]=e;return"DEFAULT"!==t}).map(e=>{let[r,n]=e;return[r,t.theme.resolveValue(r,["--breakpoint"]),n]}).filter(Boolean);for(let[t,,r]of(e.sort((e,t)=>ea(e[1],t[1],"asc")),e))if(i&&i.has(t))i.get(t).nodes.push(O("padding-inline",r));else{if(i)continue;o.push(E("@media","(width >= theme(--breakpoint-".concat(t,"))"),[O("padding-inline",r)]))}}if(i)for(let[,e]of i)o.push(e);return o}(r,t);0!==n.length&&t.utilities.static("container",()=>structuredClone(n))}(p,a),!a.theme.prefix&&f.prefix){if(f.prefix.endsWith("-")&&(f.prefix=f.prefix.slice(0,-1),console.warn('The prefix "'.concat(f.prefix,'" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing `-` to silence this warning.'))),!tE.test(f.prefix))throw Error('The prefix "'.concat(f.prefix,'" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.'));a.theme.prefix=f.prefix}if(a.important||!0!==f.important||(a.important=!0),"string"==typeof f.important){let e=f.important;U(i,(t,r)=>{let{replaceWith:n,parent:a}=r;if("at-rule"===t.kind&&"@tailwind"===t.name&&"utilities"===t.params)return(null==a?void 0:a.kind)==="rule"&&a.selector===e||n(V(e,[t])),2})}for(let e of f.blocklist)a.invalidCandidates.add(e);for(let e of f.content.files){if("raw"in e)throw Error("Error in the config file/plugin/preset. The `content` key contains a `raw` entry:\n\n".concat(JSON.stringify(e,null,2),"\n\nThis feature is not currently supported."));let t=!1;"!"==e.pattern[0]&&(t=!0,e.pattern=e.pattern.slice(1)),l.push({...e,negated:t})}return u}var tW=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/,tF=/^[a-z]+$/,t_=(e=>(e[e.None=0]="None",e[e.AtProperty=1]="AtProperty",e[e.ColorMix=2]="ColorMix",e[e.All=3]="All",e))(t_||{});function tU(){throw Error("No `loadModule` function provided to `compile`")}function tD(){throw Error("No `loadStylesheet` function provided to `compile`")}var tB=(e=>(e[e.None=0]="None",e[e.AtApply=1]="AtApply",e[e.AtImport=2]="AtImport",e[e.JsPluginCompat=4]="JsPluginCompat",e[e.ThemeFunction=8]="ThemeFunction",e[e.Utilities=16]="Utilities",e[e.Variants=32]="Variants",e))(tB||{});async function tL(e){let t,r,n,a,o,i,l,{base:s="",from:c,loadModule:u=tU,loadStylesheet:d=tD}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},f=0;e=[F({base:s},e)],f|=await e7(e,s,d,0,void 0!==c);let p=null,h=new A,m=[],v=[],b=null,x=null,T=[],W=[],L=[],M=[],I=null;U(e,(e,t)=>{let{parent:r,replaceWith:n,context:a}=t;if("at-rule"===e.kind){var o;if("@tailwind"===e.name&&("utilities"===e.params||e.params.startsWith("utilities"))){if(null!==x||a.reference)return void n([]);for(let t of Z(e.params," "))if(t.startsWith("source(")){let e=t.slice(7,-1);if("none"===e){I=e;continue}if('"'===e[0]&&'"'!==e[e.length-1]||"'"===e[0]&&"'"!==e[e.length-1]||"'"!==e[0]&&'"'!==e[0])throw Error("`source(…)` paths must be quoted.");I={base:null!=(o=a.sourceBase)?o:a.base,pattern:e.slice(1,-1)}}x=e,f|=16}if("@utility"===e.name){let t;if(null!==r)throw Error("`@utility` cannot be nested.");if(0===e.nodes.length)throw Error("`@utility ".concat(e.params,"` is empty. Utilities should include at least one property."));let n=(t=e.params,eN.test(t)?r=>{let n={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};U(e.nodes,e=>{if("declaration"!==e.kind||!e.value||!e.value.includes("--value(")&&!e.value.includes("--modifier("))return;let t=K(e.value);j(t,e=>{if("function"!==e.kind)return;if("--spacing"===e.value&&!(n["--modifier"].usedSpacingNumber&&n["--value"].usedSpacingNumber))return j(e.nodes,e=>{if("function"!==e.kind||"--value"!==e.value&&"--modifier"!==e.value)return;let t=e.value;for(let o of e.nodes)if("word"===o.kind){var r,a;if("integer"===o.value)(r=n[t]).usedSpacingInteger||(r.usedSpacingInteger=!0);else if("number"===o.value&&((a=n[t]).usedSpacingNumber||(a.usedSpacingNumber=!0),n["--modifier"].usedSpacingNumber&&n["--value"].usedSpacingNumber))return 2}}),0;if("--value"!==e.value&&"--modifier"!==e.value)return;let t=Z(C(e.nodes),",");for(let[e,r]of t.entries())"-"!==(r=(r=(r=(r=r.replace(/\\\*/g,"*")).replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2")).replace(/\s+/g,"")).replace(/(-\*){2,}/g,"-*"))[0]||"-"!==r[1]||r.includes("-*")||(r+="-*"),t[e]=r;for(let r of(e.nodes=K(t.join(",")),e.nodes))if("word"===r.kind&&('"'===r.value[0]||"'"===r.value[0])&&r.value[0]===r.value[r.value.length-1]){let t=r.value.slice(1,-1);n[e.value].literals.add(t)}else if("word"===r.kind&&"-"===r.value[0]&&"-"===r.value[1]){let t=r.value.replace(/-\*.*$/g,"");n[e.value].themeKeys.add(t)}else if("word"===r.kind&&("["!==r.value[0]||"]"!==r.value[r.value.length-1])&&!eM.includes(r.value)){console.warn('Unsupported bare value data type: "'.concat(r.value,'".\nOnly valid data types are: ').concat(eM.map(e=>'"'.concat(e,'"')).join(", "),".\n"));let t=r.value,n=structuredClone(e);j(n.nodes,(e,r)=>{let{replaceWith:n}=r;"word"===e.kind&&e.value===t&&n({kind:"word",value:"¶"})});let a="^".repeat(C([r]).length),o=C([n]).indexOf("¶");console.warn(["```css",C([e])," ".repeat(o)+a,"```"].join("\n"))}}),e.value=C(t)}),r.utilities.functional(t.slice(0,-2),t=>{let n=structuredClone(e),a=t.value,o=t.modifier;if(null===a)return;let i=!1,l=!1,s=!1,c=!1,u=new Map,d=!1;if(U([n],(e,t)=>{var n;let{parent:f,replaceWith:p}=t;if((null==f?void 0:f.kind)!=="rule"&&(null==f?void 0:f.kind)!=="at-rule"||"declaration"!==e.kind||!e.value)return;let h=K(e.value);(null!=(n=j(h,(t,n)=>{let{replaceWith:h}=n;if("function"===t.kind){if("--value"===t.value){i=!0;let n=eI(a,t,r);return n?(l=!0,n.ratio?d=!0:u.set(e,f),h(n.nodes),1):(i||(i=!1),p([]),2)}else if("--modifier"===t.value){if(null===o)return p([]),2;s=!0;let e=eI(o,t,r);return e?(c=!0,h(e.nodes),1):(s||(s=!1),p([]),2)}}}))?n:0)===0&&(e.value=C(h))}),i&&!l||s&&!c||d&&c||o&&!d&&!c)return null;if(d)for(let[e,t]of u){let r=t.nodes.indexOf(e);-1!==r&&t.nodes.splice(r,1)}return n.nodes}),r.utilities.suggest(t.slice(0,-2),()=>{let e=[],t=[];for(let[a,{literals:o,usedSpacingNumber:i,usedSpacingInteger:l,themeKeys:s}]of[[e,n["--value"]],[t,n["--modifier"]]]){for(let e of o)a.push(e);if(i)a.push(...eO);else if(l)for(let e of eO)eA(e)&&a.push(e);for(let e of r.theme.keysInNamespaces(s))a.push(e.replace(eL,(e,t,r)=>"".concat(t,".").concat(r)))}return[{values:e,modifiers:t}]})}:eE.test(t)?r=>{r.utilities.static(t,()=>structuredClone(e.nodes))}:null);if(null===n){if(!e.params.endsWith("-*")){if(e.params.endsWith("*"))throw Error("`@utility ".concat(e.params,"` defines an invalid utility name. A functional utility must end in `-*`."));if(e.params.includes("*"))throw Error("`@utility ".concat(e.params,"` defines an invalid utility name. The dynamic portion marked by `-*` must appear once at the end."))}throw Error("`@utility ".concat(e.params,"` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter."))}v.push(n)}if("@source"===e.name){if(e.nodes.length>0)throw Error("`@source` cannot have a body.");if(null!==r)throw Error("`@source` cannot be nested.");let t=!1,o=!1,i=e.params;if("n"===i[0]&&i.startsWith("not ")&&(t=!0,i=i.slice(4)),"i"===i[0]&&i.startsWith("inline(")&&(o=!0,i=i.slice(7,-1)),'"'===i[0]&&'"'!==i[i.length-1]||"'"===i[0]&&"'"!==i[i.length-1]||"'"!==i[0]&&'"'!==i[0])throw Error("`@source` paths must be quoted.");let l=i.slice(1,-1);if(o){let e=t?M:L;for(let t of Z(l," "))for(let r of function e(t){var r;let n=t.indexOf("{");if(-1===n)return[t];let a=[],o=t.slice(0,n),i=t.slice(n),l=0,s=i.lastIndexOf("}");for(let e=0;e<i.length;e++){let t=i[e];if("{"===t)l++;else if("}"===t&&0==--l){s=e;break}}if(-1===s)throw Error("The pattern `".concat(t,"` is not balanced."));let c=i.slice(1,s),u=i.slice(s+1),d;for(let t of(d=(r=c,d=tW.test(r)?function(e){let t=e.match(tW);if(!t)return[e];let[,r,n,a]=t,o=a?parseInt(a,10):void 0,i=[];if(/^-?\d+$/.test(r)&&/^-?\d+$/.test(n)){let e=parseInt(r,10),t=parseInt(n,10);if(void 0===o&&(o=e<=t?1:-1),0===o)throw Error("Step cannot be zero in sequence expansion.");let a=e<t;a&&o<0&&(o=-o),!a&&o>0&&(o=-o);for(let r=e;a?r<=t:r>=t;r+=o)i.push(r.toString())}return i}(c):Z(c,",")).flatMap(t=>e(t)),e(u)))for(let e of d)a.push(o+e+t);return a}(t))e.push(r)}else W.push({base:a.base,pattern:l,negated:t});n([]);return}if("@variant"===e.name&&(null===r?0===e.nodes.length?e.name="@custom-variant":(U(e.nodes,t=>{if("at-rule"===t.kind&&"@slot"===t.name)return e.name="@custom-variant",2}),"@variant"===e.name&&T.push(e)):T.push(e)),"@custom-variant"===e.name){if(null!==r)throw Error("`@custom-variant` cannot be nested.");n([]);let[t,a]=Z(e.params," ");if(!eG.test(t))throw Error("`@custom-variant ".concat(t,"` defines an invalid variant name. Variants should only contain alphanumeric, dashes, or underscore characters and start with a lowercase letter or number."));if(e.nodes.length>0&&a)throw Error("`@custom-variant ".concat(t,"` cannot have both a selector and a body."));if(0!==e.nodes.length)return void m.push(r=>{r.variants.fromAst(t,e.nodes)});{if(!a)throw Error("`@custom-variant ".concat(t,"` has no selector or body."));let e=Z(a.slice(1,-1),",");if(0===e.length||e.some(e=>""===e.trim()))throw Error("`@custom-variant ".concat(t," (").concat(e.join(","),")` selector is invalid."));let r=[],n=[];for(let t of e)"@"===(t=t.trim())[0]?r.push(t):n.push(t);m.push(e=>{e.variants.static(t,e=>{let t=[];for(let a of(n.length>0&&t.push(V(n.join(", "),e.nodes)),r))t.push(N(a,e.nodes));e.nodes=t},{compounds:eQ([...n,...r])})});return}}if("@media"===e.name){let t=Z(e.params," "),r=[];for(let n of t)if(n.startsWith("source(")){let t=n.slice(7,-1);U(e.nodes,(e,r)=>{let{replaceWith:n}=r;if("at-rule"===e.kind&&"@tailwind"===e.name&&"utilities"===e.params)return e.params+=" source(".concat(t,")"),n([F({sourceBase:a.base},[e])]),2})}else if(n.startsWith("theme(")){let t=n.slice(6,-1),r=t.includes("reference");U(e.nodes,e=>{if("at-rule"!==e.kind){if(r)throw Error('Files imported with `@import "…" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "…";` instead.');return 0}if("@theme"===e.name)return e.params+=" "+t,1})}else if(n.startsWith("prefix(")){let t=n.slice(7,-1);U(e.nodes,e=>{if("at-rule"===e.kind&&"@theme"===e.name)return e.params+=" prefix(".concat(t,")"),1})}else"important"===n?p=!0:"reference"===n?e.nodes=[F({reference:!0},e.nodes)]:r.push(n);r.length>0?e.params=r.join(" "):t.length>0&&n(e.nodes)}if("@theme"===e.name){let[t,r]=function(e){let t=0,r=null;for(let n of Z(e," "))"reference"===n?t|=2:"inline"===n?t|=1:"default"===n?t|=4:"static"===n?t|=8:n.startsWith("prefix(")&&n.endsWith(")")&&(r=n.slice(7,-1));return[t,r]}(e.params);if(a.reference&&(t|=2),r){if(!tF.test(r))throw Error('The prefix "'.concat(r,'" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.'));h.prefix=r}return U(e.nodes,r=>{if("at-rule"===r.kind&&"@keyframes"===r.name)return h.addKeyframes(r),1;if("comment"===r.kind)return;if("declaration"===r.kind&&r.property.startsWith("--")){var n;h.add(w(r.property),null!=(n=r.value)?n:"",t,r.src);return}let a=B([E(e.name,e.params,[r])]).split("\n").map((e,t,r)=>"".concat(0===t||t>=r.length-2?" ":">"," ").concat(e)).join("\n");throw Error("`@theme` blocks must only contain custom properties or `@keyframes`.\n\n".concat(a))}),b?n([]):((b=V(":root, :host",[])).src=e.src,n([b])),1}}});let R=(t=function(e){var t,r,n,a,o;let i=new eW;function l(t,r){function*n(t){for(let r of e.keysInNamespaces(t))yield r.replace(eL,(e,t,r)=>"".concat(t,".").concat(r))}let a=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];i.suggest(t,()=>{let e=[];for(let s of r()){var t,o,i,l;if("string"==typeof s){e.push({values:[s],modifiers:[]});continue}let r=[...null!=(t=s.values)?t:[],...n(null!=(o=s.valueThemeKeys)?o:[])],c=[...null!=(i=s.modifiers)?i:[],...n(null!=(l=s.modifierThemeKeys)?l:[])];s.supportsFractions&&r.push(...a),s.hasDefaultValue&&r.unshift(null),e.push({supportsNegative:s.supportsNegative,values:r,modifiers:c})}return e})}function s(e,t){i.static(e,()=>t.map(e=>"function"==typeof e?e():O(e[0],e[1])))}function c(t,r){function n(t){let{negative:n}=t;return t=>{var a,o,i;let l=null,s=null;if(t.value)if("arbitrary"===t.value.kind){if(t.modifier)return;l=t.value.value,s=t.value.dataType}else{if(null===(l=e.resolve(null!=(a=t.value.fraction)?a:t.value.value,null!=(o=r.themeKeys)?o:[]))&&r.supportsFractions&&t.value.fraction){let[e,r]=Z(t.value.fraction,"/");if(!eA(e)||!eA(r))return;l="calc(".concat(t.value.fraction," * 100%)")}if(null===l&&n&&r.handleNegativeBareValue){if(!(null==(l=r.handleNegativeBareValue(t.value))?void 0:l.includes("/"))&&t.modifier)return;if(null!==l)return r.handle(l,null)}if(null===l&&r.handleBareValue&&!(null==(l=r.handleBareValue(t.value))?void 0:l.includes("/"))&&t.modifier)return}else{if(t.modifier)return;l=void 0!==r.defaultValue?r.defaultValue:e.resolve(null,null!=(i=r.themeKeys)?i:[])}if(null!==l)return r.handle(n?"calc(".concat(l," * -1)"):l,s)}}r.supportsNegative&&i.functional("-".concat(t),n({negative:!0})),i.functional(t,n({negative:!1})),l(t,()=>{var e;return[{supportsNegative:r.supportsNegative,valueThemeKeys:null!=(e=r.themeKeys)?e:[],hasDefaultValue:void 0!==r.defaultValue&&null!==r.defaultValue,supportsFractions:r.supportsFractions}]})}function u(t,r){i.functional(t,t=>{if(!t.value)return;let n=null;if(null!==(n="arbitrary"===t.value.kind?eD(n=t.value.value,t.modifier,e):eB(t,e,r.themeKeys)))return r.handle(n)}),l(t,()=>[{values:["current","inherit","transparent"],valueThemeKeys:r.themeKeys,modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))}])}function d(t,r,n){let{supportsNegative:a=!1,supportsFractions:o=!1}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};a&&i.static("-".concat(t,"-px"),()=>n("-1px")),i.static("".concat(t,"-px"),()=>n("1px")),c(t,{themeKeys:r,supportsFractions:o,supportsNegative:a,defaultValue:null,handleBareValue:t=>{let{value:r}=t,n=e.resolve(null,["--spacing"]);return n&&eT(r)?"calc(".concat(n," * ").concat(r,")"):null},handleNegativeBareValue:t=>{let{value:r}=t,n=e.resolve(null,["--spacing"]);return n&&eT(r)?"calc(".concat(n," * -").concat(r,")"):null},handle:n}),l(t,()=>[{values:e.get(["--spacing"])?eO:[],supportsNegative:a,supportsFractions:o,valueThemeKeys:r}])}for(let[e,t]of(s("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip-path","inset(50%)"],["white-space","nowrap"],["border-width","0"]]),s("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip-path","none"],["white-space","normal"]]),s("pointer-events-none",[["pointer-events","none"]]),s("pointer-events-auto",[["pointer-events","auto"]]),s("visible",[["visibility","visible"]]),s("invisible",[["visibility","hidden"]]),s("collapse",[["visibility","collapse"]]),s("static",[["position","static"]]),s("fixed",[["position","fixed"]]),s("absolute",[["position","absolute"]]),s("relative",[["position","relative"]]),s("sticky",[["position","sticky"]]),[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]]))s("".concat(e,"-auto"),[[t,"auto"]]),s("".concat(e,"-full"),[[t,"100%"]]),s("-".concat(e,"-full"),[[t,"-100%"]]),d(e,["--inset","--spacing"],e=>[O(t,e)],{supportsNegative:!0,supportsFractions:!0});for(let[e,t]of(s("isolate",[["isolation","isolate"]]),s("isolation-auto",[["isolation","auto"]]),s("z-auto",[["z-index","auto"]]),c("z",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},themeKeys:["--z-index"],handle:e=>[O("z-index",e)]}),l("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),s("order-first",[["order","-9999"]]),s("order-last",[["order","9999"]]),c("order",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},themeKeys:["--order"],handle:e=>[O("order",e)]}),l("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:["--order"]}]),s("col-auto",[["grid-column","auto"]]),c("col",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},themeKeys:["--grid-column"],handle:e=>[O("grid-column",e)]}),s("col-span-full",[["grid-column","1 / -1"]]),c("col-span",{handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},handle:e=>[O("grid-column","span ".concat(e," / span ").concat(e))]}),s("col-start-auto",[["grid-column-start","auto"]]),c("col-start",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},themeKeys:["--grid-column-start"],handle:e=>[O("grid-column-start",e)]}),s("col-end-auto",[["grid-column-end","auto"]]),c("col-end",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},themeKeys:["--grid-column-end"],handle:e=>[O("grid-column-end",e)]}),l("col-span",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:[]}]),l("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-column-start"]}]),l("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-column-end"]}]),s("row-auto",[["grid-row","auto"]]),c("row",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},themeKeys:["--grid-row"],handle:e=>[O("grid-row",e)]}),s("row-span-full",[["grid-row","1 / -1"]]),c("row-span",{themeKeys:[],handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},handle:e=>[O("grid-row","span ".concat(e," / span ").concat(e))]}),s("row-start-auto",[["grid-row-start","auto"]]),c("row-start",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},themeKeys:["--grid-row-start"],handle:e=>[O("grid-row-start",e)]}),s("row-end-auto",[["grid-row-end","auto"]]),c("row-end",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},themeKeys:["--grid-row-end"],handle:e=>[O("grid-row-end",e)]}),l("row-span",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:[]}]),l("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-row-start"]}]),l("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-row-end"]}]),s("float-start",[["float","inline-start"]]),s("float-end",[["float","inline-end"]]),s("float-right",[["float","right"]]),s("float-left",[["float","left"]]),s("float-none",[["float","none"]]),s("clear-start",[["clear","inline-start"]]),s("clear-end",[["clear","inline-end"]]),s("clear-right",[["clear","right"]]),s("clear-left",[["clear","left"]]),s("clear-both",[["clear","both"]]),s("clear-none",[["clear","none"]]),[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]]))s("".concat(e,"-auto"),[[t,"auto"]]),d(e,["--margin","--spacing"],e=>[O(t,e)],{supportsNegative:!0});for(let[e,t]of(s("box-border",[["box-sizing","border-box"]]),s("box-content",[["box-sizing","content-box"]]),s("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),c("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},handle:e=>[O("overflow","hidden"),O("display","-webkit-box"),O("-webkit-box-orient","vertical"),O("-webkit-line-clamp",e)]}),l("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),s("block",[["display","block"]]),s("inline-block",[["display","inline-block"]]),s("inline",[["display","inline"]]),s("hidden",[["display","none"]]),s("inline-flex",[["display","inline-flex"]]),s("table",[["display","table"]]),s("inline-table",[["display","inline-table"]]),s("table-caption",[["display","table-caption"]]),s("table-cell",[["display","table-cell"]]),s("table-column",[["display","table-column"]]),s("table-column-group",[["display","table-column-group"]]),s("table-footer-group",[["display","table-footer-group"]]),s("table-header-group",[["display","table-header-group"]]),s("table-row-group",[["display","table-row-group"]]),s("table-row",[["display","table-row"]]),s("flow-root",[["display","flow-root"]]),s("flex",[["display","flex"]]),s("grid",[["display","grid"]]),s("inline-grid",[["display","inline-grid"]]),s("contents",[["display","contents"]]),s("list-item",[["display","list-item"]]),s("field-sizing-content",[["field-sizing","content"]]),s("field-sizing-fixed",[["field-sizing","fixed"]]),s("aspect-auto",[["aspect-ratio","auto"]]),s("aspect-square",[["aspect-ratio","1 / 1"]]),c("aspect",{themeKeys:["--aspect"],handleBareValue:e=>{let{fraction:t}=e;if(null===t)return null;let[r,n]=Z(t,"/");return eA(r)&&eA(n)?t:null},handle:e=>[O("aspect-ratio",e)]}),[["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]]))s("size-".concat(e),[["--tw-sort","size"],["width",t],["height",t]]),s("w-".concat(e),[["width",t]]),s("h-".concat(e),[["height",t]]),s("min-w-".concat(e),[["min-width",t]]),s("min-h-".concat(e),[["min-height",t]]),s("max-w-".concat(e),[["max-width",t]]),s("max-h-".concat(e),[["max-height",t]]);for(let[e,t,r]of(s("size-auto",[["--tw-sort","size"],["width","auto"],["height","auto"]]),s("w-auto",[["width","auto"]]),s("h-auto",[["height","auto"]]),s("min-w-auto",[["min-width","auto"]]),s("min-h-auto",[["min-height","auto"]]),s("h-lh",[["height","1lh"]]),s("min-h-lh",[["min-height","1lh"]]),s("max-h-lh",[["max-height","1lh"]]),s("w-screen",[["width","100vw"]]),s("min-w-screen",[["min-width","100vw"]]),s("max-w-screen",[["max-width","100vw"]]),s("h-screen",[["height","100vh"]]),s("min-h-screen",[["min-height","100vh"]]),s("max-h-screen",[["max-height","100vh"]]),s("max-w-none",[["max-width","none"]]),s("max-h-none",[["max-height","none"]]),d("size",["--size","--spacing"],e=>[O("--tw-sort","size"),O("width",e),O("height",e)],{supportsFractions:!0}),[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]]))d(e,t,e=>[O(r,e)],{supportsFractions:!0});i.static("container",()=>{let t=[...e.namespace("--breakpoint").values()];t.sort((e,t)=>ea(e,t,"asc"));let r=[O("--tw-sort","--tw-container-component"),O("width","100%")];for(let e of t)r.push(E("@media","(width >= ".concat(e,")"),[O("max-width",e)]));return r}),s("flex-auto",[["flex","auto"]]),s("flex-initial",[["flex","0 auto"]]),s("flex-none",[["flex","none"]]),i.functional("flex",e=>{if(e.value){if("arbitrary"===e.value.kind)return e.modifier?void 0:[O("flex",e.value.value)];if(e.value.fraction){let[t,r]=Z(e.value.fraction,"/");return eA(t)&&eA(r)?[O("flex","calc(".concat(e.value.fraction," * 100%)"))]:void 0}if(eA(e.value.value))return e.modifier?void 0:[O("flex",e.value.value)]}}),l("flex",()=>[{supportsFractions:!0},{values:Array.from({length:12},(e,t)=>"".concat(t+1))}]),c("shrink",{defaultValue:"1",handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},handle:e=>[O("flex-shrink",e)]}),c("grow",{defaultValue:"1",handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},handle:e=>[O("flex-grow",e)]}),l("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),l("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),s("basis-auto",[["flex-basis","auto"]]),s("basis-full",[["flex-basis","100%"]]),d("basis",["--flex-basis","--spacing","--container"],e=>[O("flex-basis",e)],{supportsFractions:!0}),s("table-auto",[["table-layout","auto"]]),s("table-fixed",[["table-layout","fixed"]]),s("caption-top",[["caption-side","top"]]),s("caption-bottom",[["caption-side","bottom"]]),s("border-collapse",[["border-collapse","collapse"]]),s("border-separate",[["border-collapse","separate"]]);let f=()=>_([eF("--tw-border-spacing-x","0","<length>"),eF("--tw-border-spacing-y","0","<length>")]);d("border-spacing",["--border-spacing","--spacing"],e=>[f(),O("--tw-border-spacing-x",e),O("--tw-border-spacing-y",e),O("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),d("border-spacing-x",["--border-spacing","--spacing"],e=>[f(),O("--tw-border-spacing-x",e),O("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),d("border-spacing-y",["--border-spacing","--spacing"],e=>[f(),O("--tw-border-spacing-y",e),O("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),s("origin-center",[["transform-origin","center"]]),s("origin-top",[["transform-origin","top"]]),s("origin-top-right",[["transform-origin","top right"]]),s("origin-right",[["transform-origin","right"]]),s("origin-bottom-right",[["transform-origin","bottom right"]]),s("origin-bottom",[["transform-origin","bottom"]]),s("origin-bottom-left",[["transform-origin","bottom left"]]),s("origin-left",[["transform-origin","left"]]),s("origin-top-left",[["transform-origin","top left"]]),c("origin",{themeKeys:["--transform-origin"],handle:e=>[O("transform-origin",e)]}),s("perspective-origin-center",[["perspective-origin","center"]]),s("perspective-origin-top",[["perspective-origin","top"]]),s("perspective-origin-top-right",[["perspective-origin","top right"]]),s("perspective-origin-right",[["perspective-origin","right"]]),s("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),s("perspective-origin-bottom",[["perspective-origin","bottom"]]),s("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),s("perspective-origin-left",[["perspective-origin","left"]]),s("perspective-origin-top-left",[["perspective-origin","top left"]]),c("perspective-origin",{themeKeys:["--perspective-origin"],handle:e=>[O("perspective-origin",e)]}),s("perspective-none",[["perspective","none"]]),c("perspective",{themeKeys:["--perspective"],handle:e=>[O("perspective",e)]});let p=()=>_([eF("--tw-translate-x","0"),eF("--tw-translate-y","0"),eF("--tw-translate-z","0")]);for(let e of(s("translate-none",[["translate","none"]]),s("-translate-full",[p,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),s("translate-full",[p,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),d("translate",["--translate","--spacing"],e=>[p(),O("--tw-translate-x",e),O("--tw-translate-y",e),O("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0}),["x","y"]))s("-translate-".concat(e,"-full"),[p,["--tw-translate-".concat(e),"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),s("translate-".concat(e,"-full"),[p,["--tw-translate-".concat(e),"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),d("translate-".concat(e),["--translate","--spacing"],t=>[p(),O("--tw-translate-".concat(e),t),O("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});d("translate-z",["--translate","--spacing"],e=>[p(),O("--tw-translate-z",e),O("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),s("translate-3d",[p,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let h=()=>_([eF("--tw-scale-x","1"),eF("--tw-scale-y","1"),eF("--tw-scale-z","1")]);function m(t){let{negative:r}=t;return t=>{let n;if(t.value&&!t.modifier)return"arbitrary"===t.value.kind?(n=t.value.value,[O("scale",n=r?"calc(".concat(n," * -1)"):n)]):(!(n=e.resolve(t.value.value,["--scale"]))&&eA(t.value.value)&&(n="".concat(t.value.value,"%")),n?(n=r?"calc(".concat(n," * -1)"):n,[h(),O("--tw-scale-x",n),O("--tw-scale-y",n),O("--tw-scale-z",n),O("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}for(let e of(s("scale-none",[["scale","none"]]),i.functional("-scale",m({negative:!0})),i.functional("scale",m({negative:!1})),l("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]),["x","y","z"]))c("scale-".concat(e),{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},handle:t=>[h(),O("--tw-scale-".concat(e),t),O("scale","var(--tw-scale-x) var(--tw-scale-y)".concat("z"===e?" var(--tw-scale-z)":""))]}),l("scale-".concat(e),()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);function v(t){let{negative:r}=t;return t=>{let n;if(t.value&&!t.modifier){if("arbitrary"===t.value.kind){var a;if(n=t.value.value,"vector"===(null!=(a=t.value.dataType)?a:es(n,["angle","vector"])))return[O("rotate","".concat(n," var(--tw-rotate)"))]}else if(!(n=e.resolve(t.value.value,["--rotate"]))&&eA(t.value.value)&&(n="".concat(t.value.value,"deg")),!n)return;return[O("rotate",r?"calc(".concat(n," * -1)"):n)]}}}s("scale-3d",[h,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),s("rotate-none",[["rotate","none"]]),i.functional("-rotate",v({negative:!0})),i.functional("rotate",v({negative:!1})),l("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let e="var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,)",t=()=>_([eF("--tw-rotate-x"),eF("--tw-rotate-y"),eF("--tw-rotate-z"),eF("--tw-skew-x"),eF("--tw-skew-y")]);for(let r of["x","y","z"])c("rotate-".concat(r),{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"deg"):null},handle:n=>[t(),O("--tw-rotate-".concat(r),"rotate".concat(r.toUpperCase(),"(").concat(n,")")),O("transform",e)]}),l("rotate-".concat(r),()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);c("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"deg"):null},handle:r=>[t(),O("--tw-skew-x","skewX(".concat(r,")")),O("--tw-skew-y","skewY(".concat(r,")")),O("transform",e)]}),c("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"deg"):null},handle:r=>[t(),O("--tw-skew-x","skewX(".concat(r,")")),O("transform",e)]}),c("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"deg"):null},handle:r=>[t(),O("--tw-skew-y","skewY(".concat(r,")")),O("transform",e)]}),l("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),l("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),l("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i.functional("transform",r=>{if(r.modifier)return;let n=null;if(r.value?"arbitrary"===r.value.kind&&(n=r.value.value):n=e,null!==n)return[t(),O("transform",n)]}),l("transform",()=>[{hasDefaultValue:!0}]),s("transform-cpu",[["transform",e]]),s("transform-gpu",[["transform","translateZ(0) ".concat(e)]]),s("transform-none",[["transform","none"]])}for(let e of(s("transform-flat",[["transform-style","flat"]]),s("transform-3d",[["transform-style","preserve-3d"]]),s("transform-content",[["transform-box","content-box"]]),s("transform-border",[["transform-box","border-box"]]),s("transform-fill",[["transform-box","fill-box"]]),s("transform-stroke",[["transform-box","stroke-box"]]),s("transform-view",[["transform-box","view-box"]]),s("backface-visible",[["backface-visibility","visible"]]),s("backface-hidden",[["backface-visibility","hidden"]]),["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"]))s("cursor-".concat(e),[["cursor",e]]);for(let e of(c("cursor",{themeKeys:["--cursor"],handle:e=>[O("cursor",e)]}),["auto","none","manipulation"]))s("touch-".concat(e),[["touch-action",e]]);let g=()=>_([eF("--tw-pan-x"),eF("--tw-pan-y"),eF("--tw-pinch-zoom")]);for(let e of["x","left","right"])s("touch-pan-".concat(e),[g,["--tw-pan-x","pan-".concat(e)],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let e of["y","up","down"])s("touch-pan-".concat(e),[g,["--tw-pan-y","pan-".concat(e)],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let e of(s("touch-pinch-zoom",[g,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]),["none","text","all","auto"]))s("select-".concat(e),[["-webkit-user-select",e],["user-select",e]]);s("resize-none",[["resize","none"]]),s("resize-x",[["resize","horizontal"]]),s("resize-y",[["resize","vertical"]]),s("resize",[["resize","both"]]),s("snap-none",[["scroll-snap-type","none"]]);let w=()=>_([eF("--tw-scroll-snap-strictness","proximity","*")]);for(let e of["x","y","both"])s("snap-".concat(e),[w,["scroll-snap-type","".concat(e," var(--tw-scroll-snap-strictness)")]]);for(let[e,t]of(s("snap-mandatory",[w,["--tw-scroll-snap-strictness","mandatory"]]),s("snap-proximity",[w,["--tw-scroll-snap-strictness","proximity"]]),s("snap-align-none",[["scroll-snap-align","none"]]),s("snap-start",[["scroll-snap-align","start"]]),s("snap-end",[["scroll-snap-align","end"]]),s("snap-center",[["scroll-snap-align","center"]]),s("snap-normal",[["scroll-snap-stop","normal"]]),s("snap-always",[["scroll-snap-stop","always"]]),[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]]))d(e,["--scroll-margin","--spacing"],e=>[O(t,e)],{supportsNegative:!0});for(let[e,t]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])d(e,["--scroll-padding","--spacing"],e=>[O(t,e)]);for(let e of(s("list-inside",[["list-style-position","inside"]]),s("list-outside",[["list-style-position","outside"]]),s("list-none",[["list-style-type","none"]]),s("list-disc",[["list-style-type","disc"]]),s("list-decimal",[["list-style-type","decimal"]]),c("list",{themeKeys:["--list-style-type"],handle:e=>[O("list-style-type",e)]}),s("list-image-none",[["list-style-image","none"]]),c("list-image",{themeKeys:["--list-style-image"],handle:e=>[O("list-style-image",e)]}),s("appearance-none",[["appearance","none"]]),s("appearance-auto",[["appearance","auto"]]),s("scheme-normal",[["color-scheme","normal"]]),s("scheme-dark",[["color-scheme","dark"]]),s("scheme-light",[["color-scheme","light"]]),s("scheme-light-dark",[["color-scheme","light dark"]]),s("scheme-only-dark",[["color-scheme","only dark"]]),s("scheme-only-light",[["color-scheme","only light"]]),s("columns-auto",[["columns","auto"]]),c("columns",{themeKeys:["--columns","--container"],handleBareValue:e=>{let{value:t}=e;return eA(t)?t:null},handle:e=>[O("columns",e)]}),l("columns",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:["--columns","--container"]}]),["auto","avoid","all","avoid-page","page","left","right","column"]))s("break-before-".concat(e),[["break-before",e]]);for(let e of["auto","avoid","avoid-page","avoid-column"])s("break-inside-".concat(e),[["break-inside",e]]);for(let e of["auto","avoid","all","avoid-page","page","left","right","column"])s("break-after-".concat(e),[["break-after",e]]);for(let e of(s("grid-flow-row",[["grid-auto-flow","row"]]),s("grid-flow-col",[["grid-auto-flow","column"]]),s("grid-flow-dense",[["grid-auto-flow","dense"]]),s("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),s("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),s("auto-cols-auto",[["grid-auto-columns","auto"]]),s("auto-cols-min",[["grid-auto-columns","min-content"]]),s("auto-cols-max",[["grid-auto-columns","max-content"]]),s("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),c("auto-cols",{themeKeys:["--grid-auto-columns"],handle:e=>[O("grid-auto-columns",e)]}),s("auto-rows-auto",[["grid-auto-rows","auto"]]),s("auto-rows-min",[["grid-auto-rows","min-content"]]),s("auto-rows-max",[["grid-auto-rows","max-content"]]),s("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),c("auto-rows",{themeKeys:["--grid-auto-rows"],handle:e=>[O("grid-auto-rows",e)]}),s("grid-cols-none",[["grid-template-columns","none"]]),s("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),c("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:e=>{let{value:t}=e;return ez(t)?"repeat(".concat(t,", minmax(0, 1fr))"):null},handle:e=>[O("grid-template-columns",e)]}),s("grid-rows-none",[["grid-template-rows","none"]]),s("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),c("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:e=>{let{value:t}=e;return ez(t)?"repeat(".concat(t,", minmax(0, 1fr))"):null},handle:e=>[O("grid-template-rows",e)]}),l("grid-cols",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-template-columns"]}]),l("grid-rows",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-template-rows"]}]),s("flex-row",[["flex-direction","row"]]),s("flex-row-reverse",[["flex-direction","row-reverse"]]),s("flex-col",[["flex-direction","column"]]),s("flex-col-reverse",[["flex-direction","column-reverse"]]),s("flex-wrap",[["flex-wrap","wrap"]]),s("flex-nowrap",[["flex-wrap","nowrap"]]),s("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),s("place-content-center",[["place-content","center"]]),s("place-content-start",[["place-content","start"]]),s("place-content-end",[["place-content","end"]]),s("place-content-center-safe",[["place-content","safe center"]]),s("place-content-end-safe",[["place-content","safe end"]]),s("place-content-between",[["place-content","space-between"]]),s("place-content-around",[["place-content","space-around"]]),s("place-content-evenly",[["place-content","space-evenly"]]),s("place-content-baseline",[["place-content","baseline"]]),s("place-content-stretch",[["place-content","stretch"]]),s("place-items-center",[["place-items","center"]]),s("place-items-start",[["place-items","start"]]),s("place-items-end",[["place-items","end"]]),s("place-items-center-safe",[["place-items","safe center"]]),s("place-items-end-safe",[["place-items","safe end"]]),s("place-items-baseline",[["place-items","baseline"]]),s("place-items-stretch",[["place-items","stretch"]]),s("content-normal",[["align-content","normal"]]),s("content-center",[["align-content","center"]]),s("content-start",[["align-content","flex-start"]]),s("content-end",[["align-content","flex-end"]]),s("content-center-safe",[["align-content","safe center"]]),s("content-end-safe",[["align-content","safe flex-end"]]),s("content-between",[["align-content","space-between"]]),s("content-around",[["align-content","space-around"]]),s("content-evenly",[["align-content","space-evenly"]]),s("content-baseline",[["align-content","baseline"]]),s("content-stretch",[["align-content","stretch"]]),s("items-center",[["align-items","center"]]),s("items-start",[["align-items","flex-start"]]),s("items-end",[["align-items","flex-end"]]),s("items-center-safe",[["align-items","safe center"]]),s("items-end-safe",[["align-items","safe flex-end"]]),s("items-baseline",[["align-items","baseline"]]),s("items-baseline-last",[["align-items","last baseline"]]),s("items-stretch",[["align-items","stretch"]]),s("justify-normal",[["justify-content","normal"]]),s("justify-center",[["justify-content","center"]]),s("justify-start",[["justify-content","flex-start"]]),s("justify-end",[["justify-content","flex-end"]]),s("justify-center-safe",[["justify-content","safe center"]]),s("justify-end-safe",[["justify-content","safe flex-end"]]),s("justify-between",[["justify-content","space-between"]]),s("justify-around",[["justify-content","space-around"]]),s("justify-evenly",[["justify-content","space-evenly"]]),s("justify-baseline",[["justify-content","baseline"]]),s("justify-stretch",[["justify-content","stretch"]]),s("justify-items-normal",[["justify-items","normal"]]),s("justify-items-center",[["justify-items","center"]]),s("justify-items-start",[["justify-items","start"]]),s("justify-items-end",[["justify-items","end"]]),s("justify-items-center-safe",[["justify-items","safe center"]]),s("justify-items-end-safe",[["justify-items","safe end"]]),s("justify-items-stretch",[["justify-items","stretch"]]),d("gap",["--gap","--spacing"],e=>[O("gap",e)]),d("gap-x",["--gap","--spacing"],e=>[O("column-gap",e)]),d("gap-y",["--gap","--spacing"],e=>[O("row-gap",e)]),d("space-x",["--space","--spacing"],e=>[_([eF("--tw-space-x-reverse","0")]),V(":where(& > :not(:last-child))",[O("--tw-sort","row-gap"),O("--tw-space-x-reverse","0"),O("margin-inline-start","calc(".concat(e," * var(--tw-space-x-reverse))")),O("margin-inline-end","calc(".concat(e," * calc(1 - var(--tw-space-x-reverse)))"))])],{supportsNegative:!0}),d("space-y",["--space","--spacing"],e=>[_([eF("--tw-space-y-reverse","0")]),V(":where(& > :not(:last-child))",[O("--tw-sort","column-gap"),O("--tw-space-y-reverse","0"),O("margin-block-start","calc(".concat(e," * var(--tw-space-y-reverse))")),O("margin-block-end","calc(".concat(e," * calc(1 - var(--tw-space-y-reverse)))"))])],{supportsNegative:!0}),s("space-x-reverse",[()=>_([eF("--tw-space-x-reverse","0")]),()=>V(":where(& > :not(:last-child))",[O("--tw-sort","row-gap"),O("--tw-space-x-reverse","1")])]),s("space-y-reverse",[()=>_([eF("--tw-space-y-reverse","0")]),()=>V(":where(& > :not(:last-child))",[O("--tw-sort","column-gap"),O("--tw-space-y-reverse","1")])]),s("accent-auto",[["accent-color","auto"]]),u("accent",{themeKeys:["--accent-color","--color"],handle:e=>[O("accent-color",e)]}),u("caret",{themeKeys:["--caret-color","--color"],handle:e=>[O("caret-color",e)]}),u("divide",{themeKeys:["--divide-color","--border-color","--color"],handle:e=>[V(":where(& > :not(:last-child))",[O("--tw-sort","divide-color"),O("border-color",e)])]}),s("place-self-auto",[["place-self","auto"]]),s("place-self-start",[["place-self","start"]]),s("place-self-end",[["place-self","end"]]),s("place-self-center",[["place-self","center"]]),s("place-self-end-safe",[["place-self","safe end"]]),s("place-self-center-safe",[["place-self","safe center"]]),s("place-self-stretch",[["place-self","stretch"]]),s("self-auto",[["align-self","auto"]]),s("self-start",[["align-self","flex-start"]]),s("self-end",[["align-self","flex-end"]]),s("self-center",[["align-self","center"]]),s("self-end-safe",[["align-self","safe flex-end"]]),s("self-center-safe",[["align-self","safe center"]]),s("self-stretch",[["align-self","stretch"]]),s("self-baseline",[["align-self","baseline"]]),s("self-baseline-last",[["align-self","last baseline"]]),s("justify-self-auto",[["justify-self","auto"]]),s("justify-self-start",[["justify-self","flex-start"]]),s("justify-self-end",[["justify-self","flex-end"]]),s("justify-self-center",[["justify-self","center"]]),s("justify-self-end-safe",[["justify-self","safe flex-end"]]),s("justify-self-center-safe",[["justify-self","safe center"]]),s("justify-self-stretch",[["justify-self","stretch"]]),["auto","hidden","clip","visible","scroll"]))s("overflow-".concat(e),[["overflow",e]]),s("overflow-x-".concat(e),[["overflow-x",e]]),s("overflow-y-".concat(e),[["overflow-y",e]]);for(let e of["auto","contain","none"])s("overscroll-".concat(e),[["overscroll-behavior",e]]),s("overscroll-x-".concat(e),[["overscroll-behavior-x",e]]),s("overscroll-y-".concat(e),[["overscroll-behavior-y",e]]);for(let[e,t]of(s("scroll-auto",[["scroll-behavior","auto"]]),s("scroll-smooth",[["scroll-behavior","smooth"]]),s("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),s("text-ellipsis",[["text-overflow","ellipsis"]]),s("text-clip",[["text-overflow","clip"]]),s("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),s("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),s("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),s("whitespace-normal",[["white-space","normal"]]),s("whitespace-nowrap",[["white-space","nowrap"]]),s("whitespace-pre",[["white-space","pre"]]),s("whitespace-pre-line",[["white-space","pre-line"]]),s("whitespace-pre-wrap",[["white-space","pre-wrap"]]),s("whitespace-break-spaces",[["white-space","break-spaces"]]),s("text-wrap",[["text-wrap","wrap"]]),s("text-nowrap",[["text-wrap","nowrap"]]),s("text-balance",[["text-wrap","balance"]]),s("text-pretty",[["text-wrap","pretty"]]),s("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),s("break-words",[["overflow-wrap","break-word"]]),s("break-all",[["word-break","break-all"]]),s("break-keep",[["word-break","keep-all"]]),s("wrap-anywhere",[["overflow-wrap","anywhere"]]),s("wrap-break-word",[["overflow-wrap","break-word"]]),s("wrap-normal",[["overflow-wrap","normal"]]),[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]]))s("".concat(e,"-none"),t.map(e=>[e,"0"])),s("".concat(e,"-full"),t.map(e=>[e,"calc(infinity * 1px)"])),c(e,{themeKeys:["--radius"],handle:e=>t.map(t=>O(t,e))});s("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),s("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),s("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),s("border-double",[["--tw-border-style","double"],["border-style","double"]]),s("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),s("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let n=function(t,r){i.functional(t,t=>{var n,o;if(!t.value){if(t.modifier)return;let o=null!=(n=e.get(["--default-border-width"]))?n:"1px",i=r.width(o);return i?[a(),...i]:void 0}if("arbitrary"===t.value.kind){let n=t.value.value;switch(null!=(o=t.value.dataType)?o:es(n,["color","line-width","length"])){case"line-width":case"length":{if(t.modifier)return;let e=r.width(n);return e?[a(),...e]:void 0}default:return null===(n=eD(n,t.modifier,e))?void 0:r.color(n)}}{let n=eB(t,e,["--border-color","--color"]);if(n)return r.color(n)}{if(t.modifier)return;let n=e.resolve(t.value.value,["--border-width"]);if(n){let e=r.width(n);return e?[a(),...e]:void 0}if(eA(t.value.value)){let e=r.width("".concat(t.value.value,"px"));return e?[a(),...e]:void 0}}}),l(t,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])},a=()=>_([eF("--tw-border-style","solid")]);for(let o of(n("border",{width:e=>[O("border-style","var(--tw-border-style)"),O("border-width",e)],color:e=>[O("border-color",e)]}),n("border-x",{width:e=>[O("border-inline-style","var(--tw-border-style)"),O("border-inline-width",e)],color:e=>[O("border-inline-color",e)]}),n("border-y",{width:e=>[O("border-block-style","var(--tw-border-style)"),O("border-block-width",e)],color:e=>[O("border-block-color",e)]}),n("border-s",{width:e=>[O("border-inline-start-style","var(--tw-border-style)"),O("border-inline-start-width",e)],color:e=>[O("border-inline-start-color",e)]}),n("border-e",{width:e=>[O("border-inline-end-style","var(--tw-border-style)"),O("border-inline-end-width",e)],color:e=>[O("border-inline-end-color",e)]}),n("border-t",{width:e=>[O("border-top-style","var(--tw-border-style)"),O("border-top-width",e)],color:e=>[O("border-top-color",e)]}),n("border-r",{width:e=>[O("border-right-style","var(--tw-border-style)"),O("border-right-width",e)],color:e=>[O("border-right-color",e)]}),n("border-b",{width:e=>[O("border-bottom-style","var(--tw-border-style)"),O("border-bottom-width",e)],color:e=>[O("border-bottom-color",e)]}),n("border-l",{width:e=>[O("border-left-style","var(--tw-border-style)"),O("border-left-width",e)],color:e=>[O("border-left-color",e)]}),c("divide-x",{defaultValue:null!=(t=e.get(["--default-border-width"]))?t:"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"px"):null},handle:e=>[_([eF("--tw-divide-x-reverse","0")]),V(":where(& > :not(:last-child))",[O("--tw-sort","divide-x-width"),a(),O("--tw-divide-x-reverse","0"),O("border-inline-style","var(--tw-border-style)"),O("border-inline-start-width","calc(".concat(e," * var(--tw-divide-x-reverse))")),O("border-inline-end-width","calc(".concat(e," * calc(1 - var(--tw-divide-x-reverse)))"))])]}),c("divide-y",{defaultValue:null!=(r=e.get(["--default-border-width"]))?r:"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"px"):null},handle:e=>[_([eF("--tw-divide-y-reverse","0")]),V(":where(& > :not(:last-child))",[O("--tw-sort","divide-y-width"),a(),O("--tw-divide-y-reverse","0"),O("border-bottom-style","var(--tw-border-style)"),O("border-top-style","var(--tw-border-style)"),O("border-top-width","calc(".concat(e," * var(--tw-divide-y-reverse))")),O("border-bottom-width","calc(".concat(e," * calc(1 - var(--tw-divide-y-reverse)))"))])]}),l("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),l("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),s("divide-x-reverse",[()=>_([eF("--tw-divide-x-reverse","0")]),()=>V(":where(& > :not(:last-child))",[O("--tw-divide-x-reverse","1")])]),s("divide-y-reverse",[()=>_([eF("--tw-divide-y-reverse","0")]),()=>V(":where(& > :not(:last-child))",[O("--tw-divide-y-reverse","1")])]),["solid","dashed","dotted","double","none"]))s("divide-".concat(o),[()=>V(":where(& > :not(:last-child))",[O("--tw-sort","divide-style"),O("--tw-border-style",o),O("border-style",o)])])}s("bg-auto",[["background-size","auto"]]),s("bg-cover",[["background-size","cover"]]),s("bg-contain",[["background-size","contain"]]),c("bg-size",{handle(e){if(e)return[O("background-size",e)]}}),s("bg-fixed",[["background-attachment","fixed"]]),s("bg-local",[["background-attachment","local"]]),s("bg-scroll",[["background-attachment","scroll"]]),s("bg-top",[["background-position","top"]]),s("bg-top-left",[["background-position","left top"]]),s("bg-top-right",[["background-position","right top"]]),s("bg-bottom",[["background-position","bottom"]]),s("bg-bottom-left",[["background-position","left bottom"]]),s("bg-bottom-right",[["background-position","right bottom"]]),s("bg-left",[["background-position","left"]]),s("bg-right",[["background-position","right"]]),s("bg-center",[["background-position","center"]]),c("bg-position",{handle(e){if(e)return[O("background-position",e)]}}),s("bg-repeat",[["background-repeat","repeat"]]),s("bg-no-repeat",[["background-repeat","no-repeat"]]),s("bg-repeat-x",[["background-repeat","repeat-x"]]),s("bg-repeat-y",[["background-repeat","repeat-y"]]),s("bg-repeat-round",[["background-repeat","round"]]),s("bg-repeat-space",[["background-repeat","space"]]),s("bg-none",[["background-image","none"]]);{let e=function(e){let t="in oklab";if((null==e?void 0:e.kind)==="named")switch(e.value){case"longer":case"shorter":case"increasing":case"decreasing":t="in oklch ".concat(e.value," hue");break;default:t="in ".concat(e.value)}else(null==e?void 0:e.kind)==="arbitrary"&&(t=e.value);return t},t=function(t){let{negative:r}=t;return t=>{if(!t.value)return;if("arbitrary"===t.value.kind){var n;if(t.modifier)return;let e=t.value.value;return(null!=(n=t.value.dataType)?n:es(e,["angle"]))==="angle"?[O("--tw-gradient-position",e=r?"calc(".concat(e," * -1)"):"".concat(e)),O("background-image","linear-gradient(var(--tw-gradient-stops,".concat(e,"))"))]:r?void 0:[O("--tw-gradient-position",e),O("background-image","linear-gradient(var(--tw-gradient-stops,".concat(e,"))"))]}let o=t.value.value;if(!r&&a.has(o))o=a.get(o);else{if(!eA(o))return;o=r?"calc(".concat(o,"deg * -1)"):"".concat(o,"deg")}let i=e(t.modifier);return[O("--tw-gradient-position","".concat(o)),N("@supports (background-image: linear-gradient(in lab, red, red))",[O("--tw-gradient-position","".concat(o," ").concat(i))]),O("background-image","linear-gradient(var(--tw-gradient-stops))")]}},r=function(t){let{negative:r}=t;return t=>{var n;if((null==(n=t.value)?void 0:n.kind)==="arbitrary"){if(t.modifier)return;let e=t.value.value;return[O("--tw-gradient-position",e),O("background-image","conic-gradient(var(--tw-gradient-stops,".concat(e,"))"))]}let a=e(t.modifier);if(!t.value)return[O("--tw-gradient-position",a),O("background-image","conic-gradient(var(--tw-gradient-stops))")];let o=t.value.value;if(eA(o))return o=r?"calc(".concat(o,"deg * -1)"):"".concat(o,"deg"),[O("--tw-gradient-position","from ".concat(o," ").concat(a)),O("background-image","conic-gradient(var(--tw-gradient-stops))")]}},n=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],a=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);i.functional("-bg-linear",t({negative:!0})),i.functional("bg-linear",t({negative:!1})),l("bg-linear",()=>[{values:[...a.keys()],modifiers:n},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:n}]),i.functional("-bg-conic",r({negative:!0})),i.functional("bg-conic",r({negative:!1})),l("bg-conic",()=>[{hasDefaultValue:!0,modifiers:n},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:n}]),i.functional("bg-radial",t=>{if(!t.value)return[O("--tw-gradient-position",e(t.modifier)),O("background-image","radial-gradient(var(--tw-gradient-stops))")];if("arbitrary"===t.value.kind){if(t.modifier)return;let e=t.value.value;return[O("--tw-gradient-position",e),O("background-image","radial-gradient(var(--tw-gradient-stops,".concat(e,"))"))]}}),l("bg-radial",()=>[{hasDefaultValue:!0,modifiers:n}])}i.functional("bg",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;switch(null!=(r=t.value.dataType)?r:es(n,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return t.modifier?void 0:[O("background-position",n)];case"bg-size":case"length":case"size":return t.modifier?void 0:[O("background-size",n)];case"image":case"url":return t.modifier?void 0:[O("background-image",n)];default:return null===(n=eD(n,t.modifier,e))?void 0:[O("background-color",n)]}}{let r=eB(t,e,["--background-color","--color"]);if(r)return[O("background-color",r)]}{if(t.modifier)return;let r=e.resolve(t.value.value,["--background-image"]);if(r)return[O("background-image",r)]}}}),l("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:[],valueThemeKeys:["--background-image"]}]);let b=()=>_([eF("--tw-gradient-position"),eF("--tw-gradient-from","#0000","<color>"),eF("--tw-gradient-via","#0000","<color>"),eF("--tw-gradient-to","#0000","<color>"),eF("--tw-gradient-stops"),eF("--tw-gradient-via-stops"),eF("--tw-gradient-from-position","0%","<length-percentage>"),eF("--tw-gradient-via-position","50%","<length-percentage>"),eF("--tw-gradient-to-position","100%","<length-percentage>")]);function x(t,r){i.functional(t,t=>{if(t.value){if("arbitrary"===t.value.kind){var n;let a=t.value.value;switch(null!=(n=t.value.dataType)?n:es(a,["color","length","percentage"])){case"length":case"percentage":return t.modifier?void 0:r.position(a);default:return null===(a=eD(a,t.modifier,e))?void 0:r.color(a)}}{let n=eB(t,e,["--background-color","--color"]);if(n)return r.color(n)}{if(t.modifier)return;let n=e.resolve(t.value.value,["--gradient-color-stop-positions"]);if(n)return r.position(n);if("%"===t.value.value[t.value.value.length-1]&&eA(t.value.value.slice(0,-1)))return r.position(t.value.value)}}}),l(t,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:Array.from({length:21},(e,t)=>"".concat(5*t,"%")),valueThemeKeys:["--gradient-color-stop-positions"]}])}x("from",{color:e=>[b(),O("--tw-sort","--tw-gradient-from"),O("--tw-gradient-from",e),O("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:e=>[b(),O("--tw-gradient-from-position",e)]}),s("via-none",[["--tw-gradient-via-stops","initial"]]),x("via",{color:e=>[b(),O("--tw-sort","--tw-gradient-via"),O("--tw-gradient-via",e),O("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),O("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:e=>[b(),O("--tw-gradient-via-position",e)]}),x("to",{color:e=>[b(),O("--tw-sort","--tw-gradient-to"),O("--tw-gradient-to",e),O("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:e=>[b(),O("--tw-gradient-to-position",e)]}),s("mask-none",[["mask-image","none"]]),i.functional("mask",e=>{var t;if(!e.value||e.modifier||"arbitrary"!==e.value.kind)return;let r=e.value.value;switch(null!=(t=e.value.dataType)?t:es(r,["image","percentage","position","bg-size","length","url"])){case"percentage":case"position":return e.modifier?void 0:[O("mask-position",r)];case"bg-size":case"length":case"size":return[O("mask-size",r)];default:return[O("mask-image",r)]}}),s("mask-add",[["mask-composite","add"]]),s("mask-subtract",[["mask-composite","subtract"]]),s("mask-intersect",[["mask-composite","intersect"]]),s("mask-exclude",[["mask-composite","exclude"]]),s("mask-alpha",[["mask-mode","alpha"]]),s("mask-luminance",[["mask-mode","luminance"]]),s("mask-match",[["mask-mode","match-source"]]),s("mask-type-alpha",[["mask-type","alpha"]]),s("mask-type-luminance",[["mask-type","luminance"]]),s("mask-auto",[["mask-size","auto"]]),s("mask-cover",[["mask-size","cover"]]),s("mask-contain",[["mask-size","contain"]]),c("mask-size",{handle(e){if(e)return[O("mask-size",e)]}}),s("mask-top",[["mask-position","top"]]),s("mask-top-left",[["mask-position","left top"]]),s("mask-top-right",[["mask-position","right top"]]),s("mask-bottom",[["mask-position","bottom"]]),s("mask-bottom-left",[["mask-position","left bottom"]]),s("mask-bottom-right",[["mask-position","right bottom"]]),s("mask-left",[["mask-position","left"]]),s("mask-right",[["mask-position","right"]]),s("mask-center",[["mask-position","center"]]),c("mask-position",{handle(e){if(e)return[O("mask-position",e)]}}),s("mask-repeat",[["mask-repeat","repeat"]]),s("mask-no-repeat",[["mask-repeat","no-repeat"]]),s("mask-repeat-x",[["mask-repeat","repeat-x"]]),s("mask-repeat-y",[["mask-repeat","repeat-y"]]),s("mask-repeat-round",[["mask-repeat","round"]]),s("mask-repeat-space",[["mask-repeat","space"]]),s("mask-clip-border",[["mask-clip","border-box"]]),s("mask-clip-padding",[["mask-clip","padding-box"]]),s("mask-clip-content",[["mask-clip","content-box"]]),s("mask-clip-fill",[["mask-clip","fill-box"]]),s("mask-clip-stroke",[["mask-clip","stroke-box"]]),s("mask-clip-view",[["mask-clip","view-box"]]),s("mask-no-clip",[["mask-clip","no-clip"]]),s("mask-origin-border",[["mask-origin","border-box"]]),s("mask-origin-padding",[["mask-origin","padding-box"]]),s("mask-origin-content",[["mask-origin","content-box"]]),s("mask-origin-fill",[["mask-origin","fill-box"]]),s("mask-origin-stroke",[["mask-origin","stroke-box"]]),s("mask-origin-view",[["mask-origin","view-box"]]);let A=()=>_([eF("--tw-mask-linear","linear-gradient(#fff, #fff)"),eF("--tw-mask-radial","linear-gradient(#fff, #fff)"),eF("--tw-mask-conic","linear-gradient(#fff, #fff)")]);function z(t,r){i.functional(t,t=>{if(t.value){if("arbitrary"===t.value.kind){var n;let a=t.value.value;switch(null!=(n=t.value.dataType)?n:es(a,["length","percentage","color"])){case"color":return null===(a=eD(a,t.modifier,e))?void 0:r.color(a);case"percentage":return t.modifier||!eA(a.slice(0,-1))?void 0:r.position(a);default:return t.modifier?void 0:r.position(a)}}{let n=eB(t,e,["--background-color","--color"]);if(n)return r.color(n)}{if(t.modifier)return;let n=es(t.value.value,["number","percentage"]);if(!n)return;switch(n){case"number":{let n=e.resolve(null,["--spacing"]);return n&&eT(t.value.value)?r.position("calc(".concat(n," * ").concat(t.value.value,")")):void 0}case"percentage":return eA(t.value.value.slice(0,-1))?r.position(t.value.value):void 0;default:return}}}}),l(t,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:Array.from({length:21},(e,t)=>"".concat(5*t,"%")),valueThemeKeys:["--gradient-color-stop-positions"]}]),l(t,()=>[{values:Array.from({length:21},(e,t)=>"".concat(5*t,"%"))},{values:e.get(["--spacing"])?eO:[]},{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))}])}let T=()=>_([eF("--tw-mask-left","linear-gradient(#fff, #fff)"),eF("--tw-mask-right","linear-gradient(#fff, #fff)"),eF("--tw-mask-bottom","linear-gradient(#fff, #fff)"),eF("--tw-mask-top","linear-gradient(#fff, #fff)")]);function j(e,t,r){z(e,{color(e){let n=[A(),T(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let a of["top","right","bottom","left"])r[a]&&(n.push(O("--tw-mask-".concat(a),"linear-gradient(to ".concat(a,", var(--tw-mask-").concat(a,"-from-color) var(--tw-mask-").concat(a,"-from-position), var(--tw-mask-").concat(a,"-to-color) var(--tw-mask-").concat(a,"-to-position))"))),n.push(_([eF("--tw-mask-".concat(a,"-from-position"),"0%"),eF("--tw-mask-".concat(a,"-to-position"),"100%"),eF("--tw-mask-".concat(a,"-from-color"),"black"),eF("--tw-mask-".concat(a,"-to-color"),"transparent")])),n.push(O("--tw-mask-".concat(a,"-").concat(t,"-color"),e)));return n},position(e){let n=[A(),T(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let a of["top","right","bottom","left"])r[a]&&(n.push(O("--tw-mask-".concat(a),"linear-gradient(to ".concat(a,", var(--tw-mask-").concat(a,"-from-color) var(--tw-mask-").concat(a,"-from-position), var(--tw-mask-").concat(a,"-to-color) var(--tw-mask-").concat(a,"-to-position))"))),n.push(_([eF("--tw-mask-".concat(a,"-from-position"),"0%"),eF("--tw-mask-".concat(a,"-to-position"),"100%"),eF("--tw-mask-".concat(a,"-from-color"),"black"),eF("--tw-mask-".concat(a,"-to-color"),"transparent")])),n.push(O("--tw-mask-".concat(a,"-").concat(t,"-position"),e)));return n}})}j("mask-x-from","from",{top:!1,right:!0,bottom:!1,left:!0}),j("mask-x-to","to",{top:!1,right:!0,bottom:!1,left:!0}),j("mask-y-from","from",{top:!0,right:!1,bottom:!0,left:!1}),j("mask-y-to","to",{top:!0,right:!1,bottom:!0,left:!1}),j("mask-t-from","from",{top:!0,right:!1,bottom:!1,left:!1}),j("mask-t-to","to",{top:!0,right:!1,bottom:!1,left:!1}),j("mask-r-from","from",{top:!1,right:!0,bottom:!1,left:!1}),j("mask-r-to","to",{top:!1,right:!0,bottom:!1,left:!1}),j("mask-b-from","from",{top:!1,right:!1,bottom:!0,left:!1}),j("mask-b-to","to",{top:!1,right:!1,bottom:!0,left:!1}),j("mask-l-from","from",{top:!1,right:!1,bottom:!1,left:!0}),j("mask-l-to","to",{top:!1,right:!1,bottom:!1,left:!0});let C=()=>_([eF("--tw-mask-linear-position","0deg"),eF("--tw-mask-linear-from-position","0%"),eF("--tw-mask-linear-to-position","100%"),eF("--tw-mask-linear-from-color","black"),eF("--tw-mask-linear-to-color","transparent")]);c("mask-linear",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue:e=>eA(e.value)?"calc(1deg * ".concat(e.value,")"):null,handleNegativeBareValue:e=>eA(e.value)?"calc(1deg * -".concat(e.value,")"):null,handle:e=>[A(),C(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"),O("--tw-mask-linear-position",e)]}),l("mask-linear",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),z("mask-linear-from",{color:e=>[A(),C(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),O("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),O("--tw-mask-linear-from-color",e)],position:e=>[A(),C(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),O("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),O("--tw-mask-linear-from-position",e)]}),z("mask-linear-to",{color:e=>[A(),C(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),O("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),O("--tw-mask-linear-to-color",e)],position:e=>[A(),C(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),O("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),O("--tw-mask-linear-to-position",e)]});let K=()=>_([eF("--tw-mask-radial-from-position","0%"),eF("--tw-mask-radial-to-position","100%"),eF("--tw-mask-radial-from-color","black"),eF("--tw-mask-radial-to-color","transparent"),eF("--tw-mask-radial-shape","ellipse"),eF("--tw-mask-radial-size","farthest-corner"),eF("--tw-mask-radial-position","center")]);s("mask-circle",[["--tw-mask-radial-shape","circle"]]),s("mask-ellipse",[["--tw-mask-radial-shape","ellipse"]]),s("mask-radial-closest-side",[["--tw-mask-radial-size","closest-side"]]),s("mask-radial-farthest-side",[["--tw-mask-radial-size","farthest-side"]]),s("mask-radial-closest-corner",[["--tw-mask-radial-size","closest-corner"]]),s("mask-radial-farthest-corner",[["--tw-mask-radial-size","farthest-corner"]]),s("mask-radial-at-top",[["--tw-mask-radial-position","top"]]),s("mask-radial-at-top-left",[["--tw-mask-radial-position","top left"]]),s("mask-radial-at-top-right",[["--tw-mask-radial-position","top right"]]),s("mask-radial-at-bottom",[["--tw-mask-radial-position","bottom"]]),s("mask-radial-at-bottom-left",[["--tw-mask-radial-position","bottom left"]]),s("mask-radial-at-bottom-right",[["--tw-mask-radial-position","bottom right"]]),s("mask-radial-at-left",[["--tw-mask-radial-position","left"]]),s("mask-radial-at-right",[["--tw-mask-radial-position","right"]]),s("mask-radial-at-center",[["--tw-mask-radial-position","center"]]),c("mask-radial-at",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:e=>[O("--tw-mask-radial-position",e)]}),c("mask-radial",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:e=>[A(),K(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"),O("--tw-mask-radial-size",e)]}),z("mask-radial-from",{color:e=>[A(),K(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),O("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),O("--tw-mask-radial-from-color",e)],position:e=>[A(),K(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),O("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),O("--tw-mask-radial-from-position",e)]}),z("mask-radial-to",{color:e=>[A(),K(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),O("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),O("--tw-mask-radial-to-color",e)],position:e=>[A(),K(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),O("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),O("--tw-mask-radial-to-position",e)]});let S=()=>_([eF("--tw-mask-conic-position","0deg"),eF("--tw-mask-conic-from-position","0%"),eF("--tw-mask-conic-to-position","100%"),eF("--tw-mask-conic-from-color","black"),eF("--tw-mask-conic-to-color","transparent")]);for(let e of(c("mask-conic",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue:e=>eA(e.value)?"calc(1deg * ".concat(e.value,")"):null,handleNegativeBareValue:e=>eA(e.value)?"calc(1deg * -".concat(e.value,")"):null,handle:e=>[A(),S(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"),O("--tw-mask-conic-position",e)]}),l("mask-conic",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),z("mask-conic-from",{color:e=>[A(),S(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),O("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),O("--tw-mask-conic-from-color",e)],position:e=>[A(),S(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),O("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),O("--tw-mask-conic-from-position",e)]}),z("mask-conic-to",{color:e=>[A(),S(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),O("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),O("--tw-mask-conic-to-color",e)],position:e=>[A(),S(),O("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),O("mask-composite","intersect"),O("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),O("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),O("--tw-mask-conic-to-position",e)]}),s("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),s("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),s("bg-clip-text",[["background-clip","text"]]),s("bg-clip-border",[["background-clip","border-box"]]),s("bg-clip-padding",[["background-clip","padding-box"]]),s("bg-clip-content",[["background-clip","content-box"]]),s("bg-origin-border",[["background-origin","border-box"]]),s("bg-origin-padding",[["background-origin","padding-box"]]),s("bg-origin-content",[["background-origin","content-box"]]),["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]))s("bg-blend-".concat(e),[["background-blend-mode",e]]),s("mix-blend-".concat(e),[["mix-blend-mode",e]]);for(let[t,r]of(s("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),s("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),s("fill-none",[["fill","none"]]),i.functional("fill",t=>{if(!t.value)return;if("arbitrary"===t.value.kind){let r=eD(t.value.value,t.modifier,e);return null===r?void 0:[O("fill",r)]}let r=eB(t,e,["--fill","--color"]);if(r)return[O("fill",r)]}),l("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))}]),s("stroke-none",[["stroke","none"]]),i.functional("stroke",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;switch(null!=(r=t.value.dataType)?r:es(n,["color","number","length","percentage"])){case"number":case"length":case"percentage":return t.modifier?void 0:[O("stroke-width",n)];default:return null===(n=eD(t.value.value,t.modifier,e))?void 0:[O("stroke",n)]}}{let r=eB(t,e,["--stroke","--color"]);if(r)return[O("stroke",r)]}{let r=e.resolve(t.value.value,["--stroke-width"]);if(r)return[O("stroke-width",r)];if(eA(t.value.value))return[O("stroke-width",t.value.value)]}}}),l("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),s("object-contain",[["object-fit","contain"]]),s("object-cover",[["object-fit","cover"]]),s("object-fill",[["object-fit","fill"]]),s("object-none",[["object-fit","none"]]),s("object-scale-down",[["object-fit","scale-down"]]),s("object-top",[["object-position","top"]]),s("object-top-left",[["object-position","left top"]]),s("object-top-right",[["object-position","right top"]]),s("object-bottom",[["object-position","bottom"]]),s("object-bottom-left",[["object-position","left bottom"]]),s("object-bottom-right",[["object-position","right bottom"]]),s("object-left",[["object-position","left"]]),s("object-right",[["object-position","right"]]),s("object-center",[["object-position","center"]]),c("object",{themeKeys:["--object-position"],handle:e=>[O("object-position",e)]}),[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]]))d(t,["--padding","--spacing"],e=>[O(r,e)]);s("text-left",[["text-align","left"]]),s("text-center",[["text-align","center"]]),s("text-right",[["text-align","right"]]),s("text-justify",[["text-align","justify"]]),s("text-start",[["text-align","start"]]),s("text-end",[["text-align","end"]]),d("indent",["--text-indent","--spacing"],e=>[O("text-indent",e)],{supportsNegative:!0}),s("align-baseline",[["vertical-align","baseline"]]),s("align-top",[["vertical-align","top"]]),s("align-middle",[["vertical-align","middle"]]),s("align-bottom",[["vertical-align","bottom"]]),s("align-text-top",[["vertical-align","text-top"]]),s("align-text-bottom",[["vertical-align","text-bottom"]]),s("align-sub",[["vertical-align","sub"]]),s("align-super",[["vertical-align","super"]]),c("align",{themeKeys:[],handle:e=>[O("vertical-align",e)]}),i.functional("font",t=>{if(!(!t.value||t.modifier)){if("arbitrary"===t.value.kind){var r;let e=t.value.value;switch(null!=(r=t.value.dataType)?r:es(e,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[O("font-family",e)];default:return[_([eF("--tw-font-weight")]),O("--tw-font-weight",e),O("font-weight",e)]}}{let r=e.resolveWith(t.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(r){let[e,t={}]=r;return[O("font-family",e),O("font-feature-settings",t["--font-feature-settings"]),O("font-variation-settings",t["--font-variation-settings"])]}}{let r=e.resolve(t.value.value,["--font-weight"]);if(r)return[_([eF("--tw-font-weight")]),O("--tw-font-weight",r),O("font-weight",r)]}}}),l("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),s("uppercase",[["text-transform","uppercase"]]),s("lowercase",[["text-transform","lowercase"]]),s("capitalize",[["text-transform","capitalize"]]),s("normal-case",[["text-transform","none"]]),s("italic",[["font-style","italic"]]),s("not-italic",[["font-style","normal"]]),s("underline",[["text-decoration-line","underline"]]),s("overline",[["text-decoration-line","overline"]]),s("line-through",[["text-decoration-line","line-through"]]),s("no-underline",[["text-decoration-line","none"]]),s("font-stretch-normal",[["font-stretch","normal"]]),s("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),s("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),s("font-stretch-condensed",[["font-stretch","condensed"]]),s("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),s("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),s("font-stretch-expanded",[["font-stretch","expanded"]]),s("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),s("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),c("font-stretch",{handleBareValue:e=>{let{value:t}=e;if(!t.endsWith("%"))return null;let r=Number(t.slice(0,-1));return!eA(r)||Number.isNaN(r)||r<50||r>200?null:t},handle:e=>[O("font-stretch",e)]}),l("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),u("placeholder",{themeKeys:["--background-color","--color"],handle:e=>[V("&::placeholder",[O("--tw-sort","placeholder-color"),O("color",e)])]}),s("decoration-solid",[["text-decoration-style","solid"]]),s("decoration-double",[["text-decoration-style","double"]]),s("decoration-dotted",[["text-decoration-style","dotted"]]),s("decoration-dashed",[["text-decoration-style","dashed"]]),s("decoration-wavy",[["text-decoration-style","wavy"]]),s("decoration-auto",[["text-decoration-thickness","auto"]]),s("decoration-from-font",[["text-decoration-thickness","from-font"]]),i.functional("decoration",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;switch(null!=(r=t.value.dataType)?r:es(n,["color","length","percentage"])){case"length":case"percentage":return t.modifier?void 0:[O("text-decoration-thickness",n)];default:return null===(n=eD(n,t.modifier,e))?void 0:[O("text-decoration-color",n)]}}{let r=e.resolve(t.value.value,["--text-decoration-thickness"]);if(r)return t.modifier?void 0:[O("text-decoration-thickness",r)];if(eA(t.value.value))return t.modifier?void 0:[O("text-decoration-thickness","".concat(t.value.value,"px"))]}{let r=eB(t,e,["--text-decoration-color","--color"]);if(r)return[O("text-decoration-color",r)]}}}),l("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),s("animate-none",[["animation","none"]]),c("animate",{themeKeys:["--animate"],handle:e=>[O("animation",e)]});{let t="var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,)",r="var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,)",n=()=>_([eF("--tw-blur"),eF("--tw-brightness"),eF("--tw-contrast"),eF("--tw-grayscale"),eF("--tw-hue-rotate"),eF("--tw-invert"),eF("--tw-opacity"),eF("--tw-saturate"),eF("--tw-sepia"),eF("--tw-drop-shadow"),eF("--tw-drop-shadow-color"),eF("--tw-drop-shadow-alpha","100%","<percentage>"),eF("--tw-drop-shadow-size")]),a=()=>_([eF("--tw-backdrop-blur"),eF("--tw-backdrop-brightness"),eF("--tw-backdrop-contrast"),eF("--tw-backdrop-grayscale"),eF("--tw-backdrop-hue-rotate"),eF("--tw-backdrop-invert"),eF("--tw-backdrop-opacity"),eF("--tw-backdrop-saturate"),eF("--tw-backdrop-sepia")]);i.functional("filter",e=>{if(!e.modifier){if(null===e.value)return[n(),O("filter",t)];if("arbitrary"===e.value.kind)return[O("filter",e.value.value)];if("none"===e.value.value)return[O("filter","none")]}}),i.functional("backdrop-filter",e=>{if(!e.modifier){if(null===e.value)return[a(),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)];if("arbitrary"===e.value.kind)return[O("-webkit-backdrop-filter",e.value.value),O("backdrop-filter",e.value.value)];if("none"===e.value.value)return[O("-webkit-backdrop-filter","none"),O("backdrop-filter","none")]}}),c("blur",{themeKeys:["--blur"],handle:e=>[n(),O("--tw-blur","blur(".concat(e,")")),O("filter",t)]}),s("blur-none",[n,["--tw-blur"," "],["filter",t]]),c("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:e=>[a(),O("--tw-backdrop-blur","blur(".concat(e,")")),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)]}),s("backdrop-blur-none",[a,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",r],["backdrop-filter",r]]),c("brightness",{themeKeys:["--brightness"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},handle:e=>[n(),O("--tw-brightness","brightness(".concat(e,")")),O("filter",t)]}),c("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},handle:e=>[a(),O("--tw-backdrop-brightness","brightness(".concat(e,")")),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)]}),l("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),l("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),c("contrast",{themeKeys:["--contrast"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},handle:e=>[n(),O("--tw-contrast","contrast(".concat(e,")")),O("filter",t)]}),c("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},handle:e=>[a(),O("--tw-backdrop-contrast","contrast(".concat(e,")")),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)]}),l("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),l("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),c("grayscale",{themeKeys:["--grayscale"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[n(),O("--tw-grayscale","grayscale(".concat(e,")")),O("filter",t)]}),c("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[a(),O("--tw-backdrop-grayscale","grayscale(".concat(e,")")),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)]}),l("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),l("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),c("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"deg"):null},handle:e=>[n(),O("--tw-hue-rotate","hue-rotate(".concat(e,")")),O("filter",t)]}),c("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"deg"):null},handle:e=>[a(),O("--tw-backdrop-hue-rotate","hue-rotate(".concat(e,")")),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)]}),l("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),l("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),c("invert",{themeKeys:["--invert"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[n(),O("--tw-invert","invert(".concat(e,")")),O("filter",t)]}),c("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[a(),O("--tw-backdrop-invert","invert(".concat(e,")")),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)]}),l("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),l("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),c("saturate",{themeKeys:["--saturate"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},handle:e=>[n(),O("--tw-saturate","saturate(".concat(e,")")),O("filter",t)]}),c("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},handle:e=>[a(),O("--tw-backdrop-saturate","saturate(".concat(e,")")),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)]}),l("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),l("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),c("sepia",{themeKeys:["--sepia"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[n(),O("--tw-sepia","sepia(".concat(e,")")),O("filter",t)]}),c("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[a(),O("--tw-backdrop-sepia","sepia(".concat(e,")")),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)]}),l("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),l("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),s("drop-shadow-none",[n,["--tw-drop-shadow"," "],["filter",t]]),i.functional("drop-shadow",r=>{let a;if(r.modifier&&("arbitrary"===r.modifier.kind?a=r.modifier.value:eA(r.modifier.value)&&(a="".concat(r.modifier.value,"%"))),!r.value){let r=e.get(["--drop-shadow"]),o=e.resolve(null,["--drop-shadow"]);return null===r||null===o?void 0:[n(),O("--tw-drop-shadow-alpha",a),...eR("--tw-drop-shadow-size",r,a,e=>"var(--tw-drop-shadow-color, ".concat(e,")")),O("--tw-drop-shadow",Z(o,",").map(e=>"drop-shadow(".concat(e,")")).join(" ")),O("filter",t)]}if("arbitrary"===r.value.kind){var o;let i=r.value.value;return(null!=(o=r.value.dataType)?o:es(i,["color"]))==="color"?null===(i=eD(i,r.modifier,e))?void 0:[n(),O("--tw-drop-shadow-color",e_(i,"var(--tw-drop-shadow-alpha)")),O("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:r.modifier&&!a?void 0:[n(),O("--tw-drop-shadow-alpha",a),...eR("--tw-drop-shadow-size",i,a,e=>"var(--tw-drop-shadow-color, ".concat(e,")")),O("--tw-drop-shadow","var(--tw-drop-shadow-size)"),O("filter",t)]}{let o=e.get(["--drop-shadow-".concat(r.value.value)]),i=e.resolve(r.value.value,["--drop-shadow"]);if(o&&i)return r.modifier&&!a?void 0:a?[n(),O("--tw-drop-shadow-alpha",a),...eR("--tw-drop-shadow-size",o,a,e=>"var(--tw-drop-shadow-color, ".concat(e,")")),O("--tw-drop-shadow","var(--tw-drop-shadow-size)"),O("filter",t)]:[n(),O("--tw-drop-shadow-alpha",a),...eR("--tw-drop-shadow-size",o,a,e=>"var(--tw-drop-shadow-color, ".concat(e,")")),O("--tw-drop-shadow",Z(i,",").map(e=>"drop-shadow(".concat(e,")")).join(" ")),O("filter",t)]}{let t=eB(r,e,["--drop-shadow-color","--color"]);if(t)return"inherit"===t?[n(),O("--tw-drop-shadow-color","inherit"),O("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:[n(),O("--tw-drop-shadow-color",e_(t,"var(--tw-drop-shadow-alpha)")),O("--tw-drop-shadow","var(--tw-drop-shadow-size)")]}}),l("drop-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--drop-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{valueThemeKeys:["--drop-shadow"]}]),c("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:e=>{let{value:t}=e;return ej(t)?"".concat(t,"%"):null},handle:e=>[a(),O("--tw-backdrop-opacity","opacity(".concat(e,")")),O("-webkit-backdrop-filter",r),O("backdrop-filter",r)]}),l("backdrop-opacity",()=>[{values:Array.from({length:21},(e,t)=>"".concat(5*t)),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let t="var(--tw-ease, ".concat(null!=(n=e.resolve(null,["--default-transition-timing-function"]))?n:"ease",")"),r="var(--tw-duration, ".concat(null!=(a=e.resolve(null,["--default-transition-duration"]))?a:"0s",")");s("transition-none",[["transition-property","none"]]),s("transition-all",[["transition-property","all"],["transition-timing-function",t],["transition-duration",r]]),s("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",t],["transition-duration",r]]),s("transition-opacity",[["transition-property","opacity"],["transition-timing-function",t],["transition-duration",r]]),s("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",t],["transition-duration",r]]),s("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",t],["transition-duration",r]]),c("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events",themeKeys:["--transition-property"],handle:e=>[O("transition-property",e),O("transition-timing-function",t),O("transition-duration",r)]}),s("transition-discrete",[["transition-behavior","allow-discrete"]]),s("transition-normal",[["transition-behavior","normal"]]),c("delay",{handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"ms"):null},themeKeys:["--transition-delay"],handle:e=>[O("transition-delay",e)]});{let t=()=>_([eF("--tw-duration")]);s("duration-initial",[t,["--tw-duration","initial"]]),i.functional("duration",r=>{var n;if(r.modifier||!r.value)return;let a=null;if("arbitrary"===r.value.kind?a=r.value.value:null===(a=e.resolve(null!=(n=r.value.fraction)?n:r.value.value,["--transition-duration"]))&&eA(r.value.value)&&(a="".concat(r.value.value,"ms")),null!==a)return[t(),O("--tw-duration",a),O("transition-duration",a)]})}l("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),l("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let e=()=>_([eF("--tw-ease")]);s("ease-initial",[e,["--tw-ease","initial"]]),s("ease-linear",[e,["--tw-ease","linear"],["transition-timing-function","linear"]]),c("ease",{themeKeys:["--ease"],handle:t=>[e(),O("--tw-ease",t),O("transition-timing-function",t)]})}s("will-change-auto",[["will-change","auto"]]),s("will-change-scroll",[["will-change","scroll-position"]]),s("will-change-contents",[["will-change","contents"]]),s("will-change-transform",[["will-change","transform"]]),c("will-change",{themeKeys:[],handle:e=>[O("will-change",e)]}),s("content-none",[["--tw-content","none"],["content","none"]]),c("content",{themeKeys:[],handle:e=>[_([eF("--tw-content",'""')]),O("--tw-content",e),O("content","var(--tw-content)")]});{let e="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",t=()=>_([eF("--tw-contain-size"),eF("--tw-contain-layout"),eF("--tw-contain-paint"),eF("--tw-contain-style")]);s("contain-none",[["contain","none"]]),s("contain-content",[["contain","content"]]),s("contain-strict",[["contain","strict"]]),s("contain-size",[t,["--tw-contain-size","size"],["contain",e]]),s("contain-inline-size",[t,["--tw-contain-size","inline-size"],["contain",e]]),s("contain-layout",[t,["--tw-contain-layout","layout"],["contain",e]]),s("contain-paint",[t,["--tw-contain-paint","paint"],["contain",e]]),s("contain-style",[t,["--tw-contain-style","style"],["contain",e]]),c("contain",{themeKeys:[],handle:e=>[O("contain",e)]})}s("forced-color-adjust-none",[["forced-color-adjust","none"]]),s("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),s("leading-none",[()=>_([eF("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),d("leading",["--leading","--spacing"],e=>[_([eF("--tw-leading")]),O("--tw-leading",e),O("line-height",e)]),c("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:e=>[_([eF("--tw-tracking")]),O("--tw-tracking",e),O("letter-spacing",e)]}),s("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),s("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let e="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",t=()=>_([eF("--tw-ordinal"),eF("--tw-slashed-zero"),eF("--tw-numeric-figure"),eF("--tw-numeric-spacing"),eF("--tw-numeric-fraction")]);s("normal-nums",[["font-variant-numeric","normal"]]),s("ordinal",[t,["--tw-ordinal","ordinal"],["font-variant-numeric",e]]),s("slashed-zero",[t,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",e]]),s("lining-nums",[t,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",e]]),s("oldstyle-nums",[t,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",e]]),s("proportional-nums",[t,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",e]]),s("tabular-nums",[t,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",e]]),s("diagonal-fractions",[t,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",e]]),s("stacked-fractions",[t,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",e]])}{let t=()=>_([eF("--tw-outline-style","solid")]);i.static("outline-hidden",()=>[O("--tw-outline-style","none"),O("outline-style","none"),E("@media","(forced-colors: active)",[O("outline","2px solid transparent"),O("outline-offset","2px")])]),s("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),s("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),s("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),s("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),s("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),i.functional("outline",r=>{var n,a;if(null===r.value){if(r.modifier)return;let a=null!=(n=e.get(["--default-outline-width"]))?n:"1px";return[t(),O("outline-style","var(--tw-outline-style)"),O("outline-width",a)]}if("arbitrary"===r.value.kind){let n=r.value.value;switch(null!=(a=r.value.dataType)?a:es(n,["color","length","number","percentage"])){case"length":case"number":case"percentage":return r.modifier?void 0:[t(),O("outline-style","var(--tw-outline-style)"),O("outline-width",n)];default:return null===(n=eD(n,r.modifier,e))?void 0:[O("outline-color",n)]}}{let t=eB(r,e,["--outline-color","--color"]);if(t)return[O("outline-color",t)]}{if(r.modifier)return;let n=e.resolve(r.value.value,["--outline-width"]);if(n)return[t(),O("outline-style","var(--tw-outline-style)"),O("outline-width",n)];if(eA(r.value.value))return[t(),O("outline-style","var(--tw-outline-style)"),O("outline-width","".concat(r.value.value,"px"))]}}),l("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),c("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"px"):null},handle:e=>[O("outline-offset",e)]}),l("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}c("opacity",{themeKeys:["--opacity"],handleBareValue:e=>{let{value:t}=e;return ej(t)?"".concat(t,"%"):null},handle:e=>[O("opacity",e)]}),l("opacity",()=>[{values:Array.from({length:21},(e,t)=>"".concat(5*t)),valueThemeKeys:["--opacity"]}]),s("underline-offset-auto",[["text-underline-offset","auto"]]),c("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:e=>{let{value:t}=e;return eA(t)?"".concat(t,"px"):null},handle:e=>[O("text-underline-offset",e)]}),l("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),i.functional("text",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;switch(null!=(r=t.value.dataType)?r:es(n,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":if(t.modifier){let r="arbitrary"===t.modifier.kind?t.modifier.value:e.resolve(t.modifier.value,["--leading"]);if(!r&&eT(t.modifier.value)){let n=e.resolve(null,["--spacing"]);if(!n)return null;r="calc(".concat(n," * ").concat(t.modifier.value,")")}return r||"none"!==t.modifier.value||(r="1"),r?[O("font-size",n),O("line-height",r)]:null}return[O("font-size",n)];default:return null===(n=eD(n,t.modifier,e))?void 0:[O("color",n)]}}{let r=eB(t,e,["--text-color","--color"]);if(r)return[O("color",r)]}{let r=e.resolveWith(t.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(r){let[n,a={}]=Array.isArray(r)?r:[r];if(t.modifier){let r="arbitrary"===t.modifier.kind?t.modifier.value:e.resolve(t.modifier.value,["--leading"]);if(!r&&eT(t.modifier.value)){let n=e.resolve(null,["--spacing"]);if(!n)return null;r="calc(".concat(n," * ").concat(t.modifier.value,")")}if(r||"none"!==t.modifier.value||(r="1"),!r)return null;let a=[O("font-size",n)];return r&&a.push(O("line-height",r)),a}return"string"==typeof a?[O("font-size",n),O("line-height",a)]:[O("font-size",n),O("line-height",a["--line-height"]?"var(--tw-leading, ".concat(a["--line-height"],")"):void 0),O("letter-spacing",a["--letter-spacing"]?"var(--tw-tracking, ".concat(a["--letter-spacing"],")"):void 0),O("font-weight",a["--font-weight"]?"var(--tw-font-weight, ".concat(a["--font-weight"],")"):void 0)]}}}}),l("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);let W=()=>_([eF("--tw-text-shadow-color"),eF("--tw-text-shadow-alpha","100%","<percentage>")]);s("text-shadow-initial",[W,["--tw-text-shadow-color","initial"]]),i.functional("text-shadow",t=>{let r;if(t.modifier&&("arbitrary"===t.modifier.kind?r=t.modifier.value:eA(t.modifier.value)&&(r="".concat(t.modifier.value,"%"))),!t.value){let t=e.get(["--text-shadow"]);return null===t?void 0:[W(),O("--tw-text-shadow-alpha",r),...eP("text-shadow",t,r,e=>"var(--tw-text-shadow-color, ".concat(e,")"))]}if("arbitrary"===t.value.kind){var n;let a=t.value.value;return(null!=(n=t.value.dataType)?n:es(a,["color"]))==="color"?null===(a=eD(a,t.modifier,e))?void 0:[W(),O("--tw-text-shadow-color",e_(a,"var(--tw-text-shadow-alpha)"))]:[W(),O("--tw-text-shadow-alpha",r),...eP("text-shadow",a,r,e=>"var(--tw-text-shadow-color, ".concat(e,")"))]}switch(t.value.value){case"none":return t.modifier?void 0:[W(),O("text-shadow","none")];case"inherit":return t.modifier?void 0:[W(),O("--tw-text-shadow-color","inherit")]}{let n=e.get(["--text-shadow-".concat(t.value.value)]);if(n)return[W(),O("--tw-text-shadow-alpha",r),...eP("text-shadow",n,r,e=>"var(--tw-text-shadow-color, ".concat(e,")"))]}{let r=eB(t,e,["--text-shadow-color","--color"]);if(r)return[W(),O("--tw-text-shadow-color",e_(r,"var(--tw-text-shadow-alpha)"))]}}),l("text-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["none"]},{valueThemeKeys:["--text-shadow"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:null!==e.get(["--text-shadow"])}]);{let t=function(e){return"var(--tw-ring-inset,) 0 0 0 calc(".concat(e," + var(--tw-ring-offset-width)) var(--tw-ring-color, ").concat(u,")")},r=function(e){return"inset 0 0 0 ".concat(e," var(--tw-inset-ring-color, currentcolor)")},n="var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)",a="0 0 #0000",c=()=>_([eF("--tw-shadow",a),eF("--tw-shadow-color"),eF("--tw-shadow-alpha","100%","<percentage>"),eF("--tw-inset-shadow",a),eF("--tw-inset-shadow-color"),eF("--tw-inset-shadow-alpha","100%","<percentage>"),eF("--tw-ring-color"),eF("--tw-ring-shadow",a),eF("--tw-inset-ring-color"),eF("--tw-inset-ring-shadow",a),eF("--tw-ring-inset"),eF("--tw-ring-offset-width","0px","<length>"),eF("--tw-ring-offset-color","#fff"),eF("--tw-ring-offset-shadow",a)]);s("shadow-initial",[c,["--tw-shadow-color","initial"]]),i.functional("shadow",t=>{let r;if(t.modifier&&("arbitrary"===t.modifier.kind?r=t.modifier.value:eA(t.modifier.value)&&(r="".concat(t.modifier.value,"%"))),!t.value){let t=e.get(["--shadow"]);return null===t?void 0:[c(),O("--tw-shadow-alpha",r),...eP("--tw-shadow",t,r,e=>"var(--tw-shadow-color, ".concat(e,")")),O("box-shadow",n)]}if("arbitrary"===t.value.kind){var o;let a=t.value.value;return(null!=(o=t.value.dataType)?o:es(a,["color"]))==="color"?null===(a=eD(a,t.modifier,e))?void 0:[c(),O("--tw-shadow-color",e_(a,"var(--tw-shadow-alpha)"))]:[c(),O("--tw-shadow-alpha",r),...eP("--tw-shadow",a,r,e=>"var(--tw-shadow-color, ".concat(e,")")),O("box-shadow",n)]}switch(t.value.value){case"none":return t.modifier?void 0:[c(),O("--tw-shadow",a),O("box-shadow",n)];case"inherit":return t.modifier?void 0:[c(),O("--tw-shadow-color","inherit")]}{let a=e.get(["--shadow-".concat(t.value.value)]);if(a)return[c(),O("--tw-shadow-alpha",r),...eP("--tw-shadow",a,r,e=>"var(--tw-shadow-color, ".concat(e,")")),O("box-shadow",n)]}{let r=eB(t,e,["--box-shadow-color","--color"]);if(r)return[c(),O("--tw-shadow-color",e_(r,"var(--tw-shadow-alpha)"))]}}),l("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["none"]},{valueThemeKeys:["--shadow"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:null!==e.get(["--shadow"])}]),s("inset-shadow-initial",[c,["--tw-inset-shadow-color","initial"]]),i.functional("inset-shadow",t=>{let r;if(t.modifier&&("arbitrary"===t.modifier.kind?r=t.modifier.value:eA(t.modifier.value)&&(r="".concat(t.modifier.value,"%"))),!t.value){let t=e.get(["--inset-shadow"]);return null===t?void 0:[c(),O("--tw-inset-shadow-alpha",r),...eP("--tw-inset-shadow",t,r,e=>"var(--tw-inset-shadow-color, ".concat(e,")")),O("box-shadow",n)]}if("arbitrary"===t.value.kind){var o;let a=t.value.value;return(null!=(o=t.value.dataType)?o:es(a,["color"]))==="color"?null===(a=eD(a,t.modifier,e))?void 0:[c(),O("--tw-inset-shadow-color",e_(a,"var(--tw-inset-shadow-alpha)"))]:[c(),O("--tw-inset-shadow-alpha",r),...eP("--tw-inset-shadow",a,r,e=>"var(--tw-inset-shadow-color, ".concat(e,")"),"inset "),O("box-shadow",n)]}switch(t.value.value){case"none":return t.modifier?void 0:[c(),O("--tw-inset-shadow",a),O("box-shadow",n)];case"inherit":return t.modifier?void 0:[c(),O("--tw-inset-shadow-color","inherit")]}{let a=e.get(["--inset-shadow-".concat(t.value.value)]);if(a)return[c(),O("--tw-inset-shadow-alpha",r),...eP("--tw-inset-shadow",a,r,e=>"var(--tw-inset-shadow-color, ".concat(e,")")),O("box-shadow",n)]}{let r=eB(t,e,["--box-shadow-color","--color"]);if(r)return[c(),O("--tw-inset-shadow-color",e_(r,"var(--tw-inset-shadow-alpha)"))]}}),l("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["none"]},{valueThemeKeys:["--inset-shadow"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:null!==e.get(["--inset-shadow"])}]),s("ring-inset",[c,["--tw-ring-inset","inset"]]);let u=null!=(o=e.get(["--default-ring-color"]))?o:"currentcolor";i.functional("ring",r=>{var a,o;if(!r.value){if(r.modifier)return;let o=null!=(a=e.get(["--default-ring-width"]))?a:"1px";return[c(),O("--tw-ring-shadow",t(o)),O("box-shadow",n)]}if("arbitrary"===r.value.kind){let a=r.value.value;return(null!=(o=r.value.dataType)?o:es(a,["color","length"]))==="length"?r.modifier?void 0:[c(),O("--tw-ring-shadow",t(a)),O("box-shadow",n)]:null===(a=eD(a,r.modifier,e))?void 0:[O("--tw-ring-color",a)]}{let t=eB(r,e,["--ring-color","--color"]);if(t)return[O("--tw-ring-color",t)]}{if(r.modifier)return;let a=e.resolve(r.value.value,["--ring-width"]);if(null===a&&eA(r.value.value)&&(a="".concat(r.value.value,"px")),a)return[c(),O("--tw-ring-shadow",t(a)),O("box-shadow",n)]}}),l("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),i.functional("inset-ring",t=>{if(!t.value)return t.modifier?void 0:[c(),O("--tw-inset-ring-shadow",r("1px")),O("box-shadow",n)];if("arbitrary"===t.value.kind){var a;let o=t.value.value;return(null!=(a=t.value.dataType)?a:es(o,["color","length"]))==="length"?t.modifier?void 0:[c(),O("--tw-inset-ring-shadow",r(o)),O("box-shadow",n)]:null===(o=eD(o,t.modifier,e))?void 0:[O("--tw-inset-ring-color",o)]}{let r=eB(t,e,["--ring-color","--color"]);if(r)return[O("--tw-inset-ring-color",r)]}{if(t.modifier)return;let a=e.resolve(t.value.value,["--ring-width"]);if(null===a&&eA(t.value.value)&&(a="".concat(t.value.value,"px")),a)return[c(),O("--tw-inset-ring-shadow",r(a)),O("box-shadow",n)]}}),l("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let d="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";i.functional("ring-offset",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;return(null!=(r=t.value.dataType)?r:es(n,["color","length"]))==="length"?t.modifier?void 0:[O("--tw-ring-offset-width",n),O("--tw-ring-offset-shadow",d)]:null===(n=eD(n,t.modifier,e))?void 0:[O("--tw-ring-offset-color",n)]}{let r=e.resolve(t.value.value,["--ring-offset-width"]);if(r)return t.modifier?void 0:[O("--tw-ring-offset-width",r),O("--tw-ring-offset-shadow",d)];if(eA(t.value.value))return t.modifier?void 0:[O("--tw-ring-offset-width","".concat(t.value.value,"px")),O("--tw-ring-offset-shadow",d)]}{let r=eB(t,e,["--ring-offset-color","--color"]);if(r)return[O("--tw-ring-offset-color",r)]}}})}return l("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),i.functional("@container",e=>{let t=null;if(null===e.value?t="inline-size":"arbitrary"===e.value.kind?t=e.value.value:"named"===e.value.kind&&"normal"===e.value.value&&(t="normal"),null!==t)return e.modifier?[O("container-type",t),O("container-name",e.modifier.value)]:[O("container-type",t)]}),l("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),i}(h),r=function(e){let t=new eX;function r(e,r){let{compounds:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};n=null!=n?n:eQ(r),t.static(e,e=>{e.nodes=r.map(t=>N(t,e.nodes))},{compounds:n})}r("*",[":is(& > *)"],{compounds:0}),r("**",[":is(& *)"],{compounds:0});let n=["@media","@supports","@container"];t.compound("not",3,(e,t)=>{if("arbitrary"===t.variant.kind&&t.variant.relative||t.modifier)return null;let r=!1;if(U([e],(t,a)=>{let{path:o}=a;if("rule"!==t.kind&&"at-rule"!==t.kind||t.nodes.length>0)return 0;let i=[],l=[];for(let e of o)"at-rule"===e.kind?i.push(e):"rule"===e.kind&&l.push(e);if(i.length>1||l.length>1)return 2;let s=[];for(let e of l){var c;let t=(c=e.selector).includes("::")?null:"&:not(".concat(Z(c,",").map(e=>e=e.replaceAll("&","*")).join(", "),")");if(!t)return r=!1,2;s.push(V(t,[]))}for(let e of i){let t=function(e){for(let t of n){if(t!==e.name)continue;let r=Z(e.params,",");return r.length>1?null:(r=function(e,t){return t.map(t=>{let r=Z(t=t.trim()," ");return"not"===r[0]?r.slice(1).join(" "):"@container"===e?"("===r[0][0]?"not ".concat(t):"not"===r[1]?"".concat(r[0]," ").concat(r.slice(2).join(" ")):"".concat(r[0]," not ").concat(r.slice(1).join(" ")):"not ".concat(t)})}(e.name,r),E(e.name,r.join(", ")))}return null}(e);if(!t)return r=!1,2;s.push(t)}return Object.assign(e,V("&",s)),r=!0,1}),"rule"===e.kind&&"&"===e.selector&&1===e.nodes.length&&Object.assign(e,e.nodes[0]),!r)return null}),t.suggest("not",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("not",e))),t.compound("group",2,(t,r)=>{if("arbitrary"===r.variant.kind&&r.variant.relative)return null;let n=r.modifier?":where(.".concat(e.prefix?"".concat(e.prefix,"\\:"):"","group\\/").concat(r.modifier.value,")"):":where(.".concat(e.prefix?"".concat(e.prefix,"\\:"):"","group)"),a=!1;if(U([t],(e,t)=>{let{path:r}=t;if("rule"!==e.kind)return 0;for(let e of r.slice(0,-1))if("rule"===e.kind)return a=!1,2;let o=e.selector.replaceAll("&",n);Z(o,",").length>1&&(o=":is(".concat(o,")")),e.selector="&:is(".concat(o," *)"),a=!0}),!a)return null}),t.suggest("group",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("group",e))),t.compound("peer",2,(t,r)=>{if("arbitrary"===r.variant.kind&&r.variant.relative)return null;let n=r.modifier?":where(.".concat(e.prefix?"".concat(e.prefix,"\\:"):"","peer\\/").concat(r.modifier.value,")"):":where(.".concat(e.prefix?"".concat(e.prefix,"\\:"):"","peer)"),a=!1;if(U([t],(e,t)=>{let{path:r}=t;if("rule"!==e.kind)return 0;for(let e of r.slice(0,-1))if("rule"===e.kind)return a=!1,2;let o=e.selector.replaceAll("&",n);Z(o,",").length>1&&(o=":is(".concat(o,")")),e.selector="&:is(".concat(o," ~ *)"),a=!0}),!a)return null}),t.suggest("peer",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("peer",e))),r("first-letter",["&::first-letter"]),r("first-line",["&::first-line"]),r("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),r("selection",["& *::selection","&::selection"]),r("file",["&::file-selector-button"]),r("placeholder",["&::placeholder"]),r("backdrop",["&::backdrop"]),r("details-content",["&::details-content"]);{let e=function(){return _([E("@property","--tw-content",[O("syntax",'"*"'),O("initial-value",'""'),O("inherits","false")])])};t.static("before",t=>{t.nodes=[V("&::before",[e(),O("content","var(--tw-content)"),...t.nodes])]},{compounds:0}),t.static("after",t=>{t.nodes=[V("&::after",[e(),O("content","var(--tw-content)"),...t.nodes])]},{compounds:0})}r("first",["&:first-child"]),r("last",["&:last-child"]),r("only",["&:only-child"]),r("odd",["&:nth-child(odd)"]),r("even",["&:nth-child(even)"]),r("first-of-type",["&:first-of-type"]),r("last-of-type",["&:last-of-type"]),r("only-of-type",["&:only-of-type"]),r("visited",["&:visited"]),r("target",["&:target"]),r("open",["&:is([open], :popover-open, :open)"]),r("default",["&:default"]),r("checked",["&:checked"]),r("indeterminate",["&:indeterminate"]),r("placeholder-shown",["&:placeholder-shown"]),r("autofill",["&:autofill"]),r("optional",["&:optional"]),r("required",["&:required"]),r("valid",["&:valid"]),r("invalid",["&:invalid"]),r("user-valid",["&:user-valid"]),r("user-invalid",["&:user-invalid"]),r("in-range",["&:in-range"]),r("out-of-range",["&:out-of-range"]),r("read-only",["&:read-only"]),r("empty",["&:empty"]),r("focus-within",["&:focus-within"]),t.static("hover",e=>{e.nodes=[V("&:hover",[E("@media","(hover: hover)",e.nodes)])]}),r("focus",["&:focus"]),r("focus-visible",["&:focus-visible"]),r("active",["&:active"]),r("enabled",["&:enabled"]),r("disabled",["&:disabled"]),r("inert",["&:is([inert], [inert] *)"]),t.compound("in",2,(e,t)=>{if(t.modifier)return null;let r=!1;if(U([e],(e,t)=>{let{path:n}=t;if("rule"!==e.kind)return 0;for(let e of n.slice(0,-1))if("rule"===e.kind)return r=!1,2;e.selector=":where(".concat(e.selector.replaceAll("&","*"),") &"),r=!0}),!r)return null}),t.suggest("in",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("in",e))),t.compound("has",2,(e,t)=>{if(t.modifier)return null;let r=!1;if(U([e],(e,t)=>{let{path:n}=t;if("rule"!==e.kind)return 0;for(let e of n.slice(0,-1))if("rule"===e.kind)return r=!1,2;e.selector="&:has(".concat(e.selector.replaceAll("&","*"),")"),r=!0}),!r)return null}),t.suggest("has",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("has",e))),t.functional("aria",(e,t)=>{if(!t.value||t.modifier)return null;"arbitrary"===t.value.kind?e.nodes=[V("&[aria-".concat(e0(t.value.value),"]"),e.nodes)]:e.nodes=[V("&[aria-".concat(t.value.value,'="true"]'),e.nodes)]}),t.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),t.functional("data",(e,t)=>{if(!t.value||t.modifier)return null;e.nodes=[V("&[data-".concat(e0(t.value.value),"]"),e.nodes)]}),t.functional("nth",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!eA(t.value.value))return null;e.nodes=[V("&:nth-child(".concat(t.value.value,")"),e.nodes)]}),t.functional("nth-last",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!eA(t.value.value))return null;e.nodes=[V("&:nth-last-child(".concat(t.value.value,")"),e.nodes)]}),t.functional("nth-of-type",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!eA(t.value.value))return null;e.nodes=[V("&:nth-of-type(".concat(t.value.value,")"),e.nodes)]}),t.functional("nth-last-of-type",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!eA(t.value.value))return null;e.nodes=[V("&:nth-last-of-type(".concat(t.value.value,")"),e.nodes)]}),t.functional("supports",(e,t)=>{if(!t.value||t.modifier)return null;let r=t.value.value;if(null===r)return null;if(/^[\w-]*\s*\(/.test(r)){e.nodes=[E("@supports",r.replace(/\b(and|or|not)\b/g," $1 "),e.nodes)];return}r.includes(":")||(r="".concat(r,": var(--tw)")),("("!==r[0]||")"!==r[r.length-1])&&(r="(".concat(r,")")),e.nodes=[E("@supports",r,e.nodes)]},{compounds:1}),r("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),r("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),r("contrast-more",["@media (prefers-contrast: more)"]),r("contrast-less",["@media (prefers-contrast: less)"]);{let r=function(e,t,r,n){if(e===t)return 0;let a=n.get(e);if(null===a)return"asc"===r?-1:1;let o=n.get(t);return null===o?"asc"===r?1:-1:ea(a,o,r)};{let n=e.namespace("--breakpoint"),a=new z(t=>{switch(t.kind){case"static":var r;return null!=(r=e.resolveValue(t.root,["--breakpoint"]))?r:null;case"functional":{if(!t.value||t.modifier)return null;let r=null;return"arbitrary"===t.value.kind?r=t.value.value:"named"===t.value.kind&&(r=e.resolveValue(t.value.value,["--breakpoint"])),!r||r.includes("var(")?null:r}case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("max",(e,t)=>{if(t.modifier)return null;let r=a.get(t);if(null===r)return null;e.nodes=[E("@media","(width < ".concat(r,")"),e.nodes)]},{compounds:1})},(e,t)=>r(e,t,"desc",a)),t.suggest("max",()=>Array.from(n.keys()).filter(e=>null!==e)),t.group(()=>{for(let[r,n]of e.namespace("--breakpoint"))null!==r&&t.static(r,e=>{e.nodes=[E("@media","(width >= ".concat(n,")"),e.nodes)]},{compounds:1});t.functional("min",(e,t)=>{if(t.modifier)return null;let r=a.get(t);if(null===r)return null;e.nodes=[E("@media","(width >= ".concat(r,")"),e.nodes)]},{compounds:1})},(e,t)=>r(e,t,"asc",a)),t.suggest("min",()=>Array.from(n.keys()).filter(e=>null!==e))}{let n=e.namespace("--container"),a=new z(t=>{switch(t.kind){case"functional":{if(null===t.value)return null;let r=null;return"arbitrary"===t.value.kind?r=t.value.value:"named"===t.value.kind&&(r=e.resolveValue(t.value.value,["--container"])),!r||r.includes("var(")?null:r}case"static":case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("@max",(e,t)=>{let r=a.get(t);if(null===r)return null;e.nodes=[E("@container",t.modifier?"".concat(t.modifier.value," (width < ").concat(r,")"):"(width < ".concat(r,")"),e.nodes)]},{compounds:1})},(e,t)=>r(e,t,"desc",a)),t.suggest("@max",()=>Array.from(n.keys()).filter(e=>null!==e)),t.group(()=>{t.functional("@",(e,t)=>{let r=a.get(t);if(null===r)return null;e.nodes=[E("@container",t.modifier?"".concat(t.modifier.value," (width >= ").concat(r,")"):"(width >= ".concat(r,")"),e.nodes)]},{compounds:1}),t.functional("@min",(e,t)=>{let r=a.get(t);if(null===r)return null;e.nodes=[E("@container",t.modifier?"".concat(t.modifier.value," (width >= ").concat(r,")"):"(width >= ".concat(r,")"),e.nodes)]},{compounds:1})},(e,t)=>r(e,t,"asc",a)),t.suggest("@min",()=>Array.from(n.keys()).filter(e=>null!==e)),t.suggest("@",()=>Array.from(n.keys()).filter(e=>null!==e))}}return r("portrait",["@media (orientation: portrait)"]),r("landscape",["@media (orientation: landscape)"]),r("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),r("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),r("dark",["@media (prefers-color-scheme: dark)"]),r("starting",["@starting-style"]),r("print",["@media print"]),r("forced-colors",["@media (forced-colors: active)"]),r("inverted-colors",["@media (inverted-colors: inverted)"]),r("pointer-none",["@media (pointer: none)"]),r("pointer-coarse",["@media (pointer: coarse)"]),r("pointer-fine",["@media (pointer: fine)"]),r("any-pointer-none",["@media (any-pointer: none)"]),r("any-pointer-coarse",["@media (any-pointer: coarse)"]),r("any-pointer-fine",["@media (any-pointer: fine)"]),r("noscript",["@media (scripting: none)"]),t}(h),n=new z(e=>(function(e,t){if("["===e[0]&&"]"===e[e.length-1]){if("@"===e[1]&&e.includes("&"))return null;let t=P(e.slice(1,-1));if(!$(t)||0===t.length||0===t.trim().length)return null;let r=">"===t[0]||"+"===t[0]||"~"===t[0];return r||"@"===t[0]||t.includes("&")||(t="&:is(".concat(t,")")),{kind:"arbitrary",selector:t,relative:r}}{let[r,n=null,a]=Z(e,"/");if(a)return null;for(let[e,a]of J(r,e=>t.variants.has(e)))switch(t.variants.kind(e)){case"static":return null!==a||null!==n?null:{kind:"static",root:e};case"functional":{let t=null===n?null:Y(n);if(null!==n&&null===t)return null;if(null===a)return{kind:"functional",root:e,modifier:t,value:null};if("]"===a[a.length-1]){if("["!==a[0])continue;let r=P(a.slice(1,-1));return $(r)&&0!==r.length&&0!==r.trim().length?{kind:"functional",root:e,modifier:t,value:{kind:"arbitrary",value:r}}:null}if(")"===a[a.length-1]){if("("!==a[0])continue;let r=P(a.slice(1,-1));return $(r)&&0!==r.length&&0!==r.trim().length&&"-"===r[0]&&"-"===r[1]?{kind:"functional",root:e,modifier:t,value:{kind:"arbitrary",value:"var(".concat(r,")")}}:null}return{kind:"functional",root:e,modifier:t,value:{kind:"named",value:a}}}case"compound":{if(null===a)return null;let r=t.parseVariant(a);if(null===r||!t.variants.compoundsWith(e,r))return null;let o=null===n?null:Y(n);return null!==n&&null===o?null:{kind:"compound",root:e,modifier:o,variant:r}}}}return null})(e,l)),a=new z(e=>Array.from(function*(e,t){let r,n=Z(e,":");if(t.theme.prefix){if(1===n.length||n[0]!==t.theme.prefix)return null;n.shift()}let a=n.pop(),o=[];for(let e=n.length-1;e>=0;--e){let r=t.parseVariant(n[e]);if(null===r)return;o.push(r)}let i=!1;"!"===a[a.length-1]?(i=!0,a=a.slice(0,-1)):"!"===a[0]&&(i=!0,a=a.slice(1)),t.utilities.has(a,"static")&&!a.includes("[")&&(yield{kind:"static",root:a,variants:o,important:i,raw:e});let[l,s=null,c]=Z(a,"/");if(c)return;let u=null===s?null:Y(s);if(null===s||null!==u){if("["===l[0]){if("]"!==l[l.length-1])return;let t=l.charCodeAt(1);if(45!==t&&!(t>=97&&t<=122))return;let r=(l=l.slice(1,-1)).indexOf(":");if(-1===r||0===r||r===l.length-1)return;let n=l.slice(0,r),a=P(l.slice(r+1));if(!$(a))return;yield{kind:"arbitrary",property:n,value:a,modifier:u,variants:o,important:i,raw:e};return}if("]"===l[l.length-1]){let e=l.indexOf("-[");if(-1===e)return;let n=l.slice(0,e);if(!t.utilities.has(n,"functional"))return;r=[[n,l.slice(e+1)]]}else if(")"===l[l.length-1]){let e=l.indexOf("-(");if(-1===e)return;let n=l.slice(0,e);if(!t.utilities.has(n,"functional"))return;let a=l.slice(e+2,-1),o=Z(a,":"),i=null;if(2===o.length&&(i=o[0],a=o[1]),"-"!==a[0]||"-"!==a[1]||!$(a))return;r=[[n,null===i?"[var(".concat(a,")]"):"[".concat(i,":var(").concat(a,")]")]]}else r=J(l,e=>t.utilities.has(e,"functional"));for(let[t,n]of r){let r={kind:"functional",root:t,modifier:u,value:null,variants:o,important:i,raw:e};if(null===n){yield r;continue}{let e=n.indexOf("[");if(-1!==e){if("]"!==n[n.length-1])return;let t=P(n.slice(e+1,-1));if(!$(t))continue;let a="";for(let e=0;e<t.length;e++){let r=t.charCodeAt(e);if(58===r){a=t.slice(0,e),t=t.slice(e+1);break}if(!(45===r||r>=97&&r<=122))break}if(0===t.length||0===t.trim().length)continue;r.value={kind:"arbitrary",dataType:a||null,value:t}}else{var d;let e=null===s||(null==(d=r.modifier)?void 0:d.kind)==="arbitrary"?null:"".concat(n,"/").concat(s);r.value={kind:"named",value:n,fraction:e}}}yield r}}}(e,l))),o=new z(e=>new z(t=>{let r=function(e,t,r){let n=function(e,t){var r;if("arbitrary"===e.kind){let r=e.value;return e.modifier&&(r=eD(r,e.modifier,t.theme)),null===r?[]:[[O(e.property,r)]]}let n=null!=(r=t.utilities.get(e.root))?r:[],a=[];for(let t of n.filter(e=>!e6(e))){if(t.kind!==e.kind)continue;let r=t.compileFn(e);if(void 0!==r){if(null===r)return a;a.push(r)}}if(a.length>0)return a;for(let t of n.filter(e=>e6(e))){if(t.kind!==e.kind)continue;let r=t.compileFn(e);if(void 0!==r){if(null===r)return a;a.push(r)}}return a}(e,t);if(0===n.length)return[];let a=t.important&&!!(1&r),o=[],i=".".concat(g(e.raw));for(let r of n){let n=function(e){let t=new Set,r=0,n=e.slice(),a=!1;for(;n.length>0;){let e=n.shift();if("declaration"===e.kind){if(void 0===e.value||(r++,a))continue;if("--tw-sort"===e.property){var o;let r=e2.indexOf(null!=(o=e.value)?o:"");if(-1!==r){t.add(r),a=!0;continue}}let n=e2.indexOf(e.property);-1!==n&&t.add(n)}else if("rule"===e.kind||"at-rule"===e.kind)for(let t of e.nodes)n.push(t)}return{order:Array.from(t).sort((e,t)=>e-t),count:r}}(r);(e.important||a)&&function e(t){for(let r of t)"at-root"!==r.kind&&("declaration"===r.kind?r.important=!0:("rule"===r.kind||"at-rule"===r.kind)&&e(r.nodes))}(r);let l={kind:"rule",selector:i,nodes:r};for(let r of e.variants)if(null===e3(l,r,t.variants))return[];o.push({node:l,propertySort:n})}return o}(t,l,e);try{eH(r.map(e=>{let{node:t}=e;return t}),l)}catch(e){return[]}return r})),i=new z(e=>{for(let t of S(e))h.markUsedVariable(t)}),l={theme:h,utilities:t,variants:r,invalidCandidates:new Set,important:!1,candidatesToCss(e){let t=[];for(let r of e){let e=!1,{astNodes:n}=e5([r],this,{onInvalidCandidate(){e=!0}});0===(n=D(n,l,0)).length||e?t.push(null):t.push(B(n))}return t},getClassOrder(e){return function(e,t){let{astNodes:r,nodeSorting:n}=e5(Array.from(t),e),a=new Map(t.map(e=>[e,null])),o=0n;for(let e of r){var i,l;let t=null==(i=n.get(e))?void 0:i.candidate;t&&a.set(t,null!=(l=a.get(t))?l:o++)}return t.map(e=>{var t;return[e,null!=(t=a.get(e))?t:null]})}(this,e)},getClassList(){return function(e){let t=new z(e=>({name:e,utility:e,fraction:!1,modifiers:[]}));for(let r of e.utilities.keys("static")){let e=t.get(r);e.fraction=!1,e.modifiers=[]}for(let r of e.utilities.keys("functional"))for(let n of e.utilities.getCompletions(r))for(let e of n.values){let a=null!==e&&eJ.test(e),o=null===e?r:"".concat(r,"-").concat(e),i=t.get(o);if(i.utility=r,i.fraction||(i.fraction=a),i.modifiers.push(...n.modifiers),n.supportsNegative){let e=t.get("-".concat(o));e.utility="-".concat(r),e.fraction||(e.fraction=a),e.modifiers.push(...n.modifiers)}}if(0===t.size)return[];let r=Array.from(t.values());return r.sort((e,t)=>eY(e.name,t.name)),function(e){let t=[],r=null,n=new Map,a=new z(()=>[]);for(let o of e){let{utility:e,fraction:i}=o;r||(r={utility:e,items:[]},n.set(e,r)),e!==r.utility&&(t.push(r),r={utility:e,items:[]},n.set(e,r)),i?a.get(e).push(o):r.items.push(o)}for(let[e,o]of(r&&t[t.length-1]!==r&&t.push(r),a)){let t=n.get(e);t&&t.items.push(...o)}let o=[];for(let e of t)for(let t of e.items)o.push([t.name,{modifiers:t.modifiers}]);return o}(r)}(this)},getVariants(){return function(e){let t=[];for(let[r,n]of e.variants.entries()){let a=function(){let{value:t,modifier:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=r;t&&(a+=o?"-".concat(t):t),n&&(a+="/".concat(n));let i=e.parseVariant(a);if(!i)return[];let l=V(".__placeholder__",[]);if(null===e3(l,i,e.variants))return[];let s=[];return function e(t,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};for(let i=0;i<t.length;i++){var o;let l=t[i],s=null!=(o=n[n.length-1])?o:null;if("rule"===l.kind||"at-rule"===l.kind)n.push(l),e(l.nodes,r,n,a),n.pop();else if("context"===l.kind){e(l.nodes,r,n,{...a,...l.context});continue}n.push(l),r(l,{parent:s,context:a,path:n,replaceWith(e){Array.isArray(e)?0===e.length?t.splice(i,1):1===e.length?t[i]=e[0]:t.splice(i,1,...e):t[i]=e,i+=e.length-1}}),n.pop()}}(l.nodes,(e,t)=>{let{path:r}=t;if("rule"!==e.kind&&"at-rule"!==e.kind||e.nodes.length>0)return;r.sort((e,t)=>{let r="at-rule"===e.kind,n="at-rule"===t.kind;return r&&!n?-1:!r&&n?1:0});let n=r.flatMap(e=>"rule"===e.kind?"&"===e.selector?[]:[e.selector]:"at-rule"===e.kind?["".concat(e.name," ").concat(e.params)]:[]),a="";for(let e=n.length-1;e>=0;e--)a=""===a?n[e]:"".concat(n[e]," { ").concat(a," }");s.push(a)}),s};if("arbitrary"===n.kind)continue;let o="@"!==r,i=e.variants.getCompletions(r);switch(n.kind){case"static":t.push({name:r,values:i,isArbitrary:!1,hasDash:o,selectors:a});break;case"functional":case"compound":t.push({name:r,values:i,isArbitrary:!0,hasDash:o,selectors:a})}}return t}(this)},parseCandidate:e=>a.get(e),parseVariant:e=>n.get(e),compileAstNodes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return o.get(t).get(e)},printCandidate:e=>(function(e,t){let r=[];for(let e of t.variants)r.unshift(X(e));e.theme.prefix&&r.unshift(e.theme.prefix);let n="";if("static"===t.kind&&(n+=t.root),"functional"===t.kind&&(n+=t.root,t.value))if("arbitrary"===t.value.kind){if(null!==t.value){var a;let e=(a=t.value.value,er.get(a)),r=e?t.value.value.slice(4,-1):t.value.value,[o,i]=e?["(",")"]:["[","]"];t.value.dataType?n+="-".concat(o).concat(t.value.dataType,":").concat(ee(r)).concat(i):n+="-".concat(o).concat(ee(r)).concat(i)}}else"named"===t.value.kind&&(n+="-".concat(t.value.value));return"arbitrary"===t.kind&&(n+="[".concat(t.property,":").concat(ee(t.value),"]")),("arbitrary"===t.kind||"functional"===t.kind)&&(n+=G(t.modifier)),t.important&&(n+="!"),r.push(n),r.join(":")})(l,e),printVariant:e=>X(e),getVariantOrder(){let e=Array.from(n.values());e.sort((e,t)=>this.variants.compare(e,t));let t=new Map,r,a=0;for(let n of e)null!==n&&(void 0!==r&&0!==this.variants.compare(r,n)&&a++,t.set(n,a),r=n);return t},resolveThemeValue(e){var t;let r=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=e.lastIndexOf("/"),a=null;-1!==n&&(a=e.slice(n+1).trim(),e=e.slice(0,n).trim());let o=null!=(t=h.resolve(null,[e],+!!r))?t:void 0;return a&&o?e_(o,a):o},trackUsedVariables(e){i.get(e)}});if(p&&(R.important=p),M.length>0)for(let e of M)R.invalidCandidates.add(e);for(let t of(f|=await tN({designSystem:R,base:s,ast:e,loadModule:u,sources:W}),m))t(R);for(let e of v)e(R);if(b){let t=[];for(let[e,r]of R.theme.entries()){if(2&r.options)continue;let n=O(g(e),r.value);n.src=r.src,t.push(n)}for(let t of R.theme.getKeyframes())e.push(F({theme:!0},[_([t])]));b.nodes=[F({theme:!0},t)]}if(T.length>0){for(let e of T){let t=V("&",e.nodes),r=e.params,n=R.parseVariant(r);if(null===n)throw Error("Cannot use `@variant` with unknown variant: ".concat(r));if(null===e3(t,n,R.variants))throw Error("Cannot use `@variant` with variant: ".concat(r));Object.assign(e,t)}f|=32}if(f|=eH(e,R),f|=e4(e,R),x){let e=x;e.kind="context",e.context={}}return U(e,(e,t)=>{let{replaceWith:r}=t;if("at-rule"===e.kind)return"@utility"===e.name&&r([]),1}),{designSystem:R,ast:e,sources:W,root:I,utilitiesNode:x,features:f,inlineCandidates:L}}async function tM(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{designSystem:r,ast:n,sources:a,root:o,utilitiesNode:i,features:l,inlineCandidates:s}=await tL(e,t);function c(e){r.invalidCandidates.add(e)}n.unshift(W("! tailwindcss v".concat("4.1.13"," | MIT License | https://tailwindcss.com ")));let u=new Set,d=null,f=0,p=!1;for(let e of s)r.invalidCandidates.has(e)||(u.add(e),p=!0);return{sources:a,root:o,features:l,build(a){if(0===l)return e;if(!i)return null!=d||(d=D(n,r,t.polyfills)),d;let o=p,s=!1;p=!1;let h=u.size;for(let e of a)if(!r.invalidCandidates.has(e))if("-"===e[0]&&"-"===e[1]){let t=r.theme.markUsedVariable(e);o||(o=t),s||(s=t)}else u.add(e),o||(o=u.size!==h);if(!o)return null!=d||(d=D(n,r,t.polyfills)),d;let m=e5(u,r,{onInvalidCandidate:c}).astNodes;return t.from&&U(m,e=>{null!=e.src||(e.src=i.src)}),s||f!==m.length?(f=m.length,i.nodes=m,d=D(n,r,t.polyfills)):(null!=d||(d=D(n,r,t.polyfills)),d)}}}async function tI(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=p(e,{from:t.from}),n=await tM(r,t),a=r,o=e;return{...n,build(e){let r=n.build(e);return r===a||(o=B(r,!!t.from),a=r),o},buildSourceMap:()=>(function(e){let{ast:t}=e,r=new z(e=>(function(e){let t=[0];for(let r=0;r<e.length;r++)10===e.charCodeAt(r)&&t.push(r+1);return{find:function(e){let r=0,n=t.length;for(;n>0;){let a=(0|n)>>1,o=r+a;t[o]<=e?(r=o+1,n=n-a-1):n=a}let a=e-t[r-=1];return{line:r+1,column:a}},findOffset:function(e){var r;let{line:n,column:a}=e;n-=1,n=Math.min(Math.max(n,0),t.length-1);let o=t[n],i=null!=(r=t[n+1])?r:o;return Math.min(Math.max(o+a,0),i)}}})(e.code)),n=new z(e=>({url:e.file,content:e.code,ignore:!1})),a={file:null,sources:[],mappings:[]};for(let e of(U(t,e=>{if(!e.src||!e.dst)return;let t=n.get(e.src[0]);if(!t.content)return;let o=r.get(e.src[0]),i=r.get(e.dst[0]),l=t.content.slice(e.src[1],e.src[2]),s=0;for(let r of l.split("\n")){if(""!==r.trim()){let r=o.find(e.src[1]+s),n=i.find(e.dst[1]);a.mappings.push({name:null,originalPosition:{source:t,...r},generatedPosition:n})}s+=r.length,s+=1}let c=o.find(e.src[2]),u=i.find(e.dst[2]);a.mappings.push({name:null,originalPosition:{source:t,...c},generatedPosition:u})}),r.keys()))a.sources.push(n.get(e));return a.mappings.sort((e,t)=>{var r,n,a,o,i,l,s,c;return e.generatedPosition.line-t.generatedPosition.line||e.generatedPosition.column-t.generatedPosition.column||(null!=(i=null==(r=e.originalPosition)?void 0:r.line)?i:0)-(null!=(l=null==(n=t.originalPosition)?void 0:n.line)?l:0)||(null!=(s=null==(a=e.originalPosition)?void 0:a.column)?s:0)-(null!=(c=null==(o=t.originalPosition)?void 0:o.column)?c:0)}),a})({ast:a})}}async function tP(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(await tL(p(e),t)).designSystem}function tR(){throw Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}for(let e in f)"default"!==e&&(tR[e]=f[e]);function tq(e){return this.prefix?"--".concat(e.slice(3+this.prefix.length)):e}function t$(e,t){for(let r of t){let t=null!==e?"".concat(r,"-").concat(e):r;if(!this.values.has(t)){if(!(null!==e&&e.includes(".")))continue;else if(t="".concat(r,"-").concat(e.replaceAll(".","_")),!this.values.has(t))continue}if(!x(t,r))return t}return null}function tH(e){let t=this.values.get(e);if(!t)return null;let r=null;return 2&t.options&&(r=t.value),"var(".concat(g(this.prefixKey(e))).concat(r?", ".concat(r):"",")")}t.exports=tR},74539,e=>{"use strict";function t(e,t,r){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return r}function r(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.add(e)}function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}e.s(["Features",()=>tW,"Polyfills",()=>tE,"__unstable__loadDesignSystem",()=>tD,"compile",()=>tU,"compileAst",()=>t_,"default",()=>tB],74539),e.s([],54672);var a,o,i,l={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}},s=new Set(["black","silver","gray","white","maroon","red","purple","fuchsia","green","lime","olive","yellow","navy","blue","teal","aqua","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen","transparent","currentcolor","canvas","canvastext","linktext","visitedtext","activetext","buttonface","buttontext","buttonborder","field","fieldtext","highlight","highlighttext","selecteditem","selecteditemtext","mark","marktext","graytext","accentcolor","accentcolortext"]),c=/^(rgba?|hsla?|hwb|color|(ok)?(lab|lch)|light-dark|color-mix)\(/i,u=["calc","min","max","clamp","mod","rem","sin","cos","tan","asin","acos","atan","atan2","pow","sqrt","hypot","log","exp","round"];function d(e){return -1!==e.indexOf("(")&&u.some(t=>e.includes("".concat(t,"(")))}var f=new Uint8Array(256);function p(e,t){let r=0,n=[],a=0,o=e.length,i=t.charCodeAt(0);for(let t=0;t<o;t++){let l=e.charCodeAt(t);if(0===r&&l===i){n.push(e.slice(a,t)),a=t+1;continue}switch(l){case 92:t+=1;break;case 39:case 34:for(;++t<o;){let r=e.charCodeAt(t);if(92===r){t+=1;continue}if(r===l)break}break;case 40:f[r]=41,r++;break;case 91:f[r]=93,r++;break;case 123:f[r]=125,r++;break;case 93:case 125:case 41:r>0&&l===f[r-1]&&r--}}return n.push(e.slice(a)),n}var h={color:function(e){return 35===e.charCodeAt(0)||c.test(e)||s.has(e.toLowerCase())},length:S,percentage:j,ratio:function(e){return C.test(e)||d(e)},number:z,integer:N,url:g,position:function(e){let t=0;for(let r of p(e," ")){if("center"===r||"top"===r||"right"===r||"bottom"===r||"left"===r){t+=1;continue}if(!r.startsWith("var(")){if(S(r)||j(r)){t+=1;continue}return!1}}return t>0},"bg-size":function(e){let t=0;for(let r of p(e,",")){if("cover"===r||"contain"===r){t+=1;continue}let e=p(r," ");if(1!==e.length&&2!==e.length)return!1;if(e.every(e=>"auto"===e||S(e)||j(e))){t+=1;continue}}return t>0},"line-width":function(e){return p(e," ").every(e=>S(e)||z(e)||"thin"===e||"medium"===e||"thick"===e)},image:function(e){let t=0;for(let r of p(e,","))if(!r.startsWith("var(")){if(g(r)||b.test(r)||w.test(r)){t+=1;continue}return!1}return t>0},"family-name":function(e){let t=0;for(let r of p(e,",")){let e=r.charCodeAt(0);if(e>=48&&e<=57)return!1;r.startsWith("var(")||(t+=1)}return t>0},"generic-name":function(e){return"serif"===e||"sans-serif"===e||"monospace"===e||"cursive"===e||"fantasy"===e||"system-ui"===e||"ui-serif"===e||"ui-sans-serif"===e||"ui-monospace"===e||"ui-rounded"===e||"math"===e||"emoji"===e||"fangsong"===e},"absolute-size":function(e){return"xx-small"===e||"x-small"===e||"small"===e||"medium"===e||"large"===e||"x-large"===e||"xx-large"===e||"xxx-large"===e},"relative-size":function(e){return"larger"===e||"smaller"===e},angle:function(e){return V.test(e)},vector:function(e){return E.test(e)}};function m(e,t){var r;if(e.startsWith("var("))return null;for(let n of t)if(null==(r=h[n])?void 0:r.call(h,e))return n;return null}var v=/^url\(.*\)$/;function g(e){return v.test(e)}var w=/^(?:element|image|cross-fade|image-set)\(/,b=/^(repeating-)?(conic|linear|radial)-gradient\(/,x=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,A=new RegExp("^".concat(x.source,"$"));function z(e){return A.test(e)||d(e)}var T=new RegExp("^".concat(x.source,"%$"));function j(e){return T.test(e)||d(e)}var C=new RegExp("^".concat(x.source,"s*/s*").concat(x.source,"$")),K=new RegExp("^".concat(x.source,"(").concat("cm|mm|Q|in|pc|pt|px|em|ex|ch|rem|lh|rlh|vw|vh|vmin|vmax|vb|vi|svw|svh|lvw|lvh|dvw|dvh|cqw|cqh|cqi|cqb|cqmin|cqmax",")$"));function S(e){return K.test(e)||d(e)}var V=new RegExp("^".concat(x.source,"(").concat("deg|rad|grad|turn",")$")),E=new RegExp("^".concat(x.source," +").concat(x.source," +").concat(x.source,"$"));function N(e){let t=Number(e);return Number.isInteger(t)&&t>=0&&String(t)===String(e)}function O(e){let t=Number(e);return Number.isInteger(t)&&t>0&&String(t)===String(e)}function W(e){return _(e,.25)}function F(e){return _(e,.25)}function _(e,t){let r=Number(e);return r>=0&&r%t==0&&String(r)===String(e)}function U(e){return{__BARE_VALUE__:e}}var D=U(e=>{if(N(e.value))return e.value}),B=U(e=>{if(N(e.value))return"".concat(e.value,"%")}),L=U(e=>{if(N(e.value))return"".concat(e.value,"px")}),M=U(e=>{if(N(e.value))return"".concat(e.value,"ms")}),I=U(e=>{if(N(e.value))return"".concat(e.value,"deg")}),P=U(e=>{if(null===e.fraction)return;let[t,r]=p(e.fraction,"/");if(!(!N(t)||!N(r)))return e.fraction}),R=U(e=>{if(N(Number(e.value)))return"repeat(".concat(e.value,", minmax(0, 1fr))")}),q={accentColor:e=>{let{theme:t}=e;return t("colors")},animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...P},backdropBlur:e=>{let{theme:t}=e;return t("blur")},backdropBrightness:e=>{let{theme:t}=e;return{...t("brightness"),...B}},backdropContrast:e=>{let{theme:t}=e;return{...t("contrast"),...B}},backdropGrayscale:e=>{let{theme:t}=e;return{...t("grayscale"),...B}},backdropHueRotate:e=>{let{theme:t}=e;return{...t("hueRotate"),...I}},backdropInvert:e=>{let{theme:t}=e;return{...t("invert"),...B}},backdropOpacity:e=>{let{theme:t}=e;return{...t("opacity"),...B}},backdropSaturate:e=>{let{theme:t}=e;return{...t("saturate"),...B}},backdropSepia:e=>{let{theme:t}=e;return{...t("sepia"),...B}},backgroundColor:e=>{let{theme:t}=e;return t("colors")},backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:e=>{let{theme:t}=e;return t("opacity")},backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:e=>{let{theme:t}=e;return{DEFAULT:"currentcolor",...t("colors")}},borderOpacity:e=>{let{theme:t}=e;return t("opacity")},borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:e=>{let{theme:t}=e;return t("spacing")},borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...L},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:e=>{let{theme:t}=e;return t("colors")},brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...B},caretColor:e=>{let{theme:t}=e;return t("colors")},colors:()=>({...l}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...D},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...B},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:e=>{let{theme:t}=e;return t("borderColor")},divideOpacity:e=>{let{theme:t}=e;return t("borderOpacity")},divideWidth:e=>{let{theme:t}=e;return{...t("borderWidth"),...L}},dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:e=>{let{theme:t}=e;return t("colors")},flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...t("spacing")}},flexGrow:{0:"0",DEFAULT:"1",...D},flexShrink:{0:"0",DEFAULT:"1",...D},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:e=>{let{theme:t}=e;return t("spacing")},gradientColorStops:e=>{let{theme:t}=e;return t("colors")},gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...B},grayscale:{0:"0",DEFAULT:"100%",...B},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...D},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...D},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...D},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...D},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...R},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...R},height:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...I},inset:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}},invert:{0:"0",DEFAULT:"100%",...B},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:e=>{let{theme:t}=e;return{auto:"auto",...t("spacing")}},lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...D},maxHeight:e=>{let{theme:t}=e;return{none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},maxWidth:e=>{let{theme:t}=e;return{none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...t("spacing")}},minHeight:e=>{let{theme:t}=e;return{full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},minWidth:e=>{let{theme:t}=e;return{full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...B},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...D},outlineColor:e=>{let{theme:t}=e;return t("colors")},outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...L},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...L},padding:e=>{let{theme:t}=e;return t("spacing")},placeholderColor:e=>{let{theme:t}=e;return t("colors")},placeholderOpacity:e=>{let{theme:t}=e;return t("opacity")},ringColor:e=>{let{theme:t}=e;return{DEFAULT:"currentcolor",...t("colors")}},ringOffsetColor:e=>{let{theme:t}=e;return t("colors")},ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...L},ringOpacity:e=>{let{theme:t}=e;return{DEFAULT:"0.5",...t("opacity")}},ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...L},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...I},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...B},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...B},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:e=>{let{theme:t}=e;return t("spacing")},scrollPadding:e=>{let{theme:t}=e;return t("spacing")},sepia:{0:"0",DEFAULT:"100%",...B},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...I},space:e=>{let{theme:t}=e;return t("spacing")},spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:e=>{let{theme:t}=e;return{none:"none",...t("colors")}},strokeWidth:{0:"0",1:"1",2:"2",...D},supports:{},data:{},textColor:e=>{let{theme:t}=e;return t("colors")},textDecorationColor:e=>{let{theme:t}=e;return t("colors")},textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...L},textIndent:e=>{let{theme:t}=e;return t("spacing")},textOpacity:e=>{let{theme:t}=e;return t("opacity")},textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...L},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...M},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...M},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:e=>{let{theme:t}=e;return{"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...t("spacing")}},size:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},width:e=>{let{theme:t}=e;return{auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...t("spacing")}},willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...D}};function $(){let e=Object.freeze(Object.defineProperties(["_"],{raw:{value:Object.freeze(["\\_"])}}));return $=function(){return e},e}function H(e,t){let r=(null==t?void 0:t.from)?{file:t.from,code:e}:null;"\uFEFF"===e[0]&&(e=" "+e.slice(1));let n=[],a=[],o=[],i=null,l=null,s="",c="",u=0,d;for(let t=0;t<e.length;t++){let p=e.charCodeAt(t);if(13!==p||10!==(d=e.charCodeAt(t+1)))if(92===p)""===s&&(u=t),s+=e.slice(t,t+2),t+=1;else if(47===p&&42===e.charCodeAt(t+1)){let n=t;for(let r=t+2;r<e.length;r++)if(92===(d=e.charCodeAt(r)))r+=1;else if(42===d&&47===e.charCodeAt(r+1)){t=r+1;break}let o=e.slice(n,t+1);if(33===o.charCodeAt(2)){let e=ef(o.slice(2,-2));a.push(e),r&&(e.src=[r,n,t+1],e.dst=[r,n,t+1])}}else if(39===p||34===p){let r=J(e,t,p);s+=e.slice(t,r+1),t=r}else{if((32===p||10===p||9===p)&&(d=e.charCodeAt(t+1))&&(32===d||10===d||9===d||13===d&&(d=e.charCodeAt(t+2))&&10==d))continue;if(10===p){if(0===s.length)continue;32!==(d=s.charCodeAt(s.length-1))&&10!==d&&9!==d&&(s+=" ")}else if(45===p&&45===e.charCodeAt(t+1)&&0===s.length){let a="",o=t,l=-1;for(let r=t+2;r<e.length;r++)if(92===(d=e.charCodeAt(r)))r+=1;else if(39===d||34===d)r=J(e,r,d);else if(47===d&&42===e.charCodeAt(r+1)){for(let t=r+2;t<e.length;t++)if(92===(d=e.charCodeAt(t)))t+=1;else if(42===d&&47===e.charCodeAt(t+1)){r=t+1;break}}else if(-1===l&&58===d)l=s.length+r-o;else if(59===d&&0===a.length){s+=e.slice(o,r),t=r;break}else if(40===d)a+=")";else if(91===d)a+="]";else if(123===d)a+="}";else if((125===d||e.length-1===r)&&0===a.length){t=r-1,s+=e.slice(o,r);break}else(41===d||93===d||125===d)&&a.length>0&&e[r]===a[a.length-1]&&(a=a.slice(0,-1));let c=Y(s,l);if(!c)throw Error("Invalid custom property, expected a value");r&&(c.src=[r,o,t],c.dst=[r,o,t]),i?i.nodes.push(c):n.push(c),s=""}else if(59===p&&64===s.charCodeAt(0))l=Z(s),r&&(l.src=[r,u,t],l.dst=[r,u,t]),i?i.nodes.push(l):n.push(l),s="",l=null;else if(59===p&&")"!==c[c.length-1]){let e=Y(s);if(!e){if(0===s.length)continue;throw Error("Invalid declaration: `".concat(s.trim(),"`"))}r&&(e.src=[r,u,t],e.dst=[r,u,t]),i?i.nodes.push(e):n.push(e),s=""}else if(123===p&&")"!==c[c.length-1])c+="}",l=eu(s.trim()),r&&(l.src=[r,u,t],l.dst=[r,u,t]),i&&i.nodes.push(l),o.push(i),i=l,s="",l=null;else if(125===p&&")"!==c[c.length-1]){var f;if(""===c)throw Error("Missing opening {");if(c=c.slice(0,-1),s.length>0)if(64===s.charCodeAt(0))l=Z(s),r&&(l.src=[r,u,t],l.dst=[r,u,t]),i?i.nodes.push(l):n.push(l),s="",l=null;else{let e=s.indexOf(":");if(i){let n=Y(s,e);if(!n)throw Error("Invalid declaration: `".concat(s.trim(),"`"));r&&(n.src=[r,u,t],n.dst=[r,u,t]),i.nodes.push(n)}}let e=null!=(f=o.pop())?f:null;null===e&&i&&n.push(i),i=e,s="",l=null}else if(40===p)c+=")",s+="(";else if(41===p){if(")"!==c[c.length-1])throw Error("Missing opening (");c=c.slice(0,-1),s+=")"}else{if(0===s.length&&(32===p||10===p||9===p))continue;""===s&&(u=t),s+=String.fromCharCode(p)}}}if(64===s.charCodeAt(0)){let t=Z(s);r&&(t.src=[r,u,e.length],t.dst=[r,u,e.length]),n.push(t)}if(c.length>0&&i){if("rule"===i.kind)throw Error("Missing closing } at ".concat(i.selector));if("at-rule"===i.kind)throw Error("Missing closing } at ".concat(i.name," ").concat(i.params))}return a.length>0?a.concat(n):n}function Z(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=e,n="";for(let t=5;t<e.length;t++){let a=e.charCodeAt(t);if(32===a||40===a){r=e.slice(0,t),n=e.slice(t);break}}return ec(r.trim(),n.trim(),t)}function Y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.indexOf(":");if(-1===t)return null;let r=e.indexOf("!important",t+1);return ed(e.slice(0,t).trim(),e.slice(t+1,-1===r?e.length:r).trim(),-1!==r)}function J(e,t,r){let n;for(let a=t+1;a<e.length;a++)if(92===(n=e.charCodeAt(a)))a+=1;else{if(n===r)return a;if(59===n&&(10===e.charCodeAt(a+1)||13===e.charCodeAt(a+1)&&10===e.charCodeAt(a+2)))throw Error("Unterminated string: ".concat(e.slice(t,a+1)+String.fromCharCode(r)));if(10===n||13===n&&10===e.charCodeAt(a+1))throw Error("Unterminated string: ".concat(e.slice(t,a)+String.fromCharCode(r)))}return t}function G(e){if(0==arguments.length)throw TypeError("`CSS.escape` requires an argument.");let t=String(e),r=t.length,n=-1,a,o="",i=t.charCodeAt(0);if(1===r&&45===i)return"\\"+t;for(;++n<r;){if(0===(a=t.charCodeAt(n))){o+="�";continue}if(a>=1&&a<=31||127===a||0===n&&a>=48&&a<=57||1===n&&a>=48&&a<=57&&45===i){o+="\\"+a.toString(16)+" ";continue}if(a>=128||45===a||95===a||a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122){o+=t.charAt(n);continue}o+="\\"+t.charAt(n)}return o}function X(e){return e.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,e=>e.length>2?String.fromCodePoint(Number.parseInt(e.slice(1).trim(),16)):e[1])}var Q=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-decoration-color","--text-decoration-thickness","--text-indent","--text-shadow","--text-underline-offset"]]]);function ee(e,t){var r;return(null!=(r=Q.get(t))?r:[]).some(t=>e===t||e.startsWith("".concat(t,"-")))}var et=(a=new WeakSet,o=new WeakSet,i=new WeakSet,class{get size(){return this.values.size}add(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3?arguments[3]:void 0;if(e.endsWith("-*")){if("initial"!==t)throw Error("Invalid theme value `".concat(t,"` for namespace `").concat(e,"`"));"--*"===e?this.values.clear():this.clearNamespace(e.slice(0,-2),0)}if(4&r){let t=this.values.get(e);if(t&&!(4&t.options))return}"initial"===t?this.values.delete(e):this.values.set(e,{value:t,options:r,src:n})}keysInNamespaces(e){let t=[];for(let r of e){let e="".concat(r,"-");for(let n of this.values.keys())n.startsWith(e)&&-1===n.indexOf("--",2)&&(ee(n,r)||t.push(n.slice(e.length)))}return t}get(e){for(let t of e){let e=this.values.get(t);if(e)return e.value}return null}hasDefault(e){return(4&this.getOptions(e))==4}getOptions(e){var r,n;return e=X(t(this,a,tL).call(this,e)),null!=(n=null==(r=this.values.get(e))?void 0:r.options)?n:0}entries(){return this.prefix?Array.from(this.values,e=>(e[0]=this.prefixKey(e[0]),e)):this.values.entries()}prefixKey(e){return this.prefix?"--".concat(this.prefix,"-").concat(e.slice(2)):e}clearNamespace(e,t){var r;let n=null!=(r=Q.get(e))?r:[];e:for(let r of this.values.keys())if(r.startsWith(e)){if(0!==t&&(this.getOptions(r)&t)!==t)continue;for(let e of n)if(r.startsWith(e))continue e;this.values.delete(r)}}markUsedVariable(e){let r=X(t(this,a,tL).call(this,e)),n=this.values.get(r);if(!n)return!1;let o=16&n.options;return n.options|=16,!o}resolve(e,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=t(this,o,tM).call(this,e,r);if(!a)return null;let l=this.values.get(a);return(n|l.options)&1?l.value:t(this,i,tI).call(this,a)}resolveValue(e,r){let n=t(this,o,tM).call(this,e,r);return n?this.values.get(n).value:null}resolveWith(e,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],a=t(this,o,tM).call(this,e,r);if(!a)return null;let l={};for(let e of n){let r="".concat(a).concat(e),n=this.values.get(r);n&&(1&n.options?l[e]=n.value:l[e]=t(this,i,tI).call(this,r))}let s=this.values.get(a);return 1&s.options?[s.value,l]:[t(this,i,tI).call(this,a),l]}namespace(e){let t=new Map,r="".concat(e,"-");for(let[n,a]of this.values)n===e?t.set(null,a.value):n.startsWith("".concat(r,"-"))?t.set(n.slice(e.length),a.value):n.startsWith(r)&&t.set(n.slice(r.length),a.value);return t}addKeyframes(e){this.keyframes.add(e)}getKeyframes(){return Array.from(this.keyframes)}constructor(e=new Map,t=new Set([])){r(this,a),r(this,o),r(this,i),n(this,"prefix",null),this.values=e,this.keyframes=t}}),er=class extends Map{get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e,this),this.set(e,t)),t}constructor(e){super(),this.factory=e}};function en(e){return{kind:"word",value:e}}function ea(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;for(let a=0;a<e.length;a++){var n;let o=e[a],i=!1,l=0,s=null!=(n=t(o,{parent:r,replaceWith(t){i||(i=!0,Array.isArray(t)?0===t.length?(e.splice(a,1),l=0):1===t.length?(e[a]=t[0],l=1):(e.splice(a,1,...t),l=t.length):e[a]=t)}}))?n:0;if(i){0===s?a--:a+=l-1;continue}if(2===s||1!==s&&"function"===o.kind&&2===ea(o.nodes,t,o))return 2}}function eo(e){let t="";for(let r of e)switch(r.kind){case"word":case"separator":t+=r.value;break;case"function":t+=r.value+"("+eo(r.nodes)+")"}return t}function ei(e){e=e.replaceAll("\r\n","\n");let t=[],r=[],n=null,a="",o;for(let i=0;i<e.length;i++){let l=e.charCodeAt(i);switch(l){case 92:a+=e[i]+e[i+1],i++;break;case 58:case 44:case 61:case 62:case 60:case 10:case 47:case 32:case 9:{if(a.length>0){let e=en(a);n?n.nodes.push(e):t.push(e),a=""}let r=i,l=i+1;for(;l<e.length&&(58===(o=e.charCodeAt(l))||44===o||61===o||62===o||60===o||10===o||47===o||32===o||9===o);l++);i=l-1;let s={kind:"separator",value:e.slice(r,l)};n?n.nodes.push(s):t.push(s);break}case 39:case 34:{let t=i;for(let t=i+1;t<e.length;t++)if(92===(o=e.charCodeAt(t)))t+=1;else if(o===l){i=t;break}a+=e.slice(t,i+1);break}case 40:{let e={kind:"function",value:a,nodes:[]};a="",n?n.nodes.push(e):t.push(e),r.push(e),n=e;break}case 41:{let e=r.pop();if(a.length>0){let t=en(a);null==e||e.nodes.push(t),a=""}n=r.length>0?r[r.length-1]:null;break}default:a+=String.fromCharCode(l)}}return a.length>0&&t.push(en(a)),t}function el(e){let t=[];return ea(ei(e),e=>{if("function"===e.kind&&"var"===e.value)return ea(e.nodes,e=>{"word"!==e.kind||"-"!==e.value[0]||"-"!==e.value[1]||t.push(e.value)}),1}),t}function es(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return{kind:"rule",selector:e,nodes:t}}function ec(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return{kind:"at-rule",name:e,params:t,nodes:r}}function eu(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return 64===e.charCodeAt(0)?Z(e,t):es(e,t)}function ed(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return{kind:"declaration",property:e,value:t,important:r}}function ef(e){return{kind:"comment",value:e}}function ep(e,t){return{kind:"context",context:e,nodes:t}}function eh(e){return{kind:"at-root",nodes:e}}function em(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};for(let i=0;i<e.length;i++){var a,o;let l=e[i],s=null!=(a=r[r.length-1])?a:null;if("context"===l.kind){if(2===em(l.nodes,t,r,{...n,...l.context}))return 2;continue}r.push(l);let c=!1,u=0,d=null!=(o=t(l,{parent:s,context:n,path:r,replaceWith(t){c||(c=!0,Array.isArray(t)?0===t.length?(e.splice(i,1),u=0):1===t.length?(e[i]=t[0],u=1):(e.splice(i,1,...t),u=t.length):(e[i]=t,u=1))}}))?o:0;if(r.pop(),c){0===d?i--:i+=u-1;continue}if(2===d)return 2;if(1!==d&&"nodes"in l){r.push(l);let e=em(l.nodes,t,r,n);if(r.pop(),2===e)return 2}}}function ev(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,n=[],a=new Set,o=new er(()=>new Set),i=new er(()=>new Set),l=new Set,s=new Set,c=[],u=[],d=new er(()=>new Set),f=[];for(let p of e)!function e(f,p){let h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},m=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;if("declaration"===f.kind){if("--tw-sort"===f.property||void 0===f.value||null===f.value)return;if(h.theme&&"-"===f.property[0]&&"-"===f.property[1]){if("initial"===f.value){f.value=void 0;return}h.keyframes||o.get(p).add(f)}if(f.value.includes("var("))if(h.theme&&"-"===f.property[0]&&"-"===f.property[1])for(let e of el(f.value))d.get(e).add(f.property);else t.trackUsedVariables(f.value);if("animation"===f.property)for(let e of ew(f.value))s.add(e);2&r&&f.value.includes("color-mix(")&&i.get(p).add(f),p.push(f)}else if("rule"===f.kind){let t=[];for(let r of f.nodes)e(r,t,h,m+1);let r={},n=new Set;for(let e of t){if("declaration"!==e.kind)continue;let t="".concat(e.property,":").concat(e.value,":").concat(e.important);null!=r[t]||(r[t]=[]),r[t].push(e)}for(let e in r)for(let t=0;t<r[e].length-1;++t)n.add(r[e][t]);if(n.size>0&&(t=t.filter(e=>!n.has(e))),0===t.length)return;"&"===f.selector?p.push(...t):p.push({...f,nodes:t})}else if("at-rule"===f.kind&&"@property"===f.name&&0===m){if(a.has(f.params))return;if(1&r){let e=f.params,t=null,r=!1;for(let e of f.nodes)"declaration"===e.kind&&("initial-value"===e.property?t=e.value:"inherits"===e.property&&(r="true"===e.value));let n=ed(e,null!=t?t:"initial");n.src=f.src,r?c.push(n):u.push(n)}a.add(f.params);let t={...f,nodes:[]};for(let r of f.nodes)e(r,t.nodes,h,m+1);p.push(t)}else if("at-rule"===f.kind){"@keyframes"===f.name&&(h={...h,keyframes:!0});let t={...f,nodes:[]};for(let r of f.nodes)e(r,t.nodes,h,m+1);"@keyframes"===f.name&&h.theme&&l.add(t),(t.nodes.length>0||"@layer"===t.name||"@charset"===t.name||"@custom-media"===t.name||"@namespace"===t.name||"@import"===t.name)&&p.push(t)}else if("at-root"===f.kind)for(let t of f.nodes){let r=[];for(let a of(e(t,r,h,0),r))n.push(a)}else if("context"===f.kind){if(f.context.reference)return;for(let t of f.nodes)e(t,p,{...h,...f.context},m)}else"comment"===f.kind&&p.push(f)}(p,f,{},0);e:for(let[e,r]of o)for(let n of r){if(function e(t,r,n){var a;let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:new Set;if(o.has(t)||(o.add(t),24&r.getOptions(t)))return!0;for(let i of null!=(a=n.get(t))?a:[])if(e(i,r,n,o))return!0;return!1}(n.property,t.theme,d)){if(n.property.startsWith(t.theme.prefixKey("--animate-")))for(let e of ew(n.value))s.add(e);continue}let r=e.indexOf(n);if(e.splice(r,1),0===e.length){let t=function(e,t){let r=[];return em(e,(e,n)=>{let{path:a}=n;if(t(e))return r=[...a],2}),r}(f,t=>"rule"===t.kind&&t.nodes===e);if(!t||0===t.length)continue e;for(t.unshift({kind:"at-root",nodes:f});;){let e=t.pop();if(!e)break;let r=t[t.length-1];if(!r||"at-root"!==r.kind&&"at-rule"!==r.kind)break;let n=r.nodes.indexOf(e);if(-1===n)break;r.nodes.splice(n,1)}continue e}}for(let e of l)if(!s.has(e.params)){let t=n.indexOf(e);n.splice(t,1)}if(f=f.concat(n),2&r)for(let[e,r]of i)for(let n of r){let r=e.indexOf(n);if(-1===r||null==n.value)continue;let a=ei(n.value),o=!1;if(ea(a,(e,r)=>{let{replaceWith:n}=r;if("function"!==e.kind||"color-mix"!==e.value)return;let a=!1,i=!1;if(ea(e.nodes,(e,r)=>{let{replaceWith:n}=r;if("word"==e.kind&&"currentcolor"===e.value.toLowerCase()){i=!0,o=!0;return}let l=e,s=null,c=new Set;do{if("function"!==l.kind||"var"!==l.value)return;let e=l.nodes[0];if(!e||"word"!==e.kind)return;let r=e.value;if(c.has(r)||(c.add(r),o=!0,!(s=t.theme.resolveValue(null,[e.value])))){a=!0;return}if("currentcolor"===s.toLowerCase()){i=!0;return}l=s.startsWith("var(")?ei(s)[0]:null}while(l)n({kind:"word",value:s})}),a||i){let t=e.nodes.findIndex(e=>"separator"===e.kind&&e.value.trim().includes(","));if(-1===t)return;let r=e.nodes.length>t?e.nodes[t+1]:null;if(!r)return;n(r)}else if(o){let t=e.nodes[2];"word"===t.kind&&("oklab"===t.value||"oklch"===t.value||"lab"===t.value||"lch"===t.value)&&(t.value="srgb")}}),!o)continue;let i={...n,value:eo(a)},l=eu("@supports (color: color-mix(in lab, red, red))",[n]);l.src=n.src,e.splice(r,1,i,l)}if(1&r){let e=[];if(c.length>0){let t=eu(":root, :host",c);t.src=c[0].src,e.push(t)}if(u.length>0){let t=eu("*, ::before, ::after, ::backdrop",u);t.src=u[0].src,e.push(t)}if(e.length>0){let t=f.findIndex(e=>"comment"!==e.kind&&("at-rule"!==e.kind||"@charset"!==e.name&&"@import"!==e.name)),r=ec("@layer","properties",[]);r.src=e[0].src,f.splice(t<0?f.length:t,0,r);let n=eu("@layer properties",[ec("@supports","((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))",e)]);n.src=e[0].src,n.nodes[0].src=e[0].src,f.push(n)}}return f}function eg(e,t){let r=0,n={file:null,code:""},a="";for(let o of e)a+=function e(a){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i="",l="  ".repeat(o);if("declaration"===a.kind){if(i+="".concat(l).concat(a.property,": ").concat(a.value).concat(a.important?" !important":"",";\n"),t){var s,c;let e=r+=l.length;r+=a.property.length,r+=2,r+=null!=(c=null==(s=a.value)?void 0:s.length)?c:0,a.important&&(r+=11);let t=r;r+=2,a.dst=[n,e,t]}}else if("rule"===a.kind){if(i+="".concat(l).concat(a.selector," {\n"),t){let e=r+=l.length;r+=a.selector.length,a.dst=[n,e,r+=1],r+=2}for(let t of a.nodes)i+=e(t,o+1);i+="".concat(l,"}\n"),t&&(r+=l.length,r+=2)}else if("at-rule"===a.kind){if(0===a.nodes.length){let e="".concat(l).concat(a.name," ").concat(a.params,";\n");if(t){let e=r+=l.length;r+=a.name.length,r+=1;let t=r+=a.params.length;r+=2,a.dst=[n,e,t]}return e}if(i+="".concat(l).concat(a.name).concat(a.params?" ".concat(a.params," "):" ","{\n"),t){let e=r+=l.length;r+=a.name.length,a.params&&(r+=1,r+=a.params.length),a.dst=[n,e,r+=1],r+=2}for(let t of a.nodes)i+=e(t,o+1);i+="".concat(l,"}\n"),t&&(r+=l.length,r+=2)}else if("comment"===a.kind){if(i+="".concat(l,"/*").concat(a.value,"*/\n"),t){let e=r+=l.length,t=r+=2+a.value.length+2;a.dst=[n,e,t],r+=1}}else if("context"===a.kind||"at-root"===a.kind)return"";return i}(o,0);return n.code=a,a}function ew(e){return e.split(/[\s,]+/)}function ek(e){if(-1===e.indexOf("("))return eb(e);let t=ei(e);return function e(t){for(let n of t)switch(n.kind){case"function":if("url"===n.value||n.value.endsWith("_url")){n.value=eb(n.value);break}if("var"===n.value||n.value.endsWith("_var")||"theme"===n.value||n.value.endsWith("_theme")){n.value=eb(n.value);for(let t=0;t<n.nodes.length;t++){if(0==t&&"word"===n.nodes[t].kind){n.nodes[t].value=eb(n.nodes[t].value,!0);continue}e([n.nodes[t]])}break}n.value=eb(n.value),e(n.nodes);break;case"separator":case"word":n.value=eb(n.value);break;default:var r=n;throw Error("Unexpected value: ".concat(r))}}(t),e=function(e){if(!u.some(t=>e.includes(t)))return e;let t="",r=[],n=null,a=null;for(let o=0;o<e.length;o++){let i=e.charCodeAt(o);if(i>=48&&i<=57||null!==n&&(37===i||i>=97&&i<=122||i>=65&&i<=90)?n=o:(a=n,n=null),40===i){t+=e[o];let n=o;for(let t=o-1;t>=0;t--){let r=e.charCodeAt(t);if(r>=48&&r<=57)n=t;else if(r>=97&&r<=122)n=t;else break}let a=e.slice(n,o);if(u.includes(a)||r[0]&&""===a){r.unshift(!0);continue}r.unshift(!1);continue}if(41===i)t+=e[o],r.shift();else if(44===i&&r[0]){t+=", ";continue}else{if(32===i&&r[0]&&32===t.charCodeAt(t.length-1))continue;if((43===i||42===i||47===i||45===i)&&r[0]){let r=t.trimEnd(),n=r.charCodeAt(r.length-1),i=r.charCodeAt(r.length-2),l=e.charCodeAt(o+1);if((101===n||69===n)&&i>=48&&i<=57){t+=e[o];continue}if(43===n||42===n||47===n||45===n){t+=e[o];continue}if(40===n||44===n){t+=e[o];continue}else 32===e.charCodeAt(o-1)?t+="".concat(e[o]," "):n>=48&&n<=57||l>=48&&l<=57||41===n||40===l||43===l||42===l||47===l||45===l||null!==a&&a===o-1?t+=" ".concat(e[o]," "):t+=e[o]}else t+=e[o]}}return t}(e=eo(t))}function eb(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r="";for(let n=0;n<e.length;n++){let a=e[n];"\\"===a&&"_"===e[n+1]?(r+="_",n+=1):"_"!==a||t?r+=a:r+=" "}return r}var ey=new Uint8Array(256);function ex(e){let t=0,r=e.length;for(let n=0;n<r;n++){let a=e.charCodeAt(n);switch(a){case 92:n+=1;break;case 39:case 34:for(;++n<r;){let t=e.charCodeAt(n);if(92===t){n+=1;continue}if(t===a)break}break;case 40:ey[t]=41,t++;break;case 91:ey[t]=93,t++;break;case 123:break;case 93:case 125:case 41:if(0===t)return!1;t>0&&a===ey[t-1]&&t--;break;case 59:if(0===t)return!1}}return!0}function eA(e){if("["===e[0]&&"]"===e[e.length-1]){let t=ek(e.slice(1,-1));return ex(t)&&0!==t.length&&0!==t.trim().length?{kind:"arbitrary",value:t}:null}return"("===e[0]&&")"===e[e.length-1]?"-"===(e=e.slice(1,-1))[0]&&"-"===e[1]&&ex(e)?{kind:"arbitrary",value:ek(e="var(".concat(e,")"))}:null:{kind:"named",value:e}}function*ez(e,t){t(e)&&(yield[e,null]);let r=e.lastIndexOf("-");for(;r>0;){let n=e.slice(0,r);if(t(n)){let a=[n,e.slice(r+1)];if(""===a[1]||"@"===a[0]&&t("@")&&"-"===e[r])break;yield a}r=e.lastIndexOf("-",r-1)}"@"===e[0]&&t("@")&&(yield["@",e.slice(1)])}function eT(e){var t;if(null===e)return"";let r=(t=e.value,eV.get(t)),n=r?e.value.slice(4,-1):e.value,[a,o]=r?["(",")"]:["[","]"];return"arbitrary"===e.kind?"/".concat(a).concat(eK(n)).concat(o):"named"===e.kind?"/".concat(e.value):""}function ej(e){var t,r;if("static"===e.kind)return e.root;if("arbitrary"===e.kind){return"[".concat(eK((t=e.selector,eS.get(t))),"]")}let n="";if("functional"===e.kind){n+=e.root;let t="@"!==e.root;if(e.value)if("arbitrary"===e.value.kind){let a=(r=e.value.value,eV.get(r)),o=a?e.value.value.slice(4,-1):e.value.value,[i,l]=a?["(",")"]:["[","]"];n+="".concat(t?"-":"").concat(i).concat(eK(o)).concat(l)}else"named"===e.value.kind&&(n+="".concat(t?"-":"").concat(e.value.value))}return"compound"===e.kind&&(n+=e.root,n+="-",n+=ej(e.variant)),("functional"===e.kind||"compound"===e.kind)&&(n+=eT(e.modifier)),n}var eC=new er(e=>{let t=ei(e),r=new Set;return ea(t,(e,n)=>{var a,o;let{parent:i}=n,l=null===i?t:null!=(a=i.nodes)?a:[];if("word"===e.kind&&("+"===e.value||"-"===e.value||"*"===e.value||"/"===e.value)){let t=null!=(o=l.indexOf(e))?o:-1;if(-1===t)return;let n=l[t-1];if((null==n?void 0:n.kind)!=="separator"||" "!==n.value)return;let a=l[t+1];if((null==a?void 0:a.kind)!=="separator"||" "!==a.value)return;r.add(n),r.add(a)}else"separator"===e.kind&&"/"===e.value.trim()?e.value="/":"separator"===e.kind&&e.value.length>0&&""===e.value.trim()?(l[0]===e||l[l.length-1]===e)&&r.add(e):"separator"===e.kind&&","===e.value.trim()&&(e.value=",")}),r.size>0&&ea(t,(e,t)=>{let{replaceWith:n}=t;r.has(e)&&(r.delete(e),n([]))}),function e(t){for(let n of t)switch(n.kind){case"function":if("url"===n.value||n.value.endsWith("_url")){n.value=eE(n.value);break}if("var"===n.value||n.value.endsWith("_var")||"theme"===n.value||n.value.endsWith("_theme")){n.value=eE(n.value);for(let t=0;t<n.nodes.length;t++)e([n.nodes[t]]);break}n.value=eE(n.value),e(n.nodes);break;case"separator":n.value=eE(n.value);break;case"word":("-"!==n.value[0]||"-"!==n.value[1])&&(n.value=eE(n.value));break;default:var r=n;throw Error("Unexpected value: ".concat(r))}}(t),eo(t)});function eK(e){return eC.get(e)}var eS=new er(e=>{let t=ei(e);return 3===t.length&&"word"===t[0].kind&&"&"===t[0].value&&"separator"===t[1].kind&&":"===t[1].value&&"function"===t[2].kind&&"is"===t[2].value?eo(t[2].nodes):e}),eV=new er(e=>{let t=ei(e);return 1===t.length&&"function"===t[0].kind&&"var"===t[0].value});function eE(e){return e.replaceAll("_",String.raw($())).replaceAll(" ","_")}function eN(e,t,r){if(e===t)return 0;let n=e.indexOf("("),a=t.indexOf("("),o=-1===n?e.replace(/[\d.]+/g,""):e.slice(0,n),i=-1===a?t.replace(/[\d.]+/g,""):t.slice(0,a),l=(o===i?0:o<i?-1:1)||("asc"===r?parseInt(e)-parseInt(t):parseInt(t)-parseInt(e));return Number.isNaN(l)?e<t?-1:1:l}var eO=new Set(["inset","inherit","initial","revert","unset"]),eW=/^-?(\d+|\.\d+)(.*?)$/g;function eF(e,t){return p(e,",").map(e=>{let r=p(e=e.trim()," ").filter(e=>""!==e.trim()),n=null,a=null,o=null;for(let e of r)eO.has(e)||(eW.test(e)?(null===a?a=e:null===o&&(o=e),eW.lastIndex=0):null===n&&(n=e));if(null===a||null===o)return e;let i=t(null!=n?n:"currentcolor");return null!==n?e.replace(n,i):"".concat(e," ").concat(i)}).join(", ")}var e_=/^-?[a-z][a-zA-Z0-9/%._-]*$/,eU=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,eD=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],eB=class{static(e,t){this.utilities.get(e).push({kind:"static",compileFn:t})}functional(e,t,r){this.utilities.get(e).push({kind:"functional",compileFn:t,options:r})}has(e,t){return this.utilities.has(e)&&this.utilities.get(e).some(e=>e.kind===t)}get(e){return this.utilities.has(e)?this.utilities.get(e):[]}getCompletions(e){var t,r;return null!=(r=null==(t=this.completions.get(e))?void 0:t())?r:[]}suggest(e,t){this.completions.set(e,t)}keys(e){let t=[];for(let[r,n]of this.utilities.entries())for(let a of n)if(a.kind===e){t.push(r);break}return t}constructor(){n(this,"utilities",new er(()=>[])),n(this,"completions",new Map)}};function eL(e,t,r){return ec("@property",e,[ed("syntax",r?'"'.concat(r,'"'):'"*"'),ed("inherits","false"),...t?[ed("initial-value",t)]:[]])}function eM(e,t){if(null===t)return e;let r=Number(t);return Number.isNaN(r)||(t="".concat(100*r,"%")),"100%"===t?e:"color-mix(in oklab, ".concat(e," ").concat(t,", transparent)")}function eI(e,t){let r=Number(t);return Number.isNaN(r)||(t="".concat(100*r,"%")),"oklab(from ".concat(e," l a b / ").concat(t,")")}function eP(e,t,r){if(!t)return e;if("arbitrary"===t.kind)return eM(e,t.value);let n=r.resolve(t.value,["--opacity"]);return n?eM(e,n):F(t.value)?eM(e,"".concat(t.value,"%")):null}function eR(e,t,r){let n=null;switch(e.value.value){case"inherit":n="inherit";break;case"transparent":n="transparent";break;case"current":n="currentcolor";break;default:n=t.resolve(e.value.value,r)}return n?eP(n,e.modifier,t):null}var eq=/(\d+)_(\d+)/g,e$=["number","integer","ratio","percentage"];function eH(e,t,r){for(let n of t.nodes){if("named"===e.kind&&"word"===n.kind&&("'"===n.value[0]||'"'===n.value[0])&&n.value[n.value.length-1]===n.value[0]&&n.value.slice(1,-1)===e.value)return{nodes:ei(e.value)};if("named"===e.kind&&"word"===n.kind&&"-"===n.value[0]&&"-"===n.value[1]){let t=n.value;if(t.endsWith("-*")){t=t.slice(0,-2);let n=r.theme.resolve(e.value,[t]);if(n)return{nodes:ei(n)}}else{let n=t.split("-*");if(n.length<=1)continue;let a=[n.shift()],o=r.theme.resolveWith(e.value,a,n);if(o){let[,e={}]=o;{let t=e[n.pop()];if(t)return{nodes:ei(t)}}}}}else if("named"===e.kind&&"word"===n.kind){if(!e$.includes(n.value))continue;let t="ratio"===n.value&&"fraction"in e?e.fraction:e.value;if(!t)continue;let r=m(t,[n.value]);if(null===r)continue;if("ratio"===r){let[e,r]=p(t,"/");if(!N(e)||!N(r))continue}else if("number"===r&&!W(t)||"percentage"===r&&!N(t.slice(0,-1)))continue;return{nodes:ei(t),ratio:"ratio"===r}}else if("arbitrary"===e.kind&&"word"===n.kind&&"["===n.value[0]&&"]"===n.value[n.value.length-1]){let t=n.value.slice(1,-1);if("*"===t)return{nodes:ei(e.value)};if("dataType"in e&&e.dataType&&e.dataType!==t)continue;if("dataType"in e&&e.dataType||null!==m(e.value,[t]))return{nodes:ei(e.value)}}}}function eZ(e,t,r,n){let a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",o=!1,i=eF(t,e=>null==r?n(e):e.startsWith("current")?n(eM(e,r)):((e.startsWith("var(")||r.startsWith("var("))&&(o=!0),n(eI(e,r))));function l(e){return a?p(e,",").map(e=>a+e).join(","):e}return o?[ed(e,l(eF(t,n))),eu("@supports (color: lab(from red l a b))",[ed(e,l(i))])]:[ed(e,l(i))]}function eY(e,t,r,n){let a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",o=!1,i=p(t,",").map(e=>eF(e,e=>null==r?n(e):e.startsWith("current")?n(eM(e,r)):((e.startsWith("var(")||r.startsWith("var("))&&(o=!0),n(eI(e,r))))).map(e=>"drop-shadow(".concat(e,")")).join(" ");return o?[ed(e,a+p(t,",").map(e=>"drop-shadow(".concat(eF(e,n),")")).join(" ")),eu("@supports (color: lab(from red l a b))",[ed(e,a+i)])]:[ed(e,a+i)]}var eJ={"--alpha":function(e,t,r){for(var n=arguments.length,a=Array(n>3?n-3:0),o=3;o<n;o++)a[o-3]=arguments[o];let[i,l]=p(r,"/").map(e=>e.trim());if(!i||!l)throw Error("The --alpha(…) function requires a color and an alpha value, e.g.: `--alpha(".concat(i||"var(--my-color)"," / ").concat(l||"50%",")`"));if(a.length>0)throw Error("The --alpha(…) function only accepts one argument, e.g.: `--alpha(".concat(i||"var(--my-color)"," / ").concat(l||"50%",")`"));return eM(i,l)},"--spacing":function(e,t,r){for(var n=arguments.length,a=Array(n>3?n-3:0),o=3;o<n;o++)a[o-3]=arguments[o];if(!r)throw Error("The --spacing(…) function requires an argument, but received none.");if(a.length>0)throw Error("The --spacing(…) function only accepts a single argument, but received ".concat(a.length+1,"."));let i=e.theme.resolve(null,["--spacing"]);if(!i)throw Error("The --spacing(…) function requires that the `--spacing` theme variable exists, but it was not found.");return"calc(".concat(i," * ").concat(r,")")},"--theme":function(e,t,r){for(var n,a,o=arguments.length,i=Array(o>3?o-3:0),l=3;l<o;l++)i[l-3]=arguments[l];if(!r.startsWith("--"))throw Error("The --theme(…) function can only be used with CSS variables from your theme.");let s=!1;r.endsWith(" inline")&&(s=!0,r=r.slice(0,-7)),"at-rule"===t.kind&&(s=!0);let c=e.resolveThemeValue(r,s);if(!c){if(i.length>0)return i.join(", ");throw Error("Could not resolve value for theme function: `theme(".concat(r,")`. Consider checking if the variable name is correct or provide a fallback value to silence this error."))}if(0===i.length)return c;let u=i.join(", ");if("initial"===u)return c;if("initial"===c)return u;if(c.startsWith("var(")||c.startsWith("theme(")||c.startsWith("--theme(")){let e=ei(c);return n=e,a=u,ea(n,e=>{if("function"===e.kind&&("var"===e.value||"theme"===e.value||"--theme"===e.value))if(1===e.nodes.length)e.nodes.push({kind:"word",value:", ".concat(a)});else{let t=e.nodes[e.nodes.length-1];"word"===t.kind&&"initial"===t.value&&(t.value=a)}}),eo(e)}return c},theme:function(e,t,r){for(var n=arguments.length,a=Array(n>3?n-3:0),o=3;o<n;o++)a[o-3]=arguments[o];r=function(e){if("'"!==e[0]&&'"'!==e[0])return e;let t="",r=e[0];for(let n=1;n<e.length-1;n++){let a=e[n],o=e[n+1];"\\"===a&&(o===r||"\\"===o)?(t+=o,n++):t+=a}return t}(r);let i=e.resolveThemeValue(r);if(!i&&a.length>0)return a.join(", ");if(!i)throw Error("Could not resolve value for theme function: `theme(".concat(r,")`. Consider checking if the path is correct or provide a fallback value to silence this error."));return i}},eG=new RegExp(Object.keys(eJ).map(e=>"".concat(e,"\\(")).join("|"));function eX(e,t){let r=0;return em(e,e=>{if("declaration"===e.kind&&e.value&&eG.test(e.value)){r|=8,e.value=eQ(e.value,e,t);return}"at-rule"===e.kind&&("@media"===e.name||"@custom-media"===e.name||"@container"===e.name||"@supports"===e.name)&&eG.test(e.params)&&(r|=8,e.params=eQ(e.params,e,t))}),r}function eQ(e,t,r){let n=ei(e);return ea(n,(e,n)=>{let{replaceWith:a}=n;if("function"===e.kind&&e.value in eJ){let n=p(eo(e.nodes).trim(),",").map(e=>e.trim());return a(ei(eJ[e.value](r,t,...n)))}}),eo(n)}function e0(e,t){let r=e.length,n=t.length,a=r<n?r:n;for(let r=0;r<a;r++){let n=e.charCodeAt(r),a=t.charCodeAt(r);if(n>=48&&n<=57&&a>=48&&a<=57){let o=r,i=r+1,l=r,s=r+1;for(n=e.charCodeAt(i);n>=48&&n<=57;)n=e.charCodeAt(++i);for(a=t.charCodeAt(s);a>=48&&a<=57;)a=t.charCodeAt(++s);let c=e.slice(o,i),u=t.slice(l,s),d=Number(c)-Number(u);if(d)return d;if(c<u)return -1;if(c>u)return 1;continue}if(n!==a)return n-a}return e.length-t.length}var e1=/^\d+\/\d+$/,e2=RegExp("^@?[a-z0-9][a-zA-Z0-9_-]*(?<![_-])$"),e5=class{static(e,t){let{compounds:r,order:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.set(e,{kind:"static",applyFn:t,compoundsWith:0,compounds:null!=r?r:2,order:n})}fromAst(e,t){let r=[];em(t,e=>{"rule"===e.kind?r.push(e.selector):"at-rule"===e.kind&&"@slot"!==e.name&&r.push("".concat(e.name," ").concat(e.params))}),this.static(e,e=>{let r=structuredClone(t);e4(r,e.nodes),e.nodes=r},{compounds:e3(r)})}functional(e,t){let{compounds:r,order:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.set(e,{kind:"functional",applyFn:t,compoundsWith:0,compounds:null!=r?r:2,order:n})}compound(e,t,r){let{compounds:n,order:a}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};this.set(e,{kind:"compound",applyFn:r,compoundsWith:t,compounds:null!=n?n:2,order:a})}group(e,t){this.groupOrder=this.nextOrder(),t&&this.compareFns.set(this.groupOrder,t),e(),this.groupOrder=null}has(e){return this.variants.has(e)}get(e){return this.variants.get(e)}kind(e){var t;return null==(t=this.variants.get(e))?void 0:t.kind}compoundsWith(e,t){let r=this.variants.get(e),n="string"==typeof t?this.variants.get(t):"arbitrary"===t.kind?{compounds:e3([t.selector])}:this.variants.get(t.root);return!(!r||!n||"compound"!==r.kind||0===n.compounds||0===r.compoundsWith||(r.compoundsWith&n.compounds)==0)}suggest(e,t){this.completions.set(e,t)}getCompletions(e){var t,r;return null!=(r=null==(t=this.completions.get(e))?void 0:t())?r:[]}compare(e,t){if(e===t)return 0;if(null===e)return -1;if(null===t)return 1;if("arbitrary"===e.kind&&"arbitrary"===t.kind)return e.selector<t.selector?-1:1;if("arbitrary"===e.kind)return 1;if("arbitrary"===t.kind)return -1;let r=this.variants.get(e.root).order,n=r-this.variants.get(t.root).order;if(0!==n)return n;if("compound"===e.kind&&"compound"===t.kind){let r=this.compare(e.variant,t.variant);return 0!==r?r:e.modifier&&t.modifier?e.modifier.value<t.modifier.value?-1:1:e.modifier?1:t.modifier?-1:0}let a=this.compareFns.get(r);if(void 0!==a)return a(e,t);if(e.root!==t.root)return e.root<t.root?-1:1;let o=e.value,i=t.value;return null===o?-1:null===i||"arbitrary"===o.kind&&"arbitrary"!==i.kind?1:"arbitrary"!==o.kind&&"arbitrary"===i.kind||o.value<i.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(e,t){let{kind:r,applyFn:n,compounds:a,compoundsWith:o,order:i}=t,l=this.variants.get(e);l?Object.assign(l,{kind:r,applyFn:n,compounds:a}):(void 0===i&&(this.lastOrder=this.nextOrder(),i=this.lastOrder),this.variants.set(e,{kind:r,applyFn:n,order:i,compoundsWith:o,compounds:a}))}nextOrder(){var e;return null!=(e=this.groupOrder)?e:this.lastOrder+1}constructor(){n(this,"compareFns",new Map),n(this,"variants",new Map),n(this,"completions",new Map),n(this,"groupOrder",null),n(this,"lastOrder",0)}};function e3(e){let t=0;for(let r of e){if("@"===r[0]){if(!r.startsWith("@media")&&!r.startsWith("@supports")&&!r.startsWith("@container"))return 0;t|=1;continue}if(r.includes("::"))return 0;t|=2}return t}function e6(e){if(e.includes("=")){let[t,...r]=p(e,"="),n=r.join("=").trim();if("'"===n[0]||'"'===n[0])return e;if(n.length>1){let e=n[n.length-1];if(" "===n[n.length-2]&&("i"===e||"I"===e||"s"===e||"S"===e))return"".concat(t,'="').concat(n.slice(0,-2),'" ').concat(e)}return"".concat(t,'="').concat(n,'"')}return e}function e4(e,t){em(e,(e,r)=>{let{replaceWith:n}=r;if("at-rule"===e.kind&&"@slot"===e.name)n(t);else if("at-rule"===e.kind&&("@keyframes"===e.name||"@property"===e.name))return Object.assign(e,eh([ec(e.name,e.params,e.nodes)])),1})}var e9=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","mask-image","--tw-mask-top","--tw-mask-top-from-color","--tw-mask-top-from-position","--tw-mask-top-to-color","--tw-mask-top-to-position","--tw-mask-right","--tw-mask-right-from-color","--tw-mask-right-from-position","--tw-mask-right-to-color","--tw-mask-right-to-position","--tw-mask-bottom","--tw-mask-bottom-from-color","--tw-mask-bottom-from-position","--tw-mask-bottom-to-color","--tw-mask-bottom-to-position","--tw-mask-left","--tw-mask-left-from-color","--tw-mask-left-from-position","--tw-mask-left-to-color","--tw-mask-left-to-position","--tw-mask-linear","--tw-mask-linear-position","--tw-mask-linear-from-color","--tw-mask-linear-from-position","--tw-mask-linear-to-color","--tw-mask-linear-to-position","--tw-mask-radial","--tw-mask-radial-shape","--tw-mask-radial-size","--tw-mask-radial-position","--tw-mask-radial-from-color","--tw-mask-radial-from-position","--tw-mask-radial-to-color","--tw-mask-radial-to-position","--tw-mask-conic","--tw-mask-conic-position","--tw-mask-conic-from-color","--tw-mask-conic-from-position","--tw-mask-conic-to-color","--tw-mask-conic-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","mask-composite","mask-mode","mask-type","mask-size","mask-clip","mask-position","mask-repeat","mask-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function e7(e,t){let{onInvalidCandidate:r,respectImportant:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=new Map,o=[],i=new Map;for(let n of e){if(t.invalidCandidates.has(n)){null==r||r(n);continue}let e=t.parseCandidate(n);if(0===e.length){null==r||r(n);continue}i.set(n,e)}let l=0;(null==n||n)&&(l|=1);let s=t.getVariantOrder();for(let[e,n]of i){let i=!1;for(let r of n){let n=t.compileAstNodes(r,l);if(0!==n.length)for(let{node:t,propertySort:l}of(i=!0,n)){let n=0n;for(let e of r.variants)n|=1n<<BigInt(s.get(e));a.set(t,{properties:l,variants:n,candidate:e}),o.push(t)}}i||null==r||r(e)}return o.sort((e,t)=>{var r,n;let o=a.get(e),i=a.get(t);if(o.variants-i.variants!==0n)return Number(o.variants-i.variants);let l=0;for(;l<o.properties.order.length&&l<i.properties.order.length&&o.properties.order[l]===i.properties.order[l];)l+=1;return(null!=(r=o.properties.order[l])?r:1/0)-(null!=(n=i.properties.order[l])?n:1/0)||i.properties.count-o.properties.count||e0(o.candidate,i.candidate)}),{astNodes:o,nodeSorting:a}}function e8(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;if("arbitrary"===t.kind)return t.relative&&0===n?null:void(e.nodes=[eu(t.selector,e.nodes)]);let{applyFn:a}=r.get(t.root);if("compound"===t.kind){let o=ec("@slot");if(null===e8(o,t.variant,r,n+1)||"not"===t.root&&o.nodes.length>1)return null;for(let e of o.nodes)if("rule"!==e.kind&&"at-rule"!==e.kind||null===a(e,t))return null;return em(o.nodes,t=>{if(("rule"===t.kind||"at-rule"===t.kind)&&t.nodes.length<=0)return t.nodes=e.nodes,1}),void(e.nodes=o.nodes)}if(null===a(e,t))return null}function te(e){var t,r;let n=null!=(r=null==(t=e.options)?void 0:t.types)?r:[];return n.length>1&&n.includes("any")}function tt(e,t){let r=0,n=eu("&",e),a=new Set,o=new er(()=>new Set),i=new er(()=>new Set);em([n],(e,n)=>{let{parent:l,path:s}=n;if("at-rule"===e.kind){if("@keyframes"===e.name)return em(e.nodes,e=>{if("at-rule"===e.kind&&"@apply"===e.name)throw Error("You cannot use `@apply` inside `@keyframes`.")}),1;if("@utility"===e.name){let r=e.params.replace(/-\*$/,"");i.get(r).add(e),em(e.nodes,r=>{if("at-rule"===r.kind&&"@apply"===r.name)for(let n of(a.add(e),tr(r,t)))o.get(e).add(n)});return}if("@apply"===e.name){if(null===l)return;for(let n of(r|=1,a.add(l),tr(e,t)))for(let t of s)t!==e&&a.has(t)&&o.get(t).add(n)}}});let l=new Set,s=[],c=new Set;for(let e of a)!function e(r){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!l.has(r)){if(c.has(r)){let e=n[(n.indexOf(r)+1)%n.length];throw"at-rule"===r.kind&&"@utility"===r.name&&"at-rule"===e.kind&&"@utility"===e.name&&em(r.nodes,r=>{if("at-rule"===r.kind&&"@apply"===r.name)for(let n of r.params.split(/\s+/g))for(let r of t.parseCandidate(n))switch(r.kind){case"arbitrary":break;case"static":case"functional":if(e.params.replace(/-\*$/,"")===r.root)throw Error("You cannot `@apply` the `".concat(n,"` utility here because it creates a circular dependency."))}}),Error("Circular dependency detected:\n\n".concat(eg([r]),"\nRelies on:\n\n").concat(eg([e])))}for(let t of(c.add(r),o.get(r)))for(let a of i.get(t))n.push(r),e(a,n),n.pop();l.add(r),c.delete(r),s.push(r)}}(e);for(let e of s)"nodes"in e&&em(e.nodes,(e,r)=>{let{replaceWith:n}=r;if("at-rule"!==e.kind||"@apply"!==e.name)return;let a=e.params.split(/(\s+)/g),o={},i=0;for(let[e,t]of a.entries())e%2==0&&(o[t]=i),i+=t.length;{let r=e7(Object.keys(o),t,{respectImportant:!1,onInvalidCandidate:e=>{if(t.theme.prefix&&!e.startsWith(t.theme.prefix))throw Error("Cannot apply unprefixed utility class `".concat(e,"`. Did you mean `").concat(t.theme.prefix,":").concat(e,"`?"));if(t.invalidCandidates.has(e))throw Error("Cannot apply utility class `".concat(e,"` because it has been explicitly disabled: https://tailwindcss.com/docs/detecting-classes-in-source-files#explicitly-excluding-classes"));let r=p(e,":");if(r.length>1){let n=r.pop();if(t.candidatesToCss([n])[0]){let n=t.candidatesToCss(r.map(e=>"".concat(e,":[--tw-variant-check:1]"))),a=r.filter((e,t)=>null===n[t]);if(a.length>0){if(1===a.length)throw Error("Cannot apply utility class `".concat(e,"` because the ").concat(a.map(e=>"`".concat(e,"`"))," variant does not exist."));{let t=new Intl.ListFormat("en",{style:"long",type:"conjunction"});throw Error("Cannot apply utility class `".concat(e,"` because the ").concat(t.format(a.map(e=>"`".concat(e,"`")))," variants do not exist."))}}}}throw 0===t.theme.size?Error("Cannot apply unknown utility class `".concat(e,"`. Are you using CSS modules or similar and missing `@reference`? https://tailwindcss.com/docs/functions-and-directives#reference-directive")):Error("Cannot apply unknown utility class `".concat(e,"`"))}}),a=e.src,i=r.astNodes.map(e=>{var t;let n=null==(t=r.nodeSorting.get(e))?void 0:t.candidate,i=n?o[n]:void 0;if(e=structuredClone(e),!a||!n||void 0===i)return em([e],e=>{e.src=a}),e;let l=[a[0],a[1],a[2]];return l[1]+=7+i,l[2]=l[1]+n.length,em([e],e=>{e.src=l}),e}),l=[];for(let e of i)if("rule"===e.kind)for(let t of e.nodes)l.push(t);else l.push(e);n(l)}});return r}function*tr(e,t){for(let r of e.params.split(/\s+/g))for(let e of t.parseCandidate(r))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root}}async function tn(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=0,i=[];return em(e,(e,l)=>{let{replaceWith:s}=l;if("at-rule"===e.kind&&("@import"===e.name||"@reference"===e.name)){let l=function(e){let t,r=null,n=null,a=null;for(let o=0;o<e.length;o++){let i=e[o];if("separator"!==i.kind){if("word"===i.kind&&!t){if(!i.value||'"'!==i.value[0]&&"'"!==i.value[0])return null;t=i.value.slice(1,-1);continue}if("function"===i.kind&&"url"===i.value.toLowerCase()||!t)return null;if(("word"===i.kind||"function"===i.kind)&&"layer"===i.value.toLowerCase()){if(r)return null;if(a)throw Error("`layer(…)` in an `@import` should come before any other functions or conditions");r="nodes"in i?eo(i.nodes):"";continue}if("function"===i.kind&&"supports"===i.value.toLowerCase()){if(a)return null;a=eo(i.nodes);continue}n=eo(e.slice(o));break}}return t?{uri:t,layer:r,media:n,supports:a}:null}(ei(e.params));if(null===l)return;"@reference"===e.name&&(l.media="reference"),o|=2;let{uri:c,layer:u,media:d,supports:f}=l;if(c.startsWith("data:")||c.startsWith("http://")||c.startsWith("https://"))return;let p=ep({},[]);return i.push((async()=>{if(n>100)throw Error("Exceeded maximum recursion depth while resolving `".concat(c,"` in `").concat(t,"`)"));let o=await r(c,t),i=H(o.content,{from:a?o.path:void 0});await tn(i,o.base,r,n+1,a),p.nodes=function(e,t,r,n,a){let o=t;if(null!==r){let t=ec("@layer",r,o);t.src=e.src,o=[t]}if(null!==n){let t=ec("@media",n,o);t.src=e.src,o=[t]}if(null!==a){let t=ec("@supports","("===a[0]?a:"(".concat(a,")"),o);t.src=e.src,o=[t]}return o}(e,[ep({base:o.base},i)],u,d,f)})()),s(p),1}}),i.length>0&&await Promise.all(i),o}function ta(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return Array.isArray(e)&&2===e.length&&"object"==typeof e[1]&&(e[1],1)?r?null!=(t=e[1][r])?t:null:e[0]:Array.isArray(e)&&null===r?e.join(", "):"string"==typeof e&&null===r?e:null}var to=/^[a-zA-Z0-9-_%/\.]+$/;function ti(e){if("container"===e[0])return null;for(let t of("animation"===(e=structuredClone(e))[0]&&(e[0]="animate"),"aspectRatio"===e[0]&&(e[0]="aspect"),"borderRadius"===e[0]&&(e[0]="radius"),"boxShadow"===e[0]&&(e[0]="shadow"),"colors"===e[0]&&(e[0]="color"),"containers"===e[0]&&(e[0]="container"),"fontFamily"===e[0]&&(e[0]="font"),"fontSize"===e[0]&&(e[0]="text"),"letterSpacing"===e[0]&&(e[0]="tracking"),"lineHeight"===e[0]&&(e[0]="leading"),"maxWidth"===e[0]&&(e[0]="container"),"screens"===e[0]&&(e[0]="breakpoint"),"transitionTimingFunction"===e[0]&&(e[0]="ease"),e))if(!to.test(t))return null;return e.map((e,t,r)=>"1"===e&&t!==r.length-1?"":e).map(e=>e.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(e,t,r)=>"".concat(t,"-").concat(r.toLowerCase()))).filter((t,r)=>"DEFAULT"!==t||r!==e.length-1).join("-")}function tl(e){let t=[];for(let r of p(e,".")){if(!r.includes("[")){t.push(r);continue}let e=0;for(;;){let n=r.indexOf("[",e),a=r.indexOf("]",n);if(-1===n||-1===a)break;n>e&&t.push(r.slice(e,n)),t.push(r.slice(n+1,a)),e=a+1}e<=r.length-1&&t.push(r.slice(e))}return t}function ts(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}function tc(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];for(let a of t)if(null!=a)for(let t of Reflect.ownKeys(a)){n.push(t);let o=r(e[t],a[t],n);void 0!==o?e[t]=o:ts(e[t])&&ts(a[t])?e[t]=tc({},[e[t],a[t]],r,n):e[t]=a[t],n.pop()}return e}function tu(e,t,r){return function(n,a){let o=n.lastIndexOf("/"),i=null;-1!==o&&(i=n.slice(o+1).trim(),n=n.slice(0,o).trim());let l=(()=>{var a,o,i,l,s;let c=tl(n),[u,d]=function(e,t){var r,n,a;if(1===t.length&&t[0].startsWith("--"))return[e.get([t[0]]),e.getOptions(t[0])];let o=ti(t),i=new Map,l=new er(()=>new Map),s=e.namespace("--".concat(o));if(0===s.size)return[null,0];let c=new Map;for(let[t,r]of s){if(!t||!t.includes("--")){i.set(t,r),c.set(t,e.getOptions(t?"--".concat(o,"-").concat(t):"--".concat(o)));continue}let n=t.indexOf("--"),a=t.slice(0,n),s=t.slice(n+2);s=s.replace(/-([a-z])/g,(e,t)=>t.toUpperCase()),l.get(""===a?null:a).set(s,[r,e.getOptions("--".concat(o).concat(t))])}let u=e.getOptions("--".concat(o));for(let[e,t]of l){let r=i.get(e);if("string"!=typeof r)continue;let n={},a={};for(let[e,[r,o]]of t)n[e]=r,a[e]=o;i.set(e,[r,n]),c.set(e,[u,a])}let d={},f={};for(let[e,t]of i)tf(d,[null!=e?e:"DEFAULT"],t);for(let[e,t]of c)tf(f,[null!=e?e:"DEFAULT"],t);return"DEFAULT"===t[t.length-1]?[null!=(r=null==d?void 0:d.DEFAULT)?r:null,null!=(n=f.DEFAULT)?n:0]:"DEFAULT"in d&&1===Object.keys(d).length?[d.DEFAULT,null!=(a=f.DEFAULT)?a:0]:(d.__CSS_VALUES__=f,[d,f])}(e.theme,c),f=r(null!=(o=td(null!=(a=t())?a:{},c))?o:null);if("string"==typeof f&&(f=f.replace("<alpha-value>","1")),"object"!=typeof u)return"object"!=typeof d&&4&d&&null!=f?f:u;if(null!==f&&"object"==typeof f&&!Array.isArray(f)){let e=tc({},[f],(e,t)=>t);if(null===u&&Object.hasOwn(f,"__CSS_VALUES__")){let t={};for(let r in f.__CSS_VALUES__)t[r]=f[r],delete e[r];u=t}for(let t in u)"__CSS_VALUES__"!==t&&((null==f||null==(i=f.__CSS_VALUES__)?void 0:i[t])&4&&void 0!==td(e,t.split("-"))||(e[X(t)]=u[t]));return e}if(Array.isArray(u)&&Array.isArray(d)&&Array.isArray(f)){let e=u[0],t=u[1];for(let r of(4&d[0]&&(e=null!=(l=f[0])?l:e),Object.keys(t)))4&d[1][r]&&(t[r]=null!=(s=f[1][r])?s:t[r]);return[e,t]}return null!=u?u:f})();return i&&"string"==typeof l&&(l=eM(l,i)),null!=l?l:a}}function td(e,t){for(let r=0;r<t.length;++r){let n=t[r];if((null==e?void 0:e[n])===void 0){if(void 0===t[r+1])return;t[r+1]="".concat(n,"-").concat(t[r+1]);continue}e=e[n]}return e}function tf(e,t,r){for(let r of t.slice(0,-1))void 0===e[r]&&(e[r]={}),e=e[r];e[t[t.length-1]]=r}function tp(e){return{kind:"selector",value:e}}function th(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;for(let a=0;a<e.length;a++){var n;let o=e[a],i=!1,l=0,s=null!=(n=t(o,{parent:r,replaceWith(t){i||(i=!0,Array.isArray(t)?0===t.length?(e.splice(a,1),l=0):1===t.length?(e[a]=t[0],l=1):(e.splice(a,1,...t),l=t.length):(e[a]=t,l=1))}}))?n:0;if(i){0===s?a--:a+=l-1;continue}if(2===s||1!==s&&"function"===o.kind&&2===th(o.nodes,t,o))return 2}}function tm(e){let t="";for(let r of e)switch(r.kind){case"combinator":case"selector":case"separator":case"value":t+=r.value;break;case"function":t+=r.value+"("+tm(r.nodes)+")"}return t}function tv(e){e=e.replaceAll("\r\n","\n");let t=[],r=[],n=null,a="",o;for(let i=0;i<e.length;i++){let l=e.charCodeAt(i);switch(l){case 44:case 62:case 10:case 32:case 43:case 9:case 126:{if(a.length>0){let e=tp(a);n?n.nodes.push(e):t.push(e),a=""}let r=i,l=i+1;for(;l<e.length&&(44===(o=e.charCodeAt(l))||62===o||10===o||32===o||43===o||9===o||126===o);l++);i=l-1;let s=e.slice(r,l),c=","===s.trim()?{kind:"separator",value:s}:{kind:"combinator",value:s};n?n.nodes.push(c):t.push(c);break}case 40:{let l={kind:"function",value:a,nodes:[]};if(a="",":not"!==l.value&&":where"!==l.value&&":has"!==l.value&&":is"!==l.value){let r=i+1,s=0;for(let t=i+1;t<e.length;t++){if(40===(o=e.charCodeAt(t))){s++;continue}if(41===o){if(0===s){i=t;break}s--}}let c=i;l.nodes.push({kind:"value",value:e.slice(r,c)}),a="",i=c,n?n.nodes.push(l):t.push(l);break}n?n.nodes.push(l):t.push(l),r.push(l),n=l;break}case 41:{let e=r.pop();if(a.length>0){let t=tp(a);e.nodes.push(t),a=""}n=r.length>0?r[r.length-1]:null;break}case 46:case 58:case 35:if(a.length>0){let e=tp(a);n?n.nodes.push(e):t.push(e)}a=String.fromCharCode(l);break;case 91:{if(a.length>0){let e=tp(a);n?n.nodes.push(e):t.push(e)}a="";let r=i,l=0;for(let t=i+1;t<e.length;t++){if(91===(o=e.charCodeAt(t))){l++;continue}if(93===o){if(0===l){i=t;break}l--}}a+=e.slice(r,i+1);break}case 39:case 34:{let t=i;for(let t=i+1;t<e.length;t++)if(92===(o=e.charCodeAt(t)))t+=1;else if(o===l){i=t;break}a+=e.slice(t,i+1);break}case 92:{let t=e.charCodeAt(i+1);a+=String.fromCharCode(l)+String.fromCharCode(t),i+=1;break}default:a+=String.fromCharCode(l)}}return a.length>0&&t.push(tp(a)),t}var tg=/^[a-z@][a-zA-Z0-9/%._-]*$/;function tw(e){let{designSystem:t,ast:r,resolvedConfig:n,featuresRef:a,referenceMode:o,src:i}=e,l={addBase(e){if(o)return;let n=tk(e);a.current|=eX(n,t);let l=ec("@layer","base",n);em([l],e=>{e.src=i}),r.push(l)},addVariant(e,r){if(!e2.test(e))throw Error("`addVariant('".concat(e,"')` defines an invalid variant name. Variants should only contain alphanumeric, dashes, or underscore characters and start with a lowercase letter or number."));if("string"==typeof r){if(r.includes(":merge("))return}else if(Array.isArray(r)){if(r.some(e=>e.includes(":merge(")))return}else if("object"==typeof r){let e=function(t,r){return Object.entries(t).some(t=>{let[n,a]=t;return n.includes(r)||"object"==typeof a&&e(a,r)})};if(e(r,":merge("))return}"string"==typeof r||Array.isArray(r)?t.variants.static(e,e=>{e.nodes=tb(r,e.nodes)},{compounds:e3("string"==typeof r?[r]:r)}):"object"==typeof r&&t.variants.fromAst(e,tk(r))},matchVariant(e,r,n){var a;function o(e,t,n){var a;return tb(r(e,{modifier:null!=(a=null==t?void 0:t.value)?a:null}),n)}try{let e=r("a",{modifier:null});if("string"==typeof e&&e.includes(":merge(")||Array.isArray(e)&&e.some(e=>e.includes(":merge(")))return}catch(e){}let i=Object.keys(null!=(a=null==n?void 0:n.values)?a:{});t.variants.group(()=>{t.variants.functional(e,(e,t)=>{if(!t.value){if((null==n?void 0:n.values)&&"DEFAULT"in n.values){e.nodes=o(n.values.DEFAULT,t.modifier,e.nodes);return}return null}if("arbitrary"===t.value.kind)e.nodes=o(t.value.value,t.modifier,e.nodes);else{if("named"!==t.value.kind||null==n||!n.values)return null;let r=n.values[t.value.value];if("string"!=typeof r)return null;e.nodes=o(r,t.modifier,e.nodes)}})},(e,t)=>{var r,a,o,l,s,c,u,d;if("functional"!==e.kind||"functional"!==t.kind)return 0;let f=e.value?e.value.value:"DEFAULT",p=t.value?t.value.value:"DEFAULT",h=null!=(s=null==n||null==(r=n.values)?void 0:r[f])?s:f,m=null!=(c=null==n||null==(a=n.values)?void 0:a[p])?c:p;if(n&&"function"==typeof n.sort)return n.sort({value:h,modifier:null!=(u=null==(o=e.modifier)?void 0:o.value)?u:null},{value:m,modifier:null!=(d=null==(l=t.modifier)?void 0:l.value)?d:null});let v=i.indexOf(f),g=i.indexOf(p);return(v=-1===v?i.length:v)!==(g=-1===g?i.length:g)?v-g:h<m?-1:1}),t.variants.suggest(e,()=>{var e;return Object.keys(null!=(e=null==n?void 0:n.values)?e:{}).filter(e=>"DEFAULT"!==e)})},addUtilities(e){let n=(e=Array.isArray(e)?e:[e]).flatMap(e=>Object.entries(e));n=n.flatMap(e=>{let[t,r]=e;return p(t,",").map(e=>[e.trim(),r])});let l=new er(()=>[]);for(let[e,t]of n){if(e.startsWith("@keyframes ")){if(!o){let n=eu(e,tk(t));em([n],e=>{e.src=i}),r.push(n)}continue}let n=tv(e),a=!1;if(th(n,e=>{if("selector"===e.kind&&"."===e.value[0]&&tg.test(e.value.slice(1))){let r=e.value;e.value="&";let o=tm(n),i=r.slice(1),s="&"===o?tk(t):[eu(o,tk(t))];l.get(i).push(...s),a=!0,e.value=r;return}if("function"===e.kind&&":not"===e.value)return 1}),!a)throw Error("`addUtilities({ '".concat(e,"' : … })` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. `.scrollbar-none`."))}for(let[e,r]of l)t.theme.prefix&&em(r,e=>{if("rule"===e.kind){let r=tv(e.selector);th(r,e=>{"selector"===e.kind&&"."===e.value[0]&&(e.value=".".concat(t.theme.prefix,"\\:").concat(e.value.slice(1)))}),e.selector=tm(r)}}),t.utilities.static(e,n=>{let o=structuredClone(r);return ty(o,e,n.raw),a.current|=tt(o,t),o})},matchUtilities(e,r){let n=(null==r?void 0:r.type)?Array.isArray(null==r?void 0:r.type)?r.type:[r.type]:["any"];for(let[o,i]of Object.entries(e)){let e=function(e){let{negative:l}=e;return e=>{var s,c,u,d,f,p,h;let v;if((null==(s=e.value)?void 0:s.kind)==="arbitrary"&&n.length>0&&!n.includes("any")&&(e.value.dataType&&!n.includes(e.value.dataType)||!e.value.dataType&&!m(e.value.value,n)))return;let g=n.includes("color"),w=null,b=!1;{let t=null!=(u=null==r?void 0:r.values)?u:{};g&&(t=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentcolor"},t)),e.value?"arbitrary"===e.value.kind?w=e.value.value:e.value.fraction&&t[e.value.fraction]?(w=t[e.value.fraction],b=!0):t[e.value.value]?w=t[e.value.value]:t.__BARE_VALUE__&&(w=null!=(d=t.__BARE_VALUE__(e.value))?d:null,b=null!=(f=null!==e.value.fraction&&(null==w?void 0:w.includes("/")))&&f):w=null!=(p=t.DEFAULT)?p:null}if(null===w)return;{let t=null!=(h=null==r?void 0:r.modifiers)?h:null;v=e.modifier?"any"===t||"arbitrary"===e.modifier.kind?e.modifier.value:(null==t?void 0:t[e.modifier.value])?t[e.modifier.value]:g&&!Number.isNaN(Number(e.modifier.value))?"".concat(e.modifier.value,"%"):null:null}if(e.modifier&&null===v&&!b)return(null==(c=e.value)?void 0:c.kind)==="arbitrary"?null:void 0;g&&null!==v&&(w=eM(w,v)),l&&(w="calc(".concat(w," * -1)"));let x=tk(i(w,{modifier:v}));return ty(x,o,e.raw),a.current|=tt(x,t),x}};if(!tg.test(o))throw Error("`matchUtilities({ '".concat(o,"' : … })` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. `scrollbar`."));(null==r?void 0:r.supportsNegativeValues)&&t.utilities.functional("-".concat(o),e({negative:!0}),{types:n}),t.utilities.functional(o,e({negative:!1}),{types:n}),t.utilities.suggest(o,()=>{var e,t,n;let a=new Set(Object.keys(null!=(e=null==r?void 0:r.values)?e:{}));a.delete("__BARE_VALUE__"),a.delete("__CSS_VALUES__"),a.has("DEFAULT")&&(a.delete("DEFAULT"),a.add(null));let o=null!=(t=null==r?void 0:r.modifiers)?t:{},i="any"===o?[]:Object.keys(o);return[{supportsNegative:null!=(n=null==r?void 0:r.supportsNegativeValues)&&n,values:Array.from(a),modifiers:i}]})}},addComponents(e,t){this.addUtilities(e,t)},matchComponents(e,t){this.matchUtilities(e,t)},theme:tu(t,()=>{var e;return null!=(e=n.theme)?e:{}},e=>e),prefix:e=>e,config(e,t){let r=n;if(!e)return r;let a=tl(e);for(let e=0;e<a.length;++e){let n=a[e];if(void 0===r[n])return t;r=r[n]}return null!=r?r:t}};return l.addComponents=l.addComponents.bind(l),l.matchComponents=l.matchComponents.bind(l),l}function tk(e){let t=[];for(let[r,n]of(e=Array.isArray(e)?e:[e]).flatMap(e=>Object.entries(e)))if(null!=n&&!1!==n)if("object"!=typeof n){if(!r.startsWith("--")){if("@slot"===n){t.push(eu(r,[ec("@slot")]));continue}r=r.replace(/([A-Z])/g,"-$1").toLowerCase()}t.push(ed(r,String(n)))}else if(Array.isArray(n))for(let e of n)"string"==typeof e?t.push(ed(r,e)):t.push(eu(r,tk(e)));else t.push(eu(r,tk(n)));return t}function tb(e,t){return("string"==typeof e?[e]:e).flatMap(e=>{if(!e.trim().endsWith("}"))return eu(e,t);{let r=H(e.replace("}","{@slot}}"));return e4(r,t),r}})}function ty(e,t,r){em(e,e=>{if("rule"===e.kind){let n=tv(e.selector);th(n,e=>{"selector"===e.kind&&e.value===".".concat(t)&&(e.value=".".concat(G(r)))}),e.selector=tm(n)}})}var tx={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function tA(e,t){var r,n,a,o;let i={design:e,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(tx)};for(let e of t)!function e(t,r){var n,a,o,i;let{config:l,base:s,path:c,reference:u,src:d}=r,f=[];for(let e of null!=(n=l.plugins)?n:[])"__isOptionsFunction"in e?f.push({...e(),reference:u,src:d}):"handler"in e?f.push({...e,reference:u,src:d}):f.push({handler:e,reference:u,src:d});if(Array.isArray(l.presets)&&0===l.presets.length)throw Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let r of null!=(a=l.presets)?a:[])e(t,{path:c,base:s,config:r,reference:u,src:d});for(let r of f)t.plugins.push(r),r.config&&e(t,{path:c,base:s,config:r.config,reference:!!r.reference,src:null!=(o=r.src)?o:d});let p=null!=(i=l.content)?i:[];for(let e of Array.isArray(p)?p:p.files)t.content.files.push("object"==typeof e?e:{base:s,pattern:e});t.configs.push(l)}(i,e);for(let e of i.configs)"darkMode"in e&&void 0!==e.darkMode&&(i.result.darkMode=null!=(r=e.darkMode)?r:null),"prefix"in e&&void 0!==e.prefix&&(i.result.prefix=null!=(n=e.prefix)?n:""),"blocklist"in e&&void 0!==e.blocklist&&(i.result.blocklist=null!=(a=e.blocklist)?a:[]),"important"in e&&void 0!==e.important&&(i.result.important=null!=(o=e.important)&&o);let s=function(e){let t=new Set,r=tu(e.design,()=>e.theme,a),n=Object.assign(r,{theme:r,colors:l});function a(e){var t;return"function"==typeof e?null!=(t=e(n))?t:null:null!=e?e:null}for(let r of e.configs){var o,i,s;let n=null!=(i=r.theme)?i:{},a=null!=(s=n.extend)?s:{};for(let e in n)"extend"!==e&&t.add(e);for(let t in Object.assign(e.theme,n),a)null!=(o=e.extend)[t]||(o[t]=[]),e.extend[t].push(a[t])}for(let t in delete e.theme.extend,e.extend){let r=[e.theme[t],...e.extend[t]];e.theme[t]=()=>tc({},r.map(a),tz)}for(let t in e.theme)e.theme[t]=a(e.theme[t]);if(e.theme.screens&&"object"==typeof e.theme.screens)for(let t of Object.keys(e.theme.screens)){let r=e.theme.screens[t];r&&"object"==typeof r&&("raw"in r||"max"in r||"min"in r&&(e.theme.screens[t]=r.min))}return t}(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:s}}function tz(e,t){return Array.isArray(e)&&ts(e[0])?e.concat(t):Array.isArray(t)&&ts(t[0])&&ts(e)?[e,...t]:Array.isArray(t)?t:void 0}function tT(e){let{addVariant:t,config:r}=e,n=r("darkMode",null),[a,o=".dark"]=Array.isArray(n)?n:[n];if("variant"===a){let e;if(Array.isArray(o)||"function"==typeof o?e=o:"string"==typeof o&&(e=[o]),Array.isArray(e))for(let t of e)".dark"===t?(a=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):t.includes("&")||(a=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));o=e}null===a||("selector"===a?t("dark","&:where(".concat(o,", ").concat(o," *)")):"media"===a?t("dark","@media (prefers-color-scheme: dark)"):"variant"===a?t("dark",o):"class"===a&&t("dark","&:is(".concat(o," *)")))}var tj=/^[a-z]+$/;async function tC(e){let{designSystem:t,base:r,ast:n,loadModule:a,sources:o}=e,i=0,l=[],s=[];em(n,(e,t)=>{let{parent:r,replaceWith:n,context:a}=t;if("at-rule"===e.kind){if("@plugin"===e.name){var o;if(null!==r)throw Error("`@plugin` cannot be nested.");let t=e.params.slice(1,-1);if(0===t.length)throw Error("`@plugin` must have a path.");let s={};for(let t of null!=(o=e.nodes)?o:[]){if("declaration"!==t.kind)throw Error("Unexpected `@plugin` option:\n\n".concat(eg([t]),"\n\n`@plugin` options must be a flat list of declarations."));if(void 0===t.value)continue;let e=p(t.value,",").map(e=>{if("null"===(e=e.trim()))return null;if("true"===e)return!0;if("false"===e)return!1;if(!Number.isNaN(Number(e)))return Number(e);if('"'===e[0]&&'"'===e[e.length-1]||"'"===e[0]&&"'"===e[e.length-1])return e.slice(1,-1);if("{"===e[0]&&"}"===e[e.length-1])throw Error("Unexpected `@plugin` option: Value of declaration `".concat(eg([t]).trim(),"` is not supported.\n\nUsing an object as a plugin option is currently only supported in JavaScript configuration files."));return e});s[t.property]=1===e.length?e[0]:e}l.push([{id:t,base:a.base,reference:!!a.reference,src:e.src},Object.keys(s).length>0?s:null]),n([]),i|=4;return}if("@config"===e.name){if(e.nodes.length>0)throw Error("`@config` cannot have a body.");if(null!==r)throw Error("`@config` cannot be nested.");s.push({id:e.params.slice(1,-1),base:a.base,reference:!!a.reference,src:e.src}),n([]),i|=4;return}}}),function(e){for(let[t,r]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])e.utilities.static("bg-gradient-to-".concat(t),()=>[ed("--tw-gradient-position","to ".concat(r," in oklab")),ed("background-image","linear-gradient(var(--tw-gradient-stops))")]);e.utilities.static("bg-left-top",()=>[ed("background-position","left top")]),e.utilities.static("bg-right-top",()=>[ed("background-position","right top")]),e.utilities.static("bg-left-bottom",()=>[ed("background-position","left bottom")]),e.utilities.static("bg-right-bottom",()=>[ed("background-position","right bottom")]),e.utilities.static("object-left-top",()=>[ed("object-position","left top")]),e.utilities.static("object-right-top",()=>[ed("object-position","right top")]),e.utilities.static("object-left-bottom",()=>[ed("object-position","left bottom")]),e.utilities.static("object-right-bottom",()=>[ed("object-position","right bottom")]),e.utilities.functional("max-w-screen",t=>{if(!t.value||"arbitrary"===t.value.kind)return;let r=e.theme.resolve(t.value.value,["--breakpoint"]);if(r)return[ed("max-width",r)]}),e.utilities.static("overflow-ellipsis",()=>[ed("text-overflow","ellipsis")]),e.utilities.static("decoration-slice",()=>[ed("-webkit-box-decoration-break","slice"),ed("box-decoration-break","slice")]),e.utilities.static("decoration-clone",()=>[ed("-webkit-box-decoration-break","clone"),ed("box-decoration-break","clone")]),e.utilities.functional("flex-shrink",e=>{if(!e.modifier){if(!e.value)return[ed("flex-shrink","1")];if("arbitrary"===e.value.kind||N(e.value.value))return[ed("flex-shrink",e.value.value)]}}),e.utilities.functional("flex-grow",e=>{if(!e.modifier){if(!e.value)return[ed("flex-grow","1")];if("arbitrary"===e.value.kind||N(e.value.value))return[ed("flex-grow",e.value.value)]}}),e.utilities.static("order-none",()=>[ed("order","0")])}(t);let c=t.resolveThemeValue;if(t.resolveThemeValue=function(e,a){return e.startsWith("--")?c(e,a):(i|=tK({designSystem:t,base:r,ast:n,sources:o,configs:[],pluginDetails:[]}),t.resolveThemeValue(e,a))},!l.length&&!s.length)return 0;let[u,d]=await Promise.all([Promise.all(s.map(async e=>{let{id:t,base:r,reference:n,src:o}=e,i=await a(t,r,"config");return{path:t,base:i.base,config:i.module,reference:n,src:o}})),Promise.all(l.map(async e=>{let[{id:t,base:r,reference:n,src:o},i]=e,l=await a(t,r,"plugin");return{path:t,base:l.base,plugin:l.module,options:i,reference:n,src:o}}))]);return i|=tK({designSystem:t,base:r,ast:n,sources:o,configs:u,pluginDetails:d})}function tK(e){var t,r,n;let{designSystem:a,base:o,ast:i,sources:l,configs:s,pluginDetails:c}=e,u=0,d=[...c.map(e=>{if(!e.options)return{config:{plugins:[e.plugin]},base:e.base,reference:e.reference,src:e.src};if("__isOptionsFunction"in e.plugin)return{config:{plugins:[e.plugin(e.options)]},base:e.base,reference:e.reference,src:e.src};throw Error('The plugin "'.concat(e.path,'" does not accept options'))}),...s],{resolvedConfig:f}=tA(a,[{config:(t=a.theme,{theme:{...q,colors:e=>{let{theme:t}=e;return t("color",{})},extend:{fontSize:e=>{let{theme:t}=e;return{...t("text",{})}},boxShadow:e=>{let{theme:t}=e;return{...t("shadow",{})}},animation:e=>{let{theme:t}=e;return{...t("animate",{})}},aspectRatio:e=>{let{theme:t}=e;return{...t("aspect",{})}},borderRadius:e=>{let{theme:t}=e;return{...t("radius",{})}},screens:e=>{let{theme:t}=e;return{...t("breakpoint",{})}},letterSpacing:e=>{let{theme:t}=e;return{...t("tracking",{})}},lineHeight:e=>{let{theme:t}=e;return{...t("leading",{})}},transitionDuration:{DEFAULT:null!=(r=t.get(["--default-transition-duration"]))?r:null},transitionTimingFunction:{DEFAULT:null!=(n=t.get(["--default-transition-timing-function"]))?n:null},maxWidth:e=>{let{theme:t}=e;return{...t("container",{})}}}}}),base:o,reference:!0,src:void 0},...d,{config:{plugins:[tT]},base:o,reference:!0,src:void 0}]),{resolvedConfig:p,replacedThemeKeys:h}=tA(a,d),m={designSystem:a,ast:i,resolvedConfig:f,featuresRef:{set current(y){u|=y}}},v=tw({...m,referenceMode:!1,src:void 0}),g=a.resolveThemeValue;for(let{handler:e,reference:t,src:r}of(a.resolveThemeValue=function(e,t){if("-"===e[0]&&"-"===e[1])return g(e,t);let r=v.theme(e,void 0);return Array.isArray(r)&&2===r.length?r[0]:Array.isArray(r)?r.join(", "):"string"==typeof r?r:void 0},f.plugins))e(tw({...m,referenceMode:null!=t&&t,src:r}));if(function(e,t,r){let n,{theme:a}=t;for(let t of r){let r=ti([t]);r&&e.theme.clearNamespace("--".concat(r),4)}for(let[t,r]of(n=[],function e(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0;for(let o of Reflect.ownKeys(t)){var a;let i=t[o];if(null==i)continue;let l=[...r,o],s=null!=(a=n(i,l))?a:0;if(1!==s&&(2===s||!(!Array.isArray(i)&&"object"!=typeof i)&&2===e(i,l,n)))return 2}}(a,[],(e,t)=>{var r;if("number"==typeof(r=e)||"string"==typeof r)return n.push([t,e]),1;if(function(e){if(!Array.isArray(e)||2!==e.length||"string"!=typeof e[0]&&"number"!=typeof e[0]||void 0===e[1]||null===e[1]||"object"!=typeof e[1])return!1;for(let t of Reflect.ownKeys(e[1]))if("string"!=typeof t||"string"!=typeof e[1][t]&&"number"!=typeof e[1][t])return!1;return!0}(e)){for(let r of(n.push([t,e[0]]),Reflect.ownKeys(e[1])))n.push([[...t,"-".concat(r)],e[1][r]]);return 1}if(Array.isArray(e)&&e.every(e=>"string"==typeof e))return"fontSize"===t[0]?(n.push([t,e[0]]),e.length>=2&&n.push([[...t,"-line-height"],e[1]])):n.push([t,e.join(", ")]),1}),n)){if("string"!=typeof r&&"number"!=typeof r)continue;if("string"==typeof r&&(r=r.replace(/<alpha-value>/g,"1")),"opacity"===t[0]&&("number"==typeof r||"string"==typeof r)){let e="string"==typeof r?parseFloat(r):r;e>=0&&e<=1&&(r=100*e+"%")}let n=ti(t);n&&e.theme.add("--".concat(n),""+r,7)}if(Object.hasOwn(a,"fontFamily")){var o,i,l,s;{let t=ta(a.fontFamily.sans);t&&e.theme.hasDefault("--font-sans")&&(e.theme.add("--default-font-family",t,5),e.theme.add("--default-font-feature-settings",null!=(o=ta(a.fontFamily.sans,"fontFeatureSettings"))?o:"normal",5),e.theme.add("--default-font-variation-settings",null!=(i=ta(a.fontFamily.sans,"fontVariationSettings"))?i:"normal",5))}{let t=ta(a.fontFamily.mono);t&&e.theme.hasDefault("--font-mono")&&(e.theme.add("--default-mono-font-family",t,5),e.theme.add("--default-mono-font-feature-settings",null!=(l=ta(a.fontFamily.mono,"fontFeatureSettings"))?l:"normal",5),e.theme.add("--default-mono-font-variation-settings",null!=(s=ta(a.fontFamily.mono,"fontVariationSettings"))?s:"normal",5))}}}(a,p,h),function(e,t,r){for(let r of function(e){let t=[];if("keyframes"in e.theme)for(let[r,n]of Object.entries(e.theme.keyframes))t.push(ec("@keyframes",r,tk(n)));return t}(t))e.theme.addKeyframes(r)}(a,p,0),function(e,t){let r=e.theme.aria||{},n=e.theme.supports||{},a=e.theme.data||{};if(Object.keys(r).length>0){let e=t.variants.get("aria"),n=null==e?void 0:e.applyFn,a=null==e?void 0:e.compounds;t.variants.functional("aria",(e,t)=>{let a=t.value;return a&&"named"===a.kind&&a.value in r?null==n?void 0:n(e,{...t,value:{kind:"arbitrary",value:r[a.value]}}):null==n?void 0:n(e,t)},{compounds:a})}if(Object.keys(n).length>0){let e=t.variants.get("supports"),r=null==e?void 0:e.applyFn,a=null==e?void 0:e.compounds;t.variants.functional("supports",(e,t)=>{let a=t.value;return a&&"named"===a.kind&&a.value in n?null==r?void 0:r(e,{...t,value:{kind:"arbitrary",value:n[a.value]}}):null==r?void 0:r(e,t)},{compounds:a})}if(Object.keys(a).length>0){let e=t.variants.get("data"),r=null==e?void 0:e.applyFn,n=null==e?void 0:e.compounds;t.variants.functional("data",(e,t)=>{let n=t.value;return n&&"named"===n.kind&&n.value in a?null==r?void 0:r(e,{...t,value:{kind:"arbitrary",value:a[n.value]}}):null==r?void 0:r(e,t)},{compounds:n})}}(p,a),function(e,t){var r,n,a;let o=e.theme.screens||{},i=null!=(n=null==(r=t.variants.get("min"))?void 0:r.order)?n:0,l=[];for(let[e,r]of Object.entries(o)){let n=function(r){t.variants.static(e,e=>{e.nodes=[ec("@media",u,e.nodes)]},{order:r})},o=t.variants.get(e),s=t.theme.resolveValue(e,["--breakpoint"]);if(o&&s&&!t.theme.hasDefault("--breakpoint-".concat(e)))continue;let c=!0;"string"==typeof r&&(c=!1);let u=(Array.isArray(a=r)?a:[a]).map(e=>"string"==typeof e?{min:e}:e&&"object"==typeof e?e:null).map(e=>{if(null===e)return null;if("raw"in e)return e.raw;let t="";return void 0!==e.max&&(t+="".concat(e.max," >= ")),t+="width",void 0!==e.min&&(t+=" >= ".concat(e.min)),"(".concat(t,")")}).filter(Boolean).join(", ");c?l.push(n):n(i)}if(0!==l.length){for(let[,e]of t.variants.variants)e.order>i&&(e.order+=l.length);for(let[e,r]of(t.variants.compareFns=new Map(Array.from(t.variants.compareFns).map(e=>{let[t,r]=e;return t>i&&(t+=l.length),[t,r]})),l.entries()))r(i+e+1)}}(p,a),function(e,t){let r=e.theme.container||{};if("object"!=typeof r||null===r)return;let n=function(e,t){let{center:r,padding:n,screens:a}=e,o=[],i=null;if(r&&o.push(ed("margin-inline","auto")),("string"==typeof n||"object"==typeof n&&null!==n&&"DEFAULT"in n)&&o.push(ed("padding-inline","string"==typeof n?n:n.DEFAULT)),"object"==typeof a&&null!==a){i=new Map;let e=Array.from(t.theme.namespace("--breakpoint").entries());if(e.sort((e,t)=>eN(e[1],t[1],"asc")),e.length>0){let[t]=e[0];o.push(ec("@media","(width >= --theme(--breakpoint-".concat(t,"))"),[ed("max-width","none")]))}for(let[e,t]of Object.entries(a)){if("object"==typeof t)if(!("min"in t))continue;else t=t.min;i.set(e,ec("@media","(width >= ".concat(t,")"),[ed("max-width",t)]))}}if("object"==typeof n&&null!==n){let e=Object.entries(n).filter(e=>{let[t]=e;return"DEFAULT"!==t}).map(e=>{let[r,n]=e;return[r,t.theme.resolveValue(r,["--breakpoint"]),n]}).filter(Boolean);for(let[t,,r]of(e.sort((e,t)=>eN(e[1],t[1],"asc")),e))if(i&&i.has(t))i.get(t).nodes.push(ed("padding-inline",r));else{if(i)continue;o.push(ec("@media","(width >= theme(--breakpoint-".concat(t,"))"),[ed("padding-inline",r)]))}}if(i)for(let[,e]of i)o.push(e);return o}(r,t);0!==n.length&&t.utilities.static("container",()=>structuredClone(n))}(p,a),!a.theme.prefix&&f.prefix){if(f.prefix.endsWith("-")&&(f.prefix=f.prefix.slice(0,-1),console.warn('The prefix "'.concat(f.prefix,'" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing `-` to silence this warning.'))),!tj.test(f.prefix))throw Error('The prefix "'.concat(f.prefix,'" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.'));a.theme.prefix=f.prefix}if(a.important||!0!==f.important||(a.important=!0),"string"==typeof f.important){let e=f.important;em(i,(t,r)=>{let{replaceWith:n,parent:a}=r;if("at-rule"===t.kind&&"@tailwind"===t.name&&"utilities"===t.params)return(null==a?void 0:a.kind)==="rule"&&a.selector===e||n(es(e,[t])),2})}for(let e of f.blocklist)a.invalidCandidates.add(e);for(let e of f.content.files){if("raw"in e)throw Error("Error in the config file/plugin/preset. The `content` key contains a `raw` entry:\n\n".concat(JSON.stringify(e,null,2),"\n\nThis feature is not currently supported."));let t=!1;"!"==e.pattern[0]&&(t=!0,e.pattern=e.pattern.slice(1)),l.push({...e,negated:t})}return u}var tS=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/,tV=/^[a-z]+$/,tE=(e=>(e[e.None=0]="None",e[e.AtProperty=1]="AtProperty",e[e.ColorMix=2]="ColorMix",e[e.All=3]="All",e))(tE||{});function tN(){throw Error("No `loadModule` function provided to `compile`")}function tO(){throw Error("No `loadStylesheet` function provided to `compile`")}var tW=(e=>(e[e.None=0]="None",e[e.AtApply=1]="AtApply",e[e.AtImport=2]="AtImport",e[e.JsPluginCompat=4]="JsPluginCompat",e[e.ThemeFunction=8]="ThemeFunction",e[e.Utilities=16]="Utilities",e[e.Variants=32]="Variants",e))(tW||{});async function tF(e){let t,r,n,a,o,i,l,{base:s="",from:c,loadModule:u=tN,loadStylesheet:d=tO}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},f=0;e=[ep({base:s},e)],f|=await tn(e,s,d,0,void 0!==c);let h=null,v=new et,g=[],w=[],b=null,x=null,A=[],z=[],T=[],j=[],C=null;em(e,(e,t)=>{let{parent:r,replaceWith:n,context:a}=t;if("at-rule"===e.kind){var o;if("@tailwind"===e.name&&("utilities"===e.params||e.params.startsWith("utilities"))){if(null!==x||a.reference)return void n([]);for(let t of p(e.params," "))if(t.startsWith("source(")){let e=t.slice(7,-1);if("none"===e){C=e;continue}if('"'===e[0]&&'"'!==e[e.length-1]||"'"===e[0]&&"'"!==e[e.length-1]||"'"!==e[0]&&'"'!==e[0])throw Error("`source(…)` paths must be quoted.");C={base:null!=(o=a.sourceBase)?o:a.base,pattern:e.slice(1,-1)}}x=e,f|=16}if("@utility"===e.name){let t;if(null!==r)throw Error("`@utility` cannot be nested.");if(0===e.nodes.length)throw Error("`@utility ".concat(e.params,"` is empty. Utilities should include at least one property."));let n=(t=e.params,eU.test(t)?r=>{let n={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};em(e.nodes,e=>{if("declaration"!==e.kind||!e.value||!e.value.includes("--value(")&&!e.value.includes("--modifier("))return;let t=ei(e.value);ea(t,e=>{if("function"!==e.kind)return;if("--spacing"===e.value&&!(n["--modifier"].usedSpacingNumber&&n["--value"].usedSpacingNumber))return ea(e.nodes,e=>{if("function"!==e.kind||"--value"!==e.value&&"--modifier"!==e.value)return;let t=e.value;for(let o of e.nodes)if("word"===o.kind){var r,a;if("integer"===o.value)(r=n[t]).usedSpacingInteger||(r.usedSpacingInteger=!0);else if("number"===o.value&&((a=n[t]).usedSpacingNumber||(a.usedSpacingNumber=!0),n["--modifier"].usedSpacingNumber&&n["--value"].usedSpacingNumber))return 2}}),0;if("--value"!==e.value&&"--modifier"!==e.value)return;let t=p(eo(e.nodes),",");for(let[e,r]of t.entries())"-"!==(r=(r=(r=(r=r.replace(/\\\*/g,"*")).replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2")).replace(/\s+/g,"")).replace(/(-\*){2,}/g,"-*"))[0]||"-"!==r[1]||r.includes("-*")||(r+="-*"),t[e]=r;for(let r of(e.nodes=ei(t.join(",")),e.nodes))if("word"===r.kind&&('"'===r.value[0]||"'"===r.value[0])&&r.value[0]===r.value[r.value.length-1]){let t=r.value.slice(1,-1);n[e.value].literals.add(t)}else if("word"===r.kind&&"-"===r.value[0]&&"-"===r.value[1]){let t=r.value.replace(/-\*.*$/g,"");n[e.value].themeKeys.add(t)}else if("word"===r.kind&&("["!==r.value[0]||"]"!==r.value[r.value.length-1])&&!e$.includes(r.value)){console.warn('Unsupported bare value data type: "'.concat(r.value,'".\nOnly valid data types are: ').concat(e$.map(e=>'"'.concat(e,'"')).join(", "),".\n"));let t=r.value,n=structuredClone(e);ea(n.nodes,(e,r)=>{let{replaceWith:n}=r;"word"===e.kind&&e.value===t&&n({kind:"word",value:"¶"})});let a="^".repeat(eo([r]).length),o=eo([n]).indexOf("¶");console.warn(["```css",eo([e])," ".repeat(o)+a,"```"].join("\n"))}}),e.value=eo(t)}),r.utilities.functional(t.slice(0,-2),t=>{let n=structuredClone(e),a=t.value,o=t.modifier;if(null===a)return;let i=!1,l=!1,s=!1,c=!1,u=new Map,d=!1;if(em([n],(e,t)=>{var n;let{parent:f,replaceWith:p}=t;if((null==f?void 0:f.kind)!=="rule"&&(null==f?void 0:f.kind)!=="at-rule"||"declaration"!==e.kind||!e.value)return;let h=ei(e.value);(null!=(n=ea(h,(t,n)=>{let{replaceWith:h}=n;if("function"===t.kind){if("--value"===t.value){i=!0;let n=eH(a,t,r);return n?(l=!0,n.ratio?d=!0:u.set(e,f),h(n.nodes),1):(i||(i=!1),p([]),2)}else if("--modifier"===t.value){if(null===o)return p([]),2;s=!0;let e=eH(o,t,r);return e?(c=!0,h(e.nodes),1):(s||(s=!1),p([]),2)}}}))?n:0)===0&&(e.value=eo(h))}),i&&!l||s&&!c||d&&c||o&&!d&&!c)return null;if(d)for(let[e,t]of u){let r=t.nodes.indexOf(e);-1!==r&&t.nodes.splice(r,1)}return n.nodes}),r.utilities.suggest(t.slice(0,-2),()=>{let e=[],t=[];for(let[a,{literals:o,usedSpacingNumber:i,usedSpacingInteger:l,themeKeys:s}]of[[e,n["--value"]],[t,n["--modifier"]]]){for(let e of o)a.push(e);if(i)a.push(...eD);else if(l)for(let e of eD)N(e)&&a.push(e);for(let e of r.theme.keysInNamespaces(s))a.push(e.replace(eq,(e,t,r)=>"".concat(t,".").concat(r)))}return[{values:e,modifiers:t}]})}:e_.test(t)?r=>{r.utilities.static(t,()=>structuredClone(e.nodes))}:null);if(null===n){if(!e.params.endsWith("-*")){if(e.params.endsWith("*"))throw Error("`@utility ".concat(e.params,"` defines an invalid utility name. A functional utility must end in `-*`."));if(e.params.includes("*"))throw Error("`@utility ".concat(e.params,"` defines an invalid utility name. The dynamic portion marked by `-*` must appear once at the end."))}throw Error("`@utility ".concat(e.params,"` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter."))}w.push(n)}if("@source"===e.name){if(e.nodes.length>0)throw Error("`@source` cannot have a body.");if(null!==r)throw Error("`@source` cannot be nested.");let t=!1,o=!1,i=e.params;if("n"===i[0]&&i.startsWith("not ")&&(t=!0,i=i.slice(4)),"i"===i[0]&&i.startsWith("inline(")&&(o=!0,i=i.slice(7,-1)),'"'===i[0]&&'"'!==i[i.length-1]||"'"===i[0]&&"'"!==i[i.length-1]||"'"!==i[0]&&'"'!==i[0])throw Error("`@source` paths must be quoted.");let l=i.slice(1,-1);if(o){let e=t?j:T;for(let t of p(l," "))for(let r of function e(t){var r;let n=t.indexOf("{");if(-1===n)return[t];let a=[],o=t.slice(0,n),i=t.slice(n),l=0,s=i.lastIndexOf("}");for(let e=0;e<i.length;e++){let t=i[e];if("{"===t)l++;else if("}"===t&&0==--l){s=e;break}}if(-1===s)throw Error("The pattern `".concat(t,"` is not balanced."));let c=i.slice(1,s),u=i.slice(s+1),d;for(let t of(d=(r=c,d=tS.test(r)?function(e){let t=e.match(tS);if(!t)return[e];let[,r,n,a]=t,o=a?parseInt(a,10):void 0,i=[];if(/^-?\d+$/.test(r)&&/^-?\d+$/.test(n)){let e=parseInt(r,10),t=parseInt(n,10);if(void 0===o&&(o=e<=t?1:-1),0===o)throw Error("Step cannot be zero in sequence expansion.");let a=e<t;a&&o<0&&(o=-o),!a&&o>0&&(o=-o);for(let r=e;a?r<=t:r>=t;r+=o)i.push(r.toString())}return i}(c):p(c,",")).flatMap(t=>e(t)),e(u)))for(let e of d)a.push(o+e+t);return a}(t))e.push(r)}else z.push({base:a.base,pattern:l,negated:t});n([]);return}if("@variant"===e.name&&(null===r?0===e.nodes.length?e.name="@custom-variant":(em(e.nodes,t=>{if("at-rule"===t.kind&&"@slot"===t.name)return e.name="@custom-variant",2}),"@variant"===e.name&&A.push(e)):A.push(e)),"@custom-variant"===e.name){if(null!==r)throw Error("`@custom-variant` cannot be nested.");n([]);let[t,a]=p(e.params," ");if(!e2.test(t))throw Error("`@custom-variant ".concat(t,"` defines an invalid variant name. Variants should only contain alphanumeric, dashes, or underscore characters and start with a lowercase letter or number."));if(e.nodes.length>0&&a)throw Error("`@custom-variant ".concat(t,"` cannot have both a selector and a body."));if(0!==e.nodes.length)return void g.push(r=>{r.variants.fromAst(t,e.nodes)});{if(!a)throw Error("`@custom-variant ".concat(t,"` has no selector or body."));let e=p(a.slice(1,-1),",");if(0===e.length||e.some(e=>""===e.trim()))throw Error("`@custom-variant ".concat(t," (").concat(e.join(","),")` selector is invalid."));let r=[],n=[];for(let t of e)"@"===(t=t.trim())[0]?r.push(t):n.push(t);g.push(e=>{e.variants.static(t,e=>{let t=[];for(let a of(n.length>0&&t.push(es(n.join(", "),e.nodes)),r))t.push(eu(a,e.nodes));e.nodes=t},{compounds:e3([...n,...r])})});return}}if("@media"===e.name){let t=p(e.params," "),r=[];for(let n of t)if(n.startsWith("source(")){let t=n.slice(7,-1);em(e.nodes,(e,r)=>{let{replaceWith:n}=r;if("at-rule"===e.kind&&"@tailwind"===e.name&&"utilities"===e.params)return e.params+=" source(".concat(t,")"),n([ep({sourceBase:a.base},[e])]),2})}else if(n.startsWith("theme(")){let t=n.slice(6,-1),r=t.includes("reference");em(e.nodes,e=>{if("at-rule"!==e.kind){if(r)throw Error('Files imported with `@import "…" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "…";` instead.');return 0}if("@theme"===e.name)return e.params+=" "+t,1})}else if(n.startsWith("prefix(")){let t=n.slice(7,-1);em(e.nodes,e=>{if("at-rule"===e.kind&&"@theme"===e.name)return e.params+=" prefix(".concat(t,")"),1})}else"important"===n?h=!0:"reference"===n?e.nodes=[ep({reference:!0},e.nodes)]:r.push(n);r.length>0?e.params=r.join(" "):t.length>0&&n(e.nodes)}if("@theme"===e.name){let[t,r]=function(e){let t=0,r=null;for(let n of p(e," "))"reference"===n?t|=2:"inline"===n?t|=1:"default"===n?t|=4:"static"===n?t|=8:n.startsWith("prefix(")&&n.endsWith(")")&&(r=n.slice(7,-1));return[t,r]}(e.params);if(a.reference&&(t|=2),r){if(!tV.test(r))throw Error('The prefix "'.concat(r,'" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.'));v.prefix=r}return em(e.nodes,r=>{if("at-rule"===r.kind&&"@keyframes"===r.name)return v.addKeyframes(r),1;if("comment"===r.kind)return;if("declaration"===r.kind&&r.property.startsWith("--")){var n;v.add(X(r.property),null!=(n=r.value)?n:"",t,r.src);return}let a=eg([ec(e.name,e.params,[r])]).split("\n").map((e,t,r)=>"".concat(0===t||t>=r.length-2?" ":">"," ").concat(e)).join("\n");throw Error("`@theme` blocks must only contain custom properties or `@keyframes`.\n\n".concat(a))}),b?n([]):((b=es(":root, :host",[])).src=e.src,n([b])),1}}});let K=(t=function(e){var t,r,n,a,o;let i=new eB;function l(t,r){function*n(t){for(let r of e.keysInNamespaces(t))yield r.replace(eq,(e,t,r)=>"".concat(t,".").concat(r))}let a=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];i.suggest(t,()=>{let e=[];for(let s of r()){var t,o,i,l;if("string"==typeof s){e.push({values:[s],modifiers:[]});continue}let r=[...null!=(t=s.values)?t:[],...n(null!=(o=s.valueThemeKeys)?o:[])],c=[...null!=(i=s.modifiers)?i:[],...n(null!=(l=s.modifierThemeKeys)?l:[])];s.supportsFractions&&r.push(...a),s.hasDefaultValue&&r.unshift(null),e.push({supportsNegative:s.supportsNegative,values:r,modifiers:c})}return e})}function s(e,t){i.static(e,()=>t.map(e=>"function"==typeof e?e():ed(e[0],e[1])))}function c(t,r){function n(t){let{negative:n}=t;return t=>{var a,o,i;let l=null,s=null;if(t.value)if("arbitrary"===t.value.kind){if(t.modifier)return;l=t.value.value,s=t.value.dataType}else{if(null===(l=e.resolve(null!=(a=t.value.fraction)?a:t.value.value,null!=(o=r.themeKeys)?o:[]))&&r.supportsFractions&&t.value.fraction){let[e,r]=p(t.value.fraction,"/");if(!N(e)||!N(r))return;l="calc(".concat(t.value.fraction," * 100%)")}if(null===l&&n&&r.handleNegativeBareValue){if(!(null==(l=r.handleNegativeBareValue(t.value))?void 0:l.includes("/"))&&t.modifier)return;if(null!==l)return r.handle(l,null)}if(null===l&&r.handleBareValue&&!(null==(l=r.handleBareValue(t.value))?void 0:l.includes("/"))&&t.modifier)return}else{if(t.modifier)return;l=void 0!==r.defaultValue?r.defaultValue:e.resolve(null,null!=(i=r.themeKeys)?i:[])}if(null!==l)return r.handle(n?"calc(".concat(l," * -1)"):l,s)}}r.supportsNegative&&i.functional("-".concat(t),n({negative:!0})),i.functional(t,n({negative:!1})),l(t,()=>{var e;return[{supportsNegative:r.supportsNegative,valueThemeKeys:null!=(e=r.themeKeys)?e:[],hasDefaultValue:void 0!==r.defaultValue&&null!==r.defaultValue,supportsFractions:r.supportsFractions}]})}function u(t,r){i.functional(t,t=>{if(!t.value)return;let n=null;if(null!==(n="arbitrary"===t.value.kind?eP(n=t.value.value,t.modifier,e):eR(t,e,r.themeKeys)))return r.handle(n)}),l(t,()=>[{values:["current","inherit","transparent"],valueThemeKeys:r.themeKeys,modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))}])}function d(t,r,n){let{supportsNegative:a=!1,supportsFractions:o=!1}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};a&&i.static("-".concat(t,"-px"),()=>n("-1px")),i.static("".concat(t,"-px"),()=>n("1px")),c(t,{themeKeys:r,supportsFractions:o,supportsNegative:a,defaultValue:null,handleBareValue:t=>{let{value:r}=t,n=e.resolve(null,["--spacing"]);return n&&W(r)?"calc(".concat(n," * ").concat(r,")"):null},handleNegativeBareValue:t=>{let{value:r}=t,n=e.resolve(null,["--spacing"]);return n&&W(r)?"calc(".concat(n," * -").concat(r,")"):null},handle:n}),l(t,()=>[{values:e.get(["--spacing"])?eD:[],supportsNegative:a,supportsFractions:o,valueThemeKeys:r}])}for(let[e,t]of(s("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip-path","inset(50%)"],["white-space","nowrap"],["border-width","0"]]),s("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip-path","none"],["white-space","normal"]]),s("pointer-events-none",[["pointer-events","none"]]),s("pointer-events-auto",[["pointer-events","auto"]]),s("visible",[["visibility","visible"]]),s("invisible",[["visibility","hidden"]]),s("collapse",[["visibility","collapse"]]),s("static",[["position","static"]]),s("fixed",[["position","fixed"]]),s("absolute",[["position","absolute"]]),s("relative",[["position","relative"]]),s("sticky",[["position","sticky"]]),[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]]))s("".concat(e,"-auto"),[[t,"auto"]]),s("".concat(e,"-full"),[[t,"100%"]]),s("-".concat(e,"-full"),[[t,"-100%"]]),d(e,["--inset","--spacing"],e=>[ed(t,e)],{supportsNegative:!0,supportsFractions:!0});for(let[e,t]of(s("isolate",[["isolation","isolate"]]),s("isolation-auto",[["isolation","auto"]]),s("z-auto",[["z-index","auto"]]),c("z",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},themeKeys:["--z-index"],handle:e=>[ed("z-index",e)]}),l("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),s("order-first",[["order","-9999"]]),s("order-last",[["order","9999"]]),c("order",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},themeKeys:["--order"],handle:e=>[ed("order",e)]}),l("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:["--order"]}]),s("col-auto",[["grid-column","auto"]]),c("col",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},themeKeys:["--grid-column"],handle:e=>[ed("grid-column",e)]}),s("col-span-full",[["grid-column","1 / -1"]]),c("col-span",{handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},handle:e=>[ed("grid-column","span ".concat(e," / span ").concat(e))]}),s("col-start-auto",[["grid-column-start","auto"]]),c("col-start",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},themeKeys:["--grid-column-start"],handle:e=>[ed("grid-column-start",e)]}),s("col-end-auto",[["grid-column-end","auto"]]),c("col-end",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},themeKeys:["--grid-column-end"],handle:e=>[ed("grid-column-end",e)]}),l("col-span",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:[]}]),l("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-column-start"]}]),l("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-column-end"]}]),s("row-auto",[["grid-row","auto"]]),c("row",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},themeKeys:["--grid-row"],handle:e=>[ed("grid-row",e)]}),s("row-span-full",[["grid-row","1 / -1"]]),c("row-span",{themeKeys:[],handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},handle:e=>[ed("grid-row","span ".concat(e," / span ").concat(e))]}),s("row-start-auto",[["grid-row-start","auto"]]),c("row-start",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},themeKeys:["--grid-row-start"],handle:e=>[ed("grid-row-start",e)]}),s("row-end-auto",[["grid-row-end","auto"]]),c("row-end",{supportsNegative:!0,handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},themeKeys:["--grid-row-end"],handle:e=>[ed("grid-row-end",e)]}),l("row-span",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:[]}]),l("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-row-start"]}]),l("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-row-end"]}]),s("float-start",[["float","inline-start"]]),s("float-end",[["float","inline-end"]]),s("float-right",[["float","right"]]),s("float-left",[["float","left"]]),s("float-none",[["float","none"]]),s("clear-start",[["clear","inline-start"]]),s("clear-end",[["clear","inline-end"]]),s("clear-right",[["clear","right"]]),s("clear-left",[["clear","left"]]),s("clear-both",[["clear","both"]]),s("clear-none",[["clear","none"]]),[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]]))s("".concat(e,"-auto"),[[t,"auto"]]),d(e,["--margin","--spacing"],e=>[ed(t,e)],{supportsNegative:!0});for(let[e,t]of(s("box-border",[["box-sizing","border-box"]]),s("box-content",[["box-sizing","content-box"]]),s("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),c("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},handle:e=>[ed("overflow","hidden"),ed("display","-webkit-box"),ed("-webkit-box-orient","vertical"),ed("-webkit-line-clamp",e)]}),l("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),s("block",[["display","block"]]),s("inline-block",[["display","inline-block"]]),s("inline",[["display","inline"]]),s("hidden",[["display","none"]]),s("inline-flex",[["display","inline-flex"]]),s("table",[["display","table"]]),s("inline-table",[["display","inline-table"]]),s("table-caption",[["display","table-caption"]]),s("table-cell",[["display","table-cell"]]),s("table-column",[["display","table-column"]]),s("table-column-group",[["display","table-column-group"]]),s("table-footer-group",[["display","table-footer-group"]]),s("table-header-group",[["display","table-header-group"]]),s("table-row-group",[["display","table-row-group"]]),s("table-row",[["display","table-row"]]),s("flow-root",[["display","flow-root"]]),s("flex",[["display","flex"]]),s("grid",[["display","grid"]]),s("inline-grid",[["display","inline-grid"]]),s("contents",[["display","contents"]]),s("list-item",[["display","list-item"]]),s("field-sizing-content",[["field-sizing","content"]]),s("field-sizing-fixed",[["field-sizing","fixed"]]),s("aspect-auto",[["aspect-ratio","auto"]]),s("aspect-square",[["aspect-ratio","1 / 1"]]),c("aspect",{themeKeys:["--aspect"],handleBareValue:e=>{let{fraction:t}=e;if(null===t)return null;let[r,n]=p(t,"/");return N(r)&&N(n)?t:null},handle:e=>[ed("aspect-ratio",e)]}),[["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]]))s("size-".concat(e),[["--tw-sort","size"],["width",t],["height",t]]),s("w-".concat(e),[["width",t]]),s("h-".concat(e),[["height",t]]),s("min-w-".concat(e),[["min-width",t]]),s("min-h-".concat(e),[["min-height",t]]),s("max-w-".concat(e),[["max-width",t]]),s("max-h-".concat(e),[["max-height",t]]);for(let[e,t,r]of(s("size-auto",[["--tw-sort","size"],["width","auto"],["height","auto"]]),s("w-auto",[["width","auto"]]),s("h-auto",[["height","auto"]]),s("min-w-auto",[["min-width","auto"]]),s("min-h-auto",[["min-height","auto"]]),s("h-lh",[["height","1lh"]]),s("min-h-lh",[["min-height","1lh"]]),s("max-h-lh",[["max-height","1lh"]]),s("w-screen",[["width","100vw"]]),s("min-w-screen",[["min-width","100vw"]]),s("max-w-screen",[["max-width","100vw"]]),s("h-screen",[["height","100vh"]]),s("min-h-screen",[["min-height","100vh"]]),s("max-h-screen",[["max-height","100vh"]]),s("max-w-none",[["max-width","none"]]),s("max-h-none",[["max-height","none"]]),d("size",["--size","--spacing"],e=>[ed("--tw-sort","size"),ed("width",e),ed("height",e)],{supportsFractions:!0}),[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]]))d(e,t,e=>[ed(r,e)],{supportsFractions:!0});i.static("container",()=>{let t=[...e.namespace("--breakpoint").values()];t.sort((e,t)=>eN(e,t,"asc"));let r=[ed("--tw-sort","--tw-container-component"),ed("width","100%")];for(let e of t)r.push(ec("@media","(width >= ".concat(e,")"),[ed("max-width",e)]));return r}),s("flex-auto",[["flex","auto"]]),s("flex-initial",[["flex","0 auto"]]),s("flex-none",[["flex","none"]]),i.functional("flex",e=>{if(e.value){if("arbitrary"===e.value.kind)return e.modifier?void 0:[ed("flex",e.value.value)];if(e.value.fraction){let[t,r]=p(e.value.fraction,"/");return N(t)&&N(r)?[ed("flex","calc(".concat(e.value.fraction," * 100%)"))]:void 0}if(N(e.value.value))return e.modifier?void 0:[ed("flex",e.value.value)]}}),l("flex",()=>[{supportsFractions:!0},{values:Array.from({length:12},(e,t)=>"".concat(t+1))}]),c("shrink",{defaultValue:"1",handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},handle:e=>[ed("flex-shrink",e)]}),c("grow",{defaultValue:"1",handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},handle:e=>[ed("flex-grow",e)]}),l("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),l("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),s("basis-auto",[["flex-basis","auto"]]),s("basis-full",[["flex-basis","100%"]]),d("basis",["--flex-basis","--spacing","--container"],e=>[ed("flex-basis",e)],{supportsFractions:!0}),s("table-auto",[["table-layout","auto"]]),s("table-fixed",[["table-layout","fixed"]]),s("caption-top",[["caption-side","top"]]),s("caption-bottom",[["caption-side","bottom"]]),s("border-collapse",[["border-collapse","collapse"]]),s("border-separate",[["border-collapse","separate"]]);let f=()=>eh([eL("--tw-border-spacing-x","0","<length>"),eL("--tw-border-spacing-y","0","<length>")]);d("border-spacing",["--border-spacing","--spacing"],e=>[f(),ed("--tw-border-spacing-x",e),ed("--tw-border-spacing-y",e),ed("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),d("border-spacing-x",["--border-spacing","--spacing"],e=>[f(),ed("--tw-border-spacing-x",e),ed("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),d("border-spacing-y",["--border-spacing","--spacing"],e=>[f(),ed("--tw-border-spacing-y",e),ed("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),s("origin-center",[["transform-origin","center"]]),s("origin-top",[["transform-origin","top"]]),s("origin-top-right",[["transform-origin","top right"]]),s("origin-right",[["transform-origin","right"]]),s("origin-bottom-right",[["transform-origin","bottom right"]]),s("origin-bottom",[["transform-origin","bottom"]]),s("origin-bottom-left",[["transform-origin","bottom left"]]),s("origin-left",[["transform-origin","left"]]),s("origin-top-left",[["transform-origin","top left"]]),c("origin",{themeKeys:["--transform-origin"],handle:e=>[ed("transform-origin",e)]}),s("perspective-origin-center",[["perspective-origin","center"]]),s("perspective-origin-top",[["perspective-origin","top"]]),s("perspective-origin-top-right",[["perspective-origin","top right"]]),s("perspective-origin-right",[["perspective-origin","right"]]),s("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),s("perspective-origin-bottom",[["perspective-origin","bottom"]]),s("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),s("perspective-origin-left",[["perspective-origin","left"]]),s("perspective-origin-top-left",[["perspective-origin","top left"]]),c("perspective-origin",{themeKeys:["--perspective-origin"],handle:e=>[ed("perspective-origin",e)]}),s("perspective-none",[["perspective","none"]]),c("perspective",{themeKeys:["--perspective"],handle:e=>[ed("perspective",e)]});let h=()=>eh([eL("--tw-translate-x","0"),eL("--tw-translate-y","0"),eL("--tw-translate-z","0")]);for(let e of(s("translate-none",[["translate","none"]]),s("-translate-full",[h,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),s("translate-full",[h,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),d("translate",["--translate","--spacing"],e=>[h(),ed("--tw-translate-x",e),ed("--tw-translate-y",e),ed("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0}),["x","y"]))s("-translate-".concat(e,"-full"),[h,["--tw-translate-".concat(e),"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),s("translate-".concat(e,"-full"),[h,["--tw-translate-".concat(e),"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),d("translate-".concat(e),["--translate","--spacing"],t=>[h(),ed("--tw-translate-".concat(e),t),ed("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});d("translate-z",["--translate","--spacing"],e=>[h(),ed("--tw-translate-z",e),ed("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),s("translate-3d",[h,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let v=()=>eh([eL("--tw-scale-x","1"),eL("--tw-scale-y","1"),eL("--tw-scale-z","1")]);function g(t){let{negative:r}=t;return t=>{let n;if(t.value&&!t.modifier)return"arbitrary"===t.value.kind?(n=t.value.value,[ed("scale",n=r?"calc(".concat(n," * -1)"):n)]):(!(n=e.resolve(t.value.value,["--scale"]))&&N(t.value.value)&&(n="".concat(t.value.value,"%")),n?(n=r?"calc(".concat(n," * -1)"):n,[v(),ed("--tw-scale-x",n),ed("--tw-scale-y",n),ed("--tw-scale-z",n),ed("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}for(let e of(s("scale-none",[["scale","none"]]),i.functional("-scale",g({negative:!0})),i.functional("scale",g({negative:!1})),l("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]),["x","y","z"]))c("scale-".concat(e),{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},handle:t=>[v(),ed("--tw-scale-".concat(e),t),ed("scale","var(--tw-scale-x) var(--tw-scale-y)".concat("z"===e?" var(--tw-scale-z)":""))]}),l("scale-".concat(e),()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);function w(t){let{negative:r}=t;return t=>{let n;if(t.value&&!t.modifier){if("arbitrary"===t.value.kind){var a;if(n=t.value.value,"vector"===(null!=(a=t.value.dataType)?a:m(n,["angle","vector"])))return[ed("rotate","".concat(n," var(--tw-rotate)"))]}else if(!(n=e.resolve(t.value.value,["--rotate"]))&&N(t.value.value)&&(n="".concat(t.value.value,"deg")),!n)return;return[ed("rotate",r?"calc(".concat(n," * -1)"):n)]}}}s("scale-3d",[v,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),s("rotate-none",[["rotate","none"]]),i.functional("-rotate",w({negative:!0})),i.functional("rotate",w({negative:!1})),l("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let e="var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,)",t=()=>eh([eL("--tw-rotate-x"),eL("--tw-rotate-y"),eL("--tw-rotate-z"),eL("--tw-skew-x"),eL("--tw-skew-y")]);for(let r of["x","y","z"])c("rotate-".concat(r),{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"deg"):null},handle:n=>[t(),ed("--tw-rotate-".concat(r),"rotate".concat(r.toUpperCase(),"(").concat(n,")")),ed("transform",e)]}),l("rotate-".concat(r),()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);c("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"deg"):null},handle:r=>[t(),ed("--tw-skew-x","skewX(".concat(r,")")),ed("--tw-skew-y","skewY(".concat(r,")")),ed("transform",e)]}),c("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"deg"):null},handle:r=>[t(),ed("--tw-skew-x","skewX(".concat(r,")")),ed("transform",e)]}),c("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"deg"):null},handle:r=>[t(),ed("--tw-skew-y","skewY(".concat(r,")")),ed("transform",e)]}),l("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),l("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),l("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i.functional("transform",r=>{if(r.modifier)return;let n=null;if(r.value?"arbitrary"===r.value.kind&&(n=r.value.value):n=e,null!==n)return[t(),ed("transform",n)]}),l("transform",()=>[{hasDefaultValue:!0}]),s("transform-cpu",[["transform",e]]),s("transform-gpu",[["transform","translateZ(0) ".concat(e)]]),s("transform-none",[["transform","none"]])}for(let e of(s("transform-flat",[["transform-style","flat"]]),s("transform-3d",[["transform-style","preserve-3d"]]),s("transform-content",[["transform-box","content-box"]]),s("transform-border",[["transform-box","border-box"]]),s("transform-fill",[["transform-box","fill-box"]]),s("transform-stroke",[["transform-box","stroke-box"]]),s("transform-view",[["transform-box","view-box"]]),s("backface-visible",[["backface-visibility","visible"]]),s("backface-hidden",[["backface-visibility","hidden"]]),["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"]))s("cursor-".concat(e),[["cursor",e]]);for(let e of(c("cursor",{themeKeys:["--cursor"],handle:e=>[ed("cursor",e)]}),["auto","none","manipulation"]))s("touch-".concat(e),[["touch-action",e]]);let b=()=>eh([eL("--tw-pan-x"),eL("--tw-pan-y"),eL("--tw-pinch-zoom")]);for(let e of["x","left","right"])s("touch-pan-".concat(e),[b,["--tw-pan-x","pan-".concat(e)],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let e of["y","up","down"])s("touch-pan-".concat(e),[b,["--tw-pan-y","pan-".concat(e)],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let e of(s("touch-pinch-zoom",[b,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]),["none","text","all","auto"]))s("select-".concat(e),[["-webkit-user-select",e],["user-select",e]]);s("resize-none",[["resize","none"]]),s("resize-x",[["resize","horizontal"]]),s("resize-y",[["resize","vertical"]]),s("resize",[["resize","both"]]),s("snap-none",[["scroll-snap-type","none"]]);let x=()=>eh([eL("--tw-scroll-snap-strictness","proximity","*")]);for(let e of["x","y","both"])s("snap-".concat(e),[x,["scroll-snap-type","".concat(e," var(--tw-scroll-snap-strictness)")]]);for(let[e,t]of(s("snap-mandatory",[x,["--tw-scroll-snap-strictness","mandatory"]]),s("snap-proximity",[x,["--tw-scroll-snap-strictness","proximity"]]),s("snap-align-none",[["scroll-snap-align","none"]]),s("snap-start",[["scroll-snap-align","start"]]),s("snap-end",[["scroll-snap-align","end"]]),s("snap-center",[["scroll-snap-align","center"]]),s("snap-normal",[["scroll-snap-stop","normal"]]),s("snap-always",[["scroll-snap-stop","always"]]),[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]]))d(e,["--scroll-margin","--spacing"],e=>[ed(t,e)],{supportsNegative:!0});for(let[e,t]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])d(e,["--scroll-padding","--spacing"],e=>[ed(t,e)]);for(let e of(s("list-inside",[["list-style-position","inside"]]),s("list-outside",[["list-style-position","outside"]]),s("list-none",[["list-style-type","none"]]),s("list-disc",[["list-style-type","disc"]]),s("list-decimal",[["list-style-type","decimal"]]),c("list",{themeKeys:["--list-style-type"],handle:e=>[ed("list-style-type",e)]}),s("list-image-none",[["list-style-image","none"]]),c("list-image",{themeKeys:["--list-style-image"],handle:e=>[ed("list-style-image",e)]}),s("appearance-none",[["appearance","none"]]),s("appearance-auto",[["appearance","auto"]]),s("scheme-normal",[["color-scheme","normal"]]),s("scheme-dark",[["color-scheme","dark"]]),s("scheme-light",[["color-scheme","light"]]),s("scheme-light-dark",[["color-scheme","light dark"]]),s("scheme-only-dark",[["color-scheme","only dark"]]),s("scheme-only-light",[["color-scheme","only light"]]),s("columns-auto",[["columns","auto"]]),c("columns",{themeKeys:["--columns","--container"],handleBareValue:e=>{let{value:t}=e;return N(t)?t:null},handle:e=>[ed("columns",e)]}),l("columns",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:["--columns","--container"]}]),["auto","avoid","all","avoid-page","page","left","right","column"]))s("break-before-".concat(e),[["break-before",e]]);for(let e of["auto","avoid","avoid-page","avoid-column"])s("break-inside-".concat(e),[["break-inside",e]]);for(let e of["auto","avoid","all","avoid-page","page","left","right","column"])s("break-after-".concat(e),[["break-after",e]]);for(let e of(s("grid-flow-row",[["grid-auto-flow","row"]]),s("grid-flow-col",[["grid-auto-flow","column"]]),s("grid-flow-dense",[["grid-auto-flow","dense"]]),s("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),s("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),s("auto-cols-auto",[["grid-auto-columns","auto"]]),s("auto-cols-min",[["grid-auto-columns","min-content"]]),s("auto-cols-max",[["grid-auto-columns","max-content"]]),s("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),c("auto-cols",{themeKeys:["--grid-auto-columns"],handle:e=>[ed("grid-auto-columns",e)]}),s("auto-rows-auto",[["grid-auto-rows","auto"]]),s("auto-rows-min",[["grid-auto-rows","min-content"]]),s("auto-rows-max",[["grid-auto-rows","max-content"]]),s("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),c("auto-rows",{themeKeys:["--grid-auto-rows"],handle:e=>[ed("grid-auto-rows",e)]}),s("grid-cols-none",[["grid-template-columns","none"]]),s("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),c("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:e=>{let{value:t}=e;return O(t)?"repeat(".concat(t,", minmax(0, 1fr))"):null},handle:e=>[ed("grid-template-columns",e)]}),s("grid-rows-none",[["grid-template-rows","none"]]),s("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),c("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:e=>{let{value:t}=e;return O(t)?"repeat(".concat(t,", minmax(0, 1fr))"):null},handle:e=>[ed("grid-template-rows",e)]}),l("grid-cols",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-template-columns"]}]),l("grid-rows",()=>[{values:Array.from({length:12},(e,t)=>"".concat(t+1)),valueThemeKeys:["--grid-template-rows"]}]),s("flex-row",[["flex-direction","row"]]),s("flex-row-reverse",[["flex-direction","row-reverse"]]),s("flex-col",[["flex-direction","column"]]),s("flex-col-reverse",[["flex-direction","column-reverse"]]),s("flex-wrap",[["flex-wrap","wrap"]]),s("flex-nowrap",[["flex-wrap","nowrap"]]),s("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),s("place-content-center",[["place-content","center"]]),s("place-content-start",[["place-content","start"]]),s("place-content-end",[["place-content","end"]]),s("place-content-center-safe",[["place-content","safe center"]]),s("place-content-end-safe",[["place-content","safe end"]]),s("place-content-between",[["place-content","space-between"]]),s("place-content-around",[["place-content","space-around"]]),s("place-content-evenly",[["place-content","space-evenly"]]),s("place-content-baseline",[["place-content","baseline"]]),s("place-content-stretch",[["place-content","stretch"]]),s("place-items-center",[["place-items","center"]]),s("place-items-start",[["place-items","start"]]),s("place-items-end",[["place-items","end"]]),s("place-items-center-safe",[["place-items","safe center"]]),s("place-items-end-safe",[["place-items","safe end"]]),s("place-items-baseline",[["place-items","baseline"]]),s("place-items-stretch",[["place-items","stretch"]]),s("content-normal",[["align-content","normal"]]),s("content-center",[["align-content","center"]]),s("content-start",[["align-content","flex-start"]]),s("content-end",[["align-content","flex-end"]]),s("content-center-safe",[["align-content","safe center"]]),s("content-end-safe",[["align-content","safe flex-end"]]),s("content-between",[["align-content","space-between"]]),s("content-around",[["align-content","space-around"]]),s("content-evenly",[["align-content","space-evenly"]]),s("content-baseline",[["align-content","baseline"]]),s("content-stretch",[["align-content","stretch"]]),s("items-center",[["align-items","center"]]),s("items-start",[["align-items","flex-start"]]),s("items-end",[["align-items","flex-end"]]),s("items-center-safe",[["align-items","safe center"]]),s("items-end-safe",[["align-items","safe flex-end"]]),s("items-baseline",[["align-items","baseline"]]),s("items-baseline-last",[["align-items","last baseline"]]),s("items-stretch",[["align-items","stretch"]]),s("justify-normal",[["justify-content","normal"]]),s("justify-center",[["justify-content","center"]]),s("justify-start",[["justify-content","flex-start"]]),s("justify-end",[["justify-content","flex-end"]]),s("justify-center-safe",[["justify-content","safe center"]]),s("justify-end-safe",[["justify-content","safe flex-end"]]),s("justify-between",[["justify-content","space-between"]]),s("justify-around",[["justify-content","space-around"]]),s("justify-evenly",[["justify-content","space-evenly"]]),s("justify-baseline",[["justify-content","baseline"]]),s("justify-stretch",[["justify-content","stretch"]]),s("justify-items-normal",[["justify-items","normal"]]),s("justify-items-center",[["justify-items","center"]]),s("justify-items-start",[["justify-items","start"]]),s("justify-items-end",[["justify-items","end"]]),s("justify-items-center-safe",[["justify-items","safe center"]]),s("justify-items-end-safe",[["justify-items","safe end"]]),s("justify-items-stretch",[["justify-items","stretch"]]),d("gap",["--gap","--spacing"],e=>[ed("gap",e)]),d("gap-x",["--gap","--spacing"],e=>[ed("column-gap",e)]),d("gap-y",["--gap","--spacing"],e=>[ed("row-gap",e)]),d("space-x",["--space","--spacing"],e=>[eh([eL("--tw-space-x-reverse","0")]),es(":where(& > :not(:last-child))",[ed("--tw-sort","row-gap"),ed("--tw-space-x-reverse","0"),ed("margin-inline-start","calc(".concat(e," * var(--tw-space-x-reverse))")),ed("margin-inline-end","calc(".concat(e," * calc(1 - var(--tw-space-x-reverse)))"))])],{supportsNegative:!0}),d("space-y",["--space","--spacing"],e=>[eh([eL("--tw-space-y-reverse","0")]),es(":where(& > :not(:last-child))",[ed("--tw-sort","column-gap"),ed("--tw-space-y-reverse","0"),ed("margin-block-start","calc(".concat(e," * var(--tw-space-y-reverse))")),ed("margin-block-end","calc(".concat(e," * calc(1 - var(--tw-space-y-reverse)))"))])],{supportsNegative:!0}),s("space-x-reverse",[()=>eh([eL("--tw-space-x-reverse","0")]),()=>es(":where(& > :not(:last-child))",[ed("--tw-sort","row-gap"),ed("--tw-space-x-reverse","1")])]),s("space-y-reverse",[()=>eh([eL("--tw-space-y-reverse","0")]),()=>es(":where(& > :not(:last-child))",[ed("--tw-sort","column-gap"),ed("--tw-space-y-reverse","1")])]),s("accent-auto",[["accent-color","auto"]]),u("accent",{themeKeys:["--accent-color","--color"],handle:e=>[ed("accent-color",e)]}),u("caret",{themeKeys:["--caret-color","--color"],handle:e=>[ed("caret-color",e)]}),u("divide",{themeKeys:["--divide-color","--border-color","--color"],handle:e=>[es(":where(& > :not(:last-child))",[ed("--tw-sort","divide-color"),ed("border-color",e)])]}),s("place-self-auto",[["place-self","auto"]]),s("place-self-start",[["place-self","start"]]),s("place-self-end",[["place-self","end"]]),s("place-self-center",[["place-self","center"]]),s("place-self-end-safe",[["place-self","safe end"]]),s("place-self-center-safe",[["place-self","safe center"]]),s("place-self-stretch",[["place-self","stretch"]]),s("self-auto",[["align-self","auto"]]),s("self-start",[["align-self","flex-start"]]),s("self-end",[["align-self","flex-end"]]),s("self-center",[["align-self","center"]]),s("self-end-safe",[["align-self","safe flex-end"]]),s("self-center-safe",[["align-self","safe center"]]),s("self-stretch",[["align-self","stretch"]]),s("self-baseline",[["align-self","baseline"]]),s("self-baseline-last",[["align-self","last baseline"]]),s("justify-self-auto",[["justify-self","auto"]]),s("justify-self-start",[["justify-self","flex-start"]]),s("justify-self-end",[["justify-self","flex-end"]]),s("justify-self-center",[["justify-self","center"]]),s("justify-self-end-safe",[["justify-self","safe flex-end"]]),s("justify-self-center-safe",[["justify-self","safe center"]]),s("justify-self-stretch",[["justify-self","stretch"]]),["auto","hidden","clip","visible","scroll"]))s("overflow-".concat(e),[["overflow",e]]),s("overflow-x-".concat(e),[["overflow-x",e]]),s("overflow-y-".concat(e),[["overflow-y",e]]);for(let e of["auto","contain","none"])s("overscroll-".concat(e),[["overscroll-behavior",e]]),s("overscroll-x-".concat(e),[["overscroll-behavior-x",e]]),s("overscroll-y-".concat(e),[["overscroll-behavior-y",e]]);for(let[e,t]of(s("scroll-auto",[["scroll-behavior","auto"]]),s("scroll-smooth",[["scroll-behavior","smooth"]]),s("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),s("text-ellipsis",[["text-overflow","ellipsis"]]),s("text-clip",[["text-overflow","clip"]]),s("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),s("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),s("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),s("whitespace-normal",[["white-space","normal"]]),s("whitespace-nowrap",[["white-space","nowrap"]]),s("whitespace-pre",[["white-space","pre"]]),s("whitespace-pre-line",[["white-space","pre-line"]]),s("whitespace-pre-wrap",[["white-space","pre-wrap"]]),s("whitespace-break-spaces",[["white-space","break-spaces"]]),s("text-wrap",[["text-wrap","wrap"]]),s("text-nowrap",[["text-wrap","nowrap"]]),s("text-balance",[["text-wrap","balance"]]),s("text-pretty",[["text-wrap","pretty"]]),s("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),s("break-words",[["overflow-wrap","break-word"]]),s("break-all",[["word-break","break-all"]]),s("break-keep",[["word-break","keep-all"]]),s("wrap-anywhere",[["overflow-wrap","anywhere"]]),s("wrap-break-word",[["overflow-wrap","break-word"]]),s("wrap-normal",[["overflow-wrap","normal"]]),[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]]))s("".concat(e,"-none"),t.map(e=>[e,"0"])),s("".concat(e,"-full"),t.map(e=>[e,"calc(infinity * 1px)"])),c(e,{themeKeys:["--radius"],handle:e=>t.map(t=>ed(t,e))});s("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),s("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),s("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),s("border-double",[["--tw-border-style","double"],["border-style","double"]]),s("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),s("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let n=function(t,r){i.functional(t,t=>{var n,o;if(!t.value){if(t.modifier)return;let o=null!=(n=e.get(["--default-border-width"]))?n:"1px",i=r.width(o);return i?[a(),...i]:void 0}if("arbitrary"===t.value.kind){let n=t.value.value;switch(null!=(o=t.value.dataType)?o:m(n,["color","line-width","length"])){case"line-width":case"length":{if(t.modifier)return;let e=r.width(n);return e?[a(),...e]:void 0}default:return null===(n=eP(n,t.modifier,e))?void 0:r.color(n)}}{let n=eR(t,e,["--border-color","--color"]);if(n)return r.color(n)}{if(t.modifier)return;let n=e.resolve(t.value.value,["--border-width"]);if(n){let e=r.width(n);return e?[a(),...e]:void 0}if(N(t.value.value)){let e=r.width("".concat(t.value.value,"px"));return e?[a(),...e]:void 0}}}),l(t,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])},a=()=>eh([eL("--tw-border-style","solid")]);for(let o of(n("border",{width:e=>[ed("border-style","var(--tw-border-style)"),ed("border-width",e)],color:e=>[ed("border-color",e)]}),n("border-x",{width:e=>[ed("border-inline-style","var(--tw-border-style)"),ed("border-inline-width",e)],color:e=>[ed("border-inline-color",e)]}),n("border-y",{width:e=>[ed("border-block-style","var(--tw-border-style)"),ed("border-block-width",e)],color:e=>[ed("border-block-color",e)]}),n("border-s",{width:e=>[ed("border-inline-start-style","var(--tw-border-style)"),ed("border-inline-start-width",e)],color:e=>[ed("border-inline-start-color",e)]}),n("border-e",{width:e=>[ed("border-inline-end-style","var(--tw-border-style)"),ed("border-inline-end-width",e)],color:e=>[ed("border-inline-end-color",e)]}),n("border-t",{width:e=>[ed("border-top-style","var(--tw-border-style)"),ed("border-top-width",e)],color:e=>[ed("border-top-color",e)]}),n("border-r",{width:e=>[ed("border-right-style","var(--tw-border-style)"),ed("border-right-width",e)],color:e=>[ed("border-right-color",e)]}),n("border-b",{width:e=>[ed("border-bottom-style","var(--tw-border-style)"),ed("border-bottom-width",e)],color:e=>[ed("border-bottom-color",e)]}),n("border-l",{width:e=>[ed("border-left-style","var(--tw-border-style)"),ed("border-left-width",e)],color:e=>[ed("border-left-color",e)]}),c("divide-x",{defaultValue:null!=(t=e.get(["--default-border-width"]))?t:"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"px"):null},handle:e=>[eh([eL("--tw-divide-x-reverse","0")]),es(":where(& > :not(:last-child))",[ed("--tw-sort","divide-x-width"),a(),ed("--tw-divide-x-reverse","0"),ed("border-inline-style","var(--tw-border-style)"),ed("border-inline-start-width","calc(".concat(e," * var(--tw-divide-x-reverse))")),ed("border-inline-end-width","calc(".concat(e," * calc(1 - var(--tw-divide-x-reverse)))"))])]}),c("divide-y",{defaultValue:null!=(r=e.get(["--default-border-width"]))?r:"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"px"):null},handle:e=>[eh([eL("--tw-divide-y-reverse","0")]),es(":where(& > :not(:last-child))",[ed("--tw-sort","divide-y-width"),a(),ed("--tw-divide-y-reverse","0"),ed("border-bottom-style","var(--tw-border-style)"),ed("border-top-style","var(--tw-border-style)"),ed("border-top-width","calc(".concat(e," * var(--tw-divide-y-reverse))")),ed("border-bottom-width","calc(".concat(e," * calc(1 - var(--tw-divide-y-reverse)))"))])]}),l("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),l("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),s("divide-x-reverse",[()=>eh([eL("--tw-divide-x-reverse","0")]),()=>es(":where(& > :not(:last-child))",[ed("--tw-divide-x-reverse","1")])]),s("divide-y-reverse",[()=>eh([eL("--tw-divide-y-reverse","0")]),()=>es(":where(& > :not(:last-child))",[ed("--tw-divide-y-reverse","1")])]),["solid","dashed","dotted","double","none"]))s("divide-".concat(o),[()=>es(":where(& > :not(:last-child))",[ed("--tw-sort","divide-style"),ed("--tw-border-style",o),ed("border-style",o)])])}s("bg-auto",[["background-size","auto"]]),s("bg-cover",[["background-size","cover"]]),s("bg-contain",[["background-size","contain"]]),c("bg-size",{handle(e){if(e)return[ed("background-size",e)]}}),s("bg-fixed",[["background-attachment","fixed"]]),s("bg-local",[["background-attachment","local"]]),s("bg-scroll",[["background-attachment","scroll"]]),s("bg-top",[["background-position","top"]]),s("bg-top-left",[["background-position","left top"]]),s("bg-top-right",[["background-position","right top"]]),s("bg-bottom",[["background-position","bottom"]]),s("bg-bottom-left",[["background-position","left bottom"]]),s("bg-bottom-right",[["background-position","right bottom"]]),s("bg-left",[["background-position","left"]]),s("bg-right",[["background-position","right"]]),s("bg-center",[["background-position","center"]]),c("bg-position",{handle(e){if(e)return[ed("background-position",e)]}}),s("bg-repeat",[["background-repeat","repeat"]]),s("bg-no-repeat",[["background-repeat","no-repeat"]]),s("bg-repeat-x",[["background-repeat","repeat-x"]]),s("bg-repeat-y",[["background-repeat","repeat-y"]]),s("bg-repeat-round",[["background-repeat","round"]]),s("bg-repeat-space",[["background-repeat","space"]]),s("bg-none",[["background-image","none"]]);{let e=function(e){let t="in oklab";if((null==e?void 0:e.kind)==="named")switch(e.value){case"longer":case"shorter":case"increasing":case"decreasing":t="in oklch ".concat(e.value," hue");break;default:t="in ".concat(e.value)}else(null==e?void 0:e.kind)==="arbitrary"&&(t=e.value);return t},t=function(t){let{negative:r}=t;return t=>{if(!t.value)return;if("arbitrary"===t.value.kind){var n;if(t.modifier)return;let e=t.value.value;return(null!=(n=t.value.dataType)?n:m(e,["angle"]))==="angle"?[ed("--tw-gradient-position",e=r?"calc(".concat(e," * -1)"):"".concat(e)),ed("background-image","linear-gradient(var(--tw-gradient-stops,".concat(e,"))"))]:r?void 0:[ed("--tw-gradient-position",e),ed("background-image","linear-gradient(var(--tw-gradient-stops,".concat(e,"))"))]}let o=t.value.value;if(!r&&a.has(o))o=a.get(o);else{if(!N(o))return;o=r?"calc(".concat(o,"deg * -1)"):"".concat(o,"deg")}let i=e(t.modifier);return[ed("--tw-gradient-position","".concat(o)),eu("@supports (background-image: linear-gradient(in lab, red, red))",[ed("--tw-gradient-position","".concat(o," ").concat(i))]),ed("background-image","linear-gradient(var(--tw-gradient-stops))")]}},r=function(t){let{negative:r}=t;return t=>{var n;if((null==(n=t.value)?void 0:n.kind)==="arbitrary"){if(t.modifier)return;let e=t.value.value;return[ed("--tw-gradient-position",e),ed("background-image","conic-gradient(var(--tw-gradient-stops,".concat(e,"))"))]}let a=e(t.modifier);if(!t.value)return[ed("--tw-gradient-position",a),ed("background-image","conic-gradient(var(--tw-gradient-stops))")];let o=t.value.value;if(N(o))return o=r?"calc(".concat(o,"deg * -1)"):"".concat(o,"deg"),[ed("--tw-gradient-position","from ".concat(o," ").concat(a)),ed("background-image","conic-gradient(var(--tw-gradient-stops))")]}},n=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],a=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);i.functional("-bg-linear",t({negative:!0})),i.functional("bg-linear",t({negative:!1})),l("bg-linear",()=>[{values:[...a.keys()],modifiers:n},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:n}]),i.functional("-bg-conic",r({negative:!0})),i.functional("bg-conic",r({negative:!1})),l("bg-conic",()=>[{hasDefaultValue:!0,modifiers:n},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:n}]),i.functional("bg-radial",t=>{if(!t.value)return[ed("--tw-gradient-position",e(t.modifier)),ed("background-image","radial-gradient(var(--tw-gradient-stops))")];if("arbitrary"===t.value.kind){if(t.modifier)return;let e=t.value.value;return[ed("--tw-gradient-position",e),ed("background-image","radial-gradient(var(--tw-gradient-stops,".concat(e,"))"))]}}),l("bg-radial",()=>[{hasDefaultValue:!0,modifiers:n}])}i.functional("bg",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;switch(null!=(r=t.value.dataType)?r:m(n,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return t.modifier?void 0:[ed("background-position",n)];case"bg-size":case"length":case"size":return t.modifier?void 0:[ed("background-size",n)];case"image":case"url":return t.modifier?void 0:[ed("background-image",n)];default:return null===(n=eP(n,t.modifier,e))?void 0:[ed("background-color",n)]}}{let r=eR(t,e,["--background-color","--color"]);if(r)return[ed("background-color",r)]}{if(t.modifier)return;let r=e.resolve(t.value.value,["--background-image"]);if(r)return[ed("background-image",r)]}}}),l("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:[],valueThemeKeys:["--background-image"]}]);let A=()=>eh([eL("--tw-gradient-position"),eL("--tw-gradient-from","#0000","<color>"),eL("--tw-gradient-via","#0000","<color>"),eL("--tw-gradient-to","#0000","<color>"),eL("--tw-gradient-stops"),eL("--tw-gradient-via-stops"),eL("--tw-gradient-from-position","0%","<length-percentage>"),eL("--tw-gradient-via-position","50%","<length-percentage>"),eL("--tw-gradient-to-position","100%","<length-percentage>")]);function z(t,r){i.functional(t,t=>{if(t.value){if("arbitrary"===t.value.kind){var n;let a=t.value.value;switch(null!=(n=t.value.dataType)?n:m(a,["color","length","percentage"])){case"length":case"percentage":return t.modifier?void 0:r.position(a);default:return null===(a=eP(a,t.modifier,e))?void 0:r.color(a)}}{let n=eR(t,e,["--background-color","--color"]);if(n)return r.color(n)}{if(t.modifier)return;let n=e.resolve(t.value.value,["--gradient-color-stop-positions"]);if(n)return r.position(n);if("%"===t.value.value[t.value.value.length-1]&&N(t.value.value.slice(0,-1)))return r.position(t.value.value)}}}),l(t,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:Array.from({length:21},(e,t)=>"".concat(5*t,"%")),valueThemeKeys:["--gradient-color-stop-positions"]}])}z("from",{color:e=>[A(),ed("--tw-sort","--tw-gradient-from"),ed("--tw-gradient-from",e),ed("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:e=>[A(),ed("--tw-gradient-from-position",e)]}),s("via-none",[["--tw-gradient-via-stops","initial"]]),z("via",{color:e=>[A(),ed("--tw-sort","--tw-gradient-via"),ed("--tw-gradient-via",e),ed("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),ed("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:e=>[A(),ed("--tw-gradient-via-position",e)]}),z("to",{color:e=>[A(),ed("--tw-sort","--tw-gradient-to"),ed("--tw-gradient-to",e),ed("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:e=>[A(),ed("--tw-gradient-to-position",e)]}),s("mask-none",[["mask-image","none"]]),i.functional("mask",e=>{var t;if(!e.value||e.modifier||"arbitrary"!==e.value.kind)return;let r=e.value.value;switch(null!=(t=e.value.dataType)?t:m(r,["image","percentage","position","bg-size","length","url"])){case"percentage":case"position":return e.modifier?void 0:[ed("mask-position",r)];case"bg-size":case"length":case"size":return[ed("mask-size",r)];default:return[ed("mask-image",r)]}}),s("mask-add",[["mask-composite","add"]]),s("mask-subtract",[["mask-composite","subtract"]]),s("mask-intersect",[["mask-composite","intersect"]]),s("mask-exclude",[["mask-composite","exclude"]]),s("mask-alpha",[["mask-mode","alpha"]]),s("mask-luminance",[["mask-mode","luminance"]]),s("mask-match",[["mask-mode","match-source"]]),s("mask-type-alpha",[["mask-type","alpha"]]),s("mask-type-luminance",[["mask-type","luminance"]]),s("mask-auto",[["mask-size","auto"]]),s("mask-cover",[["mask-size","cover"]]),s("mask-contain",[["mask-size","contain"]]),c("mask-size",{handle(e){if(e)return[ed("mask-size",e)]}}),s("mask-top",[["mask-position","top"]]),s("mask-top-left",[["mask-position","left top"]]),s("mask-top-right",[["mask-position","right top"]]),s("mask-bottom",[["mask-position","bottom"]]),s("mask-bottom-left",[["mask-position","left bottom"]]),s("mask-bottom-right",[["mask-position","right bottom"]]),s("mask-left",[["mask-position","left"]]),s("mask-right",[["mask-position","right"]]),s("mask-center",[["mask-position","center"]]),c("mask-position",{handle(e){if(e)return[ed("mask-position",e)]}}),s("mask-repeat",[["mask-repeat","repeat"]]),s("mask-no-repeat",[["mask-repeat","no-repeat"]]),s("mask-repeat-x",[["mask-repeat","repeat-x"]]),s("mask-repeat-y",[["mask-repeat","repeat-y"]]),s("mask-repeat-round",[["mask-repeat","round"]]),s("mask-repeat-space",[["mask-repeat","space"]]),s("mask-clip-border",[["mask-clip","border-box"]]),s("mask-clip-padding",[["mask-clip","padding-box"]]),s("mask-clip-content",[["mask-clip","content-box"]]),s("mask-clip-fill",[["mask-clip","fill-box"]]),s("mask-clip-stroke",[["mask-clip","stroke-box"]]),s("mask-clip-view",[["mask-clip","view-box"]]),s("mask-no-clip",[["mask-clip","no-clip"]]),s("mask-origin-border",[["mask-origin","border-box"]]),s("mask-origin-padding",[["mask-origin","padding-box"]]),s("mask-origin-content",[["mask-origin","content-box"]]),s("mask-origin-fill",[["mask-origin","fill-box"]]),s("mask-origin-stroke",[["mask-origin","stroke-box"]]),s("mask-origin-view",[["mask-origin","view-box"]]);let T=()=>eh([eL("--tw-mask-linear","linear-gradient(#fff, #fff)"),eL("--tw-mask-radial","linear-gradient(#fff, #fff)"),eL("--tw-mask-conic","linear-gradient(#fff, #fff)")]);function j(t,r){i.functional(t,t=>{if(t.value){if("arbitrary"===t.value.kind){var n;let a=t.value.value;switch(null!=(n=t.value.dataType)?n:m(a,["length","percentage","color"])){case"color":return null===(a=eP(a,t.modifier,e))?void 0:r.color(a);case"percentage":return t.modifier||!N(a.slice(0,-1))?void 0:r.position(a);default:return t.modifier?void 0:r.position(a)}}{let n=eR(t,e,["--background-color","--color"]);if(n)return r.color(n)}{if(t.modifier)return;let n=m(t.value.value,["number","percentage"]);if(!n)return;switch(n){case"number":{let n=e.resolve(null,["--spacing"]);return n&&W(t.value.value)?r.position("calc(".concat(n," * ").concat(t.value.value,")")):void 0}case"percentage":return N(t.value.value.slice(0,-1))?r.position(t.value.value):void 0;default:return}}}}),l(t,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:Array.from({length:21},(e,t)=>"".concat(5*t,"%")),valueThemeKeys:["--gradient-color-stop-positions"]}]),l(t,()=>[{values:Array.from({length:21},(e,t)=>"".concat(5*t,"%"))},{values:e.get(["--spacing"])?eD:[]},{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))}])}let C=()=>eh([eL("--tw-mask-left","linear-gradient(#fff, #fff)"),eL("--tw-mask-right","linear-gradient(#fff, #fff)"),eL("--tw-mask-bottom","linear-gradient(#fff, #fff)"),eL("--tw-mask-top","linear-gradient(#fff, #fff)")]);function K(e,t,r){j(e,{color(e){let n=[T(),C(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let a of["top","right","bottom","left"])r[a]&&(n.push(ed("--tw-mask-".concat(a),"linear-gradient(to ".concat(a,", var(--tw-mask-").concat(a,"-from-color) var(--tw-mask-").concat(a,"-from-position), var(--tw-mask-").concat(a,"-to-color) var(--tw-mask-").concat(a,"-to-position))"))),n.push(eh([eL("--tw-mask-".concat(a,"-from-position"),"0%"),eL("--tw-mask-".concat(a,"-to-position"),"100%"),eL("--tw-mask-".concat(a,"-from-color"),"black"),eL("--tw-mask-".concat(a,"-to-color"),"transparent")])),n.push(ed("--tw-mask-".concat(a,"-").concat(t,"-color"),e)));return n},position(e){let n=[T(),C(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let a of["top","right","bottom","left"])r[a]&&(n.push(ed("--tw-mask-".concat(a),"linear-gradient(to ".concat(a,", var(--tw-mask-").concat(a,"-from-color) var(--tw-mask-").concat(a,"-from-position), var(--tw-mask-").concat(a,"-to-color) var(--tw-mask-").concat(a,"-to-position))"))),n.push(eh([eL("--tw-mask-".concat(a,"-from-position"),"0%"),eL("--tw-mask-".concat(a,"-to-position"),"100%"),eL("--tw-mask-".concat(a,"-from-color"),"black"),eL("--tw-mask-".concat(a,"-to-color"),"transparent")])),n.push(ed("--tw-mask-".concat(a,"-").concat(t,"-position"),e)));return n}})}K("mask-x-from","from",{top:!1,right:!0,bottom:!1,left:!0}),K("mask-x-to","to",{top:!1,right:!0,bottom:!1,left:!0}),K("mask-y-from","from",{top:!0,right:!1,bottom:!0,left:!1}),K("mask-y-to","to",{top:!0,right:!1,bottom:!0,left:!1}),K("mask-t-from","from",{top:!0,right:!1,bottom:!1,left:!1}),K("mask-t-to","to",{top:!0,right:!1,bottom:!1,left:!1}),K("mask-r-from","from",{top:!1,right:!0,bottom:!1,left:!1}),K("mask-r-to","to",{top:!1,right:!0,bottom:!1,left:!1}),K("mask-b-from","from",{top:!1,right:!1,bottom:!0,left:!1}),K("mask-b-to","to",{top:!1,right:!1,bottom:!0,left:!1}),K("mask-l-from","from",{top:!1,right:!1,bottom:!1,left:!0}),K("mask-l-to","to",{top:!1,right:!1,bottom:!1,left:!0});let S=()=>eh([eL("--tw-mask-linear-position","0deg"),eL("--tw-mask-linear-from-position","0%"),eL("--tw-mask-linear-to-position","100%"),eL("--tw-mask-linear-from-color","black"),eL("--tw-mask-linear-to-color","transparent")]);c("mask-linear",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue:e=>N(e.value)?"calc(1deg * ".concat(e.value,")"):null,handleNegativeBareValue:e=>N(e.value)?"calc(1deg * -".concat(e.value,")"):null,handle:e=>[T(),S(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"),ed("--tw-mask-linear-position",e)]}),l("mask-linear",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),j("mask-linear-from",{color:e=>[T(),S(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),ed("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),ed("--tw-mask-linear-from-color",e)],position:e=>[T(),S(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),ed("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),ed("--tw-mask-linear-from-position",e)]}),j("mask-linear-to",{color:e=>[T(),S(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),ed("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),ed("--tw-mask-linear-to-color",e)],position:e=>[T(),S(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),ed("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),ed("--tw-mask-linear-to-position",e)]});let V=()=>eh([eL("--tw-mask-radial-from-position","0%"),eL("--tw-mask-radial-to-position","100%"),eL("--tw-mask-radial-from-color","black"),eL("--tw-mask-radial-to-color","transparent"),eL("--tw-mask-radial-shape","ellipse"),eL("--tw-mask-radial-size","farthest-corner"),eL("--tw-mask-radial-position","center")]);s("mask-circle",[["--tw-mask-radial-shape","circle"]]),s("mask-ellipse",[["--tw-mask-radial-shape","ellipse"]]),s("mask-radial-closest-side",[["--tw-mask-radial-size","closest-side"]]),s("mask-radial-farthest-side",[["--tw-mask-radial-size","farthest-side"]]),s("mask-radial-closest-corner",[["--tw-mask-radial-size","closest-corner"]]),s("mask-radial-farthest-corner",[["--tw-mask-radial-size","farthest-corner"]]),s("mask-radial-at-top",[["--tw-mask-radial-position","top"]]),s("mask-radial-at-top-left",[["--tw-mask-radial-position","top left"]]),s("mask-radial-at-top-right",[["--tw-mask-radial-position","top right"]]),s("mask-radial-at-bottom",[["--tw-mask-radial-position","bottom"]]),s("mask-radial-at-bottom-left",[["--tw-mask-radial-position","bottom left"]]),s("mask-radial-at-bottom-right",[["--tw-mask-radial-position","bottom right"]]),s("mask-radial-at-left",[["--tw-mask-radial-position","left"]]),s("mask-radial-at-right",[["--tw-mask-radial-position","right"]]),s("mask-radial-at-center",[["--tw-mask-radial-position","center"]]),c("mask-radial-at",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:e=>[ed("--tw-mask-radial-position",e)]}),c("mask-radial",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:e=>[T(),V(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"),ed("--tw-mask-radial-size",e)]}),j("mask-radial-from",{color:e=>[T(),V(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),ed("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),ed("--tw-mask-radial-from-color",e)],position:e=>[T(),V(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),ed("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),ed("--tw-mask-radial-from-position",e)]}),j("mask-radial-to",{color:e=>[T(),V(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),ed("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),ed("--tw-mask-radial-to-color",e)],position:e=>[T(),V(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),ed("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),ed("--tw-mask-radial-to-position",e)]});let E=()=>eh([eL("--tw-mask-conic-position","0deg"),eL("--tw-mask-conic-from-position","0%"),eL("--tw-mask-conic-to-position","100%"),eL("--tw-mask-conic-from-color","black"),eL("--tw-mask-conic-to-color","transparent")]);for(let e of(c("mask-conic",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue:e=>N(e.value)?"calc(1deg * ".concat(e.value,")"):null,handleNegativeBareValue:e=>N(e.value)?"calc(1deg * -".concat(e.value,")"):null,handle:e=>[T(),E(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"),ed("--tw-mask-conic-position",e)]}),l("mask-conic",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),j("mask-conic-from",{color:e=>[T(),E(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),ed("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),ed("--tw-mask-conic-from-color",e)],position:e=>[T(),E(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),ed("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),ed("--tw-mask-conic-from-position",e)]}),j("mask-conic-to",{color:e=>[T(),E(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),ed("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),ed("--tw-mask-conic-to-color",e)],position:e=>[T(),E(),ed("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),ed("mask-composite","intersect"),ed("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),ed("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),ed("--tw-mask-conic-to-position",e)]}),s("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),s("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),s("bg-clip-text",[["background-clip","text"]]),s("bg-clip-border",[["background-clip","border-box"]]),s("bg-clip-padding",[["background-clip","padding-box"]]),s("bg-clip-content",[["background-clip","content-box"]]),s("bg-origin-border",[["background-origin","border-box"]]),s("bg-origin-padding",[["background-origin","padding-box"]]),s("bg-origin-content",[["background-origin","content-box"]]),["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]))s("bg-blend-".concat(e),[["background-blend-mode",e]]),s("mix-blend-".concat(e),[["mix-blend-mode",e]]);for(let[t,r]of(s("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),s("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),s("fill-none",[["fill","none"]]),i.functional("fill",t=>{if(!t.value)return;if("arbitrary"===t.value.kind){let r=eP(t.value.value,t.modifier,e);return null===r?void 0:[ed("fill",r)]}let r=eR(t,e,["--fill","--color"]);if(r)return[ed("fill",r)]}),l("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))}]),s("stroke-none",[["stroke","none"]]),i.functional("stroke",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;switch(null!=(r=t.value.dataType)?r:m(n,["color","number","length","percentage"])){case"number":case"length":case"percentage":return t.modifier?void 0:[ed("stroke-width",n)];default:return null===(n=eP(t.value.value,t.modifier,e))?void 0:[ed("stroke",n)]}}{let r=eR(t,e,["--stroke","--color"]);if(r)return[ed("stroke",r)]}{let r=e.resolve(t.value.value,["--stroke-width"]);if(r)return[ed("stroke-width",r)];if(N(t.value.value))return[ed("stroke-width",t.value.value)]}}}),l("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),s("object-contain",[["object-fit","contain"]]),s("object-cover",[["object-fit","cover"]]),s("object-fill",[["object-fit","fill"]]),s("object-none",[["object-fit","none"]]),s("object-scale-down",[["object-fit","scale-down"]]),s("object-top",[["object-position","top"]]),s("object-top-left",[["object-position","left top"]]),s("object-top-right",[["object-position","right top"]]),s("object-bottom",[["object-position","bottom"]]),s("object-bottom-left",[["object-position","left bottom"]]),s("object-bottom-right",[["object-position","right bottom"]]),s("object-left",[["object-position","left"]]),s("object-right",[["object-position","right"]]),s("object-center",[["object-position","center"]]),c("object",{themeKeys:["--object-position"],handle:e=>[ed("object-position",e)]}),[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]]))d(t,["--padding","--spacing"],e=>[ed(r,e)]);s("text-left",[["text-align","left"]]),s("text-center",[["text-align","center"]]),s("text-right",[["text-align","right"]]),s("text-justify",[["text-align","justify"]]),s("text-start",[["text-align","start"]]),s("text-end",[["text-align","end"]]),d("indent",["--text-indent","--spacing"],e=>[ed("text-indent",e)],{supportsNegative:!0}),s("align-baseline",[["vertical-align","baseline"]]),s("align-top",[["vertical-align","top"]]),s("align-middle",[["vertical-align","middle"]]),s("align-bottom",[["vertical-align","bottom"]]),s("align-text-top",[["vertical-align","text-top"]]),s("align-text-bottom",[["vertical-align","text-bottom"]]),s("align-sub",[["vertical-align","sub"]]),s("align-super",[["vertical-align","super"]]),c("align",{themeKeys:[],handle:e=>[ed("vertical-align",e)]}),i.functional("font",t=>{if(!(!t.value||t.modifier)){if("arbitrary"===t.value.kind){var r;let e=t.value.value;switch(null!=(r=t.value.dataType)?r:m(e,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[ed("font-family",e)];default:return[eh([eL("--tw-font-weight")]),ed("--tw-font-weight",e),ed("font-weight",e)]}}{let r=e.resolveWith(t.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(r){let[e,t={}]=r;return[ed("font-family",e),ed("font-feature-settings",t["--font-feature-settings"]),ed("font-variation-settings",t["--font-variation-settings"])]}}{let r=e.resolve(t.value.value,["--font-weight"]);if(r)return[eh([eL("--tw-font-weight")]),ed("--tw-font-weight",r),ed("font-weight",r)]}}}),l("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),s("uppercase",[["text-transform","uppercase"]]),s("lowercase",[["text-transform","lowercase"]]),s("capitalize",[["text-transform","capitalize"]]),s("normal-case",[["text-transform","none"]]),s("italic",[["font-style","italic"]]),s("not-italic",[["font-style","normal"]]),s("underline",[["text-decoration-line","underline"]]),s("overline",[["text-decoration-line","overline"]]),s("line-through",[["text-decoration-line","line-through"]]),s("no-underline",[["text-decoration-line","none"]]),s("font-stretch-normal",[["font-stretch","normal"]]),s("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),s("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),s("font-stretch-condensed",[["font-stretch","condensed"]]),s("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),s("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),s("font-stretch-expanded",[["font-stretch","expanded"]]),s("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),s("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),c("font-stretch",{handleBareValue:e=>{let{value:t}=e;if(!t.endsWith("%"))return null;let r=Number(t.slice(0,-1));return!N(r)||Number.isNaN(r)||r<50||r>200?null:t},handle:e=>[ed("font-stretch",e)]}),l("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),u("placeholder",{themeKeys:["--background-color","--color"],handle:e=>[es("&::placeholder",[ed("--tw-sort","placeholder-color"),ed("color",e)])]}),s("decoration-solid",[["text-decoration-style","solid"]]),s("decoration-double",[["text-decoration-style","double"]]),s("decoration-dotted",[["text-decoration-style","dotted"]]),s("decoration-dashed",[["text-decoration-style","dashed"]]),s("decoration-wavy",[["text-decoration-style","wavy"]]),s("decoration-auto",[["text-decoration-thickness","auto"]]),s("decoration-from-font",[["text-decoration-thickness","from-font"]]),i.functional("decoration",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;switch(null!=(r=t.value.dataType)?r:m(n,["color","length","percentage"])){case"length":case"percentage":return t.modifier?void 0:[ed("text-decoration-thickness",n)];default:return null===(n=eP(n,t.modifier,e))?void 0:[ed("text-decoration-color",n)]}}{let r=e.resolve(t.value.value,["--text-decoration-thickness"]);if(r)return t.modifier?void 0:[ed("text-decoration-thickness",r)];if(N(t.value.value))return t.modifier?void 0:[ed("text-decoration-thickness","".concat(t.value.value,"px"))]}{let r=eR(t,e,["--text-decoration-color","--color"]);if(r)return[ed("text-decoration-color",r)]}}}),l("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),s("animate-none",[["animation","none"]]),c("animate",{themeKeys:["--animate"],handle:e=>[ed("animation",e)]});{let t="var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,)",r="var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,)",n=()=>eh([eL("--tw-blur"),eL("--tw-brightness"),eL("--tw-contrast"),eL("--tw-grayscale"),eL("--tw-hue-rotate"),eL("--tw-invert"),eL("--tw-opacity"),eL("--tw-saturate"),eL("--tw-sepia"),eL("--tw-drop-shadow"),eL("--tw-drop-shadow-color"),eL("--tw-drop-shadow-alpha","100%","<percentage>"),eL("--tw-drop-shadow-size")]),a=()=>eh([eL("--tw-backdrop-blur"),eL("--tw-backdrop-brightness"),eL("--tw-backdrop-contrast"),eL("--tw-backdrop-grayscale"),eL("--tw-backdrop-hue-rotate"),eL("--tw-backdrop-invert"),eL("--tw-backdrop-opacity"),eL("--tw-backdrop-saturate"),eL("--tw-backdrop-sepia")]);i.functional("filter",e=>{if(!e.modifier){if(null===e.value)return[n(),ed("filter",t)];if("arbitrary"===e.value.kind)return[ed("filter",e.value.value)];if("none"===e.value.value)return[ed("filter","none")]}}),i.functional("backdrop-filter",e=>{if(!e.modifier){if(null===e.value)return[a(),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)];if("arbitrary"===e.value.kind)return[ed("-webkit-backdrop-filter",e.value.value),ed("backdrop-filter",e.value.value)];if("none"===e.value.value)return[ed("-webkit-backdrop-filter","none"),ed("backdrop-filter","none")]}}),c("blur",{themeKeys:["--blur"],handle:e=>[n(),ed("--tw-blur","blur(".concat(e,")")),ed("filter",t)]}),s("blur-none",[n,["--tw-blur"," "],["filter",t]]),c("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:e=>[a(),ed("--tw-backdrop-blur","blur(".concat(e,")")),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)]}),s("backdrop-blur-none",[a,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",r],["backdrop-filter",r]]),c("brightness",{themeKeys:["--brightness"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},handle:e=>[n(),ed("--tw-brightness","brightness(".concat(e,")")),ed("filter",t)]}),c("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},handle:e=>[a(),ed("--tw-backdrop-brightness","brightness(".concat(e,")")),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)]}),l("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),l("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),c("contrast",{themeKeys:["--contrast"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},handle:e=>[n(),ed("--tw-contrast","contrast(".concat(e,")")),ed("filter",t)]}),c("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},handle:e=>[a(),ed("--tw-backdrop-contrast","contrast(".concat(e,")")),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)]}),l("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),l("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),c("grayscale",{themeKeys:["--grayscale"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[n(),ed("--tw-grayscale","grayscale(".concat(e,")")),ed("filter",t)]}),c("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[a(),ed("--tw-backdrop-grayscale","grayscale(".concat(e,")")),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)]}),l("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),l("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),c("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"deg"):null},handle:e=>[n(),ed("--tw-hue-rotate","hue-rotate(".concat(e,")")),ed("filter",t)]}),c("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"deg"):null},handle:e=>[a(),ed("--tw-backdrop-hue-rotate","hue-rotate(".concat(e,")")),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)]}),l("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),l("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),c("invert",{themeKeys:["--invert"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[n(),ed("--tw-invert","invert(".concat(e,")")),ed("filter",t)]}),c("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[a(),ed("--tw-backdrop-invert","invert(".concat(e,")")),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)]}),l("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),l("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),c("saturate",{themeKeys:["--saturate"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},handle:e=>[n(),ed("--tw-saturate","saturate(".concat(e,")")),ed("filter",t)]}),c("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},handle:e=>[a(),ed("--tw-backdrop-saturate","saturate(".concat(e,")")),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)]}),l("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),l("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),c("sepia",{themeKeys:["--sepia"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[n(),ed("--tw-sepia","sepia(".concat(e,")")),ed("filter",t)]}),c("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"%"):null},defaultValue:"100%",handle:e=>[a(),ed("--tw-backdrop-sepia","sepia(".concat(e,")")),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)]}),l("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),l("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),s("drop-shadow-none",[n,["--tw-drop-shadow"," "],["filter",t]]),i.functional("drop-shadow",r=>{let a;if(r.modifier&&("arbitrary"===r.modifier.kind?a=r.modifier.value:N(r.modifier.value)&&(a="".concat(r.modifier.value,"%"))),!r.value){let r=e.get(["--drop-shadow"]),o=e.resolve(null,["--drop-shadow"]);return null===r||null===o?void 0:[n(),ed("--tw-drop-shadow-alpha",a),...eY("--tw-drop-shadow-size",r,a,e=>"var(--tw-drop-shadow-color, ".concat(e,")")),ed("--tw-drop-shadow",p(o,",").map(e=>"drop-shadow(".concat(e,")")).join(" ")),ed("filter",t)]}if("arbitrary"===r.value.kind){var o;let i=r.value.value;return(null!=(o=r.value.dataType)?o:m(i,["color"]))==="color"?null===(i=eP(i,r.modifier,e))?void 0:[n(),ed("--tw-drop-shadow-color",eM(i,"var(--tw-drop-shadow-alpha)")),ed("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:r.modifier&&!a?void 0:[n(),ed("--tw-drop-shadow-alpha",a),...eY("--tw-drop-shadow-size",i,a,e=>"var(--tw-drop-shadow-color, ".concat(e,")")),ed("--tw-drop-shadow","var(--tw-drop-shadow-size)"),ed("filter",t)]}{let o=e.get(["--drop-shadow-".concat(r.value.value)]),i=e.resolve(r.value.value,["--drop-shadow"]);if(o&&i)return r.modifier&&!a?void 0:a?[n(),ed("--tw-drop-shadow-alpha",a),...eY("--tw-drop-shadow-size",o,a,e=>"var(--tw-drop-shadow-color, ".concat(e,")")),ed("--tw-drop-shadow","var(--tw-drop-shadow-size)"),ed("filter",t)]:[n(),ed("--tw-drop-shadow-alpha",a),...eY("--tw-drop-shadow-size",o,a,e=>"var(--tw-drop-shadow-color, ".concat(e,")")),ed("--tw-drop-shadow",p(i,",").map(e=>"drop-shadow(".concat(e,")")).join(" ")),ed("filter",t)]}{let t=eR(r,e,["--drop-shadow-color","--color"]);if(t)return"inherit"===t?[n(),ed("--tw-drop-shadow-color","inherit"),ed("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:[n(),ed("--tw-drop-shadow-color",eM(t,"var(--tw-drop-shadow-alpha)")),ed("--tw-drop-shadow","var(--tw-drop-shadow-size)")]}}),l("drop-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--drop-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{valueThemeKeys:["--drop-shadow"]}]),c("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:e=>{let{value:t}=e;return F(t)?"".concat(t,"%"):null},handle:e=>[a(),ed("--tw-backdrop-opacity","opacity(".concat(e,")")),ed("-webkit-backdrop-filter",r),ed("backdrop-filter",r)]}),l("backdrop-opacity",()=>[{values:Array.from({length:21},(e,t)=>"".concat(5*t)),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let t="var(--tw-ease, ".concat(null!=(n=e.resolve(null,["--default-transition-timing-function"]))?n:"ease",")"),r="var(--tw-duration, ".concat(null!=(a=e.resolve(null,["--default-transition-duration"]))?a:"0s",")");s("transition-none",[["transition-property","none"]]),s("transition-all",[["transition-property","all"],["transition-timing-function",t],["transition-duration",r]]),s("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",t],["transition-duration",r]]),s("transition-opacity",[["transition-property","opacity"],["transition-timing-function",t],["transition-duration",r]]),s("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",t],["transition-duration",r]]),s("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",t],["transition-duration",r]]),c("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events",themeKeys:["--transition-property"],handle:e=>[ed("transition-property",e),ed("transition-timing-function",t),ed("transition-duration",r)]}),s("transition-discrete",[["transition-behavior","allow-discrete"]]),s("transition-normal",[["transition-behavior","normal"]]),c("delay",{handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"ms"):null},themeKeys:["--transition-delay"],handle:e=>[ed("transition-delay",e)]});{let t=()=>eh([eL("--tw-duration")]);s("duration-initial",[t,["--tw-duration","initial"]]),i.functional("duration",r=>{var n;if(r.modifier||!r.value)return;let a=null;if("arbitrary"===r.value.kind?a=r.value.value:null===(a=e.resolve(null!=(n=r.value.fraction)?n:r.value.value,["--transition-duration"]))&&N(r.value.value)&&(a="".concat(r.value.value,"ms")),null!==a)return[t(),ed("--tw-duration",a),ed("transition-duration",a)]})}l("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),l("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let e=()=>eh([eL("--tw-ease")]);s("ease-initial",[e,["--tw-ease","initial"]]),s("ease-linear",[e,["--tw-ease","linear"],["transition-timing-function","linear"]]),c("ease",{themeKeys:["--ease"],handle:t=>[e(),ed("--tw-ease",t),ed("transition-timing-function",t)]})}s("will-change-auto",[["will-change","auto"]]),s("will-change-scroll",[["will-change","scroll-position"]]),s("will-change-contents",[["will-change","contents"]]),s("will-change-transform",[["will-change","transform"]]),c("will-change",{themeKeys:[],handle:e=>[ed("will-change",e)]}),s("content-none",[["--tw-content","none"],["content","none"]]),c("content",{themeKeys:[],handle:e=>[eh([eL("--tw-content",'""')]),ed("--tw-content",e),ed("content","var(--tw-content)")]});{let e="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",t=()=>eh([eL("--tw-contain-size"),eL("--tw-contain-layout"),eL("--tw-contain-paint"),eL("--tw-contain-style")]);s("contain-none",[["contain","none"]]),s("contain-content",[["contain","content"]]),s("contain-strict",[["contain","strict"]]),s("contain-size",[t,["--tw-contain-size","size"],["contain",e]]),s("contain-inline-size",[t,["--tw-contain-size","inline-size"],["contain",e]]),s("contain-layout",[t,["--tw-contain-layout","layout"],["contain",e]]),s("contain-paint",[t,["--tw-contain-paint","paint"],["contain",e]]),s("contain-style",[t,["--tw-contain-style","style"],["contain",e]]),c("contain",{themeKeys:[],handle:e=>[ed("contain",e)]})}s("forced-color-adjust-none",[["forced-color-adjust","none"]]),s("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),s("leading-none",[()=>eh([eL("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),d("leading",["--leading","--spacing"],e=>[eh([eL("--tw-leading")]),ed("--tw-leading",e),ed("line-height",e)]),c("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:e=>[eh([eL("--tw-tracking")]),ed("--tw-tracking",e),ed("letter-spacing",e)]}),s("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),s("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let e="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",t=()=>eh([eL("--tw-ordinal"),eL("--tw-slashed-zero"),eL("--tw-numeric-figure"),eL("--tw-numeric-spacing"),eL("--tw-numeric-fraction")]);s("normal-nums",[["font-variant-numeric","normal"]]),s("ordinal",[t,["--tw-ordinal","ordinal"],["font-variant-numeric",e]]),s("slashed-zero",[t,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",e]]),s("lining-nums",[t,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",e]]),s("oldstyle-nums",[t,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",e]]),s("proportional-nums",[t,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",e]]),s("tabular-nums",[t,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",e]]),s("diagonal-fractions",[t,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",e]]),s("stacked-fractions",[t,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",e]])}{let t=()=>eh([eL("--tw-outline-style","solid")]);i.static("outline-hidden",()=>[ed("--tw-outline-style","none"),ed("outline-style","none"),ec("@media","(forced-colors: active)",[ed("outline","2px solid transparent"),ed("outline-offset","2px")])]),s("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),s("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),s("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),s("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),s("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),i.functional("outline",r=>{var n,a;if(null===r.value){if(r.modifier)return;let a=null!=(n=e.get(["--default-outline-width"]))?n:"1px";return[t(),ed("outline-style","var(--tw-outline-style)"),ed("outline-width",a)]}if("arbitrary"===r.value.kind){let n=r.value.value;switch(null!=(a=r.value.dataType)?a:m(n,["color","length","number","percentage"])){case"length":case"number":case"percentage":return r.modifier?void 0:[t(),ed("outline-style","var(--tw-outline-style)"),ed("outline-width",n)];default:return null===(n=eP(n,r.modifier,e))?void 0:[ed("outline-color",n)]}}{let t=eR(r,e,["--outline-color","--color"]);if(t)return[ed("outline-color",t)]}{if(r.modifier)return;let n=e.resolve(r.value.value,["--outline-width"]);if(n)return[t(),ed("outline-style","var(--tw-outline-style)"),ed("outline-width",n)];if(N(r.value.value))return[t(),ed("outline-style","var(--tw-outline-style)"),ed("outline-width","".concat(r.value.value,"px"))]}}),l("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),c("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"px"):null},handle:e=>[ed("outline-offset",e)]}),l("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}c("opacity",{themeKeys:["--opacity"],handleBareValue:e=>{let{value:t}=e;return F(t)?"".concat(t,"%"):null},handle:e=>[ed("opacity",e)]}),l("opacity",()=>[{values:Array.from({length:21},(e,t)=>"".concat(5*t)),valueThemeKeys:["--opacity"]}]),s("underline-offset-auto",[["text-underline-offset","auto"]]),c("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:e=>{let{value:t}=e;return N(t)?"".concat(t,"px"):null},handle:e=>[ed("text-underline-offset",e)]}),l("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),i.functional("text",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;switch(null!=(r=t.value.dataType)?r:m(n,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":if(t.modifier){let r="arbitrary"===t.modifier.kind?t.modifier.value:e.resolve(t.modifier.value,["--leading"]);if(!r&&W(t.modifier.value)){let n=e.resolve(null,["--spacing"]);if(!n)return null;r="calc(".concat(n," * ").concat(t.modifier.value,")")}return r||"none"!==t.modifier.value||(r="1"),r?[ed("font-size",n),ed("line-height",r)]:null}return[ed("font-size",n)];default:return null===(n=eP(n,t.modifier,e))?void 0:[ed("color",n)]}}{let r=eR(t,e,["--text-color","--color"]);if(r)return[ed("color",r)]}{let r=e.resolveWith(t.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(r){let[n,a={}]=Array.isArray(r)?r:[r];if(t.modifier){let r="arbitrary"===t.modifier.kind?t.modifier.value:e.resolve(t.modifier.value,["--leading"]);if(!r&&W(t.modifier.value)){let n=e.resolve(null,["--spacing"]);if(!n)return null;r="calc(".concat(n," * ").concat(t.modifier.value,")")}if(r||"none"!==t.modifier.value||(r="1"),!r)return null;let a=[ed("font-size",n)];return r&&a.push(ed("line-height",r)),a}return"string"==typeof a?[ed("font-size",n),ed("line-height",a)]:[ed("font-size",n),ed("line-height",a["--line-height"]?"var(--tw-leading, ".concat(a["--line-height"],")"):void 0),ed("letter-spacing",a["--letter-spacing"]?"var(--tw-tracking, ".concat(a["--letter-spacing"],")"):void 0),ed("font-weight",a["--font-weight"]?"var(--tw-font-weight, ".concat(a["--font-weight"],")"):void 0)]}}}}),l("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);let _=()=>eh([eL("--tw-text-shadow-color"),eL("--tw-text-shadow-alpha","100%","<percentage>")]);s("text-shadow-initial",[_,["--tw-text-shadow-color","initial"]]),i.functional("text-shadow",t=>{let r;if(t.modifier&&("arbitrary"===t.modifier.kind?r=t.modifier.value:N(t.modifier.value)&&(r="".concat(t.modifier.value,"%"))),!t.value){let t=e.get(["--text-shadow"]);return null===t?void 0:[_(),ed("--tw-text-shadow-alpha",r),...eZ("text-shadow",t,r,e=>"var(--tw-text-shadow-color, ".concat(e,")"))]}if("arbitrary"===t.value.kind){var n;let a=t.value.value;return(null!=(n=t.value.dataType)?n:m(a,["color"]))==="color"?null===(a=eP(a,t.modifier,e))?void 0:[_(),ed("--tw-text-shadow-color",eM(a,"var(--tw-text-shadow-alpha)"))]:[_(),ed("--tw-text-shadow-alpha",r),...eZ("text-shadow",a,r,e=>"var(--tw-text-shadow-color, ".concat(e,")"))]}switch(t.value.value){case"none":return t.modifier?void 0:[_(),ed("text-shadow","none")];case"inherit":return t.modifier?void 0:[_(),ed("--tw-text-shadow-color","inherit")]}{let n=e.get(["--text-shadow-".concat(t.value.value)]);if(n)return[_(),ed("--tw-text-shadow-alpha",r),...eZ("text-shadow",n,r,e=>"var(--tw-text-shadow-color, ".concat(e,")"))]}{let r=eR(t,e,["--text-shadow-color","--color"]);if(r)return[_(),ed("--tw-text-shadow-color",eM(r,"var(--tw-text-shadow-alpha)"))]}}),l("text-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["none"]},{valueThemeKeys:["--text-shadow"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:null!==e.get(["--text-shadow"])}]);{let t=function(e){return"var(--tw-ring-inset,) 0 0 0 calc(".concat(e," + var(--tw-ring-offset-width)) var(--tw-ring-color, ").concat(u,")")},r=function(e){return"inset 0 0 0 ".concat(e," var(--tw-inset-ring-color, currentcolor)")},n="var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow)",a="0 0 #0000",c=()=>eh([eL("--tw-shadow",a),eL("--tw-shadow-color"),eL("--tw-shadow-alpha","100%","<percentage>"),eL("--tw-inset-shadow",a),eL("--tw-inset-shadow-color"),eL("--tw-inset-shadow-alpha","100%","<percentage>"),eL("--tw-ring-color"),eL("--tw-ring-shadow",a),eL("--tw-inset-ring-color"),eL("--tw-inset-ring-shadow",a),eL("--tw-ring-inset"),eL("--tw-ring-offset-width","0px","<length>"),eL("--tw-ring-offset-color","#fff"),eL("--tw-ring-offset-shadow",a)]);s("shadow-initial",[c,["--tw-shadow-color","initial"]]),i.functional("shadow",t=>{let r;if(t.modifier&&("arbitrary"===t.modifier.kind?r=t.modifier.value:N(t.modifier.value)&&(r="".concat(t.modifier.value,"%"))),!t.value){let t=e.get(["--shadow"]);return null===t?void 0:[c(),ed("--tw-shadow-alpha",r),...eZ("--tw-shadow",t,r,e=>"var(--tw-shadow-color, ".concat(e,")")),ed("box-shadow",n)]}if("arbitrary"===t.value.kind){var o;let a=t.value.value;return(null!=(o=t.value.dataType)?o:m(a,["color"]))==="color"?null===(a=eP(a,t.modifier,e))?void 0:[c(),ed("--tw-shadow-color",eM(a,"var(--tw-shadow-alpha)"))]:[c(),ed("--tw-shadow-alpha",r),...eZ("--tw-shadow",a,r,e=>"var(--tw-shadow-color, ".concat(e,")")),ed("box-shadow",n)]}switch(t.value.value){case"none":return t.modifier?void 0:[c(),ed("--tw-shadow",a),ed("box-shadow",n)];case"inherit":return t.modifier?void 0:[c(),ed("--tw-shadow-color","inherit")]}{let a=e.get(["--shadow-".concat(t.value.value)]);if(a)return[c(),ed("--tw-shadow-alpha",r),...eZ("--tw-shadow",a,r,e=>"var(--tw-shadow-color, ".concat(e,")")),ed("box-shadow",n)]}{let r=eR(t,e,["--box-shadow-color","--color"]);if(r)return[c(),ed("--tw-shadow-color",eM(r,"var(--tw-shadow-alpha)"))]}}),l("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["none"]},{valueThemeKeys:["--shadow"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:null!==e.get(["--shadow"])}]),s("inset-shadow-initial",[c,["--tw-inset-shadow-color","initial"]]),i.functional("inset-shadow",t=>{let r;if(t.modifier&&("arbitrary"===t.modifier.kind?r=t.modifier.value:N(t.modifier.value)&&(r="".concat(t.modifier.value,"%"))),!t.value){let t=e.get(["--inset-shadow"]);return null===t?void 0:[c(),ed("--tw-inset-shadow-alpha",r),...eZ("--tw-inset-shadow",t,r,e=>"var(--tw-inset-shadow-color, ".concat(e,")")),ed("box-shadow",n)]}if("arbitrary"===t.value.kind){var o;let a=t.value.value;return(null!=(o=t.value.dataType)?o:m(a,["color"]))==="color"?null===(a=eP(a,t.modifier,e))?void 0:[c(),ed("--tw-inset-shadow-color",eM(a,"var(--tw-inset-shadow-alpha)"))]:[c(),ed("--tw-inset-shadow-alpha",r),...eZ("--tw-inset-shadow",a,r,e=>"var(--tw-inset-shadow-color, ".concat(e,")"),"inset "),ed("box-shadow",n)]}switch(t.value.value){case"none":return t.modifier?void 0:[c(),ed("--tw-inset-shadow",a),ed("box-shadow",n)];case"inherit":return t.modifier?void 0:[c(),ed("--tw-inset-shadow-color","inherit")]}{let a=e.get(["--inset-shadow-".concat(t.value.value)]);if(a)return[c(),ed("--tw-inset-shadow-alpha",r),...eZ("--tw-inset-shadow",a,r,e=>"var(--tw-inset-shadow-color, ".concat(e,")")),ed("box-shadow",n)]}{let r=eR(t,e,["--box-shadow-color","--color"]);if(r)return[c(),ed("--tw-inset-shadow-color",eM(r,"var(--tw-inset-shadow-alpha)"))]}}),l("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["none"]},{valueThemeKeys:["--inset-shadow"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t)),hasDefaultValue:null!==e.get(["--inset-shadow"])}]),s("ring-inset",[c,["--tw-ring-inset","inset"]]);let u=null!=(o=e.get(["--default-ring-color"]))?o:"currentcolor";i.functional("ring",r=>{var a,o;if(!r.value){if(r.modifier)return;let o=null!=(a=e.get(["--default-ring-width"]))?a:"1px";return[c(),ed("--tw-ring-shadow",t(o)),ed("box-shadow",n)]}if("arbitrary"===r.value.kind){let a=r.value.value;return(null!=(o=r.value.dataType)?o:m(a,["color","length"]))==="length"?r.modifier?void 0:[c(),ed("--tw-ring-shadow",t(a)),ed("box-shadow",n)]:null===(a=eP(a,r.modifier,e))?void 0:[ed("--tw-ring-color",a)]}{let t=eR(r,e,["--ring-color","--color"]);if(t)return[ed("--tw-ring-color",t)]}{if(r.modifier)return;let a=e.resolve(r.value.value,["--ring-width"]);if(null===a&&N(r.value.value)&&(a="".concat(r.value.value,"px")),a)return[c(),ed("--tw-ring-shadow",t(a)),ed("box-shadow",n)]}}),l("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),i.functional("inset-ring",t=>{if(!t.value)return t.modifier?void 0:[c(),ed("--tw-inset-ring-shadow",r("1px")),ed("box-shadow",n)];if("arbitrary"===t.value.kind){var a;let o=t.value.value;return(null!=(a=t.value.dataType)?a:m(o,["color","length"]))==="length"?t.modifier?void 0:[c(),ed("--tw-inset-ring-shadow",r(o)),ed("box-shadow",n)]:null===(o=eP(o,t.modifier,e))?void 0:[ed("--tw-inset-ring-color",o)]}{let r=eR(t,e,["--ring-color","--color"]);if(r)return[ed("--tw-inset-ring-color",r)]}{if(t.modifier)return;let a=e.resolve(t.value.value,["--ring-width"]);if(null===a&&N(t.value.value)&&(a="".concat(t.value.value,"px")),a)return[c(),ed("--tw-inset-ring-shadow",r(a)),ed("box-shadow",n)]}}),l("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let d="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";i.functional("ring-offset",t=>{if(t.value){if("arbitrary"===t.value.kind){var r;let n=t.value.value;return(null!=(r=t.value.dataType)?r:m(n,["color","length"]))==="length"?t.modifier?void 0:[ed("--tw-ring-offset-width",n),ed("--tw-ring-offset-shadow",d)]:null===(n=eP(n,t.modifier,e))?void 0:[ed("--tw-ring-offset-color",n)]}{let r=e.resolve(t.value.value,["--ring-offset-width"]);if(r)return t.modifier?void 0:[ed("--tw-ring-offset-width",r),ed("--tw-ring-offset-shadow",d)];if(N(t.value.value))return t.modifier?void 0:[ed("--tw-ring-offset-width","".concat(t.value.value,"px")),ed("--tw-ring-offset-shadow",d)]}{let r=eR(t,e,["--ring-offset-color","--color"]);if(r)return[ed("--tw-ring-offset-color",r)]}}})}return l("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(e,t)=>"".concat(5*t))},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),i.functional("@container",e=>{let t=null;if(null===e.value?t="inline-size":"arbitrary"===e.value.kind?t=e.value.value:"named"===e.value.kind&&"normal"===e.value.value&&(t="normal"),null!==t)return e.modifier?[ed("container-type",t),ed("container-name",e.modifier.value)]:[ed("container-type",t)]}),l("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),i}(v),r=function(e){let t=new e5;function r(e,r){let{compounds:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};n=null!=n?n:e3(r),t.static(e,e=>{e.nodes=r.map(t=>eu(t,e.nodes))},{compounds:n})}r("*",[":is(& > *)"],{compounds:0}),r("**",[":is(& *)"],{compounds:0});let n=["@media","@supports","@container"];t.compound("not",3,(e,t)=>{if("arbitrary"===t.variant.kind&&t.variant.relative||t.modifier)return null;let r=!1;if(em([e],(t,a)=>{let{path:o}=a;if("rule"!==t.kind&&"at-rule"!==t.kind||t.nodes.length>0)return 0;let i=[],l=[];for(let e of o)"at-rule"===e.kind?i.push(e):"rule"===e.kind&&l.push(e);if(i.length>1||l.length>1)return 2;let s=[];for(let e of l){var c;let t=(c=e.selector).includes("::")?null:"&:not(".concat(p(c,",").map(e=>e=e.replaceAll("&","*")).join(", "),")");if(!t)return r=!1,2;s.push(es(t,[]))}for(let e of i){let t=function(e){for(let t of n){if(t!==e.name)continue;let r=p(e.params,",");return r.length>1?null:(r=function(e,t){return t.map(t=>{let r=p(t=t.trim()," ");return"not"===r[0]?r.slice(1).join(" "):"@container"===e?"("===r[0][0]?"not ".concat(t):"not"===r[1]?"".concat(r[0]," ").concat(r.slice(2).join(" ")):"".concat(r[0]," not ").concat(r.slice(1).join(" ")):"not ".concat(t)})}(e.name,r),ec(e.name,r.join(", ")))}return null}(e);if(!t)return r=!1,2;s.push(t)}return Object.assign(e,es("&",s)),r=!0,1}),"rule"===e.kind&&"&"===e.selector&&1===e.nodes.length&&Object.assign(e,e.nodes[0]),!r)return null}),t.suggest("not",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("not",e))),t.compound("group",2,(t,r)=>{if("arbitrary"===r.variant.kind&&r.variant.relative)return null;let n=r.modifier?":where(.".concat(e.prefix?"".concat(e.prefix,"\\:"):"","group\\/").concat(r.modifier.value,")"):":where(.".concat(e.prefix?"".concat(e.prefix,"\\:"):"","group)"),a=!1;if(em([t],(e,t)=>{let{path:r}=t;if("rule"!==e.kind)return 0;for(let e of r.slice(0,-1))if("rule"===e.kind)return a=!1,2;let o=e.selector.replaceAll("&",n);p(o,",").length>1&&(o=":is(".concat(o,")")),e.selector="&:is(".concat(o," *)"),a=!0}),!a)return null}),t.suggest("group",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("group",e))),t.compound("peer",2,(t,r)=>{if("arbitrary"===r.variant.kind&&r.variant.relative)return null;let n=r.modifier?":where(.".concat(e.prefix?"".concat(e.prefix,"\\:"):"","peer\\/").concat(r.modifier.value,")"):":where(.".concat(e.prefix?"".concat(e.prefix,"\\:"):"","peer)"),a=!1;if(em([t],(e,t)=>{let{path:r}=t;if("rule"!==e.kind)return 0;for(let e of r.slice(0,-1))if("rule"===e.kind)return a=!1,2;let o=e.selector.replaceAll("&",n);p(o,",").length>1&&(o=":is(".concat(o,")")),e.selector="&:is(".concat(o," ~ *)"),a=!0}),!a)return null}),t.suggest("peer",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("peer",e))),r("first-letter",["&::first-letter"]),r("first-line",["&::first-line"]),r("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),r("selection",["& *::selection","&::selection"]),r("file",["&::file-selector-button"]),r("placeholder",["&::placeholder"]),r("backdrop",["&::backdrop"]),r("details-content",["&::details-content"]);{let e=function(){return eh([ec("@property","--tw-content",[ed("syntax",'"*"'),ed("initial-value",'""'),ed("inherits","false")])])};t.static("before",t=>{t.nodes=[es("&::before",[e(),ed("content","var(--tw-content)"),...t.nodes])]},{compounds:0}),t.static("after",t=>{t.nodes=[es("&::after",[e(),ed("content","var(--tw-content)"),...t.nodes])]},{compounds:0})}r("first",["&:first-child"]),r("last",["&:last-child"]),r("only",["&:only-child"]),r("odd",["&:nth-child(odd)"]),r("even",["&:nth-child(even)"]),r("first-of-type",["&:first-of-type"]),r("last-of-type",["&:last-of-type"]),r("only-of-type",["&:only-of-type"]),r("visited",["&:visited"]),r("target",["&:target"]),r("open",["&:is([open], :popover-open, :open)"]),r("default",["&:default"]),r("checked",["&:checked"]),r("indeterminate",["&:indeterminate"]),r("placeholder-shown",["&:placeholder-shown"]),r("autofill",["&:autofill"]),r("optional",["&:optional"]),r("required",["&:required"]),r("valid",["&:valid"]),r("invalid",["&:invalid"]),r("user-valid",["&:user-valid"]),r("user-invalid",["&:user-invalid"]),r("in-range",["&:in-range"]),r("out-of-range",["&:out-of-range"]),r("read-only",["&:read-only"]),r("empty",["&:empty"]),r("focus-within",["&:focus-within"]),t.static("hover",e=>{e.nodes=[es("&:hover",[ec("@media","(hover: hover)",e.nodes)])]}),r("focus",["&:focus"]),r("focus-visible",["&:focus-visible"]),r("active",["&:active"]),r("enabled",["&:enabled"]),r("disabled",["&:disabled"]),r("inert",["&:is([inert], [inert] *)"]),t.compound("in",2,(e,t)=>{if(t.modifier)return null;let r=!1;if(em([e],(e,t)=>{let{path:n}=t;if("rule"!==e.kind)return 0;for(let e of n.slice(0,-1))if("rule"===e.kind)return r=!1,2;e.selector=":where(".concat(e.selector.replaceAll("&","*"),") &"),r=!0}),!r)return null}),t.suggest("in",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("in",e))),t.compound("has",2,(e,t)=>{if(t.modifier)return null;let r=!1;if(em([e],(e,t)=>{let{path:n}=t;if("rule"!==e.kind)return 0;for(let e of n.slice(0,-1))if("rule"===e.kind)return r=!1,2;e.selector="&:has(".concat(e.selector.replaceAll("&","*"),")"),r=!0}),!r)return null}),t.suggest("has",()=>Array.from(t.keys()).filter(e=>t.compoundsWith("has",e))),t.functional("aria",(e,t)=>{if(!t.value||t.modifier)return null;"arbitrary"===t.value.kind?e.nodes=[es("&[aria-".concat(e6(t.value.value),"]"),e.nodes)]:e.nodes=[es("&[aria-".concat(t.value.value,'="true"]'),e.nodes)]}),t.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),t.functional("data",(e,t)=>{if(!t.value||t.modifier)return null;e.nodes=[es("&[data-".concat(e6(t.value.value),"]"),e.nodes)]}),t.functional("nth",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!N(t.value.value))return null;e.nodes=[es("&:nth-child(".concat(t.value.value,")"),e.nodes)]}),t.functional("nth-last",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!N(t.value.value))return null;e.nodes=[es("&:nth-last-child(".concat(t.value.value,")"),e.nodes)]}),t.functional("nth-of-type",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!N(t.value.value))return null;e.nodes=[es("&:nth-of-type(".concat(t.value.value,")"),e.nodes)]}),t.functional("nth-last-of-type",(e,t)=>{if(!t.value||t.modifier||"named"===t.value.kind&&!N(t.value.value))return null;e.nodes=[es("&:nth-last-of-type(".concat(t.value.value,")"),e.nodes)]}),t.functional("supports",(e,t)=>{if(!t.value||t.modifier)return null;let r=t.value.value;if(null===r)return null;if(/^[\w-]*\s*\(/.test(r)){e.nodes=[ec("@supports",r.replace(/\b(and|or|not)\b/g," $1 "),e.nodes)];return}r.includes(":")||(r="".concat(r,": var(--tw)")),("("!==r[0]||")"!==r[r.length-1])&&(r="(".concat(r,")")),e.nodes=[ec("@supports",r,e.nodes)]},{compounds:1}),r("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),r("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),r("contrast-more",["@media (prefers-contrast: more)"]),r("contrast-less",["@media (prefers-contrast: less)"]);{let r=function(e,t,r,n){if(e===t)return 0;let a=n.get(e);if(null===a)return"asc"===r?-1:1;let o=n.get(t);return null===o?"asc"===r?1:-1:eN(a,o,r)};{let n=e.namespace("--breakpoint"),a=new er(t=>{switch(t.kind){case"static":var r;return null!=(r=e.resolveValue(t.root,["--breakpoint"]))?r:null;case"functional":{if(!t.value||t.modifier)return null;let r=null;return"arbitrary"===t.value.kind?r=t.value.value:"named"===t.value.kind&&(r=e.resolveValue(t.value.value,["--breakpoint"])),!r||r.includes("var(")?null:r}case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("max",(e,t)=>{if(t.modifier)return null;let r=a.get(t);if(null===r)return null;e.nodes=[ec("@media","(width < ".concat(r,")"),e.nodes)]},{compounds:1})},(e,t)=>r(e,t,"desc",a)),t.suggest("max",()=>Array.from(n.keys()).filter(e=>null!==e)),t.group(()=>{for(let[r,n]of e.namespace("--breakpoint"))null!==r&&t.static(r,e=>{e.nodes=[ec("@media","(width >= ".concat(n,")"),e.nodes)]},{compounds:1});t.functional("min",(e,t)=>{if(t.modifier)return null;let r=a.get(t);if(null===r)return null;e.nodes=[ec("@media","(width >= ".concat(r,")"),e.nodes)]},{compounds:1})},(e,t)=>r(e,t,"asc",a)),t.suggest("min",()=>Array.from(n.keys()).filter(e=>null!==e))}{let n=e.namespace("--container"),a=new er(t=>{switch(t.kind){case"functional":{if(null===t.value)return null;let r=null;return"arbitrary"===t.value.kind?r=t.value.value:"named"===t.value.kind&&(r=e.resolveValue(t.value.value,["--container"])),!r||r.includes("var(")?null:r}case"static":case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("@max",(e,t)=>{let r=a.get(t);if(null===r)return null;e.nodes=[ec("@container",t.modifier?"".concat(t.modifier.value," (width < ").concat(r,")"):"(width < ".concat(r,")"),e.nodes)]},{compounds:1})},(e,t)=>r(e,t,"desc",a)),t.suggest("@max",()=>Array.from(n.keys()).filter(e=>null!==e)),t.group(()=>{t.functional("@",(e,t)=>{let r=a.get(t);if(null===r)return null;e.nodes=[ec("@container",t.modifier?"".concat(t.modifier.value," (width >= ").concat(r,")"):"(width >= ".concat(r,")"),e.nodes)]},{compounds:1}),t.functional("@min",(e,t)=>{let r=a.get(t);if(null===r)return null;e.nodes=[ec("@container",t.modifier?"".concat(t.modifier.value," (width >= ").concat(r,")"):"(width >= ".concat(r,")"),e.nodes)]},{compounds:1})},(e,t)=>r(e,t,"asc",a)),t.suggest("@min",()=>Array.from(n.keys()).filter(e=>null!==e)),t.suggest("@",()=>Array.from(n.keys()).filter(e=>null!==e))}}return r("portrait",["@media (orientation: portrait)"]),r("landscape",["@media (orientation: landscape)"]),r("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),r("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),r("dark",["@media (prefers-color-scheme: dark)"]),r("starting",["@starting-style"]),r("print",["@media print"]),r("forced-colors",["@media (forced-colors: active)"]),r("inverted-colors",["@media (inverted-colors: inverted)"]),r("pointer-none",["@media (pointer: none)"]),r("pointer-coarse",["@media (pointer: coarse)"]),r("pointer-fine",["@media (pointer: fine)"]),r("any-pointer-none",["@media (any-pointer: none)"]),r("any-pointer-coarse",["@media (any-pointer: coarse)"]),r("any-pointer-fine",["@media (any-pointer: fine)"]),r("noscript",["@media (scripting: none)"]),t}(v),n=new er(e=>(function(e,t){if("["===e[0]&&"]"===e[e.length-1]){if("@"===e[1]&&e.includes("&"))return null;let t=ek(e.slice(1,-1));if(!ex(t)||0===t.length||0===t.trim().length)return null;let r=">"===t[0]||"+"===t[0]||"~"===t[0];return r||"@"===t[0]||t.includes("&")||(t="&:is(".concat(t,")")),{kind:"arbitrary",selector:t,relative:r}}{let[r,n=null,a]=p(e,"/");if(a)return null;for(let[e,a]of ez(r,e=>t.variants.has(e)))switch(t.variants.kind(e)){case"static":return null!==a||null!==n?null:{kind:"static",root:e};case"functional":{let t=null===n?null:eA(n);if(null!==n&&null===t)return null;if(null===a)return{kind:"functional",root:e,modifier:t,value:null};if("]"===a[a.length-1]){if("["!==a[0])continue;let r=ek(a.slice(1,-1));return ex(r)&&0!==r.length&&0!==r.trim().length?{kind:"functional",root:e,modifier:t,value:{kind:"arbitrary",value:r}}:null}if(")"===a[a.length-1]){if("("!==a[0])continue;let r=ek(a.slice(1,-1));return ex(r)&&0!==r.length&&0!==r.trim().length&&"-"===r[0]&&"-"===r[1]?{kind:"functional",root:e,modifier:t,value:{kind:"arbitrary",value:"var(".concat(r,")")}}:null}return{kind:"functional",root:e,modifier:t,value:{kind:"named",value:a}}}case"compound":{if(null===a)return null;let r=t.parseVariant(a);if(null===r||!t.variants.compoundsWith(e,r))return null;let o=null===n?null:eA(n);return null!==n&&null===o?null:{kind:"compound",root:e,modifier:o,variant:r}}}}return null})(e,l)),a=new er(e=>Array.from(function*(e,t){let r,n=p(e,":");if(t.theme.prefix){if(1===n.length||n[0]!==t.theme.prefix)return null;n.shift()}let a=n.pop(),o=[];for(let e=n.length-1;e>=0;--e){let r=t.parseVariant(n[e]);if(null===r)return;o.push(r)}let i=!1;"!"===a[a.length-1]?(i=!0,a=a.slice(0,-1)):"!"===a[0]&&(i=!0,a=a.slice(1)),t.utilities.has(a,"static")&&!a.includes("[")&&(yield{kind:"static",root:a,variants:o,important:i,raw:e});let[l,s=null,c]=p(a,"/");if(c)return;let u=null===s?null:eA(s);if(null===s||null!==u){if("["===l[0]){if("]"!==l[l.length-1])return;let t=l.charCodeAt(1);if(45!==t&&!(t>=97&&t<=122))return;let r=(l=l.slice(1,-1)).indexOf(":");if(-1===r||0===r||r===l.length-1)return;let n=l.slice(0,r),a=ek(l.slice(r+1));if(!ex(a))return;yield{kind:"arbitrary",property:n,value:a,modifier:u,variants:o,important:i,raw:e};return}if("]"===l[l.length-1]){let e=l.indexOf("-[");if(-1===e)return;let n=l.slice(0,e);if(!t.utilities.has(n,"functional"))return;r=[[n,l.slice(e+1)]]}else if(")"===l[l.length-1]){let e=l.indexOf("-(");if(-1===e)return;let n=l.slice(0,e);if(!t.utilities.has(n,"functional"))return;let a=l.slice(e+2,-1),o=p(a,":"),i=null;if(2===o.length&&(i=o[0],a=o[1]),"-"!==a[0]||"-"!==a[1]||!ex(a))return;r=[[n,null===i?"[var(".concat(a,")]"):"[".concat(i,":var(").concat(a,")]")]]}else r=ez(l,e=>t.utilities.has(e,"functional"));for(let[t,n]of r){let r={kind:"functional",root:t,modifier:u,value:null,variants:o,important:i,raw:e};if(null===n){yield r;continue}{let e=n.indexOf("[");if(-1!==e){if("]"!==n[n.length-1])return;let t=ek(n.slice(e+1,-1));if(!ex(t))continue;let a="";for(let e=0;e<t.length;e++){let r=t.charCodeAt(e);if(58===r){a=t.slice(0,e),t=t.slice(e+1);break}if(!(45===r||r>=97&&r<=122))break}if(0===t.length||0===t.trim().length)continue;r.value={kind:"arbitrary",dataType:a||null,value:t}}else{var d;let e=null===s||(null==(d=r.modifier)?void 0:d.kind)==="arbitrary"?null:"".concat(n,"/").concat(s);r.value={kind:"named",value:n,fraction:e}}}yield r}}}(e,l))),o=new er(e=>new er(t=>{let r=function(e,t,r){let n=function(e,t){var r;if("arbitrary"===e.kind){let r=e.value;return e.modifier&&(r=eP(r,e.modifier,t.theme)),null===r?[]:[[ed(e.property,r)]]}let n=null!=(r=t.utilities.get(e.root))?r:[],a=[];for(let t of n.filter(e=>!te(e))){if(t.kind!==e.kind)continue;let r=t.compileFn(e);if(void 0!==r){if(null===r)return a;a.push(r)}}if(a.length>0)return a;for(let t of n.filter(e=>te(e))){if(t.kind!==e.kind)continue;let r=t.compileFn(e);if(void 0!==r){if(null===r)return a;a.push(r)}}return a}(e,t);if(0===n.length)return[];let a=t.important&&!!(1&r),o=[],i=".".concat(G(e.raw));for(let r of n){let n=function(e){let t=new Set,r=0,n=e.slice(),a=!1;for(;n.length>0;){let e=n.shift();if("declaration"===e.kind){if(void 0===e.value||(r++,a))continue;if("--tw-sort"===e.property){var o;let r=e9.indexOf(null!=(o=e.value)?o:"");if(-1!==r){t.add(r),a=!0;continue}}let n=e9.indexOf(e.property);-1!==n&&t.add(n)}else if("rule"===e.kind||"at-rule"===e.kind)for(let t of e.nodes)n.push(t)}return{order:Array.from(t).sort((e,t)=>e-t),count:r}}(r);(e.important||a)&&function e(t){for(let r of t)"at-root"!==r.kind&&("declaration"===r.kind?r.important=!0:("rule"===r.kind||"at-rule"===r.kind)&&e(r.nodes))}(r);let l={kind:"rule",selector:i,nodes:r};for(let r of e.variants)if(null===e8(l,r,t.variants))return[];o.push({node:l,propertySort:n})}return o}(t,l,e);try{eX(r.map(e=>{let{node:t}=e;return t}),l)}catch(e){return[]}return r})),i=new er(e=>{for(let t of el(e))v.markUsedVariable(t)}),l={theme:v,utilities:t,variants:r,invalidCandidates:new Set,important:!1,candidatesToCss(e){let t=[];for(let r of e){let e=!1,{astNodes:n}=e7([r],this,{onInvalidCandidate(){e=!0}});0===(n=ev(n,l,0)).length||e?t.push(null):t.push(eg(n))}return t},getClassOrder(e){return function(e,t){let{astNodes:r,nodeSorting:n}=e7(Array.from(t),e),a=new Map(t.map(e=>[e,null])),o=0n;for(let e of r){var i,l;let t=null==(i=n.get(e))?void 0:i.candidate;t&&a.set(t,null!=(l=a.get(t))?l:o++)}return t.map(e=>{var t;return[e,null!=(t=a.get(e))?t:null]})}(this,e)},getClassList(){return function(e){let t=new er(e=>({name:e,utility:e,fraction:!1,modifiers:[]}));for(let r of e.utilities.keys("static")){let e=t.get(r);e.fraction=!1,e.modifiers=[]}for(let r of e.utilities.keys("functional"))for(let n of e.utilities.getCompletions(r))for(let e of n.values){let a=null!==e&&e1.test(e),o=null===e?r:"".concat(r,"-").concat(e),i=t.get(o);if(i.utility=r,i.fraction||(i.fraction=a),i.modifiers.push(...n.modifiers),n.supportsNegative){let e=t.get("-".concat(o));e.utility="-".concat(r),e.fraction||(e.fraction=a),e.modifiers.push(...n.modifiers)}}if(0===t.size)return[];let r=Array.from(t.values());return r.sort((e,t)=>e0(e.name,t.name)),function(e){let t=[],r=null,n=new Map,a=new er(()=>[]);for(let o of e){let{utility:e,fraction:i}=o;r||(r={utility:e,items:[]},n.set(e,r)),e!==r.utility&&(t.push(r),r={utility:e,items:[]},n.set(e,r)),i?a.get(e).push(o):r.items.push(o)}for(let[e,o]of(r&&t[t.length-1]!==r&&t.push(r),a)){let t=n.get(e);t&&t.items.push(...o)}let o=[];for(let e of t)for(let t of e.items)o.push([t.name,{modifiers:t.modifiers}]);return o}(r)}(this)},getVariants(){return function(e){let t=[];for(let[r,n]of e.variants.entries()){let a=function(){let{value:t,modifier:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=r;t&&(a+=o?"-".concat(t):t),n&&(a+="/".concat(n));let i=e.parseVariant(a);if(!i)return[];let l=es(".__placeholder__",[]);if(null===e8(l,i,e.variants))return[];let s=[];return function e(t,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};for(let i=0;i<t.length;i++){var o;let l=t[i],s=null!=(o=n[n.length-1])?o:null;if("rule"===l.kind||"at-rule"===l.kind)n.push(l),e(l.nodes,r,n,a),n.pop();else if("context"===l.kind){e(l.nodes,r,n,{...a,...l.context});continue}n.push(l),r(l,{parent:s,context:a,path:n,replaceWith(e){Array.isArray(e)?0===e.length?t.splice(i,1):1===e.length?t[i]=e[0]:t.splice(i,1,...e):t[i]=e,i+=e.length-1}}),n.pop()}}(l.nodes,(e,t)=>{let{path:r}=t;if("rule"!==e.kind&&"at-rule"!==e.kind||e.nodes.length>0)return;r.sort((e,t)=>{let r="at-rule"===e.kind,n="at-rule"===t.kind;return r&&!n?-1:!r&&n?1:0});let n=r.flatMap(e=>"rule"===e.kind?"&"===e.selector?[]:[e.selector]:"at-rule"===e.kind?["".concat(e.name," ").concat(e.params)]:[]),a="";for(let e=n.length-1;e>=0;e--)a=""===a?n[e]:"".concat(n[e]," { ").concat(a," }");s.push(a)}),s};if("arbitrary"===n.kind)continue;let o="@"!==r,i=e.variants.getCompletions(r);switch(n.kind){case"static":t.push({name:r,values:i,isArbitrary:!1,hasDash:o,selectors:a});break;case"functional":case"compound":t.push({name:r,values:i,isArbitrary:!0,hasDash:o,selectors:a})}}return t}(this)},parseCandidate:e=>a.get(e),parseVariant:e=>n.get(e),compileAstNodes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return o.get(t).get(e)},printCandidate:e=>(function(e,t){let r=[];for(let e of t.variants)r.unshift(ej(e));e.theme.prefix&&r.unshift(e.theme.prefix);let n="";if("static"===t.kind&&(n+=t.root),"functional"===t.kind&&(n+=t.root,t.value))if("arbitrary"===t.value.kind){if(null!==t.value){var a;let e=(a=t.value.value,eV.get(a)),r=e?t.value.value.slice(4,-1):t.value.value,[o,i]=e?["(",")"]:["[","]"];t.value.dataType?n+="-".concat(o).concat(t.value.dataType,":").concat(eK(r)).concat(i):n+="-".concat(o).concat(eK(r)).concat(i)}}else"named"===t.value.kind&&(n+="-".concat(t.value.value));return"arbitrary"===t.kind&&(n+="[".concat(t.property,":").concat(eK(t.value),"]")),("arbitrary"===t.kind||"functional"===t.kind)&&(n+=eT(t.modifier)),t.important&&(n+="!"),r.push(n),r.join(":")})(l,e),printVariant:e=>ej(e),getVariantOrder(){let e=Array.from(n.values());e.sort((e,t)=>this.variants.compare(e,t));let t=new Map,r,a=0;for(let n of e)null!==n&&(void 0!==r&&0!==this.variants.compare(r,n)&&a++,t.set(n,a),r=n);return t},resolveThemeValue(e){var t;let r=!(arguments.length>1)||void 0===arguments[1]||arguments[1],n=e.lastIndexOf("/"),a=null;-1!==n&&(a=e.slice(n+1).trim(),e=e.slice(0,n).trim());let o=null!=(t=v.resolve(null,[e],+!!r))?t:void 0;return a&&o?eM(o,a):o},trackUsedVariables(e){i.get(e)}});if(h&&(K.important=h),j.length>0)for(let e of j)K.invalidCandidates.add(e);for(let t of(f|=await tC({designSystem:K,base:s,ast:e,loadModule:u,sources:z}),g))t(K);for(let e of w)e(K);if(b){let t=[];for(let[e,r]of K.theme.entries()){if(2&r.options)continue;let n=ed(G(e),r.value);n.src=r.src,t.push(n)}for(let t of K.theme.getKeyframes())e.push(ep({theme:!0},[eh([t])]));b.nodes=[ep({theme:!0},t)]}if(A.length>0){for(let e of A){let t=es("&",e.nodes),r=e.params,n=K.parseVariant(r);if(null===n)throw Error("Cannot use `@variant` with unknown variant: ".concat(r));if(null===e8(t,n,K.variants))throw Error("Cannot use `@variant` with variant: ".concat(r));Object.assign(e,t)}f|=32}if(f|=eX(e,K),f|=tt(e,K),x){let e=x;e.kind="context",e.context={}}return em(e,(e,t)=>{let{replaceWith:r}=t;if("at-rule"===e.kind)return"@utility"===e.name&&r([]),1}),{designSystem:K,ast:e,sources:z,root:C,utilitiesNode:x,features:f,inlineCandidates:T}}async function t_(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{designSystem:r,ast:n,sources:a,root:o,utilitiesNode:i,features:l,inlineCandidates:s}=await tF(e,t);function c(e){r.invalidCandidates.add(e)}n.unshift(ef("! tailwindcss v".concat("4.1.13"," | MIT License | https://tailwindcss.com ")));let u=new Set,d=null,f=0,p=!1;for(let e of s)r.invalidCandidates.has(e)||(u.add(e),p=!0);return{sources:a,root:o,features:l,build(a){if(0===l)return e;if(!i)return null!=d||(d=ev(n,r,t.polyfills)),d;let o=p,s=!1;p=!1;let h=u.size;for(let e of a)if(!r.invalidCandidates.has(e))if("-"===e[0]&&"-"===e[1]){let t=r.theme.markUsedVariable(e);o||(o=t),s||(s=t)}else u.add(e),o||(o=u.size!==h);if(!o)return null!=d||(d=ev(n,r,t.polyfills)),d;let m=e7(u,r,{onInvalidCandidate:c}).astNodes;return t.from&&em(m,e=>{null!=e.src||(e.src=i.src)}),s||f!==m.length?(f=m.length,i.nodes=m,d=ev(n,r,t.polyfills)):(null!=d||(d=ev(n,r,t.polyfills)),d)}}}async function tU(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=H(e,{from:t.from}),n=await t_(r,t),a=r,o=e;return{...n,build(e){let r=n.build(e);return r===a||(o=eg(r,!!t.from),a=r),o},buildSourceMap:()=>(function(e){let{ast:t}=e,r=new er(e=>(function(e){let t=[0];for(let r=0;r<e.length;r++)10===e.charCodeAt(r)&&t.push(r+1);return{find:function(e){let r=0,n=t.length;for(;n>0;){let a=(0|n)>>1,o=r+a;t[o]<=e?(r=o+1,n=n-a-1):n=a}let a=e-t[r-=1];return{line:r+1,column:a}},findOffset:function(e){var r;let{line:n,column:a}=e;n-=1,n=Math.min(Math.max(n,0),t.length-1);let o=t[n],i=null!=(r=t[n+1])?r:o;return Math.min(Math.max(o+a,0),i)}}})(e.code)),n=new er(e=>({url:e.file,content:e.code,ignore:!1})),a={file:null,sources:[],mappings:[]};for(let e of(em(t,e=>{if(!e.src||!e.dst)return;let t=n.get(e.src[0]);if(!t.content)return;let o=r.get(e.src[0]),i=r.get(e.dst[0]),l=t.content.slice(e.src[1],e.src[2]),s=0;for(let r of l.split("\n")){if(""!==r.trim()){let r=o.find(e.src[1]+s),n=i.find(e.dst[1]);a.mappings.push({name:null,originalPosition:{source:t,...r},generatedPosition:n})}s+=r.length,s+=1}let c=o.find(e.src[2]),u=i.find(e.dst[2]);a.mappings.push({name:null,originalPosition:{source:t,...c},generatedPosition:u})}),r.keys()))a.sources.push(n.get(e));return a.mappings.sort((e,t)=>{var r,n,a,o,i,l,s,c;return e.generatedPosition.line-t.generatedPosition.line||e.generatedPosition.column-t.generatedPosition.column||(null!=(i=null==(r=e.originalPosition)?void 0:r.line)?i:0)-(null!=(l=null==(n=t.originalPosition)?void 0:n.line)?l:0)||(null!=(s=null==(a=e.originalPosition)?void 0:a.column)?s:0)-(null!=(c=null==(o=t.originalPosition)?void 0:o.column)?c:0)}),a})({ast:a})}}async function tD(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(await tF(H(e),t)).designSystem}function tB(){throw Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}function tL(e){return this.prefix?"--".concat(e.slice(3+this.prefix.length)):e}function tM(e,t){for(let r of t){let t=null!==e?"".concat(r,"-").concat(e):r;if(!this.values.has(t)){if(!(null!==e&&e.includes(".")))continue;else if(t="".concat(r,"-").concat(e.replaceAll(".","_")),!this.values.has(t))continue}if(!ee(t,r))return t}return null}function tI(e){let t=this.values.get(e);if(!t)return null;let r=null;return 2&t.options&&(r=t.value),"var(".concat(G(this.prefixKey(e))).concat(r?", ".concat(r):"",")")}e.i(54672)}]);