{"version": 3, "sources": ["turbopack:///[project]/src/lib/mongodb.ts", "turbopack:///[project]/src/lib/models/ProductOrder.ts", "turbopack:///[project]/src/app/api/products/orders/route.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI!, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n\n// Type declaration for global mongoose\ndeclare global {\n  var mongoose: {\n    conn: typeof mongoose | null;\n    promise: Promise<typeof mongoose> | null;\n  };\n}\n", "import mongoose, { Schema } from 'mongoose';\nimport { IProductOrder } from '@/types';\n\nconst ProductOrderSchema = new Schema<IProductOrder>(\n  {\n    customerName: {\n      type: String,\n      required: [true, 'Customer name is required'],\n      trim: true,\n      maxlength: [100, 'Name cannot exceed 100 characters'],\n    },\n    customerEmail: {\n      type: String,\n      required: [true, 'Email is required'],\n      trim: true,\n      lowercase: true,\n      match: [\n        /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n        'Please enter a valid email address',\n      ],\n    },\n    customerPhone: {\n      type: String,\n      required: [true, 'Phone number is required'],\n      trim: true,\n      match: [\n        /^[\\+]?[1-9][\\d]{0,15}$/,\n        'Please enter a valid phone number',\n      ],\n    },\n    products: [\n      {\n        productId: {\n          type: String,\n          required: [true, 'Product ID is required'],\n        },\n        productName: {\n          type: String,\n          required: [true, 'Product name is required'],\n          trim: true,\n        },\n        quantity: {\n          type: Number,\n          required: [true, 'Quantity is required'],\n          min: [1, 'Quantity must be at least 1'],\n          max: [100, 'Quantity cannot exceed 100'],\n        },\n        price: {\n          type: Number,\n          required: [true, 'Price is required'],\n          min: [0, 'Price cannot be negative'],\n        },\n      },\n    ],\n    totalAmount: {\n      type: Number,\n      required: [true, 'Total amount is required'],\n      min: [0, 'Total amount cannot be negative'],\n    },\n    shippingAddress: {\n      street: {\n        type: String,\n        required: [true, 'Street address is required'],\n        trim: true,\n        maxlength: [200, 'Street address cannot exceed 200 characters'],\n      },\n      city: {\n        type: String,\n        required: [true, 'City is required'],\n        trim: true,\n        maxlength: [50, 'City cannot exceed 50 characters'],\n      },\n      state: {\n        type: String,\n        required: [true, 'State is required'],\n        trim: true,\n        maxlength: [50, 'State cannot exceed 50 characters'],\n      },\n      pincode: {\n        type: String,\n        required: [true, 'Pincode is required'],\n        trim: true,\n        match: [/^[1-9][0-9]{5}$/, 'Please enter a valid pincode'],\n      },\n      country: {\n        type: String,\n        trim: true,\n        default: 'India',\n        maxlength: [50, 'Country cannot exceed 50 characters'],\n      },\n    },\n    orderStatus: {\n      type: String,\n      required: [true, 'Order status is required'],\n      enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'],\n      default: 'pending',\n    },\n    paymentStatus: {\n      type: String,\n      required: [true, 'Payment status is required'],\n      enum: ['pending', 'completed', 'failed', 'refunded'],\n      default: 'pending',\n    },\n    paymentMethod: {\n      type: String,\n      required: [true, 'Payment method is required'],\n      enum: ['cod', 'online', 'bank_transfer'],\n      default: 'cod',\n    },\n    trackingNumber: {\n      type: String,\n      trim: true,\n      sparse: true,\n    },\n    notes: {\n      type: String,\n      trim: true,\n      maxlength: [500, 'Notes cannot exceed 500 characters'],\n    },\n  },\n  {\n    timestamps: true,\n    toJSON: { virtuals: true },\n    toObject: { virtuals: true },\n  }\n);\n\n// Indexes for better query performance\nProductOrderSchema.index({ customerEmail: 1 });\nProductOrderSchema.index({ orderStatus: 1 });\nProductOrderSchema.index({ paymentStatus: 1 });\nProductOrderSchema.index({ createdAt: -1 });\nProductOrderSchema.index({ trackingNumber: 1 }, { sparse: true });\n\n// Virtual for order number\nProductOrderSchema.virtual('orderNumber').get(function () {\n  const year = this.createdAt.getFullYear();\n  const month = String(this.createdAt.getMonth() + 1).padStart(2, '0');\n  const id = this._id.toString().slice(-6).toUpperCase();\n  return `ORD${year}${month}${id}`;\n});\n\n// Virtual for formatted total amount\nProductOrderSchema.virtual('formattedTotalAmount').get(function () {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n  }).format(this.totalAmount);\n});\n\n// Virtual for formatted order date\nProductOrderSchema.virtual('formattedOrderDate').get(function () {\n  return this.createdAt.toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n});\n\n// Virtual for full shipping address\nProductOrderSchema.virtual('fullShippingAddress').get(function () {\n  const addr = this.shippingAddress;\n  return `${addr.street}, ${addr.city}, ${addr.state} - ${addr.pincode}, ${addr.country}`;\n});\n\n// Virtual for order status display name\nProductOrderSchema.virtual('orderStatusDisplayName').get(function () {\n  const statusMap = {\n    pending: 'Pending',\n    confirmed: 'Confirmed',\n    processing: 'Processing',\n    shipped: 'Shipped',\n    delivered: 'Delivered',\n    cancelled: 'Cancelled',\n  };\n  return statusMap[this.orderStatus] || this.orderStatus;\n});\n\n// Virtual for payment status display name\nProductOrderSchema.virtual('paymentStatusDisplayName').get(function () {\n  const statusMap = {\n    pending: 'Pending',\n    completed: 'Completed',\n    failed: 'Failed',\n    refunded: 'Refunded',\n  };\n  return statusMap[this.paymentStatus] || this.paymentStatus;\n});\n\n// Pre-save middleware\nProductOrderSchema.pre('save', function (next) {\n  // Calculate total amount from products\n  if (this.products && this.products.length > 0) {\n    this.totalAmount = this.products.reduce((total, product) => {\n      return total + (product.price * product.quantity);\n    }, 0);\n  }\n  \n  // Generate tracking number when order is shipped\n  if (this.orderStatus === 'shipped' && !this.trackingNumber) {\n    const random = Math.random().toString(36).substr(2, 10).toUpperCase();\n    this.trackingNumber = `TRK${random}`;\n  }\n  \n  // Sanitize text fields\n  this.customerName = this.customerName.replace(/<[^>]*>?/gm, '');\n  if (this.notes) {\n    this.notes = this.notes.replace(/<[^>]*>?/gm, '');\n  }\n  \n  next();\n});\n\n// Static method to get order statistics\nProductOrderSchema.statics.getStats = function () {\n  return this.aggregate([\n    {\n      $group: {\n        _id: '$orderStatus',\n        count: { $sum: 1 },\n        totalAmount: { $sum: '$totalAmount' },\n      },\n    },\n  ]);\n};\n\n// Static method to get recent orders\nProductOrderSchema.statics.getRecent = function (limit: number = 10) {\n  return this.find()\n    .sort({ createdAt: -1 })\n    .limit(limit)\n    .select('customerName totalAmount orderStatus paymentStatus createdAt');\n};\n\n// Static method to get orders by customer\nProductOrderSchema.statics.getByCustomer = function (email: string) {\n  return this.find({ customerEmail: email })\n    .sort({ createdAt: -1 })\n    .select('-customerEmail');\n};\n\n// Static method to update order status\nProductOrderSchema.statics.updateStatus = function (orderId: string, status: string) {\n  return this.findByIdAndUpdate(\n    orderId,\n    { orderStatus: status },\n    { new: true }\n  );\n};\n\nconst ProductOrder = mongoose.models.ProductOrder || mongoose.model<IProductOrder>('ProductOrder', ProductOrderSchema);\n\nexport default ProductOrder;\n", "import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport ProductOrder from '@/lib/models/ProductOrder';\nimport { productOrderSchema } from '@/utils/validation';\nimport { formatSuccessResponse, formatErrorResponse, createRateLimiter } from '@/utils/validation';\n\n// Rate limiter: 5 requests per 15 minutes per IP\nconst rateLimiter = createRateLimiter(15 * 60 * 1000, 5);\n\n// Sample products data (in a real app, this would come from a database)\nconst PRODUCTS = [\n  {\n    id: 'ayur-001',\n    name: 'Ayurvedic Immunity Booster',\n    price: 299,\n    description: 'Natural immunity booster with herbs',\n    image: '/products/immunity-booster.jpg',\n    inStock: true,\n  },\n  {\n    id: 'ayur-002',\n    name: 'Herbal Digestive Tea',\n    price: 199,\n    description: 'Digestive tea blend with traditional herbs',\n    image: '/products/digestive-tea.jpg',\n    inStock: true,\n  },\n  {\n    id: 'ayur-003',\n    name: 'Natural Pain Relief Oil',\n    price: 249,\n    description: 'Ayurvedic oil for joint and muscle pain',\n    image: '/products/pain-relief-oil.jpg',\n    inStock: true,\n  },\n  {\n    id: 'ayur-004',\n    name: 'Stress Relief Capsules',\n    price: 399,\n    description: 'Natural stress relief with ashwagandha',\n    image: '/products/stress-relief.jpg',\n    inStock: true,\n  },\n];\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Rate limiting\n    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';\n    if (!rateLimiter(clientIP)) {\n      return NextResponse.json(\n        formatErrorResponse('Too many requests. Please try again later.'),\n        { status: 429 }\n      );\n    }\n\n    // Parse request body\n    const body = await request.json();\n\n    // Validate input data\n    const validationResult = productOrderSchema.safeParse(body);\n    if (!validationResult.success) {\n      return NextResponse.json(\n        formatErrorResponse('Validation failed', validationResult.error),\n        { status: 400 }\n      );\n    }\n\n    const orderData = validationResult.data;\n\n    // Connect to database\n    await connectDB();\n\n    // Validate products and calculate total\n    const orderProducts = [];\n    let totalAmount = 0;\n\n    for (const item of orderData.products) {\n      const product = PRODUCTS.find(p => p.id === item.productId);\n      if (!product) {\n        return NextResponse.json(\n          formatErrorResponse(`Product with ID ${item.productId} not found`),\n          { status: 400 }\n        );\n      }\n\n      if (!product.inStock) {\n        return NextResponse.json(\n          formatErrorResponse(`Product ${product.name} is currently out of stock`),\n          { status: 400 }\n        );\n      }\n\n      const productTotal = product.price * item.quantity;\n      totalAmount += productTotal;\n\n      orderProducts.push({\n        productId: product.id,\n        productName: product.name,\n        quantity: item.quantity,\n        price: product.price,\n      });\n    }\n\n    // Create new order\n    const order = new ProductOrder({\n      customerName: orderData.customerName,\n      customerEmail: orderData.customerEmail,\n      customerPhone: orderData.customerPhone,\n      products: orderProducts,\n      totalAmount,\n      shippingAddress: orderData.shippingAddress,\n      paymentMethod: orderData.paymentMethod,\n      notes: orderData.notes,\n      orderStatus: 'pending',\n      paymentStatus: orderData.paymentMethod === 'cod' ? 'pending' : 'pending',\n    });\n\n    await order.save();\n\n    // TODO: Send order confirmation email\n    // TODO: Integrate with payment gateway for online payments\n    // TODO: Update inventory\n    // TODO: Send notification to admin\n\n    return NextResponse.json(\n      formatSuccessResponse(\n        {\n          orderId: order._id,\n          orderNumber: order.orderNumber,\n          totalAmount: order.totalAmount,\n          paymentMethod: order.paymentMethod,\n        },\n        'Order placed successfully! You will receive a confirmation email shortly.'\n      ),\n      { status: 201 }\n    );\n\n  } catch (error) {\n    console.error('Product order creation error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while processing your order. Please try again later.'),\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const status = searchParams.get('status');\n    const customerEmail = searchParams.get('customerEmail');\n\n    await connectDB();\n\n    // Build query\n    const query: any = {};\n    if (status) query.orderStatus = status;\n    if (customerEmail) query.customerEmail = customerEmail;\n\n    // Get total count\n    const total = await ProductOrder.countDocuments(query);\n\n    // Get paginated results\n    const orders = await ProductOrder.find(query)\n      .sort({ createdAt: -1 })\n      .skip((page - 1) * limit)\n      .limit(limit)\n      .select('customerName customerEmail totalAmount orderStatus paymentStatus createdAt products');\n\n    // Get statistics\n    const stats = await ProductOrder.aggregate([\n      {\n        $group: {\n          _id: '$orderStatus',\n          count: { $sum: 1 },\n          totalAmount: { $sum: '$totalAmount' },\n        },\n      },\n    ]);\n\n    return NextResponse.json(\n      formatSuccessResponse({\n        orders,\n        stats,\n        pagination: {\n          page,\n          limit,\n          total,\n          pages: Math.ceil(total / limit),\n        },\n      }),\n      { status: 200 }\n    );\n\n  } catch (error) {\n    console.error('Orders fetch error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while fetching orders.'),\n      { status: 500 }\n    );\n  }\n}\n\nexport async function PATCH(request: NextRequest) {\n  try {\n    // This endpoint is for admin updates\n    const body = await request.json();\n    const { id, orderStatus, paymentStatus, trackingNumber } = body;\n\n    if (!id) {\n      return NextResponse.json(\n        formatErrorResponse('Order ID is required'),\n        { status: 400 }\n      );\n    }\n\n    await connectDB();\n\n    const updateData: any = {};\n    if (orderStatus) updateData.orderStatus = orderStatus;\n    if (paymentStatus) updateData.paymentStatus = paymentStatus;\n    if (trackingNumber) updateData.trackingNumber = trackingNumber;\n\n    const updatedOrder = await ProductOrder.findByIdAndUpdate(\n      id,\n      updateData,\n      { new: true }\n    );\n\n    if (!updatedOrder) {\n      return NextResponse.json(\n        formatErrorResponse('Order not found'),\n        { status: 404 }\n      );\n    }\n\n    // TODO: Send status update email to customer\n    // TODO: Send tracking information if order is shipped\n\n    return NextResponse.json(\n      formatSuccessResponse(updatedOrder, 'Order updated successfully'),\n      { status: 200 }\n    );\n\n  } catch (error) {\n    console.error('Order update error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while updating the order.'),\n      { status: 500 }\n    );\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/products/orders/route\",\n        pathname: \"/api/products/orders\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/products/orders/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/products/orders/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "u6CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAc,QAAQ,GAAG,CAAC,WAAW,CAE3C,GAAI,CAAC,EACH,MAAM,AAAI,KADM,CAEd,kEASJ,IAAI,EAAS,EAAA,CAAA,CAAO,GAAP,KAAe,AAExB,CAAC,GACH,GAAS,EADE,AACF,CAAA,CAAO,EAAP,MAAe,CAAG,CAAE,KAAM,KAAM,QAAS,KAAK,QAGzD,eAAe,EACb,GAAI,EAAO,IAAI,CACb,CADe,GAwBJ,GAvBJ,EAAO,IAAI,CAGf,EAAO,OAAO,EAAE,CAKnB,EAAO,OAAO,CAAG,EAAA,OAAQ,CAAC,OAAO,CAAC,EAJrB,CACX,UAG8C,MAH9B,CAClB,GAEsD,IAAI,CAAC,AAAC,GACnD,EACT,EAGF,GAAI,CACF,EAAO,IAAI,CAAG,MAAM,EAAO,OAAO,AACpC,CAAE,MAAO,EAAG,CAEV,MADA,EAAO,OAAO,CAAG,KACX,CACR,CAEA,OAAO,EAAO,IAAI,AACpB,iDC5CA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAqB,IAAI,EAAA,MAAM,CACnC,CACE,aAAc,CACZ,KAAM,OACN,SAAU,EAAC,EAAM,4BAA4B,CAC7C,MAAM,EACN,UAAW,CAAC,IAAK,oCAAoC,AACvD,EACA,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,MAAM,EACN,WAAW,EACX,MAAO,CACL,8CACA,qCACD,AACH,EACA,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,2BAA2B,CAC5C,MAAM,EACN,MAAO,CACL,yBACA,oCACD,AACH,EACA,SAAU,CACR,CACE,UAAW,CACT,KAAM,OACN,SAAU,EAAC,EAAM,yBAAyB,AAC5C,EACA,YAAa,CACX,KAAM,OACN,SAAU,EAAC,EAAM,2BAA2B,CAC5C,MAAM,CACR,EACA,SAAU,CACR,KAAM,OACN,SAAU,EAAC,EAAM,uBAAuB,CACxC,IAAK,CAAC,EAAG,8BAA8B,CACvC,IAAK,CAAC,IAAK,6BAA6B,AAC1C,EACA,MAAO,CACL,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,IAAK,CAAC,EAAG,2BAA2B,AACtC,CACF,EACD,CACD,YAAa,CACX,KAAM,OACN,SAAU,EAAC,EAAM,2BAA2B,CAC5C,IAAK,CAAC,EAAG,kCAAkC,AAC7C,EACA,gBAAiB,CACf,OAAQ,CACN,KAAM,OACN,SAAU,CAAC,GAAM,6BAA6B,CAC9C,MAAM,EACN,UAAW,CAAC,IAAK,8CAA8C,AACjE,EACA,KAAM,CACJ,KAAM,OACN,SAAU,EAAC,EAAM,mBAAmB,CACpC,MAAM,EACN,UAAW,CAAC,GAAI,mCAAmC,AACrD,EACA,MAAO,CACL,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,MAAM,EACN,UAAW,CAAC,GAAI,oCAAoC,AACtD,EACA,QAAS,CACP,KAAM,OACN,SAAU,EAAC,EAAM,sBAAsB,CACvC,MAAM,EACN,MAAO,CAAC,kBAAmB,+BAA+B,AAC5D,EACA,QAAS,CACP,KAAM,OACN,MAAM,EACN,QAAS,QACT,UAAW,CAAC,GAAI,sCAAsC,AACxD,CACF,EACA,YAAa,CACX,KAAM,OACN,SAAU,EAAC,EAAM,2BAA2B,CAC5C,KAAM,CAAC,UAAW,YAAa,aAAc,UAAW,YAAa,YAAY,CACjF,QAAS,SACX,EACA,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,6BAA6B,CAC9C,KAAM,CAAC,UAAW,YAAa,SAAU,WAAW,CACpD,QAAS,SACX,EACA,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,6BAA6B,CAC9C,KAAM,CAAC,MAAO,SAAU,gBAAgB,CACxC,QAAS,KACX,EACA,eAAgB,CACd,KAAM,OACN,MAAM,EACN,QAAQ,CACV,EACA,MAAO,CACL,KAAM,OACN,KAAM,GACN,UAAW,CAAC,IAAK,qCAAqC,AACxD,CACF,EACA,CACE,YAAY,EACZ,OAAQ,CAAE,UAAU,CAAK,EACzB,SAAU,CAAE,UAAU,CAAK,CAC7B,GAIF,EAAmB,KAAK,CAAC,CAAE,cAAe,CAAE,GAC5C,EAAmB,KAAK,CAAC,CAAE,YAAa,CAAE,GAC1C,EAAmB,KAAK,CAAC,CAAE,cAAe,CAAE,GAC5C,EAAmB,KAAK,CAAC,CAAE,UAAW,CAAC,CAAE,GACzC,EAAmB,KAAK,CAAC,CAAE,eAAgB,CAAE,EAAG,CAAE,QAAQ,CAAK,GAG/D,EAAmB,OAAO,CAAC,eAAe,GAAG,CAAC,WAC5C,IAAM,EAAO,IAAI,CAAC,SAAS,CAAC,WAAW,GACjC,EAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAK,GAAG,QAAQ,CAAC,EAAG,KAC1D,EAAK,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,GAAG,WAAW,GACpD,MAAO,CAAC,GAAG,EAAE,EAAA,EAAO,EAAA,EAAQ,EAAA,CAAI,AAClC,GAGA,EAAmB,OAAO,CAAC,wBAAwB,GAAG,CAAC,WACrD,OAAO,IAAI,KAAK,YAAY,CAAC,QAAS,CACpC,MAAO,WACP,SAAU,KACZ,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAC5B,GAGA,EAAmB,OAAO,CAAC,sBAAsB,GAAG,CAAC,WACnD,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAS,CAChD,KAAM,UACN,MAAO,OACP,IAAK,SACP,EACF,GAGA,EAAmB,OAAO,CAAC,uBAAuB,GAAG,CAAC,WACpD,IAAM,EAAO,IAAI,CAAC,eAAe,CACjC,MAAO,CAAA,EAAG,EAAK,MAAM,CAAC,EAAE,EAAE,EAAK,IAAI,CAAC,EAAE,EAAE,EAAK,KAAK,CAAC,GAAG,EAAE,EAAK,OAAO,CAAC,EAAE,EAAE,EAAK,OAAO,CAAA,CAAE,AACzF,GAGA,EAAmB,OAAO,CAAC,0BAA0B,GAAG,CAAC,WASvD,MAAO,CARW,CAChB,QAAS,UACT,UAAW,YACX,WAAY,aACZ,QAAS,UACT,UAAW,YACX,UAAW,YACb,CACgB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAI,IAAI,CAAC,WAC7C,AADwD,GAIxD,EAAmB,OAAO,CAAC,4BAA4B,GAAG,CAAC,WAOzD,MAAO,CANW,CAChB,QAAS,UACT,UAAW,YACX,OAAQ,SACR,SAAU,UACZ,EACgB,CAAC,IAAI,CAAC,aAAa,CAAC,EAAI,IAAI,CAAC,aAAa,AAC5D,GAGA,EAAmB,GAAG,CAAC,OAAQ,SAAU,CAAI,EAS3C,GAPI,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAG,GAAG,CAC7C,IAAI,CAAC,WAAW,CAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAO,IACvC,EAAS,EAAQ,KAAK,CAAG,EAAQ,QAAQ,CAC/C,EAAA,EAIoB,YAArB,IAAI,CAAC,WAAW,EAAkB,CAAC,IAAI,CAAC,cAAc,CAAE,CAC1D,IAAM,EAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,EAAG,IAAI,WAAW,GACnE,IAAI,CAAC,cAAc,CAAG,CAAC,GAAG,EAAE,EAAA,CAAQ,AACtC,CAGA,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,aAAc,IACxD,IAAI,CAAC,KAAK,EAAE,CACd,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAc,GAAA,EAGhD,GACF,GAGA,EAAmB,OAAO,CAAC,QAAQ,CAAG,WACpC,OAAO,IAAI,CAAC,SAAS,CAAC,CACpB,CACE,OAAQ,CACN,IAAK,eACL,MAAO,CAAE,KAAM,CAAE,EACjB,YAAa,CAAE,KAAM,cAAe,CACtC,CACF,EACD,CACH,EAGA,EAAmB,OAAO,CAAC,SAAS,CAAG,SAAU,EAAgB,EAAE,EACjE,OAAO,IAAI,CAAC,IAAI,GACb,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,KAAK,CAAC,GACN,MAAM,CAAC,+DACZ,EAGA,EAAmB,OAAO,CAAC,aAAa,CAAG,SAAU,CAAa,EAChE,OAAO,IAAI,CAAC,IAAI,CAAC,CAAE,cAAe,CAAM,GACrC,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,MAAM,CAAC,iBACZ,EAGA,EAAmB,OAAO,CAAC,YAAY,CAAG,SAAU,CAAe,CAAE,CAAc,EACjF,OAAO,IAAI,CAAC,iBAAiB,CAC3B,EACA,CAAE,YAAa,CAAO,EACtB,CAAE,KAAK,CAAK,EAEhB,QAEqB,EAAA,OAAQ,CAAC,MAAM,CAAC,YAAY,EAAI,AAEtC,EAFsC,OAAQ,CAAC,KAAK,CAAgB,eAAgB,yLE1PnG,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,yDDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,CAAA,EAAA,EAAA,iBAAA,AAAiB,EAAC,IAAgB,CAAX,EAGrC,EAAW,CAH+B,AAI9C,CACE,GAAI,WACJ,KAAM,6BACN,MAAO,IACP,YAAa,sCACb,MAAO,iCACP,SAAS,CACX,EACA,CACE,GAAI,WACJ,KAAM,uBACN,MAAO,IACP,YAAa,6CACb,MAAO,8BACP,SAAS,CACX,EACA,CACE,GAAI,WACJ,KAAM,0BACN,MAAO,IACP,YAAa,0CACb,MAAO,gCACP,SAAS,CACX,EACA,CACE,GAAI,WACJ,KAAM,yBACN,MAAO,IACP,YAAa,yCACb,MAAO,8BACP,SAAS,CACX,EACD,CAEM,eAAe,EAAK,CAAoB,EAC7C,GAAI,CAEF,IAAM,EAAW,EAAQ,EAAE,EAAI,EAAQ,OAAO,CAAC,GAAG,CAAC,oBAAsB,UACzE,GAAI,CAAC,EAAY,GACf,OAAO,CADmB,CACnB,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,8CACpB,CAAE,OAAQ,GAAI,GAKlB,IAAM,EAAO,MAAM,EAAQ,IAAI,GAGzB,EAAmB,EAAA,kBAAkB,CAAC,SAAS,CAAC,GACtD,GAAI,CAAC,EAAiB,OAAO,CAC3B,CAD6B,MACtB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,oBAAqB,EAAiB,KAAK,EAC/D,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAY,EAAiB,IAAI,AAGvC,OAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAGf,IAAM,EAAgB,EAAE,CACpB,EAAc,EAElB,IAAK,IAAM,KAAQ,EAAU,QAAQ,CAAE,CACrC,IAAM,EAAU,EAAS,IAAI,CAAC,GAAK,EAAE,EAAE,GAAK,EAAK,SAAS,EAC1D,GAAI,CAAC,EACH,OADY,AACL,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,CAAC,gBAAgB,EAAE,EAAK,SAAS,CAAC,UAAU,CAAC,EACjE,CAAE,OAAQ,GAAI,GAIlB,GAAI,CAAC,EAAQ,OAAO,CAClB,CADoB,MACb,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,CAAC,QAAQ,EAAE,EAAQ,IAAI,CAAC,0BAA0B,CAAC,EACvE,CAAE,OAAQ,GAAI,GAIlB,IAAM,EAAe,EAAQ,KAAK,CAAG,EAAK,QAAQ,CAClD,GAAe,EAEf,EAAc,IAAI,CAAC,CACjB,UAAW,EAAQ,EAAE,CACrB,YAAa,EAAQ,IAAI,CACzB,SAAU,EAAK,QAAQ,CACvB,MAAO,EAAQ,KAAK,AACtB,EACF,CAGA,IAAM,EAAQ,IAAI,EAAA,OAAY,CAAC,CAC7B,aAAc,EAAU,YAAY,CACpC,cAAe,EAAU,aAAa,CACtC,cAAe,EAAU,aAAa,CACtC,SAAU,cACV,EACA,gBAAiB,EAAU,eAAe,CAC1C,cAAe,EAAU,aAAa,CACtC,MAAO,EAAU,KAAK,CACtB,YAAa,UACb,aAAA,EAAe,EAAU,aAAa,CAAa,IAAR,MAC7C,EADiE,CAUjE,OAPA,MAAM,EAAM,IAAI,GAOT,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EACnB,CACE,QAAS,EAAM,GAAG,CAClB,YAAa,EAAM,WAAW,CAC9B,YAAa,EAAM,WAAW,CAC9B,cAAe,EAAM,aAAa,AACpC,EACA,6EAEF,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,gCAAiC,GACxC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAmB,AAAnB,EAAoB,0EACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EACtC,EAAO,SAAS,EAAa,GAAG,CAAC,SAAW,KAC5C,EAAQ,SAAS,EAAa,GAAG,CAAC,UAAY,MAC9C,EAAS,EAAa,GAAG,CAAC,UAC1B,EAAgB,EAAa,GAAG,CAAC,gBAEvC,OAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAGf,IAAM,EAAa,CAAC,EAChB,GAAQ,GAAM,WAAW,CAAG,CAAA,EAC5B,IAAe,EAAM,aAAa,CAAG,CAAA,EAGzC,IAAM,EAAQ,MAAM,EAAA,OAAY,CAAC,cAAc,CAAC,GAG1C,EAAS,MAAM,EAAA,OAAY,CAAC,IAAI,CAAC,GACpC,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,IAAI,CAAC,AAAC,IAAO,CAAC,CAAI,GAClB,KAAK,CAAC,GACN,MAAM,CAAC,uFAGJ,EAAQ,MAAM,EAAA,OAAY,CAAC,SAAS,CAAC,CACzC,CACE,OAAQ,CACN,IAAK,eACL,MAAO,CAAE,KAAM,CAAE,EACjB,YAAa,CAAE,KAAM,cAAe,CACtC,CACF,EACD,EAED,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,QACpB,EACA,QACA,WAAY,MACV,EACA,cACA,EACA,MAAO,KAAK,IAAI,CAAC,EAAQ,EAC3B,CACF,GACA,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,sBAAuB,GAC9B,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,4CACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAM,CAAoB,EAC9C,GAAI,CAGF,GAAM,IAAE,CAAE,aAAE,CAAW,eAAE,CAAa,gBAAE,CAAc,CAAE,CAD3C,EAC8C,IADxC,EAAQ,IAAI,GAG/B,GAAI,CAAC,EACH,EADO,KACA,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,wBACpB,CAAE,OAAQ,GAAI,EAIlB,OAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAEf,IAAM,EAAkB,CAAC,CACrB,IAAa,GAAW,WAAW,CAAG,CAAA,EACtC,IAAe,EAAW,aAAa,CAAG,CAAA,EAC1C,IAAgB,EAAW,cAAc,CAAG,CAAA,EAEhD,IAAM,EAAe,MAAM,EAAA,OAAY,CAAC,iBAAiB,CACvD,EACA,EACA,CAAE,IAAK,EAAK,GAGd,GAAI,CAAC,EACH,OAAO,EAAA,GADU,SACE,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,mBACpB,CAAE,OAAQ,GAAI,GAOlB,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAc,8BACpC,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,sBAAuB,GAC9B,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,+CACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CC9OA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,6BACN,SAAU,uBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,iDAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,6BAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,CAAE,qBAAmB,CAAE,sBAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAiB,AAAjB,EACnG,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,EAAgB,EAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,oBACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,MAAvD,GAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,iBAAkB,OAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAA+D,AAAlD,SAAO,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAuD,AAA9C,SAAO,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAO,AAAP,EAAS,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,kBAAmB,wBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,GAAK,GAAqB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,gBACrB,AADqC,EACjC,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZM,AAAF,AAAE,CAAA,AAAD,EAAC,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [3]}