import mongoose, { Schema } from 'mongoose';
import { IContactForm } from '@/types';

const ContactFormSchema = new Schema<IContactForm>(
  {
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
      maxlength: [100, 'Name cannot exceed 100 characters'],
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      trim: true,
      lowercase: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email address',
      ],
    },
    phone: {
      type: String,
      trim: true,
      match: [
        /^[\+]?[1-9][\d]{0,15}$/,
        'Please enter a valid phone number',
      ],
    },
    subject: {
      type: String,
      required: [true, 'Subject is required'],
      trim: true,
      maxlength: [200, 'Subject cannot exceed 200 characters'],
    },
    message: {
      type: String,
      required: [true, 'Message is required'],
      trim: true,
      maxlength: [2000, 'Message cannot exceed 2000 characters'],
    },
    isRead: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better query performance
ContactFormSchema.index({ email: 1 });
ContactFormSchema.index({ createdAt: -1 });
ContactFormSchema.index({ isRead: 1 });

// Virtual for formatted creation date
ContactFormSchema.virtual('formattedDate').get(function () {
  return this.createdAt.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
});

// Pre-save middleware to sanitize data
ContactFormSchema.pre('save', function (next) {
  // Remove any HTML tags from text fields for security
  this.name = this.name.replace(/<[^>]*>?/gm, '');
  this.subject = this.subject.replace(/<[^>]*>?/gm, '');
  this.message = this.message.replace(/<[^>]*>?/gm, '');
  
  next();
});

// Static method to get unread count
ContactFormSchema.statics.getUnreadCount = function () {
  return this.countDocuments({ isRead: false });
};

// Static method to mark as read
ContactFormSchema.statics.markAsRead = function (id: string) {
  return this.findByIdAndUpdate(id, { isRead: true }, { new: true });
};

// Static method to get recent contacts
ContactFormSchema.statics.getRecent = function (limit: number = 10) {
  return this.find()
    .sort({ createdAt: -1 })
    .limit(limit)
    .select('name email subject createdAt isRead');
};

const ContactForm = mongoose.models.ContactForm || mongoose.model<IContactForm>('ContactForm', ContactFormSchema);

export default ContactForm;
