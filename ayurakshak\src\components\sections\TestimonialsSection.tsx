'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import Card, { CardContent } from '@/components/ui/Card';

const TestimonialsSection: React.FC = () => {
  const [activeTestimonial, setActiveTestimonial] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      role: 'Beneficiary, Health Camp',
      location: 'Udaipur, Rajasthan',
      image: '/testimonials/priya-sharma.jpg',
      quote: 'Ayurakshak brought hope to our village when we needed it most. The free health camps and natural medicines helped my entire family recover from chronic ailments that we couldn\'t afford to treat.',
      rating: 5,
      program: 'Mobile Health Camps',
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      role: 'Women Entrepreneur',
      location: 'Varanasi, UP',
      image: '/testimonials/sunita-devi.jpg',
      quote: 'Through Ayurakshak\'s livelihood program, I learned to make herbal products and now run my own business. I can support my family with dignity and send my children to school.',
      rating: 5,
      program: 'Women Empowerment',
    },
    {
      id: 3,
      name: 'Dr. <PERSON><PERSON>',
      role: 'Community Health Worker',
      location: 'Jaipur, Rajasthan',
      image: '/testimonials/dr-rajesh.jpg',
      quote: 'Working with Ayurakshak has been incredibly fulfilling. Their approach to combining traditional Ayurveda with modern outreach is exactly what rural India needs.',
      rating: 5,
      program: 'Health Education',
    },
    {
      id: 4,
      name: 'Kamala Devi',
      role: 'Village Elder',
      location: 'Bikaner, Rajasthan',
      image: '/testimonials/kamala-devi.jpg',
      quote: 'In my 60 years, I have never seen an organization care for our community like Ayurakshak. They don\'t just provide medicine, they bring hope and healing to our hearts.',
      rating: 5,
      program: 'Community Development',
    },
    {
      id: 5,
      name: 'Ravi Patel',
      role: 'Volunteer',
      location: 'Ahmedabad, Gujarat',
      image: '/testimonials/ravi-patel.jpg',
      quote: 'Volunteering with Ayurakshak has been life-changing. Seeing the direct impact of our work on communities motivates me to do more for society.',
      rating: 5,
      program: 'Volunteer Program',
    },
  ];

  const nextTestimonial = () => {
    setActiveTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setActiveTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <section className="py-20 bg-gradient-to-br from-primary-50 to-sage-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            What People <span className="text-primary-600">Say</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Hear from the communities, volunteers, and partners who have experienced our impact firsthand
          </p>
        </motion.div>

        {/* Main Testimonial Display */}
        <div className="relative mb-16">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTestimonial}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              transition={{ duration: 0.5 }}
              className="grid lg:grid-cols-2 gap-12 items-center"
            >
              {/* Image */}
              <div className="relative">
                <div className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
                  <Image
                    src={testimonials[activeTestimonial].image}
                    alt={testimonials[activeTestimonial].name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />
                </div>
                
                {/* Program Badge */}
                <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-full px-4 py-2">
                  <span className="text-sm font-medium text-primary-700">
                    {testimonials[activeTestimonial].program}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="space-y-6">
                {/* Quote */}
                <div className="relative">
                  <div className="text-6xl text-primary-200 absolute -top-4 -left-2">"</div>
                  <blockquote className="text-xl lg:text-2xl text-gray-700 leading-relaxed pl-8">
                    {testimonials[activeTestimonial].quote}
                  </blockquote>
                </div>

                {/* Rating */}
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      className={`w-6 h-6 ${
                        i < testimonials[activeTestimonial].rating
                          ? 'text-yellow-400'
                          : 'text-gray-300'
                      }`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>

                {/* Author Info */}
                <div>
                  <h4 className="text-2xl font-bold text-gray-900">
                    {testimonials[activeTestimonial].name}
                  </h4>
                  <p className="text-lg text-primary-600 font-medium">
                    {testimonials[activeTestimonial].role}
                  </p>
                  <p className="text-gray-600">
                    📍 {testimonials[activeTestimonial].location}
                  </p>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm rounded-full p-3 shadow-lg hover:bg-white transition-all duration-200"
            aria-label="Previous testimonial"
          >
            <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm rounded-full p-3 shadow-lg hover:bg-white transition-all duration-200"
            aria-label="Next testimonial"
          >
            <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Testimonial Thumbnails */}
        <div className="flex justify-center space-x-4 mb-12">
          {testimonials.map((testimonial, index) => (
            <button
              key={testimonial.id}
              onClick={() => setActiveTestimonial(index)}
              className={`relative w-16 h-16 rounded-full overflow-hidden transition-all duration-300 ${
                index === activeTestimonial
                  ? 'ring-4 ring-primary-500 scale-110'
                  : 'ring-2 ring-gray-200 hover:ring-primary-300'
              }`}
            >
              <Image
                src={testimonial.image}
                alt={testimonial.name}
                fill
                className="object-cover"
                sizes="64px"
              />
            </button>
          ))}
        </div>

        {/* Additional Testimonials Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid md:grid-cols-3 gap-8"
        >
          {testimonials.slice(0, 3).map((testimonial, index) => (
            <motion.div
              key={`grid-${testimonial.id}`}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card variant="elevated" hover className="h-full">
                <CardContent className="p-6 text-center">
                  <div className="relative w-20 h-20 mx-auto mb-4 rounded-full overflow-hidden">
                    <Image
                      src={testimonial.image}
                      alt={testimonial.name}
                      fill
                      className="object-cover"
                      sizes="80px"
                    />
                  </div>
                  
                  <div className="flex justify-center mb-4">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className={`w-4 h-4 ${
                          i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    "{testimonial.quote.substring(0, 120)}..."
                  </p>
                  
                  <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                  <p className="text-sm text-primary-600">{testimonial.role}</p>
                  <p className="text-xs text-gray-500">{testimonial.location}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
