/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    { d: "M6 22V2.8a.8.8 0 0 1 1.17-.71l11.38 5.69a.8.8 0 0 1 0 1.44L6 15.5", key: "kfjsu0" }
  ]
];
const FlagTriangleRight = createLucideIcon("flag-triangle-right", __iconNode);

export { __iconNode, FlagTriangleRight as default };
//# sourceMappingURL=flag-triangle-right.js.map
