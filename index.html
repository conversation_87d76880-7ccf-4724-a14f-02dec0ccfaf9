<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Ayurakshak — Care · Restore · Protect</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Bootstrap 5 -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- Swiper (carousel) -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css"
    />

    <!-- AOS (scroll animations) -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet" />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Custom styles -->
    <link rel="stylesheet" href="style.css" />

    <!-- Fav icon (optional) -->
    <meta name="theme-color" content="#165c37" />
  </head>
  <body>
    <!-- NAV -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top nav-glass">
      <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center gap-2" href="#">
          <img
            src="AyurRakshakImage.jpeg"
            alt="Ayurakshak"
            class="logo-small"
          />
          <div class="brand-text">
            <div class="brand-name">AYURAKSHAK</div>
            <small class="text-muted tagline">Care · Restore · Protect</small>
          </div>
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#mainNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse justify-content-end" id="mainNav">
          <ul class="navbar-nav align-items-lg-center gap-2">
            <li class="nav-item">
              <a class="nav-link" href="#about">About</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#programs">Programs</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="products.html">Products</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#impact">Impact</a>
            </li>
            <li class="nav-item"><a class="nav-link" href="#team">Team</a></li>
            <li class="nav-item">
              <a class="btn btn-cta ms-2" href="index.html#donate">Donate Now</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- HERO (Swiper slider) -->
    <header class="hero-section">
      <div class="swiper mySwiper">
        <div class="swiper-wrapper">
          <div
            class="swiper-slide hero-slide"
            style="
              background-image: url('https://images.unsplash.com/photo-**********-1cf6624b9987?auto=format&fit=crop&w=1600&q=60');
            "
          >
            <div class="hero-overlay"></div>
            <div class="hero-inner container" data-aos="fade-up">
              <h1 class="gradient-text">
                Healing communities with
                <span class="text-accent">nature-led care</span>
              </h1>
              <p class="lead mb-4">
                Ayurakshak combines traditional Ayurveda with modern outreach —
                health camps, sustainable livelihoods, and green initiatives
                across India.
              </p>

              <!-- Hero Statistics -->
              <div
                class="hero-stats mb-4 stats-section"
                data-aos="fade-up"
                data-aos-delay="200"
              >
                <div class="row g-3">
                  <div class="col-4">
                    <div class="stat-item text-center">
                      <div class="counter" data-target="5000">0</div>
                      <small class="text-white-50">Lives Impacted</small>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="stat-item text-center">
                      <div class="counter" data-target="200">0</div>
                      <small class="text-white-50">Villages Reached</small>
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="stat-item text-center">
                      <div class="counter" data-target="50">0</div>
                      <small class="text-white-50">Health Camps</small>
                    </div>
                  </div>
                </div>
              </div>

              <div class="hero-actions" data-aos="fade-up" data-aos-delay="400">
                <a href="#programs" class="btn btn-cta me-3 glow-on-hover">
                  Our Programs
                </a>
                <a href="#donate" class="btn btn-outline-cta"> Donate Now </a>
              </div>
            </div>
          </div>

          <div
            class="swiper-slide hero-slide"
            style="
              background-image: url('https://images.unsplash.com/photo-1506126613408-eca07ce68773?auto=format&fit=crop&w=1600&q=60');
            "
          >
            <div class="hero-overlay"></div>
            <div class="hero-inner container" data-aos="fade-up">
              <h1>Natural Healing for All</h1>
              <p class="lead mb-4">
                Bringing ancient wisdom of Ayurveda and naturopathy to modern
                healthcare, making natural healing accessible to every
                community.
              </p>
              <a href="#impact" class="btn btn-cta me-2">Impact Stories</a>
              <a href="#contact" class="btn btn-outline-cta">Contact Us</a>
            </div>
          </div>

          <div
            class="swiper-slide hero-slide"
            style="
              background-image: url('https://images.unsplash.com/photo-1524504388940-b1c1722653e1?auto=format&fit=crop&w=1600&q=60');
            "
          >
            <div class="hero-overlay"></div>
            <div class="hero-inner container" data-aos="fade-up">
              <h1>Holistic Wellness Programs</h1>
              <p class="lead mb-4">
                Comprehensive naturopathy treatments, yoga therapy, and herbal
                medicine programs designed to heal mind, body, and spirit
                naturally.
              </p>
              <a href="#programs" class="btn btn-cta me-2">Volunteer</a>
              <a href="#donate" class="btn btn-outline-cta">Fund a Project</a>
            </div>
          </div>
        </div>
        <div class="swiper-pagination"></div>
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>
      </div>
    </header>

    <!-- ABOUT -->
    <section id="about" class="py-5">
      <div class="container">
        <div class="row align-items-center g-4">
          <div class="col-lg-6" data-aos="fade-right">
            <img
              src="https://images.unsplash.com/photo-1531746790731-6c087fecd65a?auto=format&fit=crop&w=1200&q=60"
              class="img-fluid rounded shadow-sm"
              alt="community care"
            />
          </div>
          <div class="col-lg-6" data-aos="fade-left">
            <h2>
              <i class="fas fa-heart text-success me-2"></i>About Ayurakshak
            </h2>
            <p class="lead">
              Ayurakshak is a registered naturopathy NGO in India dedicated to
              reviving ancient healing wisdom through accessible Ayurveda health
              camps, medicinal plant gardens, holistic education and natural
              disaster relief. We partner with local healers and communities to
              preserve traditional medicine knowledge and make natural
              healthcare accessible to all.
            </p>
            <div class="row mt-4">
              <div class="col-sm-6 mb-3">
                <div class="info-card p-3 rounded">
                  <h5>Mission</h5>
                  <p>
                    To empower communities with health, education and
                    sustainable livelihoods rooted in traditional knowledge.
                  </p>
                </div>
              </div>
              <div class="col-sm-6 mb-3">
                <div class="info-card p-3 rounded">
                  <h5>Vision</h5>
                  <p>
                    A healthier, greener India where traditional wisdom meets
                    measurable impact.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- TRUST & TRANSPARENCY SECTION -->
    <section class="py-5 section-pattern">
      <div class="container">
        <div class="row align-items-center mb-5">
          <div class="col-lg-8 mx-auto text-center">
            <h2 class="gradient-text" data-aos="fade-up">
              Trusted by Communities Across India
            </h2>
            <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
              Our commitment to transparency and measurable impact has earned
              the trust of thousands of beneficiaries and donors.
            </p>
          </div>
        </div>

        <!-- Trust Indicators -->
        <div class="row g-4 mb-5">
          <div class="col-md-3 col-6" data-aos="zoom-in" data-aos-delay="100">
            <div class="trust-badge text-center p-4">
              <div class="badge-icon mb-3">
                <i class="fas fa-certificate text-green fa-3x"></i>
              </div>
              <h6>Registered NGO</h6>
              <small class="text-muted">Government Certified</small>
            </div>
          </div>
          <div class="col-md-3 col-6" data-aos="zoom-in" data-aos-delay="200">
            <div class="trust-badge text-center p-4">
              <div class="badge-icon mb-3">
                <i class="fas fa-shield-alt text-green fa-3x"></i>
              </div>
              <h6>80G Certified</h6>
              <small class="text-muted">Tax Deductible</small>
            </div>
          </div>
          <div class="col-md-3 col-6" data-aos="zoom-in" data-aos-delay="300">
            <div class="trust-badge text-center p-4">
              <div class="badge-icon mb-3">
                <i class="fas fa-chart-line text-green fa-3x"></i>
              </div>
              <h6>100% Transparent</h6>
              <small class="text-muted">Annual Reports</small>
            </div>
          </div>
          <div class="col-md-3 col-6" data-aos="zoom-in" data-aos-delay="400">
            <div class="trust-badge text-center p-4">
              <div class="badge-icon mb-3">
                <i class="fas fa-users text-green fa-3x"></i>
              </div>
              <h6>5000+ Lives</h6>
              <small class="text-muted">Directly Impacted</small>
            </div>
          </div>
        </div>

        <!-- Testimonials -->
        <div class="row">
          <div class="col-lg-10 mx-auto">
            <h3 class="text-center mb-4" data-aos="fade-up">What People Say</h3>
            <div class="row g-4">
              <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
                <div class="testimonial-card p-4 rounded shadow-sm">
                  <div class="stars mb-3">
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                  </div>
                  <p class="mb-3">
                    "The health camp in our village was a blessing. My mother
                    received free Ayurvedic treatment that helped her diabetes
                    significantly."
                  </p>
                  <div class="testimonial-author">
                    <strong>Priya Sharma</strong>
                    <small class="text-muted d-block"
                      >Village Beneficiary, Bihar</small
                    >
                  </div>
                </div>
              </div>
              <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
                <div class="testimonial-card p-4 rounded shadow-sm">
                  <div class="stars mb-3">
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                  </div>
                  <p class="mb-3">
                    "Ayurakshak's transparency in fund utilization gives me
                    confidence. I can see exactly how my donations are making a
                    difference."
                  </p>
                  <div class="testimonial-author">
                    <strong>Dr. Rajesh Kumar</strong>
                    <small class="text-muted d-block"
                      >Regular Donor, Mumbai</small
                    >
                  </div>
                </div>
              </div>
              <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
                <div class="testimonial-card p-4 rounded shadow-sm">
                  <div class="stars mb-3">
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                    <i class="fas fa-star text-warning"></i>
                  </div>
                  <p class="mb-3">
                    "The herbal product training changed my life. I now earn
                    ₹8,000 monthly and support my family independently."
                  </p>
                  <div class="testimonial-author">
                    <strong>Sunita Devi</strong>
                    <small class="text-muted d-block"
                      >Women Entrepreneur, Rajasthan</small
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- PROGRAMS (swiper slider with navigation) -->
    <section id="programs" class="py-5 bg-soft">
      <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h2 class="mb-0">Our Programs</h2>
          <small class="text-muted">Use arrows to navigate</small>
        </div>

        <div class="programs-container" data-aos="fade-up">
          <div class="swiper program-swiper">
            <div class="swiper-wrapper">
              <div class="swiper-slide">
                <div class="program-card" data-program="health">
                  <div class="card-image-wrapper">
                    <img
                      src="https://images.unsplash.com/photo-**********-0b93528c311a?auto=format&fit=crop&w=900&q=60"
                      alt="Health Camps"
                    />
                    <div class="card-overlay">
                      <div class="card-stats">
                        <span class="stat-badge">
                          <i class="fas fa-users"></i> 2,500+ Beneficiaries
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="card-header-section">
                      <div class="program-icon">
                        <i class="fas fa-leaf"></i>
                      </div>
                      <h5>Ayurveda Health Camps</h5>
                    </div>
                    <p>
                      Free Ayurvedic consultations, herbal medicines, pulse
                      diagnosis, and traditional healing workshops in remote
                      villages across India.
                    </p>
                    <div class="card-progress mb-3">
                      <div class="progress-info">
                        <small>Monthly Target</small>
                        <small>15/20 Camps</small>
                      </div>
                      <div class="progress" style="height: 6px">
                        <div
                          class="progress-bar bg-success"
                          style="width: 75%"
                        ></div>
                      </div>
                    </div>
                    <div class="card-actions">
                      <button
                        class="btn btn-sm btn-outline-success"
                        onclick="learnMore('health')"
                      >
                        Learn More
                      </button>
                      <button
                        class="btn btn-sm btn-success"
                        onclick="donateToProgram('health')"
                      >
                        <i class="fas fa-heart"></i> Support
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="swiper-slide">
                <div class="program-card" data-program="gardens">
                  <div class="card-image-wrapper">
                    <img
                      src="https://images.unsplash.com/photo-**********-92c53300491e?auto=format&fit=crop&w=900&q=60"
                      alt="Community Gardens"
                    />
                    <div class="card-overlay">
                      <div class="card-stats">
                        <span class="stat-badge">
                          <i class="fas fa-seedling"></i> 150+ Gardens
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="card-header-section">
                      <div class="program-icon">
                        <i class="fas fa-seedling"></i>
                      </div>
                      <h5>Medicinal Plant Gardens</h5>
                    </div>
                    <p>
                      Establishing medicinal herb gardens and teaching
                      communities to grow, harvest, and prepare traditional
                      Ayurvedic medicines.
                    </p>
                    <div class="card-progress mb-3">
                      <div class="progress-info">
                        <small>Annual Goal</small>
                        <small>150/200 Gardens</small>
                      </div>
                      <div class="progress" style="height: 6px">
                        <div
                          class="progress-bar bg-success"
                          style="width: 75%"
                        ></div>
                      </div>
                    </div>
                    <div class="card-actions">
                      <button
                        class="btn btn-sm btn-outline-success"
                        onclick="learnMore('gardens')"
                      >
                        Learn More
                      </button>
                      <button
                        class="btn btn-sm btn-success"
                        onclick="donateToProgram('gardens')"
                      >
                        <i class="fas fa-heart"></i> Support
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="swiper-slide">
                <div class="program-card" data-program="education">
                  <div class="card-image-wrapper">
                    <img
                      src="https://images.unsplash.com/photo-1543269865-cbf427effbad?auto=format&fit=crop&w=900&q=60"
                      alt="Education & Skills"
                    />
                    <div class="card-overlay">
                      <div class="card-stats">
                        <span class="stat-badge">
                          <i class="fas fa-graduation-cap"></i> 800+ Students
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="card-header-section">
                      <div class="program-icon">
                        <i class="fas fa-om"></i>
                      </div>
                      <h5>Holistic Education & Training</h5>
                    </div>
                    <p>
                      Training programs in Ayurveda, yoga therapy, meditation,
                      and traditional healing practices for community health
                      workers.
                    </p>
                    <div class="card-progress mb-3">
                      <div class="progress-info">
                        <small>This Year</small>
                        <small>800/1000 Students</small>
                      </div>
                      <div class="progress" style="height: 6px">
                        <div
                          class="progress-bar bg-success"
                          style="width: 80%"
                        ></div>
                      </div>
                    </div>
                    <div class="card-actions">
                      <button
                        class="btn btn-sm btn-outline-success"
                        onclick="learnMore('education')"
                      >
                        Learn More
                      </button>
                      <button
                        class="btn btn-sm btn-success"
                        onclick="donateToProgram('education')"
                      >
                        <i class="fas fa-heart"></i> Support
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="swiper-slide">
                <div class="program-card" data-program="relief">
                  <div class="card-image-wrapper">
                    <img
                      src="https://images.unsplash.com/photo-1505751172876-fa1923c5c528?auto=format&fit=crop&w=900&q=60"
                      alt="Disaster Relief"
                    />
                    <div class="card-overlay">
                      <div class="card-stats">
                        <span class="stat-badge">
                          <i class="fas fa-hands-helping"></i> 5,000+ Families
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="card-header-section">
                      <div class="program-icon">
                        <i class="fas fa-shield-alt"></i>
                      </div>
                      <h5>Disaster Relief</h5>
                    </div>
                    <p>
                      Rapid distribution of food, water, hygiene kits, and
                      medicines during emergencies and natural disasters.
                    </p>
                    <div class="card-progress mb-3">
                      <div class="progress-info">
                        <small>Emergency Fund</small>
                        <small>₹2.5L / ₹5L</small>
                      </div>
                      <div class="progress" style="height: 6px">
                        <div
                          class="progress-bar bg-warning"
                          style="width: 50%"
                        ></div>
                      </div>
                    </div>
                    <div class="card-actions">
                      <button
                        class="btn btn-sm btn-outline-success"
                        onclick="learnMore('relief')"
                      >
                        Learn More
                      </button>
                      <button
                        class="btn btn-sm btn-success"
                        onclick="donateToProgram('relief')"
                      >
                        <i class="fas fa-heart"></i> Support
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="swiper-slide">
                <div class="program-card" data-program="livelihoods">
                  <div class="card-image-wrapper">
                    <img
                      src="https://images.unsplash.com/photo-1521335629791-ce4aec67dd47?auto=format&fit=crop&w=900&q=60"
                      alt="Women Livelihoods"
                    />
                    <div class="card-overlay">
                      <div class="card-stats">
                        <span class="stat-badge">
                          <i class="fas fa-female"></i> 300+ Women
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="card-header-section">
                      <div class="program-icon">
                        <i class="fas fa-business-time"></i>
                      </div>
                      <h5>Women Livelihoods</h5>
                    </div>
                    <p>
                      Micro-grants and training for women entrepreneurs
                      producing herbal products and sustainable crafts.
                    </p>
                    <div class="card-progress mb-3">
                      <div class="progress-info">
                        <small>Entrepreneurs</small>
                        <small>300/400 Women</small>
                      </div>
                      <div class="progress" style="height: 6px">
                        <div
                          class="progress-bar bg-success"
                          style="width: 75%"
                        ></div>
                      </div>
                    </div>
                    <div class="card-actions">
                      <button
                        class="btn btn-sm btn-outline-success"
                        onclick="learnMore('livelihoods')"
                      >
                        Learn More
                      </button>
                      <button
                        class="btn btn-sm btn-success"
                        onclick="donateToProgram('livelihoods')"
                      >
                        <i class="fas fa-heart"></i> Support
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Navigation buttons -->
          <div class="program-nav-btn prev" id="programPrev">
            <i class="fas fa-chevron-left"></i>
          </div>
          <div class="program-nav-btn next" id="programNext">
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
      </div>
    </section>

    <!-- IMPACT (cards with AOS) -->
    <section id="impact" class="py-5">
      <div class="container">
        <h2 data-aos="fade-up">Impact Stories</h2>
        <div class="row mt-4 g-4">
          <div class="col-md-4" data-aos="fade-up" data-aos-delay="50">
            <article class="impact-card p-4 rounded shadow-sm">
              <h5>Flood Relief — Bihar</h5>
              <p>
                Distributed food and medicine to 5,000+ families after floods,
                restoring essential services quickly.
              </p>
            </article>
          </div>

          <div class="col-md-4" data-aos="fade-up" data-aos-delay="120">
            <article class="impact-card p-4 rounded shadow-sm">
              <h5>Herbal Livelihoods</h5>
              <p>
                Trained 200 women in herbal soap and oil production, enabling
                sustainable income streams.
              </p>
            </article>
          </div>

          <div class="col-md-4" data-aos="fade-up" data-aos-delay="190">
            <article class="impact-card p-4 rounded shadow-sm">
              <h5>Education Drive</h5>
              <p>
                Provided digital classes and school kits to villages; 300
                children gained basic computer skills.
              </p>
            </article>
          </div>
        </div>
      </div>
    </section>

    <!-- TEAM / CONTACT -->
    <section id="team" class="py-5 bg-soft">
      <div class="container">
        <div class="row align-items-center g-4">
          <div class="col-md-6" data-aos="fade-right">
            <h2>Contact & Volunteer</h2>
            <p class="lead">
              For partnerships, volunteering or donations, reach out. We respond
              within 48 hours.
            </p>
            <ul class="list-unstyled">
              <li><strong>Email:</strong> <EMAIL></li>
              <li><strong>Phone:</strong> +91 90000 00000</li>
              <li><strong>Registered:</strong> Non-profit (India)</li>
            </ul>
            <a href="#donate" class="btn btn-cta mt-3">Donate</a>
          </div>
          <div class="col-md-6" data-aos="fade-left">
            <img
              src="https://images.unsplash.com/photo-1508385082359-f3b35fbd1a1a?auto=format&fit=crop&w=1200&q=60"
              class="img-fluid rounded shadow-sm"
              alt=""
            />
          </div>
        </div>
      </div>
    </section>

    <!-- ENHANCED DONATE SECTION -->
    <section id="donate" class="py-5 cta-donate text-white section-pattern">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-6" data-aos="fade-right">
            <h2 class="mb-4">Transform Lives with Your Donation</h2>
            <p class="lead mb-4">
              Every rupee you donate creates a ripple effect of positive change.
              See exactly how your contribution makes a difference in real
              lives.
            </p>

            <!-- Impact Calculator -->
            <div class="impact-calculator mb-4">
              <h5 class="mb-3">Your Impact Calculator</h5>
              <div class="calculator-item mb-3">
                <div class="d-flex justify-content-between align-items-center">
                  <span>₹500 = 1 Health Camp Visit</span>
                  <div class="impact-icon">
                    <i class="fas fa-stethoscope"></i>
                  </div>
                </div>
              </div>
              <div class="calculator-item mb-3">
                <div class="d-flex justify-content-between align-items-center">
                  <span>₹1,000 = 1 Month Herbal Medicine</span>
                  <div class="impact-icon">
                    <i class="fas fa-pills"></i>
                  </div>
                </div>
              </div>
              <div class="calculator-item mb-3">
                <div class="d-flex justify-content-between align-items-center">
                  <span>₹2,500 = 1 Family Emergency Kit</span>
                  <div class="impact-icon">
                    <i class="fas fa-first-aid"></i>
                  </div>
                </div>
              </div>
            </div>

            <div class="donation-actions">
              <a
                class="btn btn-lg btn-light me-3 glow-on-hover"
                href="#"
                onclick="openDonationModal()"
              >
                <i class="fas fa-heart me-2"></i>Donate Now
              </a>
              <a class="btn btn-outline-light" href="#contact">
                <i class="fas fa-hands-helping me-2"></i>Become a Volunteer
              </a>
            </div>
          </div>

          <div class="col-lg-6" data-aos="fade-left">
            <!-- Quick Donation Options -->
            <div class="donation-card p-4 rounded">
              <h4 class="text-dark mb-4">Quick Donation</h4>

              <!-- Donation Amounts -->
              <div class="donation-amounts mb-4">
                <div class="row g-2">
                  <div class="col-4">
                    <button
                      class="btn btn-outline-success w-100 donation-btn"
                      data-amount="500"
                    >
                      ₹500
                    </button>
                  </div>
                  <div class="col-4">
                    <button
                      class="btn btn-outline-success w-100 donation-btn"
                      data-amount="1000"
                    >
                      ₹1,000
                    </button>
                  </div>
                  <div class="col-4">
                    <button
                      class="btn btn-outline-success w-100 donation-btn"
                      data-amount="2500"
                    >
                      ₹2,500
                    </button>
                  </div>
                  <div class="col-4">
                    <button
                      class="btn btn-outline-success w-100 donation-btn"
                      data-amount="5000"
                    >
                      ₹5,000
                    </button>
                  </div>
                  <div class="col-4">
                    <button
                      class="btn btn-outline-success w-100 donation-btn"
                      data-amount="10000"
                    >
                      ₹10,000
                    </button>
                  </div>
                  <div class="col-4">
                    <input
                      type="number"
                      class="form-control"
                      placeholder="Custom"
                      id="customAmount"
                    />
                  </div>
                </div>
              </div>

              <!-- Donation Purpose -->
              <div class="mb-4">
                <label class="form-label text-dark">Choose Purpose</label>
                <select class="form-select" id="donationPurpose">
                  <option value="general">General Fund</option>
                  <option value="health">Health Camps</option>
                  <option value="education">Education Programs</option>
                  <option value="emergency">Emergency Relief</option>
                  <option value="livelihoods">Women Livelihoods</option>
                </select>
              </div>

              <!-- Progress Bar -->
              <div class="campaign-progress mb-4">
                <div class="d-flex justify-content-between mb-2">
                  <small class="text-dark"
                    >Current Campaign: Health Camps</small
                  >
                  <small class="text-dark">₹45,000 / ₹100,000</small>
                </div>
                <div class="progress" style="height: 8px">
                  <div
                    class="progress-bar bg-success"
                    role="progressbar"
                    style="width: 45%"
                  ></div>
                </div>
              </div>

              <button
                class="btn btn-success w-100 btn-lg"
                onclick="processDonation()"
              >
                <i class="fas fa-shield-alt me-2"></i>Secure Donation
              </button>

              <div class="text-center mt-3">
                <small class="text-muted">
                  <i class="fas fa-lock me-1"></i>Secure payment • Tax
                  deductible under 80G
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FOOTER -->
    <footer class="footer">
      <div class="container">
        <div class="row gy-4">
          <div class="col-md-6">
            <div class="d-flex gap-3 align-items-center">
              <img
                src="./AyurRakshakImage.jpeg"
                alt="logo"
                class="logo-small"
              />
              <div>
                <strong>AYURAKSHAK</strong>
                <div class="text-muted">Care · Restore · Protect</div>
              </div>
            </div>
            <p class="mt-3 text-muted">
              We publish annual reports and audited financials to keep
              operations transparent and accountable.
            </p>
          </div>

          <div class="col-md-3">
            <h6 class="footer-heading">
              <i class="fas fa-envelope me-2"></i>Contact
            </h6>
            <p class="footer-text mb-1">
              <i class="fas fa-at me-2"></i><EMAIL>
            </p>
            <p class="footer-text">
              <i class="fas fa-phone me-2"></i>+91 90000 00000
            </p>
          </div>

          <div class="col-md-3">
            <h6 class="footer-heading">
              <i class="fas fa-share-alt me-2"></i>Follow
            </h6>
            <div class="social-links">
              <a href="#" class="social-link">
                <i class="fab fa-twitter"></i> Twitter
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-instagram"></i> Instagram
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-linkedin"></i> LinkedIn
              </a>
            </div>
          </div>
        </div>

        <hr class="my-4 border-2" />

        <div
          class="d-flex justify-content-between align-items-center flex-column flex-md-row gap-3"
        >
          <small class="footer-copyright">
            <i class="fas fa-copyright me-1"></i>
            Ayurakshak 2025 • Registered Charity in India
          </small>
          <div class="developer-credit">
            <i class="fas fa-code me-1"></i>
            Developed by
            <a
              href="https://kush-personal-portfolio-my-portfolio.vercel.app/"
              target="_blank"
              class="developer-link"
            >
              <i class="fas fa-user-tie me-1"></i>Kush Vardhan
            </a>
          </div>
        </div>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
    <!-- AOS -->
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="script.js"></script>
  </body>
</html>
