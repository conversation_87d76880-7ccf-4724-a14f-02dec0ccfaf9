'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/utils/cn';
import Button from '@/components/ui/Button';

const Header: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { name: 'Programs', href: '/#programs' },
    { name: 'Products', href: '/products' },
    { name: 'Impact', href: '/#impact' },
    { name: 'Contact', href: '/contact' },
  ];

  return (
    <motion.header
      className={cn(
        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
        isScrolled
          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200'
          : 'bg-transparent'
      )}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <motion.div
            className="flex items-center space-x-3"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <Link href="/" className="flex items-center space-x-3">
              <div className="relative w-10 h-10 lg:w-12 lg:h-12">
                <Image
                  src="/logo.jpeg"
                  alt="Ayurakshak Logo"
                  fill
                  className="object-contain rounded-full"
                  priority
                />
              </div>
              <div className="hidden sm:block">
                <h1 className={cn(
                  'text-xl lg:text-2xl font-bold transition-colors duration-300',
                  isScrolled ? 'text-primary-600' : 'text-white'
                )}>
                  Ayurakshak
                </h1>
                <p className={cn(
                  'text-xs lg:text-sm transition-colors duration-300',
                  isScrolled ? 'text-gray-600' : 'text-white/80'
                )}>
                  Care · Restore · Protect
                </p>
              </div>
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navigation.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Link
                  href={item.href}
                  className={cn(
                    'text-sm font-medium transition-all duration-200 hover:scale-105',
                    'relative group',
                    isScrolled
                      ? 'text-gray-700 hover:text-primary-600'
                      : 'text-white hover:text-primary-200'
                  )}
                >
                  {item.name}
                  <span className={cn(
                    'absolute -bottom-1 left-0 w-0 h-0.5 transition-all duration-300 group-hover:w-full',
                    isScrolled ? 'bg-primary-600' : 'bg-white'
                  )} />
                </Link>
              </motion.div>
            ))}
          </nav>

          {/* CTA Button */}
          <div className="hidden lg:flex items-center space-x-4">
            <Button
              variant={isScrolled ? 'primary' : 'outline'}
              size="md"
              className={cn(
                'transition-all duration-300',
                !isScrolled && 'border-white text-white hover:bg-white hover:text-primary-600'
              )}
            >
              Donate Now
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden p-2 rounded-md transition-colors duration-200"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <div className="w-6 h-6 relative">
              <span className={cn(
                'absolute block w-full h-0.5 transform transition-all duration-300',
                'top-1.5',
                isScrolled ? 'bg-gray-700' : 'bg-white',
                isMobileMenuOpen && 'rotate-45 top-3'
              )} />
              <span className={cn(
                'absolute block w-full h-0.5 transform transition-all duration-300',
                'top-3',
                isScrolled ? 'bg-gray-700' : 'bg-white',
                isMobileMenuOpen && 'opacity-0'
              )} />
              <span className={cn(
                'absolute block w-full h-0.5 transform transition-all duration-300',
                'top-4.5',
                isScrolled ? 'bg-gray-700' : 'bg-white',
                isMobileMenuOpen && '-rotate-45 top-3'
              )} />
            </div>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="lg:hidden bg-white border-t border-gray-200 shadow-lg"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="px-4 py-6 space-y-4">
              {navigation.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className="block text-gray-700 hover:text-primary-600 font-medium py-2 transition-colors duration-200"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                </motion.div>
              ))}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: navigation.length * 0.1 }}
                className="pt-4 border-t border-gray-200"
              >
                <Button variant="primary" size="md" className="w-full">
                  Donate Now
                </Button>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  );
};

export default Header;
