import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Newsletter from '@/lib/models/Newsletter';
import { newsletterSchema } from '@/utils/validation';
import { formatSuccessResponse, formatErrorResponse, createRateLimiter } from '@/utils/validation';

// Rate limiter: 3 requests per 10 minutes per IP
const rateLimiter = createRateLimiter(10 * 60 * 1000, 3);

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    if (!rateLimiter(clientIP)) {
      return NextResponse.json(
        formatErrorResponse('Too many requests. Please try again later.'),
        { status: 429 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate input data
    const validationResult = newsletterSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        formatErrorResponse('Validation failed', validationResult.error.errors),
        { status: 400 }
      );
    }

    const { email, name } = validationResult.data;

    // Connect to database
    await connectDB();

    // Check if email already exists
    const existingSubscription = await Newsletter.findOne({ email });

    if (existingSubscription) {
      if (existingSubscription.isActive) {
        return NextResponse.json(
          formatErrorResponse('This email is already subscribed to our newsletter.'),
          { status: 409 }
        );
      } else {
        // Reactivate subscription
        existingSubscription.isActive = true;
        existingSubscription.subscribedAt = new Date();
        existingSubscription.unsubscribedAt = undefined;
        if (name) existingSubscription.name = name;
        
        await existingSubscription.save();

        return NextResponse.json(
          formatSuccessResponse(
            { id: existingSubscription._id },
            'Welcome back! Your newsletter subscription has been reactivated.'
          ),
          { status: 200 }
        );
      }
    }

    // Create new subscription
    const subscription = new Newsletter({
      email,
      name,
      isActive: true,
      subscribedAt: new Date(),
    });

    await subscription.save();

    // TODO: Send welcome email
    // TODO: Add to email marketing platform

    return NextResponse.json(
      formatSuccessResponse(
        { id: subscription._id },
        'Thank you for subscribing! You will receive our latest updates and news.'
      ),
      { status: 201 }
    );

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while processing your subscription. Please try again later.'),
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const token = searchParams.get('token'); // For unsubscribe links

    if (!email) {
      return NextResponse.json(
        formatErrorResponse('Email is required'),
        { status: 400 }
      );
    }

    await connectDB();

    // Find and unsubscribe
    const subscription = await Newsletter.findOne({ email });

    if (!subscription) {
      return NextResponse.json(
        formatErrorResponse('Email not found in our newsletter list.'),
        { status: 404 }
      );
    }

    if (!subscription.isActive) {
      return NextResponse.json(
        formatErrorResponse('This email is already unsubscribed.'),
        { status: 409 }
      );
    }

    // Unsubscribe
    subscription.isActive = false;
    subscription.unsubscribedAt = new Date();
    await subscription.save();

    return NextResponse.json(
      formatSuccessResponse(
        null,
        'You have been successfully unsubscribed from our newsletter.'
      ),
      { status: 200 }
    );

  } catch (error) {
    console.error('Newsletter unsubscribe error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while processing your unsubscribe request.'),
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // This endpoint is for admin use only - add authentication here
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const isActive = searchParams.get('isActive');

    await connectDB();

    // Build query
    const query: any = {};
    if (isActive !== null) {
      query.isActive = isActive === 'true';
    }

    // Get total count
    const total = await Newsletter.countDocuments(query);

    // Get paginated results
    const subscriptions = await Newsletter.find(query)
      .sort({ subscribedAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .select('email name isActive subscribedAt unsubscribedAt');

    // Get statistics
    const stats = {
      totalSubscribers: await Newsletter.countDocuments({ isActive: true }),
      totalUnsubscribed: await Newsletter.countDocuments({ isActive: false }),
      recentSubscriptions: await Newsletter.countDocuments({
        isActive: true,
        subscribedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }),
    };

    return NextResponse.json(
      formatSuccessResponse({
        subscriptions,
        stats,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      }),
      { status: 200 }
    );

  } catch (error) {
    console.error('Newsletter fetch error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while fetching newsletter data.'),
      { status: 500 }
    );
  }
}
