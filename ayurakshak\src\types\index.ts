import { Document } from 'mongoose';

// Contact Form Types
export interface IContactForm extends Document {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  isRead: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Newsletter Subscription Types
export interface INewsletter extends Document {
  email: string;
  name?: string;
  isActive: boolean;
  subscribedAt: Date;
  unsubscribedAt?: Date;
}

// Donation Types
export interface IDonation extends Document {
  donorName: string;
  donorEmail: string;
  donorPhone?: string;
  amount: number;
  currency: string;
  purpose: 'general' | 'health' | 'education' | 'emergency' | 'livelihoods';
  paymentMethod: 'online' | 'bank_transfer' | 'cash' | 'cheque';
  paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded';
  transactionId?: string;
  paymentGatewayResponse?: any;
  isAnonymous: boolean;
  address?: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  panNumber?: string;
  receiptNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Product Order Types
export interface IProductOrder extends Document {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  products: {
    productId: string;
    productName: string;
    quantity: number;
    price: number;
  }[];
  totalAmount: number;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  orderStatus: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: 'cod' | 'online' | 'bank_transfer';
  trackingNumber?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Volunteer Application Types
export interface IVolunteer extends Document {
  name: string;
  email: string;
  phone: string;
  age: number;
  occupation: string;
  address: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  skills: string[];
  experience?: string;
  availability: {
    days: string[];
    timeSlots: string[];
  };
  motivation: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  status: 'pending' | 'approved' | 'rejected' | 'inactive';
  appliedAt: Date;
  approvedAt?: Date;
  notes?: string;
}

// Program Types
export interface IProgram {
  id: string;
  name: string;
  description: string;
  image: string;
  category: 'health' | 'education' | 'environment' | 'livelihood' | 'emergency';
  targetBeneficiaries: number;
  currentBeneficiaries: number;
  targetAmount: number;
  raisedAmount: number;
  startDate: Date;
  endDate?: Date;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  location: string[];
  features: string[];
  impact: string[];
}

// Product Types
export interface IProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  images: string[];
  category: string;
  ingredients: string[];
  benefits: string[];
  usage: string;
  features: string[];
  inStock: boolean;
  stockQuantity: number;
  weight?: string;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  isActive: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

// Form Validation Types
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

export interface DonationFormData {
  donorName: string;
  donorEmail: string;
  donorPhone?: string;
  amount: number;
  purpose: string;
  isAnonymous: boolean;
  address?: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  panNumber?: string;
}

export interface NewsletterFormData {
  email: string;
  name?: string;
}

export interface ProductOrderFormData {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  products: {
    productId: string;
    quantity: number;
  }[];
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  paymentMethod: 'cod' | 'online' | 'bank_transfer';
  notes?: string;
}

// Statistics Types
export interface SiteStats {
  totalDonations: number;
  totalDonors: number;
  livesImpacted: number;
  villagesReached: number;
  healthCamps: number;
  activePrograms: number;
  volunteers: number;
  newsletterSubscribers: number;
}
