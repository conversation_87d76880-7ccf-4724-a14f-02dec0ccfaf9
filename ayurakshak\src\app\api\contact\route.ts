import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import ContactForm from '@/lib/models/Contact';
import { contactFormSchema } from '@/utils/validation';
import { formatSuccessResponse, formatErrorResponse, createRateLimiter } from '@/utils/validation';
import { z } from 'zod';

// Rate limiter: 5 requests per 15 minutes per IP
const rateLimiter = createRateLimiter(15 * 60 * 1000, 5);

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    if (!rateLimiter(clientIP)) {
      return NextResponse.json(
        formatErrorResponse('Too many requests. Please try again later.'),
        { status: 429 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate input data
    const validationResult = contactFormSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        formatErrorResponse('Validation failed', validationResult.error.errors),
        { status: 400 }
      );
    }

    const { name, email, phone, subject, message } = validationResult.data;

    // Connect to database
    await connectDB();

    // Check for duplicate submissions (same email and subject within 1 hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const existingSubmission = await ContactForm.findOne({
      email,
      subject,
      createdAt: { $gte: oneHourAgo },
    });

    if (existingSubmission) {
      return NextResponse.json(
        formatErrorResponse('You have already submitted a similar message recently. Please wait before submitting again.'),
        { status: 409 }
      );
    }

    // Create new contact form submission
    const contactSubmission = new ContactForm({
      name,
      email,
      phone,
      subject,
      message,
    });

    await contactSubmission.save();

    // TODO: Send email notification to admin
    // TODO: Send auto-reply email to user

    return NextResponse.json(
      formatSuccessResponse(
        { id: contactSubmission._id },
        'Thank you for your message! We will get back to you soon.'
      ),
      { status: 201 }
    );

  } catch (error) {
    console.error('Contact form submission error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while processing your request. Please try again later.'),
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // This endpoint is for admin use only - add authentication here
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const isRead = searchParams.get('isRead');

    await connectDB();

    // Build query
    const query: any = {};
    if (isRead !== null) {
      query.isRead = isRead === 'true';
    }

    // Get total count
    const total = await ContactForm.countDocuments(query);

    // Get paginated results
    const contacts = await ContactForm.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .select('name email subject createdAt isRead');

    return NextResponse.json(
      formatSuccessResponse({
        contacts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      }),
      { status: 200 }
    );

  } catch (error) {
    console.error('Contact form fetch error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while fetching contact forms.'),
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // This endpoint is for admin use only - add authentication here
    const body = await request.json();
    const { id, isRead } = body;

    if (!id || typeof isRead !== 'boolean') {
      return NextResponse.json(
        formatErrorResponse('Invalid request data'),
        { status: 400 }
      );
    }

    await connectDB();

    const updatedContact = await ContactForm.findByIdAndUpdate(
      id,
      { isRead },
      { new: true }
    );

    if (!updatedContact) {
      return NextResponse.json(
        formatErrorResponse('Contact form not found'),
        { status: 404 }
      );
    }

    return NextResponse.json(
      formatSuccessResponse(updatedContact, 'Contact form updated successfully'),
      { status: 200 }
    );

  } catch (error) {
    console.error('Contact form update error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while updating the contact form.'),
      { status: 500 }
    );
  }
}
