import mongoose, { Schema } from 'mongoose';
import { IDonation } from '@/types';

const DonationSchema = new Schema<IDonation>(
  {
    donorName: {
      type: String,
      required: [true, 'Donor name is required'],
      trim: true,
      maxlength: [100, 'Name cannot exceed 100 characters'],
    },
    donorEmail: {
      type: String,
      required: [true, 'Email is required'],
      trim: true,
      lowercase: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email address',
      ],
    },
    donorPhone: {
      type: String,
      trim: true,
      match: [
        /^[\+]?[1-9][\d]{0,15}$/,
        'Please enter a valid phone number',
      ],
    },
    amount: {
      type: Number,
      required: [true, 'Donation amount is required'],
      min: [1, 'Amount must be at least ₹1'],
      max: [********, 'Amount cannot exceed ₹1 crore'],
    },
    currency: {
      type: String,
      default: 'INR',
      enum: ['INR', 'USD', 'EUR'],
    },
    purpose: {
      type: String,
      required: [true, 'Purpose is required'],
      enum: ['general', 'health', 'education', 'emergency', 'livelihoods'],
      default: 'general',
    },
    paymentMethod: {
      type: String,
      required: [true, 'Payment method is required'],
      enum: ['online', 'bank_transfer', 'cash', 'cheque'],
      default: 'online',
    },
    paymentStatus: {
      type: String,
      required: [true, 'Payment status is required'],
      enum: ['pending', 'completed', 'failed', 'refunded'],
      default: 'pending',
    },
    transactionId: {
      type: String,
      trim: true,
      sparse: true, // Allows multiple null values
    },
    paymentGatewayResponse: {
      type: Schema.Types.Mixed,
    },
    isAnonymous: {
      type: Boolean,
      default: false,
    },
    address: {
      street: {
        type: String,
        trim: true,
        maxlength: [200, 'Street address cannot exceed 200 characters'],
      },
      city: {
        type: String,
        trim: true,
        maxlength: [50, 'City cannot exceed 50 characters'],
      },
      state: {
        type: String,
        trim: true,
        maxlength: [50, 'State cannot exceed 50 characters'],
      },
      pincode: {
        type: String,
        trim: true,
        match: [/^[1-9][0-9]{5}$/, 'Please enter a valid pincode'],
      },
      country: {
        type: String,
        trim: true,
        default: 'India',
        maxlength: [50, 'Country cannot exceed 50 characters'],
      },
    },
    panNumber: {
      type: String,
      trim: true,
      uppercase: true,
      match: [/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Please enter a valid PAN number'],
    },
    receiptNumber: {
      type: String,
      trim: true,
      unique: true,
      sparse: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better query performance
DonationSchema.index({ donorEmail: 1 });
DonationSchema.index({ paymentStatus: 1 });
DonationSchema.index({ purpose: 1 });
DonationSchema.index({ createdAt: -1 });
DonationSchema.index({ amount: -1 });
DonationSchema.index({ transactionId: 1 }, { sparse: true });

// Virtual for formatted amount
DonationSchema.virtual('formattedAmount').get(function () {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: this.currency,
  }).format(this.amount);
});

// Virtual for formatted date
DonationSchema.virtual('formattedDate').get(function () {
  return this.createdAt.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
});

// Virtual for purpose display name
DonationSchema.virtual('purposeDisplayName').get(function () {
  const purposeMap = {
    general: 'General Fund',
    health: 'Health Camps',
    education: 'Education Programs',
    emergency: 'Emergency Relief',
    livelihoods: 'Women Livelihoods',
  };
  return purposeMap[this.purpose] || this.purpose;
});

// Pre-save middleware
DonationSchema.pre('save', function (next) {
  // Generate receipt number for completed donations
  if (this.paymentStatus === 'completed' && !this.receiptNumber) {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const random = Math.random().toString(36).substr(2, 6).toUpperCase();
    this.receiptNumber = `AYU${year}${month}${random}`;
  }
  
  // Sanitize text fields
  this.donorName = this.donorName.replace(/<[^>]*>?/gm, '');
  
  next();
});

// Static method to get total donations
DonationSchema.statics.getTotalAmount = function (purpose?: string) {
  const match = purpose ? { purpose, paymentStatus: 'completed' } : { paymentStatus: 'completed' };
  return this.aggregate([
    { $match: match },
    { $group: { _id: null, total: { $sum: '$amount' } } },
  ]);
};

// Static method to get donation statistics
DonationSchema.statics.getStats = function () {
  return this.aggregate([
    { $match: { paymentStatus: 'completed' } },
    {
      $group: {
        _id: '$purpose',
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 },
        avgAmount: { $avg: '$amount' },
      },
    },
  ]);
};

// Static method to get recent donations
DonationSchema.statics.getRecent = function (limit: number = 10) {
  return this.find({ paymentStatus: 'completed' })
    .sort({ createdAt: -1 })
    .limit(limit)
    .select('donorName amount purpose createdAt isAnonymous');
};

// Static method to get top donors
DonationSchema.statics.getTopDonors = function (limit: number = 10) {
  return this.aggregate([
    { $match: { paymentStatus: 'completed', isAnonymous: false } },
    {
      $group: {
        _id: '$donorEmail',
        donorName: { $first: '$donorName' },
        totalAmount: { $sum: '$amount' },
        donationCount: { $sum: 1 },
        lastDonation: { $max: '$createdAt' },
      },
    },
    { $sort: { totalAmount: -1 } },
    { $limit: limit },
  ]);
};

const Donation = mongoose.models.Donation || mongoose.model<IDonation>('Donation', DonationSchema);

export default Donation;
