module.exports=[61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},26046,(e,t,r)=>{t.exports=e.x("mongoose",()=>require("mongoose"))},49991,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(26046);let r=process.env.MONGODB_URI;if(!r)throw Error("Please define the MONGODB_URI environment variable inside .env");let a=e.g.mongoose;a||(a=e.g.mongoose={conn:null,promise:null});let n=async function(){if(a.conn)return a.conn;a.promise||(a.promise=t.default.connect(r,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},38335,e=>{"use strict";e.s(["default",()=>a]);var t=e.i(26046);let r=new t.Schema({name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},email:{type:String,required:[!0,"Email is required"],trim:!0,lowercase:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email address"]},phone:{type:String,trim:!0,match:[/^[\+]?[1-9][\d]{0,15}$/,"Please enter a valid phone number"]},subject:{type:String,required:[!0,"Subject is required"],trim:!0,maxlength:[200,"Subject cannot exceed 200 characters"]},message:{type:String,required:[!0,"Message is required"],trim:!0,maxlength:[2e3,"Message cannot exceed 2000 characters"]},isRead:{type:Boolean,default:!1}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({email:1}),r.index({createdAt:-1}),r.index({isRead:1}),r.virtual("formattedDate").get(function(){return this.createdAt.toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}),r.pre("save",function(e){this.name=this.name.replace(/<[^>]*>?/gm,""),this.subject=this.subject.replace(/<[^>]*>?/gm,""),this.message=this.message.replace(/<[^>]*>?/gm,""),e()}),r.statics.getUnreadCount=function(){return this.countDocuments({isRead:!1})},r.statics.markAsRead=function(e){return this.findByIdAndUpdate(e,{isRead:!0},{new:!0})},r.statics.getRecent=function(e=10){return this.find().sort({createdAt:-1}).limit(e).select("name email subject createdAt isRead")};let a=t.default.models.ContactForm||t.default.model("ContactForm",r)},29216,(e,t,r)=>{},86590,e=>{"use strict";e.s(["handler",()=>O,"patchFetch",()=>P,"routeModule",()=>j,"serverHooks",()=>S,"workAsyncStorage",()=>N,"workUnitAsyncStorage",()=>q],86590);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),l=e.i(87718),u=e.i(95169),d=e.i(47587),c=e.i(66012),p=e.i(70101),m=e.i(26937),f=e.i(10372),h=e.i(93695);e.i(52474);var x=e.i(220);e.s(["GET",()=>C,"PATCH",()=>b,"POST",()=>E],66122);var R=e.i(89171),g=e.i(49991),v=e.i(38335),w=e.i(78044);let y=(0,w.createRateLimiter)(9e5,5);async function E(e){try{let t=e.ip||e.headers.get("x-forwarded-for")||"unknown";if(!y(t))return R.NextResponse.json((0,w.formatErrorResponse)("Too many requests. Please try again later."),{status:429});let r=await e.json(),a=w.contactFormSchema.safeParse(r);if(!a.success)return R.NextResponse.json((0,w.formatErrorResponse)("Validation failed",a.error.errors),{status:400});let{name:n,email:s,phone:o,subject:i,message:l}=a.data;await (0,g.default)();let u=new Date(Date.now()-36e5);if(await v.default.findOne({email:s,subject:i,createdAt:{$gte:u}}))return R.NextResponse.json((0,w.formatErrorResponse)("You have already submitted a similar message recently. Please wait before submitting again."),{status:409});let d=new v.default({name:n,email:s,phone:o,subject:i,message:l});return await d.save(),R.NextResponse.json((0,w.formatSuccessResponse)({id:d._id},"Thank you for your message! We will get back to you soon."),{status:201})}catch(e){return console.error("Contact form submission error:",e),R.NextResponse.json((0,w.formatErrorResponse)("An error occurred while processing your request. Please try again later."),{status:500})}}async function C(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),n=t.get("isRead");await (0,g.default)();let s={};null!==n&&(s.isRead="true"===n);let o=await v.default.countDocuments(s),i=await v.default.find(s).sort({createdAt:-1}).skip((r-1)*a).limit(a).select("name email subject createdAt isRead");return R.NextResponse.json((0,w.formatSuccessResponse)({contacts:i,pagination:{page:r,limit:a,total:o,pages:Math.ceil(o/a)}}),{status:200})}catch(e){return console.error("Contact form fetch error:",e),R.NextResponse.json((0,w.formatErrorResponse)("An error occurred while fetching contact forms."),{status:500})}}async function b(e){try{let{id:t,isRead:r}=await e.json();if(!t||"boolean"!=typeof r)return R.NextResponse.json((0,w.formatErrorResponse)("Invalid request data"),{status:400});await (0,g.default)();let a=await v.default.findByIdAndUpdate(t,{isRead:r},{new:!0});if(!a)return R.NextResponse.json((0,w.formatErrorResponse)("Contact form not found"),{status:404});return R.NextResponse.json((0,w.formatSuccessResponse)(a,"Contact form updated successfully"),{status:200})}catch(e){return console.error("Contact form update error:",e),R.NextResponse.json((0,w.formatErrorResponse)("An error occurred while updating the contact form."),{status:500})}}var A=e.i(66122);let j=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/contact/route.ts",nextConfigOutput:"",userland:A}),{workAsyncStorage:N,workUnitAsyncStorage:q,serverHooks:S}=j;function P(){return(0,a.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:q})}async function O(e,t,a){var R;let g="/api/contact/route";g=g.replace(/\/index$/,"")||"/";let v=await j.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:E,isDraftMode:C,prerenderManifest:b,routerServerContext:A,isOnDemandRevalidate:N,revalidateOnlyGenerated:q,resolvedPathname:S}=v,P=(0,o.normalizeAppPath)(g),O=!!(b.dynamicRoutes[P]||b.routes[S]);if(O&&!C){let e=!!b.routes[S],t=b.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let T=null;!O||j.isDev||C||(T="/index"===(T=S)?"/":T);let k=!0===j.isDev||!O,_=O&&!k,I=e.method||"GET",U=(0,s.getTracer)(),D=U.getActiveScopeSpan(),M={params:y,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:k,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(R=E.experimental)?void 0:R.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>j.onRequestError(e,t,a,A)},sharedContext:{buildId:w}},H=new i.NodeNextRequest(e),$=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(H,(0,l.signalFromNodeResponse)(t));try{let o=async r=>j.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${I} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${I} ${e.url}`)}),i=async s=>{var i,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&N&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let u=M.renderOpts.collectedTags;if(!O)return await (0,c.sendResponse)(H,$,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(i.headers);u&&(t[f.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:x.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await j.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})},A),t}},h=await j.handleResponse({req:e,nextConfig:E,cacheKey:T,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:q,responseGenerator:u,waitUntil:a.waitUntil});if(!O)return null;if((null==h||null==(i=h.value)?void 0:i.kind)!==x.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let R=(0,p.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&O||R.delete(f.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||R.get("Cache-Control")||R.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,c.sendResponse)(H,$,new Response(h.value.body,{headers:R,status:h.value.status||200})),null};D?await i(D):await U.withPropagatedContext(e.headers,()=>U.trace(u.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},i))}catch(t){if(D||t instanceof h.NoFallbackError||await j.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})}),O)throw t;return await (0,c.sendResponse)(H,$,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__b7ebc7a7._.js.map