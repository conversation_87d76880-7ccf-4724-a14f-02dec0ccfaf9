import { z } from 'zod';

// Contact Form Validation Schema
export const contactFormSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name cannot exceed 100 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  email: z
    .string()
    .email('Please enter a valid email address')
    .max(255, 'Email cannot exceed 255 characters'),
  phone: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[\+]?[1-9][\d]{0,15}$/.test(val),
      'Please enter a valid phone number'
    ),
  subject: z
    .string()
    .min(5, 'Subject must be at least 5 characters')
    .max(200, 'Subject cannot exceed 200 characters'),
  message: z
    .string()
    .min(10, 'Message must be at least 10 characters')
    .max(2000, 'Message cannot exceed 2000 characters'),
});

// Newsletter Subscription Validation Schema
export const newsletterSchema = z.object({
  email: z
    .string()
    .email('Please enter a valid email address')
    .max(255, 'Email cannot exceed 255 characters'),
  name: z
    .string()
    .optional()
    .refine(
      (val) => !val || (val.length >= 2 && val.length <= 100),
      'Name must be between 2 and 100 characters'
    ),
});

// Donation Form Validation Schema
export const donationFormSchema = z.object({
  donorName: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name cannot exceed 100 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  donorEmail: z
    .string()
    .email('Please enter a valid email address')
    .max(255, 'Email cannot exceed 255 characters'),
  donorPhone: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[\+]?[1-9][\d]{0,15}$/.test(val),
      'Please enter a valid phone number'
    ),
  amount: z
    .number()
    .min(1, 'Amount must be at least ₹1')
    .max(10000000, 'Amount cannot exceed ₹1 crore'),
  purpose: z.enum(['general', 'health', 'education', 'emergency', 'livelihoods']),
  isAnonymous: z.boolean().default(false),
  address: z
    .object({
      street: z.string().max(200, 'Street address cannot exceed 200 characters'),
      city: z.string().max(50, 'City cannot exceed 50 characters'),
      state: z.string().max(50, 'State cannot exceed 50 characters'),
      pincode: z.string().regex(/^[1-9][0-9]{5}$/, 'Please enter a valid pincode'),
      country: z.string().max(50, 'Country cannot exceed 50 characters').default('India'),
    })
    .optional(),
  panNumber: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(val),
      'Please enter a valid PAN number'
    ),
});

// Product Order Validation Schema
export const productOrderSchema = z.object({
  customerName: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name cannot exceed 100 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  customerEmail: z
    .string()
    .email('Please enter a valid email address')
    .max(255, 'Email cannot exceed 255 characters'),
  customerPhone: z
    .string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number'),
  products: z
    .array(
      z.object({
        productId: z.string().min(1, 'Product ID is required'),
        quantity: z
          .number()
          .min(1, 'Quantity must be at least 1')
          .max(100, 'Quantity cannot exceed 100'),
      })
    )
    .min(1, 'At least one product is required'),
  shippingAddress: z.object({
    street: z
      .string()
      .min(5, 'Street address must be at least 5 characters')
      .max(200, 'Street address cannot exceed 200 characters'),
    city: z
      .string()
      .min(2, 'City must be at least 2 characters')
      .max(50, 'City cannot exceed 50 characters'),
    state: z
      .string()
      .min(2, 'State must be at least 2 characters')
      .max(50, 'State cannot exceed 50 characters'),
    pincode: z.string().regex(/^[1-9][0-9]{5}$/, 'Please enter a valid pincode'),
    country: z.string().max(50, 'Country cannot exceed 50 characters').default('India'),
  }),
  paymentMethod: z.enum(['cod', 'online', 'bank_transfer']),
  notes: z
    .string()
    .optional()
    .refine(
      (val) => !val || val.length <= 500,
      'Notes cannot exceed 500 characters'
    ),
});

// Utility functions for validation
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone);
};

export const validatePAN = (pan: string): boolean => {
  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
  return panRegex.test(pan);
};

export const validatePincode = (pincode: string): boolean => {
  const pincodeRegex = /^[1-9][0-9]{5}$/;
  return pincodeRegex.test(pincode);
};

// Sanitization functions
export const sanitizeString = (str: string): string => {
  return str.replace(/<[^>]*>?/gm, '').trim();
};

export const sanitizeEmail = (email: string): string => {
  return email.toLowerCase().trim();
};

export const sanitizeName = (name: string): string => {
  return name
    .replace(/[^a-zA-Z\s]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
};

// Rate limiting helper
export const createRateLimiter = (windowMs: number, maxRequests: number) => {
  const requests = new Map();

  return (identifier: string): boolean => {
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    for (const [key, timestamp] of requests.entries()) {
      if (timestamp < windowStart) {
        requests.delete(key);
      }
    }

    // Get current requests for this identifier
    const userRequests = Array.from(requests.entries())
      .filter(([key]) => key.startsWith(identifier))
      .length;

    if (userRequests >= maxRequests) {
      return false;
    }

    // Add current request
    requests.set(`${identifier}:${now}`, now);
    return true;
  };
};

// Error formatting
export const formatValidationError = (error: z.ZodError) => {
  return error.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message,
  }));
};

// Success response formatter
export const formatSuccessResponse = <T>(data: T, message: string = 'Success') => {
  return {
    success: true,
    message,
    data,
  };
};

// Error response formatter
export const formatErrorResponse = (message: string, error?: any) => {
  return {
    success: false,
    message,
    error: process.env.NODE_ENV === 'development' ? error : undefined,
  };
};

// Type guards
export const isValidObjectId = (id: string): boolean => {
  return /^[0-9a-fA-F]{24}$/.test(id);
};

export const isValidDate = (date: string): boolean => {
  return !isNaN(Date.parse(date));
};

export const isValidAmount = (amount: number): boolean => {
  return typeof amount === 'number' && amount > 0 && amount <= 10000000;
};
