module.exports=[32618,a=>{a.v({className:"inter_c15e96cb-module__0bjUvq__className",variable:"inter_c15e96cb-module__0bjUvq__variable"})},79862,a=>{a.v({className:"poppins_b6769e12-module__2yJm1a__className",variable:"poppins_b6769e12-module__2yJm1a__variable"})},27572,a=>{"use strict";a.s(["default",()=>h,"metadata",()=>g],27572);var b=a.i(7997),c=a.i(32618);let d={className:c.default.className,style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"}};null!=c.default.variable&&(d.variable=c.default.variable);var e=a.i(79862);let f={className:e.default.className,style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"}};null!=e.default.variable&&(f.variable=e.default.variable);let g={title:{default:"Ayurakshak - Care · Restore · Protect",template:"%s | Ayurakshak"},description:"Ayurakshak combines traditional Ayurveda with modern outreach — health camps, sustainable livelihoods, and green initiatives across India. Join us in healing communities with nature-led care.",keywords:["Ayurakshak","Ayurveda","NGO","Healthcare","Traditional Medicine","Health Camps","Natural Healing","Community Health","Herbal Products","Sustainable Livelihoods","India","Naturopathy"],authors:[{name:"Ayurakshak Team",url:"https://ayurakshak.org"},{name:"Kush Vardhan",url:"https://kush-personal-portfolio-my-portfolio.vercel.app/"}],creator:"Kush Vardhan",publisher:"Ayurakshak",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),alternates:{canonical:"/"},openGraph:{type:"website",locale:"en_IN",url:"/",title:"Ayurakshak - Care · Restore · Protect",description:"Healing communities with traditional Ayurveda and modern outreach across India.",siteName:"Ayurakshak",images:[{url:"/logo.jpeg",width:1200,height:630,alt:"Ayurakshak - Traditional Ayurveda meets modern healthcare"}]},twitter:{card:"summary_large_image",title:"Ayurakshak - Care · Restore · Protect",description:"Healing communities with traditional Ayurveda and modern outreach across India.",images:["/logo.jpeg"],creator:"@ayurakshak"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code",yandex:"your-yandex-verification-code"}};function h({children:a}){return(0,b.jsxs)("html",{lang:"en",className:"scroll-smooth",children:[(0,b.jsxs)("head",{children:[(0,b.jsx)("link",{rel:"icon",href:"/logo.jpeg",sizes:"any"}),(0,b.jsx)("link",{rel:"apple-touch-icon",href:"/logo.jpeg"}),(0,b.jsx)("meta",{name:"theme-color",content:"#4a7c59"}),(0,b.jsx)("meta",{name:"msapplication-TileColor",content:"#4a7c59"}),(0,b.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=5"})]}),(0,b.jsx)("body",{className:`${d.variable} ${f.variable} font-sans antialiased bg-white text-gray-900`,children:a})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__4537bde8._.js.map