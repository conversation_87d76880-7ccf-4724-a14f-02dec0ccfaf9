module.exports=[61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},26046,(e,t,r)=>{t.exports=e.x("mongoose",()=>require("mongoose"))},49991,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(26046);let r=process.env.MONGODB_URI;if(!r)throw Error("Please define the MONGODB_URI environment variable inside .env");let a=e.g.mongoose;a||(a=e.g.mongoose={conn:null,promise:null});let n=async function(){if(a.conn)return a.conn;a.promise||(a.promise=t.default.connect(r,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},25425,e=>{"use strict";e.s(["default",()=>a]);var t=e.i(26046);let r=new t.Schema({customerName:{type:String,required:[!0,"Customer name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},customerEmail:{type:String,required:[!0,"Email is required"],trim:!0,lowercase:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email address"]},customerPhone:{type:String,required:[!0,"Phone number is required"],trim:!0,match:[/^[\+]?[1-9][\d]{0,15}$/,"Please enter a valid phone number"]},products:[{productId:{type:String,required:[!0,"Product ID is required"]},productName:{type:String,required:[!0,"Product name is required"],trim:!0},quantity:{type:Number,required:[!0,"Quantity is required"],min:[1,"Quantity must be at least 1"],max:[100,"Quantity cannot exceed 100"]},price:{type:Number,required:[!0,"Price is required"],min:[0,"Price cannot be negative"]}}],totalAmount:{type:Number,required:[!0,"Total amount is required"],min:[0,"Total amount cannot be negative"]},shippingAddress:{street:{type:String,required:[!0,"Street address is required"],trim:!0,maxlength:[200,"Street address cannot exceed 200 characters"]},city:{type:String,required:[!0,"City is required"],trim:!0,maxlength:[50,"City cannot exceed 50 characters"]},state:{type:String,required:[!0,"State is required"],trim:!0,maxlength:[50,"State cannot exceed 50 characters"]},pincode:{type:String,required:[!0,"Pincode is required"],trim:!0,match:[/^[1-9][0-9]{5}$/,"Please enter a valid pincode"]},country:{type:String,trim:!0,default:"India",maxlength:[50,"Country cannot exceed 50 characters"]}},orderStatus:{type:String,required:[!0,"Order status is required"],enum:["pending","confirmed","processing","shipped","delivered","cancelled"],default:"pending"},paymentStatus:{type:String,required:[!0,"Payment status is required"],enum:["pending","completed","failed","refunded"],default:"pending"},paymentMethod:{type:String,required:[!0,"Payment method is required"],enum:["cod","online","bank_transfer"],default:"cod"},trackingNumber:{type:String,trim:!0,sparse:!0},notes:{type:String,trim:!0,maxlength:[500,"Notes cannot exceed 500 characters"]}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});r.index({customerEmail:1}),r.index({orderStatus:1}),r.index({paymentStatus:1}),r.index({createdAt:-1}),r.index({trackingNumber:1},{sparse:!0}),r.virtual("orderNumber").get(function(){let e=this.createdAt.getFullYear(),t=String(this.createdAt.getMonth()+1).padStart(2,"0"),r=this._id.toString().slice(-6).toUpperCase();return`ORD${e}${t}${r}`}),r.virtual("formattedTotalAmount").get(function(){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR"}).format(this.totalAmount)}),r.virtual("formattedOrderDate").get(function(){return this.createdAt.toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})}),r.virtual("fullShippingAddress").get(function(){let e=this.shippingAddress;return`${e.street}, ${e.city}, ${e.state} - ${e.pincode}, ${e.country}`}),r.virtual("orderStatusDisplayName").get(function(){return({pending:"Pending",confirmed:"Confirmed",processing:"Processing",shipped:"Shipped",delivered:"Delivered",cancelled:"Cancelled"})[this.orderStatus]||this.orderStatus}),r.virtual("paymentStatusDisplayName").get(function(){return({pending:"Pending",completed:"Completed",failed:"Failed",refunded:"Refunded"})[this.paymentStatus]||this.paymentStatus}),r.pre("save",function(e){if(this.products&&this.products.length>0&&(this.totalAmount=this.products.reduce((e,t)=>e+t.price*t.quantity,0)),"shipped"===this.orderStatus&&!this.trackingNumber){let e=Math.random().toString(36).substr(2,10).toUpperCase();this.trackingNumber=`TRK${e}`}this.customerName=this.customerName.replace(/<[^>]*>?/gm,""),this.notes&&(this.notes=this.notes.replace(/<[^>]*>?/gm,"")),e()}),r.statics.getStats=function(){return this.aggregate([{$group:{_id:"$orderStatus",count:{$sum:1},totalAmount:{$sum:"$totalAmount"}}}])},r.statics.getRecent=function(e=10){return this.find().sort({createdAt:-1}).limit(e).select("customerName totalAmount orderStatus paymentStatus createdAt")},r.statics.getByCustomer=function(e){return this.find({customerEmail:e}).sort({createdAt:-1}).select("-customerEmail")},r.statics.updateStatus=function(e,t){return this.findByIdAndUpdate(e,{orderStatus:t},{new:!0})};let a=t.default.models.ProductOrder||t.default.model("ProductOrder",r)},20578,(e,t,r)=>{},3534,e=>{"use strict";e.s(["handler",()=>k,"patchFetch",()=>O,"routeModule",()=>b,"serverHooks",()=>j,"workAsyncStorage",()=>P,"workUnitAsyncStorage",()=>C],3534);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),i=e.i(69741),o=e.i(16795),u=e.i(87718),d=e.i(95169),l=e.i(47587),c=e.i(66012),p=e.i(70101),m=e.i(26937),h=e.i(10372),g=e.i(93695);e.i(52474);var f=e.i(220);e.s(["GET",()=>A,"PATCH",()=>E,"POST",()=>N],26744);var y=e.i(89171),x=e.i(49991),R=e.i(25425),v=e.i(78044);let S=(0,v.createRateLimiter)(9e5,5),w=[{id:"ayur-001",name:"Ayurvedic Immunity Booster",price:299,description:"Natural immunity booster with herbs",image:"/products/immunity-booster.jpg",inStock:!0},{id:"ayur-002",name:"Herbal Digestive Tea",price:199,description:"Digestive tea blend with traditional herbs",image:"/products/digestive-tea.jpg",inStock:!0},{id:"ayur-003",name:"Natural Pain Relief Oil",price:249,description:"Ayurvedic oil for joint and muscle pain",image:"/products/pain-relief-oil.jpg",inStock:!0},{id:"ayur-004",name:"Stress Relief Capsules",price:399,description:"Natural stress relief with ashwagandha",image:"/products/stress-relief.jpg",inStock:!0}];async function N(e){try{let t=e.ip||e.headers.get("x-forwarded-for")||"unknown";if(!S(t))return y.NextResponse.json((0,v.formatErrorResponse)("Too many requests. Please try again later."),{status:429});let r=await e.json(),a=v.productOrderSchema.safeParse(r);if(!a.success)return y.NextResponse.json((0,v.formatErrorResponse)("Validation failed",a.error),{status:400});let n=a.data;await (0,x.default)();let s=[],i=0;for(let e of n.products){let t=w.find(t=>t.id===e.productId);if(!t)return y.NextResponse.json((0,v.formatErrorResponse)(`Product with ID ${e.productId} not found`),{status:400});if(!t.inStock)return y.NextResponse.json((0,v.formatErrorResponse)(`Product ${t.name} is currently out of stock`),{status:400});let r=t.price*e.quantity;i+=r,s.push({productId:t.id,productName:t.name,quantity:e.quantity,price:t.price})}let o=new R.default({customerName:n.customerName,customerEmail:n.customerEmail,customerPhone:n.customerPhone,products:s,totalAmount:i,shippingAddress:n.shippingAddress,paymentMethod:n.paymentMethod,notes:n.notes,orderStatus:"pending",paymentStatus:(n.paymentMethod,"pending")});return await o.save(),y.NextResponse.json((0,v.formatSuccessResponse)({orderId:o._id,orderNumber:o.orderNumber,totalAmount:o.totalAmount,paymentMethod:o.paymentMethod},"Order placed successfully! You will receive a confirmation email shortly."),{status:201})}catch(e){return console.error("Product order creation error:",e),y.NextResponse.json((0,v.formatErrorResponse)("An error occurred while processing your order. Please try again later."),{status:500})}}async function A(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),n=t.get("status"),s=t.get("customerEmail");await (0,x.default)();let i={};n&&(i.orderStatus=n),s&&(i.customerEmail=s);let o=await R.default.countDocuments(i),u=await R.default.find(i).sort({createdAt:-1}).skip((r-1)*a).limit(a).select("customerName customerEmail totalAmount orderStatus paymentStatus createdAt products"),d=await R.default.aggregate([{$group:{_id:"$orderStatus",count:{$sum:1},totalAmount:{$sum:"$totalAmount"}}}]);return y.NextResponse.json((0,v.formatSuccessResponse)({orders:u,stats:d,pagination:{page:r,limit:a,total:o,pages:Math.ceil(o/a)}}),{status:200})}catch(e){return console.error("Orders fetch error:",e),y.NextResponse.json((0,v.formatErrorResponse)("An error occurred while fetching orders."),{status:500})}}async function E(e){try{let{id:t,orderStatus:r,paymentStatus:a,trackingNumber:n}=await e.json();if(!t)return y.NextResponse.json((0,v.formatErrorResponse)("Order ID is required"),{status:400});await (0,x.default)();let s={};r&&(s.orderStatus=r),a&&(s.paymentStatus=a),n&&(s.trackingNumber=n);let i=await R.default.findByIdAndUpdate(t,s,{new:!0});if(!i)return y.NextResponse.json((0,v.formatErrorResponse)("Order not found"),{status:404});return y.NextResponse.json((0,v.formatSuccessResponse)(i,"Order updated successfully"),{status:200})}catch(e){return console.error("Order update error:",e),y.NextResponse.json((0,v.formatErrorResponse)("An error occurred while updating the order."),{status:500})}}var q=e.i(26744);let b=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/products/orders/route",pathname:"/api/products/orders",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/products/orders/route.ts",nextConfigOutput:"",userland:q}),{workAsyncStorage:P,workUnitAsyncStorage:C,serverHooks:j}=b;function O(){return(0,a.patchFetch)({workAsyncStorage:P,workUnitAsyncStorage:C})}async function k(e,t,a){var y;let x="/api/products/orders/route";x=x.replace(/\/index$/,"")||"/";let R=await b.prepare(e,t,{srcPage:x,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:v,params:S,nextConfig:w,isDraftMode:N,prerenderManifest:A,routerServerContext:E,isOnDemandRevalidate:q,revalidateOnlyGenerated:P,resolvedPathname:C}=R,j=(0,i.normalizeAppPath)(x),O=!!(A.dynamicRoutes[j]||A.routes[C]);if(O&&!N){let e=!!A.routes[C],t=A.dynamicRoutes[j];if(t&&!1===t.fallback&&!e)throw new g.NoFallbackError}let k=null;!O||b.isDev||N||(k="/index"===(k=C)?"/":k);let I=!0===b.isDev||!O,$=O&&!I,T=e.method||"GET",_=(0,s.getTracer)(),D=_.getActiveScopeSpan(),M={params:S,prerenderManifest:A,renderOpts:{experimental:{cacheComponents:!!w.experimental.cacheComponents,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:I,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=w.experimental)?void 0:y.cacheLife,isRevalidate:$,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>b.onRequestError(e,t,a,E)},sharedContext:{buildId:v}},U=new o.NodeNextRequest(e),H=new o.NodeNextResponse(t),B=u.NextRequestAdapter.fromNodeNextRequest(U,(0,u.signalFromNodeResponse)(t));try{let i=async r=>b.handle(B,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=_.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${T} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${T} ${e.url}`)}),o=async s=>{var o,u;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&q&&P&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let u=M.renderOpts.pendingWaitUntil;u&&a.waitUntil&&(a.waitUntil(u),u=void 0);let d=M.renderOpts.collectedTags;if(!O)return await (0,c.sendResponse)(U,H,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await b.onRequestError(e,t,{routerKind:"App Router",routePath:x,routeType:"route",revalidateReason:(0,l.getRevalidateReason)({isRevalidate:$,isOnDemandRevalidate:q})},E),t}},g=await b.handleResponse({req:e,nextConfig:w,cacheKey:k,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:A,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:P,responseGenerator:d,waitUntil:a.waitUntil});if(!O)return null;if((null==g||null==(o=g.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==g||null==(u=g.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":g.isMiss?"MISS":g.isStale?"STALE":"HIT"),N&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,p.fromNodeOutgoingHttpHeaders)(g.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&O||y.delete(h.NEXT_CACHE_TAGS_HEADER),!g.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,m.getCacheControlHeader)(g.cacheControl)),await (0,c.sendResponse)(U,H,new Response(g.value.body,{headers:y,status:g.value.status||200})),null};D?await o(D):await _.withPropagatedContext(e.headers,()=>_.trace(d.BaseServerSpan.handleRequest,{spanName:`${T} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":T,"http.target":e.url}},o))}catch(t){if(D||t instanceof g.NoFallbackError||await b.onRequestError(e,t,{routerKind:"App Router",routePath:j,routeType:"route",revalidateReason:(0,l.getRevalidateReason)({isRevalidate:$,isOnDemandRevalidate:q})}),O)throw t;return await (0,c.sendResponse)(U,H,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d2b8d197._.js.map