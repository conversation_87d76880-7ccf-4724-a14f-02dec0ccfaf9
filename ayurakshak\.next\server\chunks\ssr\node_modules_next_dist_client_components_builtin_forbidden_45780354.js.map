{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/client/components/builtin/forbidden.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from '../http-access-fallback/error-fallback'\n\nexport default function Forbidden() {\n  return (\n    <HTTPAccessErrorFallback\n      status={403}\n      message=\"This page could not be accessed.\"\n    />\n  )\n}\n"], "names": ["Forbidden", "HTTPAccessErrorFallback", "status", "message"], "mappings": "sHAEA,UAAA,qCAAwBA,yBAFgB,CAAA,CAAA,IAAA,GAEzB,SAASA,IACtB,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAACC,EADH,AACGA,uBAAuB,CAAA,CACtBC,OAAQ,IACRC,QAAQ,oCAGd", "ignoreList": [0]}