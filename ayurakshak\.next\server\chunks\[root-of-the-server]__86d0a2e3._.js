module.exports=[61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},91614,(e,t,r)=>{},47974,e=>{"use strict";e.s(["handler",()=>k,"patchFetch",()=>E,"routeModule",()=>C,"serverHooks",()=>N,"workAsyncStorage",()=>b,"workUnitAsyncStorage",()=>A],47974);var t=e.i(47909),r=e.i(74017),a=e.i(96250),i=e.i(59756),n=e.i(61916),s=e.i(69741),o=e.i(16795),l=e.i(87718),u=e.i(95169),d=e.i(47587),c=e.i(66012),p=e.i(70101),m=e.i(26937),g=e.i(10372),h=e.i(93695);e.i(52474);var y=e.i(220);e.s(["GET",()=>w],17861);var f=e.i(89171),v=e.i(78044);let x=[{id:"ayur-001",name:"Ayurvedic Immunity Booster",price:299,currency:"INR",description:"Boost your natural immunity with our carefully crafted blend of traditional Ayurvedic herbs including Ashwagandha, Tulsi, and Amla.",images:["/products/immunity-booster.jpg"],category:"Immunity",ingredients:["Ashwagandha","Tulsi","Amla","Giloy","Turmeric"],benefits:["Strengthens immune system","Reduces stress and fatigue","Improves overall vitality","Natural antioxidant properties"],usage:"Take 1-2 capsules daily with warm water after meals",features:["100% Natural","No Side Effects","Clinically Tested","Vegetarian"],inStock:!0,stockQuantity:50,weight:"60 capsules",isActive:!0},{id:"ayur-002",name:"Herbal Digestive Tea",price:199,currency:"INR",description:"A soothing blend of digestive herbs that helps improve digestion and reduces bloating naturally.",images:["/products/digestive-tea.jpg"],category:"Digestive Health",ingredients:["Fennel","Ginger","Mint","Cumin","Coriander"],benefits:["Improves digestion","Reduces bloating and gas","Soothes stomach discomfort","Promotes healthy metabolism"],usage:"Steep 1 tea bag in hot water for 3-5 minutes. Drink after meals.",features:["Caffeine Free","Natural Ingredients","Pleasant Taste","Daily Use"],inStock:!0,stockQuantity:75,weight:"25 tea bags",isActive:!0},{id:"ayur-003",name:"Natural Pain Relief Oil",price:249,currency:"INR",description:"Traditional Ayurvedic oil blend for natural relief from joint and muscle pain.",images:["/products/pain-relief-oil.jpg"],category:"Pain Relief",ingredients:["Eucalyptus","Wintergreen","Camphor","Sesame Oil","Mustard Oil"],benefits:["Relieves joint and muscle pain","Reduces inflammation","Improves blood circulation","Provides warming sensation"],usage:"Gently massage on affected area 2-3 times daily",features:["Fast Acting","Deep Penetration","Non-Greasy","Pleasant Aroma"],inStock:!0,stockQuantity:30,weight:"100ml",isActive:!0},{id:"ayur-004",name:"Stress Relief Capsules",price:399,currency:"INR",description:"Natural stress relief formula with Ashwagandha and other calming herbs to promote mental well-being.",images:["/products/stress-relief.jpg"],category:"Mental Wellness",ingredients:["Ashwagandha","Brahmi","Jatamansi","Shankhpushpi","Tagar"],benefits:["Reduces stress and anxiety","Improves sleep quality","Enhances mental clarity","Balances mood naturally"],usage:"Take 1 capsule twice daily with milk or water",features:["Clinically Proven","Non-Habit Forming","Safe for Long-term Use","Vegetarian"],inStock:!0,stockQuantity:40,weight:"60 capsules",isActive:!0},{id:"ayur-005",name:"Detox Herbal Powder",price:349,currency:"INR",description:"Complete body detox powder made with traditional herbs to cleanse and rejuvenate your system.",images:["/products/detox-powder.jpg"],category:"Detox",ingredients:["Triphala","Neem","Aloe Vera","Guduchi","Punarnava"],benefits:["Cleanses digestive system","Removes toxins naturally","Improves skin health","Boosts energy levels"],usage:"Mix 1 teaspoon with warm water, take on empty stomach",features:["Complete Detox","Natural Cleansing","Improves Metabolism","Organic"],inStock:!0,stockQuantity:25,weight:"200g powder",isActive:!0},{id:"ayur-006",name:"Hair Growth Oil",price:279,currency:"INR",description:"Nourishing hair oil with traditional herbs to promote healthy hair growth and prevent hair fall.",images:["/products/hair-oil.jpg"],category:"Hair Care",ingredients:["Bhringraj","Amla","Fenugreek","Coconut Oil","Curry Leaves"],benefits:["Promotes hair growth","Reduces hair fall","Nourishes scalp","Adds natural shine"],usage:"Massage gently into scalp, leave for 2 hours or overnight, then wash",features:["Chemical Free","Suitable for All Hair Types","Natural Fragrance","Deep Nourishment"],inStock:!0,stockQuantity:35,weight:"200ml",isActive:!0}];async function w(e){try{let{searchParams:t}=new URL(e.url),r=t.get("category"),a=t.get("search"),i=t.get("inStock"),n=t.get("id");if(n){let e=x.find(e=>e.id===n);if(!e)return f.NextResponse.json((0,v.formatErrorResponse)("Product not found"),{status:404});return f.NextResponse.json((0,v.formatSuccessResponse)(e),{status:200})}let s=x.filter(e=>e.isActive);if(r&&(s=s.filter(e=>e.category.toLowerCase()===r.toLowerCase())),a){let e=a.toLowerCase();s=s.filter(t=>t.name.toLowerCase().includes(e)||t.description.toLowerCase().includes(e)||t.category.toLowerCase().includes(e)||t.ingredients.some(t=>t.toLowerCase().includes(e)))}"true"===i&&(s=s.filter(e=>e.inStock));let o=[...new Set(x.map(e=>e.category))];return f.NextResponse.json((0,v.formatSuccessResponse)({products:s,categories:o,total:s.length}),{status:200})}catch(e){return console.error("Products fetch error:",e),f.NextResponse.json((0,v.formatErrorResponse)("An error occurred while fetching products."),{status:500})}}var R=e.i(17861);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/products/route.ts",nextConfigOutput:"",userland:R}),{workAsyncStorage:b,workUnitAsyncStorage:A,serverHooks:N}=C;function E(){return(0,a.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:A})}async function k(e,t,a){var f;let v="/api/products/route";v=v.replace(/\/index$/,"")||"/";let x=await C.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!x)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:R,nextConfig:b,isDraftMode:A,prerenderManifest:N,routerServerContext:E,isOnDemandRevalidate:k,revalidateOnlyGenerated:S,resolvedPathname:j}=x,P=(0,s.normalizeAppPath)(v),T=!!(N.dynamicRoutes[P]||N.routes[j]);if(T&&!A){let e=!!N.routes[j],t=N.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let I=null;!T||C.isDev||A||(I="/index"===(I=j)?"/":I);let O=!0===C.isDev||!T,H=T&&!O,q=e.method||"GET",D=(0,n.getTracer)(),M=D.getActiveScopeSpan(),_={params:R,prerenderManifest:N,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,i.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=b.experimental)?void 0:f.cacheLife,isRevalidate:H,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>C.onRequestError(e,t,a,E)},sharedContext:{buildId:w}},U=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),L=l.NextRequestAdapter.fromNodeNextRequest(U,(0,l.signalFromNodeResponse)(t));try{let s=async r=>C.handle(L,_).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=D.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let i=a.get("next.route");if(i){let e=`${q} ${i}`;r.setAttributes({"next.route":i,"http.route":i,"next.span_name":e}),r.updateName(e)}else r.updateName(`${q} ${e.url}`)}),o=async n=>{var o,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,i.getRequestMeta)(e,"minimalMode")&&k&&S&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await s(n);e.fetchMetrics=_.renderOpts.fetchMetrics;let l=_.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let u=_.renderOpts.collectedTags;if(!T)return await (0,c.sendResponse)(U,F,o,_.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(o.headers);u&&(t[g.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==_.renderOpts.collectedRevalidate&&!(_.renderOpts.collectedRevalidate>=g.INFINITE_CACHE)&&_.renderOpts.collectedRevalidate,a=void 0===_.renderOpts.collectedExpire||_.renderOpts.collectedExpire>=g.INFINITE_CACHE?void 0:_.renderOpts.collectedExpire;return{value:{kind:y.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:H,isOnDemandRevalidate:k})},E),t}},h=await C.handleResponse({req:e,nextConfig:b,cacheKey:I,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:N,isRoutePPREnabled:!1,isOnDemandRevalidate:k,revalidateOnlyGenerated:S,responseGenerator:u,waitUntil:a.waitUntil});if(!T)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==y.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,i.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",k?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,p.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,i.getRequestMeta)(e,"minimalMode")&&T||f.delete(g.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,c.sendResponse)(U,F,new Response(h.value.body,{headers:f,status:h.value.status||200})),null};M?await o(M):await D.withPropagatedContext(e.headers,()=>D.trace(u.BaseServerSpan.handleRequest,{spanName:`${q} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":q,"http.target":e.url}},o))}catch(t){if(M||t instanceof h.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:H,isOnDemandRevalidate:k})}),T)throw t;return await (0,c.sendResponse)(U,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__86d0a2e3._.js.map