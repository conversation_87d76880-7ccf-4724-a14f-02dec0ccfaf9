/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10.88 21.94 15.46 14", key: "xkve6t" }],
  ["path", { d: "M21.17 8H12", key: "19dcdn" }],
  ["path", { d: "M3.95 6.06 8.54 14", key: "g8jz9m" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["circle", { cx: "12", cy: "12", r: "4", key: "4exip2" }]
];
const Chromium = createLucideIcon("chromium", __iconNode);

export { __iconNode, Chromium as default };
//# sourceMappingURL=chromium.js.map
