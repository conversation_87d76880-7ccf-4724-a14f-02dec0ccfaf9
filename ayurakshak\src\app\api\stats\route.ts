import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import ContactForm from '@/lib/models/Contact';
import Newsletter from '@/lib/models/Newsletter';
import Donation from '@/lib/models/Donation';
import ProductOrder from '@/lib/models/ProductOrder';
import { formatSuccessResponse, formatErrorResponse } from '@/utils/validation';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get current date for time-based queries
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);

    // Parallel queries for better performance
    const [
      totalDonations,
      totalDonors,
      recentDonations,
      donationsByPurpose,
      totalNewsletterSubscribers,
      recentSubscribers,
      totalContactForms,
      unreadContactForms,
      recentContactForms,
      totalOrders,
      recentOrders,
      ordersByStatus,
    ] = await Promise.all([
      // Donation statistics
      Donation.aggregate([
        { $match: { paymentStatus: 'completed' } },
        { $group: { _id: null, total: { $sum: '$amount' }, count: { $sum: 1 } } },
      ]),
      
      // Unique donors count
      Donation.distinct('donorEmail', { paymentStatus: 'completed' }),
      
      // Recent donations (last 30 days)
      Donation.countDocuments({
        paymentStatus: 'completed',
        createdAt: { $gte: thirtyDaysAgo },
      }),
      
      // Donations by purpose
      Donation.aggregate([
        { $match: { paymentStatus: 'completed' } },
        {
          $group: {
            _id: '$purpose',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 },
          },
        },
      ]),
      
      // Newsletter statistics
      Newsletter.countDocuments({ isActive: true }),
      
      // Recent newsletter subscribers (last 30 days)
      Newsletter.countDocuments({
        isActive: true,
        subscribedAt: { $gte: thirtyDaysAgo },
      }),
      
      // Contact form statistics
      ContactForm.countDocuments(),
      
      // Unread contact forms
      ContactForm.countDocuments({ isRead: false }),
      
      // Recent contact forms (last 30 days)
      ContactForm.countDocuments({
        createdAt: { $gte: thirtyDaysAgo },
      }),
      
      // Order statistics
      ProductOrder.countDocuments(),
      
      // Recent orders (last 30 days)
      ProductOrder.countDocuments({
        createdAt: { $gte: thirtyDaysAgo },
      }),
      
      // Orders by status
      ProductOrder.aggregate([
        {
          $group: {
            _id: '$orderStatus',
            count: { $sum: 1 },
            totalAmount: { $sum: '$totalAmount' },
          },
        },
      ]),
    ]);

    // Calculate growth rates (comparing last 30 days to previous 30 days)
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
    
    const [
      previousDonations,
      previousSubscribers,
      previousContacts,
      previousOrders,
    ] = await Promise.all([
      Donation.countDocuments({
        paymentStatus: 'completed',
        createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },
      }),
      
      Newsletter.countDocuments({
        isActive: true,
        subscribedAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },
      }),
      
      ContactForm.countDocuments({
        createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },
      }),
      
      ProductOrder.countDocuments({
        createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },
      }),
    ]);

    // Calculate growth percentages
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return Math.round(((current - previous) / previous) * 100);
    };

    // Format the response
    const stats = {
      donations: {
        total: totalDonations[0]?.total || 0,
        count: totalDonations[0]?.count || 0,
        uniqueDonors: totalDonors.length,
        recent: recentDonations,
        growth: calculateGrowth(recentDonations, previousDonations),
        byPurpose: donationsByPurpose.map(item => ({
          purpose: item._id,
          amount: item.totalAmount,
          count: item.count,
          percentage: totalDonations[0]?.total 
            ? Math.round((item.totalAmount / totalDonations[0].total) * 100)
            : 0,
        })),
      },
      
      newsletter: {
        totalSubscribers: totalNewsletterSubscribers,
        recentSubscribers: recentSubscribers,
        growth: calculateGrowth(recentSubscribers, previousSubscribers),
      },
      
      contacts: {
        total: totalContactForms,
        unread: unreadContactForms,
        recent: recentContactForms,
        growth: calculateGrowth(recentContactForms, previousContacts),
      },
      
      orders: {
        total: totalOrders,
        recent: recentOrders,
        growth: calculateGrowth(recentOrders, previousOrders),
        byStatus: ordersByStatus.map(item => ({
          status: item._id,
          count: item.count,
          totalAmount: item.totalAmount,
        })),
      },
      
      // Impact metrics (these would be manually updated or calculated based on programs)
      impact: {
        livesImpacted: 15000, // This would come from program data
        villagesReached: 250,
        healthCamps: 180,
        activePrograms: 12,
        volunteers: 85,
      },
      
      // Recent activity summary
      recentActivity: {
        donations: recentDonations,
        subscribers: recentSubscribers,
        contacts: recentContactForms,
        orders: recentOrders,
      },
      
      // Performance metrics
      performance: {
        donationGrowth: calculateGrowth(recentDonations, previousDonations),
        subscriberGrowth: calculateGrowth(recentSubscribers, previousSubscribers),
        contactGrowth: calculateGrowth(recentContactForms, previousContacts),
        orderGrowth: calculateGrowth(recentOrders, previousOrders),
      },
    };

    return NextResponse.json(
      formatSuccessResponse(stats, 'Statistics retrieved successfully'),
      { status: 200 }
    );

  } catch (error) {
    console.error('Statistics fetch error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while fetching statistics.'),
      { status: 500 }
    );
  }
}
