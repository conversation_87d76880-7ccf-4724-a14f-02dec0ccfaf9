import mongoose, { Schema } from 'mongoose';
import { IProductOrder } from '@/types';

const ProductOrderSchema = new Schema<IProductOrder>(
  {
    customerName: {
      type: String,
      required: [true, 'Customer name is required'],
      trim: true,
      maxlength: [100, 'Name cannot exceed 100 characters'],
    },
    customerEmail: {
      type: String,
      required: [true, 'Email is required'],
      trim: true,
      lowercase: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email address',
      ],
    },
    customerPhone: {
      type: String,
      required: [true, 'Phone number is required'],
      trim: true,
      match: [
        /^[\+]?[1-9][\d]{0,15}$/,
        'Please enter a valid phone number',
      ],
    },
    products: [
      {
        productId: {
          type: String,
          required: [true, 'Product ID is required'],
        },
        productName: {
          type: String,
          required: [true, 'Product name is required'],
          trim: true,
        },
        quantity: {
          type: Number,
          required: [true, 'Quantity is required'],
          min: [1, 'Quantity must be at least 1'],
          max: [100, 'Quantity cannot exceed 100'],
        },
        price: {
          type: Number,
          required: [true, 'Price is required'],
          min: [0, 'Price cannot be negative'],
        },
      },
    ],
    totalAmount: {
      type: Number,
      required: [true, 'Total amount is required'],
      min: [0, 'Total amount cannot be negative'],
    },
    shippingAddress: {
      street: {
        type: String,
        required: [true, 'Street address is required'],
        trim: true,
        maxlength: [200, 'Street address cannot exceed 200 characters'],
      },
      city: {
        type: String,
        required: [true, 'City is required'],
        trim: true,
        maxlength: [50, 'City cannot exceed 50 characters'],
      },
      state: {
        type: String,
        required: [true, 'State is required'],
        trim: true,
        maxlength: [50, 'State cannot exceed 50 characters'],
      },
      pincode: {
        type: String,
        required: [true, 'Pincode is required'],
        trim: true,
        match: [/^[1-9][0-9]{5}$/, 'Please enter a valid pincode'],
      },
      country: {
        type: String,
        trim: true,
        default: 'India',
        maxlength: [50, 'Country cannot exceed 50 characters'],
      },
    },
    orderStatus: {
      type: String,
      required: [true, 'Order status is required'],
      enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'],
      default: 'pending',
    },
    paymentStatus: {
      type: String,
      required: [true, 'Payment status is required'],
      enum: ['pending', 'completed', 'failed', 'refunded'],
      default: 'pending',
    },
    paymentMethod: {
      type: String,
      required: [true, 'Payment method is required'],
      enum: ['cod', 'online', 'bank_transfer'],
      default: 'cod',
    },
    trackingNumber: {
      type: String,
      trim: true,
      sparse: true,
    },
    notes: {
      type: String,
      trim: true,
      maxlength: [500, 'Notes cannot exceed 500 characters'],
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better query performance
ProductOrderSchema.index({ customerEmail: 1 });
ProductOrderSchema.index({ orderStatus: 1 });
ProductOrderSchema.index({ paymentStatus: 1 });
ProductOrderSchema.index({ createdAt: -1 });
ProductOrderSchema.index({ trackingNumber: 1 }, { sparse: true });

// Virtual for order number
ProductOrderSchema.virtual('orderNumber').get(function () {
  const year = this.createdAt.getFullYear();
  const month = String(this.createdAt.getMonth() + 1).padStart(2, '0');
  const id = this._id.toString().slice(-6).toUpperCase();
  return `ORD${year}${month}${id}`;
});

// Virtual for formatted total amount
ProductOrderSchema.virtual('formattedTotalAmount').get(function () {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
  }).format(this.totalAmount);
});

// Virtual for formatted order date
ProductOrderSchema.virtual('formattedOrderDate').get(function () {
  return this.createdAt.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
});

// Virtual for full shipping address
ProductOrderSchema.virtual('fullShippingAddress').get(function () {
  const addr = this.shippingAddress;
  return `${addr.street}, ${addr.city}, ${addr.state} - ${addr.pincode}, ${addr.country}`;
});

// Virtual for order status display name
ProductOrderSchema.virtual('orderStatusDisplayName').get(function () {
  const statusMap = {
    pending: 'Pending',
    confirmed: 'Confirmed',
    processing: 'Processing',
    shipped: 'Shipped',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
  };
  return statusMap[this.orderStatus] || this.orderStatus;
});

// Virtual for payment status display name
ProductOrderSchema.virtual('paymentStatusDisplayName').get(function () {
  const statusMap = {
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed',
    refunded: 'Refunded',
  };
  return statusMap[this.paymentStatus] || this.paymentStatus;
});

// Pre-save middleware
ProductOrderSchema.pre('save', function (next) {
  // Calculate total amount from products
  if (this.products && this.products.length > 0) {
    this.totalAmount = this.products.reduce((total, product) => {
      return total + (product.price * product.quantity);
    }, 0);
  }
  
  // Generate tracking number when order is shipped
  if (this.orderStatus === 'shipped' && !this.trackingNumber) {
    const random = Math.random().toString(36).substr(2, 10).toUpperCase();
    this.trackingNumber = `TRK${random}`;
  }
  
  // Sanitize text fields
  this.customerName = this.customerName.replace(/<[^>]*>?/gm, '');
  if (this.notes) {
    this.notes = this.notes.replace(/<[^>]*>?/gm, '');
  }
  
  next();
});

// Static method to get order statistics
ProductOrderSchema.statics.getStats = function () {
  return this.aggregate([
    {
      $group: {
        _id: '$orderStatus',
        count: { $sum: 1 },
        totalAmount: { $sum: '$totalAmount' },
      },
    },
  ]);
};

// Static method to get recent orders
ProductOrderSchema.statics.getRecent = function (limit: number = 10) {
  return this.find()
    .sort({ createdAt: -1 })
    .limit(limit)
    .select('customerName totalAmount orderStatus paymentStatus createdAt');
};

// Static method to get orders by customer
ProductOrderSchema.statics.getByCustomer = function (email: string) {
  return this.find({ customerEmail: email })
    .sort({ createdAt: -1 })
    .select('-customerEmail');
};

// Static method to update order status
ProductOrderSchema.statics.updateStatus = function (orderId: string, status: string) {
  return this.findByIdAndUpdate(
    orderId,
    { orderStatus: status },
    { new: true }
  );
};

const ProductOrder = mongoose.models.ProductOrder || mongoose.model<IProductOrder>('ProductOrder', ProductOrderSchema);

export default ProductOrder;
