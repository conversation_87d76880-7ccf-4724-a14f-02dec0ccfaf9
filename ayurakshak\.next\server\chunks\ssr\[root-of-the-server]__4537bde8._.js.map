{"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_c15e96cb.module.css [app-rsc] (css module)", "turbopack:///[next]/internal/font/google/poppins_b6769e12.module.css [app-rsc] (css module)", "turbopack:///[next]/internal/font/google/inter_c15e96cb.js", "turbopack:///[next]/internal/font/google/poppins_b6769e12.js", "turbopack:///[project]/src/app/layout.tsx"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_c15e96cb-module__0bjUvq__className\",\n  \"variable\": \"inter_c15e96cb-module__0bjUvq__variable\",\n});\n", "__turbopack_context__.v({\n  \"className\": \"poppins_b6769e12-module__2yJm1a__className\",\n  \"variable\": \"poppins_b6769e12-module__2yJm1a__variable\",\n});\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22,%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-poppins%22,%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22],%22display%22:%22swap%22}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import type { Metadata } from \"next\";\nimport { Inter, Poppins } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n  display: \"swap\",\n});\n\nconst poppins = Poppins({\n  subsets: [\"latin\"],\n  variable: \"--font-poppins\",\n  weight: [\"300\", \"400\", \"500\", \"600\", \"700\", \"800\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: {\n    default: \"Ayurakshak - Care · Restore · Protect\",\n    template: \"%s | Ayurakshak\",\n  },\n  description:\n    \"Ayurakshak combines traditional Ayurveda with modern outreach — health camps, sustainable livelihoods, and green initiatives across India. Join us in healing communities with nature-led care.\",\n  keywords: [\n    \"Ayurakshak\",\n    \"Ayurveda\",\n    \"NGO\",\n    \"Healthcare\",\n    \"Traditional Medicine\",\n    \"Health Camps\",\n    \"Natural Healing\",\n    \"Community Health\",\n    \"Herbal Products\",\n    \"Sustainable Livelihoods\",\n    \"India\",\n    \"Naturopathy\",\n  ],\n  authors: [\n    {\n      name: \"Ayurakshak Team\",\n      url: \"https://ayurakshak.org\",\n    },\n    {\n      name: \"<PERSON><PERSON>\",\n      url: \"https://kush-personal-portfolio-my-portfolio.vercel.app/\",\n    },\n  ],\n  creator: \"Kush Vardhan\",\n  publisher: \"Ayurakshak\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL(\n    process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3000\"\n  ),\n  alternates: {\n    canonical: \"/\",\n  },\n  openGraph: {\n    type: \"website\",\n    locale: \"en_IN\",\n    url: \"/\",\n    title: \"Ayurakshak - Care · Restore · Protect\",\n    description:\n      \"Healing communities with traditional Ayurveda and modern outreach across India.\",\n    siteName: \"Ayurakshak\",\n    images: [\n      {\n        url: \"/logo.jpeg\",\n        width: 1200,\n        height: 630,\n        alt: \"Ayurakshak - Traditional Ayurveda meets modern healthcare\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Ayurakshak - Care · Restore · Protect\",\n    description:\n      \"Healing communities with traditional Ayurveda and modern outreach across India.\",\n    images: [\"/logo.jpeg\"],\n    creator: \"@ayurakshak\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  verification: {\n    google: \"your-google-verification-code\",\n    yandex: \"your-yandex-verification-code\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"scroll-smooth\">\n      <head>\n        <link rel=\"icon\" href=\"/logo.jpeg\" sizes=\"any\" />\n        <link rel=\"apple-touch-icon\" href=\"/logo.jpeg\" />\n        <meta name=\"theme-color\" content=\"#4a7c59\" />\n        <meta name=\"msapplication-TileColor\" content=\"#4a7c59\" />\n        <meta\n          name=\"viewport\"\n          content=\"width=device-width, initial-scale=1, maximum-scale=5\"\n        />\n      </head>\n      <body\n        className={`${inter.variable} ${poppins.variable} font-sans antialiased bg-white text-gray-900`}\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "0BAAA,EAAA,CAAA,CAAA,CACA,UAAA,2CACA,SAAA,yCACA,cCHA,EAAA,CAAA,CAAA,CACA,UAAA,6CACA,SAAA,2CACA,yFCHA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,4BACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECX1C,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,gCACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECMnC,IAAM,EAAqB,CAChC,MAAO,CACL,QAAS,wCACT,SAAU,iBACZ,EACA,YACE,kMACF,SAAU,CACR,aACA,WACA,MACA,aACA,uBACA,eACA,kBACA,mBACA,kBACA,0BACA,QACA,cACD,CACD,QAAS,CACP,CACE,KAAM,kBACN,IAAK,wBACP,EACA,CACE,KAAM,eACN,IAAK,0DACP,EACD,CACD,QAAS,eACT,UAAW,aACX,gBAAiB,CACf,OAAO,EACP,QAAS,GACT,WAAW,CACb,EACA,aAAc,IAAI,IAChB,yBAEF,GAFqC,QAEzB,CACV,UAAW,GACb,EACA,UAAW,CACT,KAAM,UACN,OAAQ,QACR,IAAK,IACL,MAAO,wCACP,YACE,kFACF,SAAU,aACV,OAAQ,CACN,CACE,IAAK,aACL,MAAO,KACP,OAAQ,IACR,IAAK,2DACP,EACD,AACH,EACA,QAAS,CACP,KAAM,sBACN,MAAO,wCACP,YACE,kFACF,OAAQ,CAAC,aAAa,CACtB,QAAS,aACX,EACA,OAAQ,CACN,OAAO,EACP,QAAQ,EACR,UAAW,CACT,OAAO,EACP,QAAQ,EACR,oBAAqB,CAAC,EACtB,oBAAqB,QACrB,cAAe,CAAC,CAClB,CACF,EACA,aAAc,CACZ,OAAQ,gCACR,OAAQ,+BACV,CACF,EAEe,SAAS,EAAW,UACjC,CAAQ,CAGR,EACA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,KAAK,KAAK,UAAU,0BACxB,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,OAAO,KAAK,aAAa,MAAM,QACzC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,IAAI,mBAAmB,KAAK,eAClC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,cAAc,QAAQ,YACjC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,0BAA0B,QAAQ,YAC7C,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,KAAK,WACL,QAAQ,4DAGZ,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,UAAW,CAAA,EF3GJ,AE2GO,EAAM,QAAQ,CAAC,CAAC,ED3GvB,AC2GyB,EAAQ,QAAQ,CAAC,6CAA6C,CAAC,UAE9F,MAIT", "ignoreList": [0, 1, 2, 3]}