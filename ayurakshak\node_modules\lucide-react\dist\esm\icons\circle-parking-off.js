/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12.656 7H13a3 3 0 0 1 2.984 3.307", key: "1sjx87" }],
  ["path", { d: "M13 13H9", key: "e2beee" }],
  ["path", { d: "M19.071 19.071A1 1 0 0 1 4.93 4.93", key: "1kb595" }],
  ["path", { d: "m2 2 20 20", key: "1ooewy" }],
  ["path", { d: "M8.357 2.687a10 10 0 0 1 12.956 12.956", key: "5bsfdx" }],
  ["path", { d: "M9 17V9", key: "ojradj" }]
];
const CircleParkingOff = createLucideIcon("circle-parking-off", __iconNode);

export { __iconNode, CircleParkingOff as default };
//# sourceMappingURL=circle-parking-off.js.map
