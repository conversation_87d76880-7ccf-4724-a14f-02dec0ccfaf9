module.exports=[4276,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(11857);a.n(d("[project]/node_modules/next/dist/client/components/builtin/global-error.js <module evaluation>"))},82509,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(11857);a.n(d("[project]/node_modules/next/dist/client/components/builtin/global-error.js"))},66114,a=>{"use strict";a.i(4276);var b=a.i(82509);a.n(b)}];

//# sourceMappingURL=node_modules_next_dist_client_components_builtin_global-error_ece394eb.js.map