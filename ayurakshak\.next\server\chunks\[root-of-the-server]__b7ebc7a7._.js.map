{"version": 3, "sources": ["turbopack:///[project]/src/lib/mongodb.ts", "turbopack:///[project]/src/lib/models/Contact.ts", "turbopack:///[project]/src/app/api/contact/route.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI!, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n\n// Type declaration for global mongoose\ndeclare global {\n  var mongoose: {\n    conn: typeof mongoose | null;\n    promise: Promise<typeof mongoose> | null;\n  };\n}\n", "import mongoose, { Schema } from 'mongoose';\nimport { IContactForm } from '@/types';\n\nconst ContactFormSchema = new Schema<IContactForm>(\n  {\n    name: {\n      type: String,\n      required: [true, 'Name is required'],\n      trim: true,\n      maxlength: [100, 'Name cannot exceed 100 characters'],\n    },\n    email: {\n      type: String,\n      required: [true, 'Email is required'],\n      trim: true,\n      lowercase: true,\n      match: [\n        /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n        'Please enter a valid email address',\n      ],\n    },\n    phone: {\n      type: String,\n      trim: true,\n      match: [\n        /^[\\+]?[1-9][\\d]{0,15}$/,\n        'Please enter a valid phone number',\n      ],\n    },\n    subject: {\n      type: String,\n      required: [true, 'Subject is required'],\n      trim: true,\n      maxlength: [200, 'Subject cannot exceed 200 characters'],\n    },\n    message: {\n      type: String,\n      required: [true, 'Message is required'],\n      trim: true,\n      maxlength: [2000, 'Message cannot exceed 2000 characters'],\n    },\n    isRead: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  {\n    timestamps: true,\n    toJSON: { virtuals: true },\n    toObject: { virtuals: true },\n  }\n);\n\n// Indexes for better query performance\nContactFormSchema.index({ email: 1 });\nContactFormSchema.index({ createdAt: -1 });\nContactFormSchema.index({ isRead: 1 });\n\n// Virtual for formatted creation date\nContactFormSchema.virtual('formattedDate').get(function () {\n  return this.createdAt.toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n});\n\n// Pre-save middleware to sanitize data\nContactFormSchema.pre('save', function (next) {\n  // Remove any HTML tags from text fields for security\n  this.name = this.name.replace(/<[^>]*>?/gm, '');\n  this.subject = this.subject.replace(/<[^>]*>?/gm, '');\n  this.message = this.message.replace(/<[^>]*>?/gm, '');\n  \n  next();\n});\n\n// Static method to get unread count\nContactFormSchema.statics.getUnreadCount = function () {\n  return this.countDocuments({ isRead: false });\n};\n\n// Static method to mark as read\nContactFormSchema.statics.markAsRead = function (id: string) {\n  return this.findByIdAndUpdate(id, { isRead: true }, { new: true });\n};\n\n// Static method to get recent contacts\nContactFormSchema.statics.getRecent = function (limit: number = 10) {\n  return this.find()\n    .sort({ createdAt: -1 })\n    .limit(limit)\n    .select('name email subject createdAt isRead');\n};\n\nconst ContactForm = mongoose.models.ContactForm || mongoose.model<IContactForm>('ContactForm', ContactFormSchema);\n\nexport default ContactForm;\n", "import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport ContactForm from '@/lib/models/Contact';\nimport { contactFormSchema } from '@/utils/validation';\nimport { formatSuccessResponse, formatErrorResponse, createRateLimiter } from '@/utils/validation';\nimport { z } from 'zod';\n\n// Rate limiter: 5 requests per 15 minutes per IP\nconst rateLimiter = createRateLimiter(15 * 60 * 1000, 5);\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Rate limiting\n    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';\n    if (!rateLimiter(clientIP)) {\n      return NextResponse.json(\n        formatErrorResponse('Too many requests. Please try again later.'),\n        { status: 429 }\n      );\n    }\n\n    // Parse request body\n    const body = await request.json();\n\n    // Validate input data\n    const validationResult = contactFormSchema.safeParse(body);\n    if (!validationResult.success) {\n      return NextResponse.json(\n        formatErrorResponse('Validation failed', validationResult.error.errors),\n        { status: 400 }\n      );\n    }\n\n    const { name, email, phone, subject, message } = validationResult.data;\n\n    // Connect to database\n    await connectDB();\n\n    // Check for duplicate submissions (same email and subject within 1 hour)\n    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);\n    const existingSubmission = await ContactForm.findOne({\n      email,\n      subject,\n      createdAt: { $gte: oneHourAgo },\n    });\n\n    if (existingSubmission) {\n      return NextResponse.json(\n        formatErrorResponse('You have already submitted a similar message recently. Please wait before submitting again.'),\n        { status: 409 }\n      );\n    }\n\n    // Create new contact form submission\n    const contactSubmission = new ContactForm({\n      name,\n      email,\n      phone,\n      subject,\n      message,\n    });\n\n    await contactSubmission.save();\n\n    // TODO: Send email notification to admin\n    // TODO: Send auto-reply email to user\n\n    return NextResponse.json(\n      formatSuccessResponse(\n        { id: contactSubmission._id },\n        'Thank you for your message! We will get back to you soon.'\n      ),\n      { status: 201 }\n    );\n\n  } catch (error) {\n    console.error('Contact form submission error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while processing your request. Please try again later.'),\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // This endpoint is for admin use only - add authentication here\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const isRead = searchParams.get('isRead');\n\n    await connectDB();\n\n    // Build query\n    const query: any = {};\n    if (isRead !== null) {\n      query.isRead = isRead === 'true';\n    }\n\n    // Get total count\n    const total = await ContactForm.countDocuments(query);\n\n    // Get paginated results\n    const contacts = await ContactForm.find(query)\n      .sort({ createdAt: -1 })\n      .skip((page - 1) * limit)\n      .limit(limit)\n      .select('name email subject createdAt isRead');\n\n    return NextResponse.json(\n      formatSuccessResponse({\n        contacts,\n        pagination: {\n          page,\n          limit,\n          total,\n          pages: Math.ceil(total / limit),\n        },\n      }),\n      { status: 200 }\n    );\n\n  } catch (error) {\n    console.error('Contact form fetch error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while fetching contact forms.'),\n      { status: 500 }\n    );\n  }\n}\n\nexport async function PATCH(request: NextRequest) {\n  try {\n    // This endpoint is for admin use only - add authentication here\n    const body = await request.json();\n    const { id, isRead } = body;\n\n    if (!id || typeof isRead !== 'boolean') {\n      return NextResponse.json(\n        formatErrorResponse('Invalid request data'),\n        { status: 400 }\n      );\n    }\n\n    await connectDB();\n\n    const updatedContact = await ContactForm.findByIdAndUpdate(\n      id,\n      { isRead },\n      { new: true }\n    );\n\n    if (!updatedContact) {\n      return NextResponse.json(\n        formatErrorResponse('Contact form not found'),\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json(\n      formatSuccessResponse(updatedContact, 'Contact form updated successfully'),\n      { status: 200 }\n    );\n\n  } catch (error) {\n    console.error('Contact form update error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while updating the contact form.'),\n      { status: 500 }\n    );\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/contact/route\",\n        pathname: \"/api/contact\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/contact/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/contact/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "u6CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAc,QAAQ,GAAG,CAAC,WAAW,CAE3C,GAAI,CAAC,EACH,MAAU,AAAJ,KADU,CAEd,kEASJ,IAAI,EAAS,EAAA,CAAA,CAAO,GAAP,KAAe,AAExB,CAAC,IACH,EAAS,EADE,AACF,CAAA,CAAO,GAAP,KAAe,CAAG,CAAE,KAAM,KAAM,QAAS,IAAK,SAGzD,eAAe,EACb,GAAI,EAAO,IAAI,CACb,CADe,GAwBJ,GAvBJ,EAAO,IAAI,CAGf,EAAO,OAAO,EAAE,CAKnB,EAAO,OAAO,CAAG,EAAA,OAAQ,CAAC,OAAO,CAAC,EAJrB,CACX,UAG8C,MAH9B,CAClB,GAEsD,IAAI,CAAC,AAAC,GACnD,EACT,EAGF,GAAI,CACF,EAAO,IAAI,CAAG,MAAM,EAAO,OAAO,AACpC,CAAE,MAAO,EAAG,CAEV,MADA,EAAO,OAAO,CAAG,KACX,CACR,CAEA,OAAO,EAAO,IAAI,AACpB,iDC5CA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAoB,IAAI,EAAA,MAAM,CAClC,CACE,KAAM,CACJ,KAAM,OACN,SAAU,EAAC,EAAM,mBAAmB,CACpC,KAAM,GACN,UAAW,CAAC,IAAK,oCAAoC,AACvD,EACA,MAAO,CACL,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,MAAM,EACN,WAAW,EACX,MAAO,CACL,8CACA,qCAEJ,AADG,EAEH,MAAO,CACL,KAAM,OACN,MAAM,EACN,MAAO,CACL,yBACA,oCACD,AACH,EACA,QAAS,CACP,KAAM,OACN,SAAU,EAAC,EAAM,sBAAsB,CACvC,MAAM,EACN,UAAW,CAAC,IAAK,uCACnB,AAD0D,EAE1D,QAAS,CACP,KAAM,OACN,SAAU,EAAC,EAAM,sBAAsB,CACvC,MAAM,EACN,UAAW,CAAC,IAAM,wCAAwC,AAC5D,EACA,OAAQ,CACN,KAAM,QACN,SAAS,CACX,CACF,EACA,CACE,YAAY,EACZ,OAAQ,CAAE,UAAU,CAAK,EACzB,SAAU,CAAE,SAAU,EAAK,CAC7B,GAIF,EAAkB,KAAK,CAAC,CAAE,MAAO,CAAE,GACnC,EAAkB,KAAK,CAAC,CAAE,UAAW,CAAC,CAAE,GACxC,EAAkB,KAAK,CAAC,CAAE,OAAQ,CAAE,GAGpC,EAAkB,OAAO,CAAC,iBAAiB,GAAG,CAAC,WAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAS,CAChD,KAAM,UACN,MAAO,OACP,IAAK,UACL,KAAM,UACN,OAAQ,SACV,EACF,GAGA,EAAkB,GAAG,CAAC,OAAQ,SAAU,CAAI,EAE1C,IAAI,CAAC,IAAI,CAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAc,IAC5C,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAc,IAClD,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAc,IAElD,GACF,GAGA,EAAkB,OAAO,CAAC,cAAc,CAAG,WACzC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAE,QAAQ,CAAM,EAC7C,EAGA,EAAkB,OAAO,CAAC,UAAU,CAAG,SAAU,CAAU,EACzD,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAI,CAAE,QAAQ,CAAK,EAAG,CAAE,KAAK,CAAK,EAClE,EAGA,EAAkB,OAAO,CAAC,SAAS,CAAG,SAAU,EAAgB,EAAE,EAChE,OAAO,IAAI,CAAC,IAAI,GACb,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,KAAK,CAAC,GACN,MAAM,CAAC,sCACZ,QAEoB,EAAA,OAAQ,CAAC,MAAM,CAAC,WAAW,EAAI,CAEpC,CAFoC,OAAQ,CAAC,KAAK,CAAe,cAAe,2LEjG/F,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,yDDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAKA,IAAM,EAAc,CAAA,EAAA,EAAA,iBAAA,AAAiB,EAAC,IAAgB,CAAX,EAEpC,GAFyC,YAE1B,EAAK,CAAoB,EAC7C,GAAI,CAEF,IAAM,EAAW,EAAQ,EAAE,EAAI,EAAQ,OAAO,CAAC,GAAG,CAAC,oBAAsB,UACzE,GAAI,CAAC,EAAY,GACf,OAAO,CADmB,CACnB,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAmB,AAAnB,EAAoB,8CACpB,CAAE,OAAQ,GAAI,GAKlB,IAAM,EAAO,MAAM,EAAQ,IAAI,GAGzB,EAAmB,EAAA,iBAAiB,CAAC,SAAS,CAAC,GACrD,GAAI,CAAC,EAAiB,OAAO,CAC3B,CAD6B,MACtB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,oBAAqB,EAAiB,KAAK,CAAC,MAAM,EACtE,CAAE,OAAQ,GAAI,GAIlB,GAAM,MAAE,CAAI,OAAE,CAAK,OAAE,CAAK,SAAE,CAAO,SAAE,CAAO,CAAE,CAAG,EAAiB,IAAI,AAGtE,OAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAGf,IAAM,EAAa,IAAI,KAAK,KAAK,GAAG,GAAK,KAAK,CAO9C,GAN2B,CADwB,AAO/C,KAN6B,EAAA,OAAW,CAAC,KAMrB,EAN4B,CAAC,CACnD,gBACA,EACA,UAAW,CAAE,KAAM,CAAW,CAChC,GAGE,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,+FACpB,CAAE,OAAQ,GAAI,GAKlB,IAAM,EAAoB,IAAI,EAAA,OAAW,CAAC,MACxC,QACA,QACA,UACA,UACA,CACF,GAOA,OALA,MAAM,EAAkB,IAAI,GAKrB,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EACnB,CAAE,GAAI,EAAkB,GAAG,AAAC,EAC5B,6DAEF,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,iCAAkC,GACzC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,4EACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CAEF,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EACtC,EAAO,SAAS,EAAa,GAAG,CAAC,SAAW,KAC5C,EAAQ,SAAS,EAAa,GAAG,CAAC,UAAY,MAC9C,EAAS,EAAa,GAAG,CAAC,SAEhC,OAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAGf,IAAM,EAAa,CAAC,CACL,MAAM,EAAjB,IACF,EAAM,MAAM,CAAc,SAAX,CAAW,EAI5B,IAAM,EAAQ,MAAM,EAAA,OAAW,CAAC,cAAc,CAAC,GAGzC,EAAW,MAAM,EAAA,OAAW,CAAC,IAAI,CAAC,GACrC,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,IAAI,CAAC,CAAC,GAAO,CAAC,CAAI,GAClB,KAAK,CAAC,GACN,MAAM,CAAC,uCAEV,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAqB,AAArB,EAAsB,UACpB,EACA,WAAY,MACV,QACA,QACA,EACA,MAAO,KAAK,IAAI,CAAC,EAAQ,EAC3B,CACF,GACA,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,4BAA6B,GACpC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,mDACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CAEO,eAAe,EAAM,CAAoB,EAC9C,GAAI,CAGF,GAAM,IAAE,CAAE,QAAE,CAAM,CAAE,CADP,EACU,IADJ,EAAQ,IAAI,GAG/B,GAAI,CAAC,GAAwB,WAAlB,AAA6B,OAAtB,EAChB,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,wBACpB,CAAE,OAAQ,GAAI,EAIlB,OAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAEf,IAAM,EAAiB,MAAM,EAAA,OAAW,CAAC,iBAAiB,CACxD,EACA,QAAE,CAAO,EACT,CAAE,KAAK,CAAK,GAGd,GAAI,CAAC,EACH,OAAO,EAAA,KADY,OACA,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,0BACpB,CAAE,OAAQ,GAAI,GAIlB,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAgB,qCACtC,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,6BAA8B,GACrC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,sDACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CC5JA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,qBACN,SAAU,eACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,yCAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,qBAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACR,AAAiB,OAAO,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,CAAE,YAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAiB,AAAjB,EACnG,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,EACN,CAAsB,MAAV,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,EACA,oBACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,OAAvD,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,CAC7H,eACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,iBAAkB,OAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,EACzC,GAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,CACV,aACA,QACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAXI,AAAsB,QAAO,KAAK,EAAI,EAAmB,OAAO,AAAP,EAAS,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAI,AAAL,SAAc,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [3]}