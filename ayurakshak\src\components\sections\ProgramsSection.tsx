'use client';

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import Card, { <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const ProgramsSection: React.FC = () => {
  const programs = [
    {
      id: 'health-camps',
      title: 'Mobile Health Camps',
      description: 'Bringing Ayurvedic healthcare directly to remote villages and underserved communities across India.',
      image: '/programs/health-camps.jpg',
      icon: '🏥',
      stats: { beneficiaries: '15,000+', locations: '250+' },
      features: [
        'Free Ayurvedic consultations',
        'Traditional medicine distribution',
        'Health education workshops',
        'Preventive care guidance',
      ],
      color: 'from-green-500 to-emerald-600',
    },
    {
      id: 'women-empowerment',
      title: 'Women Livelihood Programs',
      description: 'Empowering women through skill development, entrepreneurship training, and sustainable business opportunities.',
      image: '/programs/women-empowerment.jpg',
      icon: '👩‍💼',
      stats: { beneficiaries: '2,500+', groups: '150+' },
      features: [
        'Skill development training',
        'Microfinance support',
        'Business mentorship',
        'Market linkage assistance',
      ],
      color: 'from-purple-500 to-pink-600',
    },
    {
      id: 'education',
      title: 'Health Education Initiative',
      description: 'Spreading awareness about preventive healthcare, nutrition, and traditional wellness practices.',
      image: '/programs/education.jpg',
      icon: '📚',
      stats: { beneficiaries: '8,000+', workshops: '300+' },
      features: [
        'Community health workshops',
        'Nutrition education',
        'Hygiene awareness programs',
        'Traditional medicine training',
      ],
      color: 'from-blue-500 to-indigo-600',
    },
    {
      id: 'emergency-relief',
      title: 'Emergency Relief Support',
      description: 'Rapid response healthcare support during natural disasters and health emergencies.',
      image: '/programs/emergency-relief.jpg',
      icon: '🚨',
      stats: { responses: '25+', families: '5,000+' },
      features: [
        'Emergency medical camps',
        'Relief material distribution',
        'Psychological support',
        'Rehabilitation assistance',
      ],
      color: 'from-red-500 to-orange-600',
    },
    {
      id: 'environment',
      title: 'Green Health Initiative',
      description: 'Promoting environmental conservation through medicinal plant cultivation and eco-friendly practices.',
      image: '/programs/environment.jpg',
      icon: '🌱',
      stats: { plants: '50,000+', farmers: '800+' },
      features: [
        'Medicinal plant cultivation',
        'Organic farming training',
        'Environmental awareness',
        'Sustainable practices',
      ],
      color: 'from-green-600 to-teal-600',
    },
    {
      id: 'community-development',
      title: 'Community Development',
      description: 'Building resilient communities through integrated development programs and capacity building.',
      image: '/programs/community-development.jpg',
      icon: '🏘️',
      stats: { villages: '180+', leaders: '500+' },
      features: [
        'Community leadership training',
        'Infrastructure development',
        'Social cohesion programs',
        'Local governance support',
      ],
      color: 'from-yellow-500 to-amber-600',
    },
  ];

  return (
    <section id="programs" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Our <span className="text-primary-600">Programs</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive initiatives designed to heal, empower, and transform communities across India
          </p>
        </motion.div>

        {/* Programs Grid */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {programs.map((program, index) => (
            <motion.div
              key={program.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card variant="elevated" hover className="h-full overflow-hidden group">
                {/* Image Header */}
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={program.image}
                    alt={program.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                  <div className={`absolute inset-0 bg-gradient-to-r ${program.color} opacity-80`} />
                  <div className="absolute top-4 left-4 bg-white/20 backdrop-blur-sm rounded-full p-3">
                    <span className="text-2xl">{program.icon}</span>
                  </div>
                  <div className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                    <span className="text-sm font-medium text-gray-800">
                      {Object.values(program.stats)[0]} impacted
                    </span>
                  </div>
                </div>

                <CardContent className="p-6">
                  <CardHeader className="p-0 mb-4">
                    <CardTitle className="text-xl font-bold text-gray-900 mb-2">
                      {program.title}
                    </CardTitle>
                    <p className="text-gray-600 leading-relaxed">
                      {program.description}
                    </p>
                  </CardHeader>

                  {/* Stats */}
                  <div className="flex gap-6 mb-4">
                    {Object.entries(program.stats).map(([key, value]) => (
                      <div key={key} className="text-center">
                        <div className="text-lg font-bold text-primary-600">{value}</div>
                        <div className="text-xs text-gray-500 capitalize">{key}</div>
                      </div>
                    ))}
                  </div>

                  {/* Features */}
                  <div className="space-y-2 mb-6">
                    {program.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>

                  <Button variant="outline" size="sm" className="w-full">
                    Learn More
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center bg-gradient-to-r from-primary-50 to-sage-50 rounded-2xl p-8 lg:p-12"
        >
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            Want to Support Our Programs?
          </h3>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Your contribution can help us expand our reach and impact more lives. 
            Join us in building healthier, more empowered communities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="primary" size="lg">
              Donate Now
            </Button>
            <Button variant="outline" size="lg">
              Become a Volunteer
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProgramsSection;
