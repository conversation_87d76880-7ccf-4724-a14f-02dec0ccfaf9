import mongoose, { Schema } from 'mongoose';
import { INewsletter } from '@/types';

const NewsletterSchema = new Schema<INewsletter>(
  {
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email address',
      ],
    },
    name: {
      type: String,
      trim: true,
      maxlength: [100, 'Name cannot exceed 100 characters'],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    subscribedAt: {
      type: Date,
      default: Date.now,
    },
    unsubscribedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for better query performance
NewsletterSchema.index({ email: 1 }, { unique: true });
NewsletterSchema.index({ isActive: 1 });
NewsletterSchema.index({ subscribedAt: -1 });

// Virtual for subscription status
NewsletterSchema.virtual('subscriptionStatus').get(function () {
  return this.isActive ? 'Active' : 'Unsubscribed';
});

// Virtual for formatted subscription date
NewsletterSchema.virtual('formattedSubscriptionDate').get(function () {
  return this.subscribedAt.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
});

// Pre-save middleware
NewsletterSchema.pre('save', function (next) {
  // Set unsubscribed date when marking as inactive
  if (!this.isActive && !this.unsubscribedAt) {
    this.unsubscribedAt = new Date();
  }
  
  // Clear unsubscribed date when reactivating
  if (this.isActive && this.unsubscribedAt) {
    this.unsubscribedAt = undefined;
  }
  
  next();
});

// Static method to get active subscribers count
NewsletterSchema.statics.getActiveCount = function () {
  return this.countDocuments({ isActive: true });
};

// Static method to unsubscribe
NewsletterSchema.statics.unsubscribe = function (email: string) {
  return this.findOneAndUpdate(
    { email },
    { 
      isActive: false, 
      unsubscribedAt: new Date() 
    },
    { new: true }
  );
};

// Static method to resubscribe
NewsletterSchema.statics.resubscribe = function (email: string) {
  return this.findOneAndUpdate(
    { email },
    { 
      isActive: true, 
      $unset: { unsubscribedAt: 1 } 
    },
    { new: true, upsert: true }
  );
};

// Static method to get recent subscribers
NewsletterSchema.statics.getRecent = function (limit: number = 10) {
  return this.find({ isActive: true })
    .sort({ subscribedAt: -1 })
    .limit(limit)
    .select('email name subscribedAt');
};

// Static method to bulk unsubscribe
NewsletterSchema.statics.bulkUnsubscribe = function (emails: string[]) {
  return this.updateMany(
    { email: { $in: emails } },
    { 
      isActive: false, 
      unsubscribedAt: new Date() 
    }
  );
};

const Newsletter = mongoose.models.Newsletter || mongoose.model<INewsletter>('Newsletter', NewsletterSchema);

export default Newsletter;
