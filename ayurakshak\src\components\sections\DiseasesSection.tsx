'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';

const DiseasesSection: React.FC = () => {
  const diseases = [
    {
      id: 'kidney',
      name: 'Kidney Disease',
      description: 'If left untreated, it can lead to kidney failure.',
      icon: '🫘',
      color: 'from-red-500 to-red-600',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600',
      stats: '85% Success Rate',
      treatments: ['Ayurvedic Medicine', 'Panchakarma', 'Diet Therapy']
    },
    {
      id: 'liver',
      name: 'Liver Disease',
      description: 'Catching it early can prevent liver damage.',
      icon: '🫀',
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-600',
      stats: '90% Success Rate',
      treatments: ['Herbal Medicine', 'Detox Therapy', 'Lifestyle Changes']
    },
    {
      id: 'cancer',
      name: 'Cancer',
      description: 'Early management can reverse cancer.',
      icon: '🎗️',
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-600',
      stats: '75% Success Rate',
      treatments: ['Immunotherapy', 'Herbal Support', 'Nutrition Therapy']
    },
    {
      id: 'heart',
      name: 'Heart Disease',
      description: 'Manage your heart health to avoid failure.',
      icon: '❤️',
      color: 'from-pink-500 to-pink-600',
      bgColor: 'bg-pink-50',
      textColor: 'text-pink-600',
      stats: '88% Success Rate',
      treatments: ['Cardiac Care', 'Yoga Therapy', 'Stress Management']
    },
    {
      id: 'blood-pressure',
      name: 'Blood Pressure',
      description: 'Reverse BP & protect yourself.',
      icon: '🩺',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
      stats: '92% Success Rate',
      treatments: ['Natural Medicine', 'Meditation', 'Diet Control']
    },
    {
      id: 'diabetes',
      name: 'Diabetes',
      description: 'Reverse diabetes to avoid serious problems.',
      icon: '🍯',
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600',
      stats: '87% Success Rate',
      treatments: ['Sugar Control', 'Panchakarma', 'Exercise Therapy']
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Diseases and <span className="text-primary-600">Conditions We Treat</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our traditional Ayurvedic approach has helped thousands of patients recover from chronic conditions naturally and effectively.
          </p>
        </motion.div>

        {/* Diseases Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {diseases.map((disease, index) => (
            <motion.div
              key={disease.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Link href={`/diseases/${disease.id}`}>
                <div className={`relative overflow-hidden rounded-2xl ${disease.bgColor} p-8 h-full transition-all duration-300 hover:shadow-xl hover:scale-105 cursor-pointer`}>
                  {/* Background Gradient */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${disease.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
                  
                  {/* Content */}
                  <div className="relative z-10">
                    {/* Icon */}
                    <div className="text-4xl mb-4">{disease.icon}</div>
                    
                    {/* Title */}
                    <h3 className={`text-2xl font-bold ${disease.textColor} mb-3 group-hover:text-gray-900 transition-colors`}>
                      {disease.name}
                    </h3>
                    
                    {/* Description */}
                    <p className="text-gray-600 mb-4 leading-relaxed">
                      {disease.description}
                    </p>
                    
                    {/* Stats */}
                    <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${disease.bgColor} ${disease.textColor} mb-4`}>
                      {disease.stats}
                    </div>
                    
                    {/* Treatments */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-semibold text-gray-700">Treatment Methods:</h4>
                      <div className="flex flex-wrap gap-2">
                        {disease.treatments.map((treatment) => (
                          <span
                            key={treatment}
                            className="text-xs px-2 py-1 bg-white rounded-full text-gray-600 border"
                          >
                            {treatment}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    {/* Arrow */}
                    <div className="mt-6 flex items-center text-sm font-medium text-gray-500 group-hover:text-primary-600 transition-colors">
                      <span>Learn More</span>
                      <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Other Diseases */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-primary-50 to-sage-50 rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Other Diseases We Treat
            </h3>
            <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
              We also provide effective treatment for joint pain, digestive issues, respiratory problems, skin disorders, thyroid conditions, and many other chronic health conditions.
            </p>
            
            {/* Additional Conditions */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              {[
                'Joint Pain', 'Digestive Issues', 'Respiratory Problems', 'Skin Disorders',
                'Thyroid', 'Migraine', 'Obesity', 'Stress & Anxiety'
              ].map((condition) => (
                <div
                  key={condition}
                  className="bg-white rounded-lg p-4 shadow-soft hover:shadow-medium transition-shadow"
                >
                  <div className="text-sm font-medium text-gray-700">{condition}</div>
                </div>
              ))}
            </div>
            
            <Link
              href="/diseases"
              className="inline-flex items-center px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-300"
            >
              View All Conditions
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default DiseasesSection;
