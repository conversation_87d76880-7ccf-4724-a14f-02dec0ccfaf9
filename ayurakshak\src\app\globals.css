@import "tailwindcss";
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap");

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Ayurakshak Color Palette */
  --primary-green: #4a7c59;
  --primary-light: #6b9b7a;
  --primary-dark: #2d5a3d;
  --sage-green: #87a96b;
  --mint-green: #a8d5ba;
  --earth-brown: #8b7355;
  --earth-light: #b8a082;
  --earth-dark: #6b5b47;
  --warm-beige: #f5f1eb;
  --cream: #faf8f3;
  --accent-gold: #d4af37;
  --accent-orange: #e67e22;

  /* Shadows */
  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07),
    0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --shadow-green: 0 10px 30px rgba(74, 124, 89, 0.15);
  --shadow-warm: 0 10px 30px rgba(249, 115, 22, 0.15);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: "Inter", system-ui, sans-serif;
  --font-heading: "Poppins", system-ui, sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Inter", system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Utility classes */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-green), var(--sage-green));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.bg-glass-dark {
  background: rgba(74, 124, 89, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

/* Button hover effects */
.btn-hover-lift {
  transition: all 0.3s ease;
}

.btn-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-green);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-soft);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--primary-green);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive text */
@media (max-width: 640px) {
  .text-responsive-lg {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .text-responsive-xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .text-responsive-2xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
