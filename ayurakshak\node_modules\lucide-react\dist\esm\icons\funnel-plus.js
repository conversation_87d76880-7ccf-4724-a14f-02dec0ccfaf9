/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M13.354 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14v6a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341l1.218-1.348",
      key: "8mvsmf"
    }
  ],
  ["path", { d: "M16 6h6", key: "1dogtp" }],
  ["path", { d: "M19 3v6", key: "1ytpjt" }]
];
const FunnelPlus = createLucideIcon("funnel-plus", __iconNode);

export { __iconNode, FunnelPlus as default };
//# sourceMappingURL=funnel-plus.js.map
