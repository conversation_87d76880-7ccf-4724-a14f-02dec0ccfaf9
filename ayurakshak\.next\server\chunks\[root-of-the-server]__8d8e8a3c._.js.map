{"version": 3, "sources": ["turbopack:///[project]/src/lib/mongodb.ts", "turbopack:///[project]/src/lib/models/Contact.ts", "turbopack:///[project]/src/lib/models/Newsletter.ts", "turbopack:///[project]/src/lib/models/Donation.ts", "turbopack:///[project]/src/lib/models/ProductOrder.ts", "turbopack:///[project]/src/app/api/stats/route.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI!, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n\n// Type declaration for global mongoose\ndeclare global {\n  var mongoose: {\n    conn: typeof mongoose | null;\n    promise: Promise<typeof mongoose> | null;\n  };\n}\n", "import mongoose, { Schema } from 'mongoose';\nimport { IContactForm } from '@/types';\n\nconst ContactFormSchema = new Schema<IContactForm>(\n  {\n    name: {\n      type: String,\n      required: [true, 'Name is required'],\n      trim: true,\n      maxlength: [100, 'Name cannot exceed 100 characters'],\n    },\n    email: {\n      type: String,\n      required: [true, 'Email is required'],\n      trim: true,\n      lowercase: true,\n      match: [\n        /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n        'Please enter a valid email address',\n      ],\n    },\n    phone: {\n      type: String,\n      trim: true,\n      match: [\n        /^[\\+]?[1-9][\\d]{0,15}$/,\n        'Please enter a valid phone number',\n      ],\n    },\n    subject: {\n      type: String,\n      required: [true, 'Subject is required'],\n      trim: true,\n      maxlength: [200, 'Subject cannot exceed 200 characters'],\n    },\n    message: {\n      type: String,\n      required: [true, 'Message is required'],\n      trim: true,\n      maxlength: [2000, 'Message cannot exceed 2000 characters'],\n    },\n    isRead: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  {\n    timestamps: true,\n    toJSON: { virtuals: true },\n    toObject: { virtuals: true },\n  }\n);\n\n// Indexes for better query performance\nContactFormSchema.index({ email: 1 });\nContactFormSchema.index({ createdAt: -1 });\nContactFormSchema.index({ isRead: 1 });\n\n// Virtual for formatted creation date\nContactFormSchema.virtual('formattedDate').get(function () {\n  return this.createdAt.toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  });\n});\n\n// Pre-save middleware to sanitize data\nContactFormSchema.pre('save', function (next) {\n  // Remove any HTML tags from text fields for security\n  this.name = this.name.replace(/<[^>]*>?/gm, '');\n  this.subject = this.subject.replace(/<[^>]*>?/gm, '');\n  this.message = this.message.replace(/<[^>]*>?/gm, '');\n  \n  next();\n});\n\n// Static method to get unread count\nContactFormSchema.statics.getUnreadCount = function () {\n  return this.countDocuments({ isRead: false });\n};\n\n// Static method to mark as read\nContactFormSchema.statics.markAsRead = function (id: string) {\n  return this.findByIdAndUpdate(id, { isRead: true }, { new: true });\n};\n\n// Static method to get recent contacts\nContactFormSchema.statics.getRecent = function (limit: number = 10) {\n  return this.find()\n    .sort({ createdAt: -1 })\n    .limit(limit)\n    .select('name email subject createdAt isRead');\n};\n\nconst ContactForm = mongoose.models.ContactForm || mongoose.model<IContactForm>('ContactForm', ContactFormSchema);\n\nexport default ContactForm;\n", "import mongoose, { Schema } from 'mongoose';\nimport { INewsletter } from '@/types';\n\nconst NewsletterSchema = new Schema<INewsletter>(\n  {\n    email: {\n      type: String,\n      required: [true, 'Email is required'],\n      unique: true,\n      trim: true,\n      lowercase: true,\n      match: [\n        /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n        'Please enter a valid email address',\n      ],\n    },\n    name: {\n      type: String,\n      trim: true,\n      maxlength: [100, 'Name cannot exceed 100 characters'],\n    },\n    isActive: {\n      type: Boolean,\n      default: true,\n    },\n    subscribedAt: {\n      type: Date,\n      default: Date.now,\n    },\n    unsubscribedAt: {\n      type: Date,\n    },\n  },\n  {\n    timestamps: true,\n    toJSON: { virtuals: true },\n    toObject: { virtuals: true },\n  }\n);\n\n// Indexes for better query performance\nNewsletterSchema.index({ email: 1 }, { unique: true });\nNewsletterSchema.index({ isActive: 1 });\nNewsletterSchema.index({ subscribedAt: -1 });\n\n// Virtual for subscription status\nNewsletterSchema.virtual('subscriptionStatus').get(function () {\n  return this.isActive ? 'Active' : 'Unsubscribed';\n});\n\n// Virtual for formatted subscription date\nNewsletterSchema.virtual('formattedSubscriptionDate').get(function () {\n  return this.subscribedAt.toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n});\n\n// Pre-save middleware\nNewsletterSchema.pre('save', function (next) {\n  // Set unsubscribed date when marking as inactive\n  if (!this.isActive && !this.unsubscribedAt) {\n    this.unsubscribedAt = new Date();\n  }\n  \n  // Clear unsubscribed date when reactivating\n  if (this.isActive && this.unsubscribedAt) {\n    this.unsubscribedAt = undefined;\n  }\n  \n  next();\n});\n\n// Static method to get active subscribers count\nNewsletterSchema.statics.getActiveCount = function () {\n  return this.countDocuments({ isActive: true });\n};\n\n// Static method to unsubscribe\nNewsletterSchema.statics.unsubscribe = function (email: string) {\n  return this.findOneAndUpdate(\n    { email },\n    { \n      isActive: false, \n      unsubscribedAt: new Date() \n    },\n    { new: true }\n  );\n};\n\n// Static method to resubscribe\nNewsletterSchema.statics.resubscribe = function (email: string) {\n  return this.findOneAndUpdate(\n    { email },\n    { \n      isActive: true, \n      $unset: { unsubscribedAt: 1 } \n    },\n    { new: true, upsert: true }\n  );\n};\n\n// Static method to get recent subscribers\nNewsletterSchema.statics.getRecent = function (limit: number = 10) {\n  return this.find({ isActive: true })\n    .sort({ subscribedAt: -1 })\n    .limit(limit)\n    .select('email name subscribedAt');\n};\n\n// Static method to bulk unsubscribe\nNewsletterSchema.statics.bulkUnsubscribe = function (emails: string[]) {\n  return this.updateMany(\n    { email: { $in: emails } },\n    { \n      isActive: false, \n      unsubscribedAt: new Date() \n    }\n  );\n};\n\nconst Newsletter = mongoose.models.Newsletter || mongoose.model<INewsletter>('Newsletter', NewsletterSchema);\n\nexport default Newsletter;\n", "import mongoose, { Schema } from 'mongoose';\nimport { IDonation } from '@/types';\n\nconst DonationSchema = new Schema<IDonation>(\n  {\n    donorName: {\n      type: String,\n      required: [true, 'Donor name is required'],\n      trim: true,\n      maxlength: [100, 'Name cannot exceed 100 characters'],\n    },\n    donorEmail: {\n      type: String,\n      required: [true, 'Email is required'],\n      trim: true,\n      lowercase: true,\n      match: [\n        /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n        'Please enter a valid email address',\n      ],\n    },\n    donorPhone: {\n      type: String,\n      trim: true,\n      match: [\n        /^[\\+]?[1-9][\\d]{0,15}$/,\n        'Please enter a valid phone number',\n      ],\n    },\n    amount: {\n      type: Number,\n      required: [true, 'Donation amount is required'],\n      min: [1, 'Amount must be at least ₹1'],\n      max: [********, 'Amount cannot exceed ₹1 crore'],\n    },\n    currency: {\n      type: String,\n      default: 'INR',\n      enum: ['INR', 'USD', 'EUR'],\n    },\n    purpose: {\n      type: String,\n      required: [true, 'Purpose is required'],\n      enum: ['general', 'health', 'education', 'emergency', 'livelihoods'],\n      default: 'general',\n    },\n    paymentMethod: {\n      type: String,\n      required: [true, 'Payment method is required'],\n      enum: ['online', 'bank_transfer', 'cash', 'cheque'],\n      default: 'online',\n    },\n    paymentStatus: {\n      type: String,\n      required: [true, 'Payment status is required'],\n      enum: ['pending', 'completed', 'failed', 'refunded'],\n      default: 'pending',\n    },\n    transactionId: {\n      type: String,\n      trim: true,\n      sparse: true, // Allows multiple null values\n    },\n    paymentGatewayResponse: {\n      type: Schema.Types.Mixed,\n    },\n    isAnonymous: {\n      type: Boolean,\n      default: false,\n    },\n    address: {\n      street: {\n        type: String,\n        trim: true,\n        maxlength: [200, 'Street address cannot exceed 200 characters'],\n      },\n      city: {\n        type: String,\n        trim: true,\n        maxlength: [50, 'City cannot exceed 50 characters'],\n      },\n      state: {\n        type: String,\n        trim: true,\n        maxlength: [50, 'State cannot exceed 50 characters'],\n      },\n      pincode: {\n        type: String,\n        trim: true,\n        match: [/^[1-9][0-9]{5}$/, 'Please enter a valid pincode'],\n      },\n      country: {\n        type: String,\n        trim: true,\n        default: 'India',\n        maxlength: [50, 'Country cannot exceed 50 characters'],\n      },\n    },\n    panNumber: {\n      type: String,\n      trim: true,\n      uppercase: true,\n      match: [/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Please enter a valid PAN number'],\n    },\n    receiptNumber: {\n      type: String,\n      trim: true,\n      unique: true,\n      sparse: true,\n    },\n  },\n  {\n    timestamps: true,\n    toJSON: { virtuals: true },\n    toObject: { virtuals: true },\n  }\n);\n\n// Indexes for better query performance\nDonationSchema.index({ donorEmail: 1 });\nDonationSchema.index({ paymentStatus: 1 });\nDonationSchema.index({ purpose: 1 });\nDonationSchema.index({ createdAt: -1 });\nDonationSchema.index({ amount: -1 });\nDonationSchema.index({ transactionId: 1 }, { sparse: true });\n\n// Virtual for formatted amount\nDonationSchema.virtual('formattedAmount').get(function () {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: this.currency,\n  }).format(this.amount);\n});\n\n// Virtual for formatted date\nDonationSchema.virtual('formattedDate').get(function () {\n  return this.createdAt.toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n});\n\n// Virtual for purpose display name\nDonationSchema.virtual('purposeDisplayName').get(function () {\n  const purposeMap = {\n    general: 'General Fund',\n    health: 'Health Camps',\n    education: 'Education Programs',\n    emergency: 'Emergency Relief',\n    livelihoods: 'Women Livelihoods',\n  };\n  return purposeMap[this.purpose] || this.purpose;\n});\n\n// Pre-save middleware\nDonationSchema.pre('save', function (next) {\n  // Generate receipt number for completed donations\n  if (this.paymentStatus === 'completed' && !this.receiptNumber) {\n    const year = new Date().getFullYear();\n    const month = String(new Date().getMonth() + 1).padStart(2, '0');\n    const random = Math.random().toString(36).substr(2, 6).toUpperCase();\n    this.receiptNumber = `AYU${year}${month}${random}`;\n  }\n  \n  // Sanitize text fields\n  this.donorName = this.donorName.replace(/<[^>]*>?/gm, '');\n  \n  next();\n});\n\n// Static method to get total donations\nDonationSchema.statics.getTotalAmount = function (purpose?: string) {\n  const match = purpose ? { purpose, paymentStatus: 'completed' } : { paymentStatus: 'completed' };\n  return this.aggregate([\n    { $match: match },\n    { $group: { _id: null, total: { $sum: '$amount' } } },\n  ]);\n};\n\n// Static method to get donation statistics\nDonationSchema.statics.getStats = function () {\n  return this.aggregate([\n    { $match: { paymentStatus: 'completed' } },\n    {\n      $group: {\n        _id: '$purpose',\n        totalAmount: { $sum: '$amount' },\n        count: { $sum: 1 },\n        avgAmount: { $avg: '$amount' },\n      },\n    },\n  ]);\n};\n\n// Static method to get recent donations\nDonationSchema.statics.getRecent = function (limit: number = 10) {\n  return this.find({ paymentStatus: 'completed' })\n    .sort({ createdAt: -1 })\n    .limit(limit)\n    .select('donorName amount purpose createdAt isAnonymous');\n};\n\n// Static method to get top donors\nDonationSchema.statics.getTopDonors = function (limit: number = 10) {\n  return this.aggregate([\n    { $match: { paymentStatus: 'completed', isAnonymous: false } },\n    {\n      $group: {\n        _id: '$donorEmail',\n        donorName: { $first: '$donorName' },\n        totalAmount: { $sum: '$amount' },\n        donationCount: { $sum: 1 },\n        lastDonation: { $max: '$createdAt' },\n      },\n    },\n    { $sort: { totalAmount: -1 } },\n    { $limit: limit },\n  ]);\n};\n\nconst Donation = mongoose.models.Donation || mongoose.model<IDonation>('Donation', DonationSchema);\n\nexport default Donation;\n", "import mongoose, { Schema } from 'mongoose';\nimport { IProductOrder } from '@/types';\n\nconst ProductOrderSchema = new Schema<IProductOrder>(\n  {\n    customerName: {\n      type: String,\n      required: [true, 'Customer name is required'],\n      trim: true,\n      maxlength: [100, 'Name cannot exceed 100 characters'],\n    },\n    customerEmail: {\n      type: String,\n      required: [true, 'Email is required'],\n      trim: true,\n      lowercase: true,\n      match: [\n        /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n        'Please enter a valid email address',\n      ],\n    },\n    customerPhone: {\n      type: String,\n      required: [true, 'Phone number is required'],\n      trim: true,\n      match: [\n        /^[\\+]?[1-9][\\d]{0,15}$/,\n        'Please enter a valid phone number',\n      ],\n    },\n    products: [\n      {\n        productId: {\n          type: String,\n          required: [true, 'Product ID is required'],\n        },\n        productName: {\n          type: String,\n          required: [true, 'Product name is required'],\n          trim: true,\n        },\n        quantity: {\n          type: Number,\n          required: [true, 'Quantity is required'],\n          min: [1, 'Quantity must be at least 1'],\n          max: [100, 'Quantity cannot exceed 100'],\n        },\n        price: {\n          type: Number,\n          required: [true, 'Price is required'],\n          min: [0, 'Price cannot be negative'],\n        },\n      },\n    ],\n    totalAmount: {\n      type: Number,\n      required: [true, 'Total amount is required'],\n      min: [0, 'Total amount cannot be negative'],\n    },\n    shippingAddress: {\n      street: {\n        type: String,\n        required: [true, 'Street address is required'],\n        trim: true,\n        maxlength: [200, 'Street address cannot exceed 200 characters'],\n      },\n      city: {\n        type: String,\n        required: [true, 'City is required'],\n        trim: true,\n        maxlength: [50, 'City cannot exceed 50 characters'],\n      },\n      state: {\n        type: String,\n        required: [true, 'State is required'],\n        trim: true,\n        maxlength: [50, 'State cannot exceed 50 characters'],\n      },\n      pincode: {\n        type: String,\n        required: [true, 'Pincode is required'],\n        trim: true,\n        match: [/^[1-9][0-9]{5}$/, 'Please enter a valid pincode'],\n      },\n      country: {\n        type: String,\n        trim: true,\n        default: 'India',\n        maxlength: [50, 'Country cannot exceed 50 characters'],\n      },\n    },\n    orderStatus: {\n      type: String,\n      required: [true, 'Order status is required'],\n      enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'],\n      default: 'pending',\n    },\n    paymentStatus: {\n      type: String,\n      required: [true, 'Payment status is required'],\n      enum: ['pending', 'completed', 'failed', 'refunded'],\n      default: 'pending',\n    },\n    paymentMethod: {\n      type: String,\n      required: [true, 'Payment method is required'],\n      enum: ['cod', 'online', 'bank_transfer'],\n      default: 'cod',\n    },\n    trackingNumber: {\n      type: String,\n      trim: true,\n      sparse: true,\n    },\n    notes: {\n      type: String,\n      trim: true,\n      maxlength: [500, 'Notes cannot exceed 500 characters'],\n    },\n  },\n  {\n    timestamps: true,\n    toJSON: { virtuals: true },\n    toObject: { virtuals: true },\n  }\n);\n\n// Indexes for better query performance\nProductOrderSchema.index({ customerEmail: 1 });\nProductOrderSchema.index({ orderStatus: 1 });\nProductOrderSchema.index({ paymentStatus: 1 });\nProductOrderSchema.index({ createdAt: -1 });\nProductOrderSchema.index({ trackingNumber: 1 }, { sparse: true });\n\n// Virtual for order number\nProductOrderSchema.virtual('orderNumber').get(function () {\n  const year = this.createdAt.getFullYear();\n  const month = String(this.createdAt.getMonth() + 1).padStart(2, '0');\n  const id = this._id.toString().slice(-6).toUpperCase();\n  return `ORD${year}${month}${id}`;\n});\n\n// Virtual for formatted total amount\nProductOrderSchema.virtual('formattedTotalAmount').get(function () {\n  return new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n  }).format(this.totalAmount);\n});\n\n// Virtual for formatted order date\nProductOrderSchema.virtual('formattedOrderDate').get(function () {\n  return this.createdAt.toLocaleDateString('en-IN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n});\n\n// Virtual for full shipping address\nProductOrderSchema.virtual('fullShippingAddress').get(function () {\n  const addr = this.shippingAddress;\n  return `${addr.street}, ${addr.city}, ${addr.state} - ${addr.pincode}, ${addr.country}`;\n});\n\n// Virtual for order status display name\nProductOrderSchema.virtual('orderStatusDisplayName').get(function () {\n  const statusMap = {\n    pending: 'Pending',\n    confirmed: 'Confirmed',\n    processing: 'Processing',\n    shipped: 'Shipped',\n    delivered: 'Delivered',\n    cancelled: 'Cancelled',\n  };\n  return statusMap[this.orderStatus] || this.orderStatus;\n});\n\n// Virtual for payment status display name\nProductOrderSchema.virtual('paymentStatusDisplayName').get(function () {\n  const statusMap = {\n    pending: 'Pending',\n    completed: 'Completed',\n    failed: 'Failed',\n    refunded: 'Refunded',\n  };\n  return statusMap[this.paymentStatus] || this.paymentStatus;\n});\n\n// Pre-save middleware\nProductOrderSchema.pre('save', function (next) {\n  // Calculate total amount from products\n  if (this.products && this.products.length > 0) {\n    this.totalAmount = this.products.reduce((total, product) => {\n      return total + (product.price * product.quantity);\n    }, 0);\n  }\n  \n  // Generate tracking number when order is shipped\n  if (this.orderStatus === 'shipped' && !this.trackingNumber) {\n    const random = Math.random().toString(36).substr(2, 10).toUpperCase();\n    this.trackingNumber = `TRK${random}`;\n  }\n  \n  // Sanitize text fields\n  this.customerName = this.customerName.replace(/<[^>]*>?/gm, '');\n  if (this.notes) {\n    this.notes = this.notes.replace(/<[^>]*>?/gm, '');\n  }\n  \n  next();\n});\n\n// Static method to get order statistics\nProductOrderSchema.statics.getStats = function () {\n  return this.aggregate([\n    {\n      $group: {\n        _id: '$orderStatus',\n        count: { $sum: 1 },\n        totalAmount: { $sum: '$totalAmount' },\n      },\n    },\n  ]);\n};\n\n// Static method to get recent orders\nProductOrderSchema.statics.getRecent = function (limit: number = 10) {\n  return this.find()\n    .sort({ createdAt: -1 })\n    .limit(limit)\n    .select('customerName totalAmount orderStatus paymentStatus createdAt');\n};\n\n// Static method to get orders by customer\nProductOrderSchema.statics.getByCustomer = function (email: string) {\n  return this.find({ customerEmail: email })\n    .sort({ createdAt: -1 })\n    .select('-customerEmail');\n};\n\n// Static method to update order status\nProductOrderSchema.statics.updateStatus = function (orderId: string, status: string) {\n  return this.findByIdAndUpdate(\n    orderId,\n    { orderStatus: status },\n    { new: true }\n  );\n};\n\nconst ProductOrder = mongoose.models.ProductOrder || mongoose.model<IProductOrder>('ProductOrder', ProductOrderSchema);\n\nexport default ProductOrder;\n", "import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport ContactForm from '@/lib/models/Contact';\nimport Newsletter from '@/lib/models/Newsletter';\nimport Donation from '@/lib/models/Donation';\nimport ProductOrder from '@/lib/models/ProductOrder';\nimport { formatSuccessResponse, formatErrorResponse } from '@/utils/validation';\n\nexport async function GET(request: NextRequest) {\n  try {\n    await connectDB();\n\n    // Get current date for time-based queries\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);\n\n    // Parallel queries for better performance\n    const [\n      totalDonations,\n      totalDonors,\n      recentDonations,\n      donationsByPurpose,\n      totalNewsletterSubscribers,\n      recentSubscribers,\n      totalContactForms,\n      unreadContactForms,\n      recentContactForms,\n      totalOrders,\n      recentOrders,\n      ordersByStatus,\n    ] = await Promise.all([\n      // Donation statistics\n      Donation.aggregate([\n        { $match: { paymentStatus: 'completed' } },\n        { $group: { _id: null, total: { $sum: '$amount' }, count: { $sum: 1 } } },\n      ]),\n      \n      // Unique donors count\n      Donation.distinct('donorEmail', { paymentStatus: 'completed' }),\n      \n      // Recent donations (last 30 days)\n      Donation.countDocuments({\n        paymentStatus: 'completed',\n        createdAt: { $gte: thirtyDaysAgo },\n      }),\n      \n      // Donations by purpose\n      Donation.aggregate([\n        { $match: { paymentStatus: 'completed' } },\n        {\n          $group: {\n            _id: '$purpose',\n            totalAmount: { $sum: '$amount' },\n            count: { $sum: 1 },\n          },\n        },\n      ]),\n      \n      // Newsletter statistics\n      Newsletter.countDocuments({ isActive: true }),\n      \n      // Recent newsletter subscribers (last 30 days)\n      Newsletter.countDocuments({\n        isActive: true,\n        subscribedAt: { $gte: thirtyDaysAgo },\n      }),\n      \n      // Contact form statistics\n      ContactForm.countDocuments(),\n      \n      // Unread contact forms\n      ContactForm.countDocuments({ isRead: false }),\n      \n      // Recent contact forms (last 30 days)\n      ContactForm.countDocuments({\n        createdAt: { $gte: thirtyDaysAgo },\n      }),\n      \n      // Order statistics\n      ProductOrder.countDocuments(),\n      \n      // Recent orders (last 30 days)\n      ProductOrder.countDocuments({\n        createdAt: { $gte: thirtyDaysAgo },\n      }),\n      \n      // Orders by status\n      ProductOrder.aggregate([\n        {\n          $group: {\n            _id: '$orderStatus',\n            count: { $sum: 1 },\n            totalAmount: { $sum: '$totalAmount' },\n          },\n        },\n      ]),\n    ]);\n\n    // Calculate growth rates (comparing last 30 days to previous 30 days)\n    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);\n    \n    const [\n      previousDonations,\n      previousSubscribers,\n      previousContacts,\n      previousOrders,\n    ] = await Promise.all([\n      Donation.countDocuments({\n        paymentStatus: 'completed',\n        createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },\n      }),\n      \n      Newsletter.countDocuments({\n        isActive: true,\n        subscribedAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },\n      }),\n      \n      ContactForm.countDocuments({\n        createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },\n      }),\n      \n      ProductOrder.countDocuments({\n        createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },\n      }),\n    ]);\n\n    // Calculate growth percentages\n    const calculateGrowth = (current: number, previous: number) => {\n      if (previous === 0) return current > 0 ? 100 : 0;\n      return Math.round(((current - previous) / previous) * 100);\n    };\n\n    // Format the response\n    const stats = {\n      donations: {\n        total: totalDonations[0]?.total || 0,\n        count: totalDonations[0]?.count || 0,\n        uniqueDonors: totalDonors.length,\n        recent: recentDonations,\n        growth: calculateGrowth(recentDonations, previousDonations),\n        byPurpose: donationsByPurpose.map(item => ({\n          purpose: item._id,\n          amount: item.totalAmount,\n          count: item.count,\n          percentage: totalDonations[0]?.total \n            ? Math.round((item.totalAmount / totalDonations[0].total) * 100)\n            : 0,\n        })),\n      },\n      \n      newsletter: {\n        totalSubscribers: totalNewsletterSubscribers,\n        recentSubscribers: recentSubscribers,\n        growth: calculateGrowth(recentSubscribers, previousSubscribers),\n      },\n      \n      contacts: {\n        total: totalContactForms,\n        unread: unreadContactForms,\n        recent: recentContactForms,\n        growth: calculateGrowth(recentContactForms, previousContacts),\n      },\n      \n      orders: {\n        total: totalOrders,\n        recent: recentOrders,\n        growth: calculateGrowth(recentOrders, previousOrders),\n        byStatus: ordersByStatus.map(item => ({\n          status: item._id,\n          count: item.count,\n          totalAmount: item.totalAmount,\n        })),\n      },\n      \n      // Impact metrics (these would be manually updated or calculated based on programs)\n      impact: {\n        livesImpacted: 15000, // This would come from program data\n        villagesReached: 250,\n        healthCamps: 180,\n        activePrograms: 12,\n        volunteers: 85,\n      },\n      \n      // Recent activity summary\n      recentActivity: {\n        donations: recentDonations,\n        subscribers: recentSubscribers,\n        contacts: recentContactForms,\n        orders: recentOrders,\n      },\n      \n      // Performance metrics\n      performance: {\n        donationGrowth: calculateGrowth(recentDonations, previousDonations),\n        subscriberGrowth: calculateGrowth(recentSubscribers, previousSubscribers),\n        contactGrowth: calculateGrowth(recentContactForms, previousContacts),\n        orderGrowth: calculateGrowth(recentOrders, previousOrders),\n      },\n    };\n\n    return NextResponse.json(\n      formatSuccessResponse(stats, 'Statistics retrieved successfully'),\n      { status: 200 }\n    );\n\n  } catch (error) {\n    console.error('Statistics fetch error:', error);\n    return NextResponse.json(\n      formatErrorResponse('An error occurred while fetching statistics.'),\n      { status: 500 }\n    );\n  }\n}\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/stats/route\",\n        pathname: \"/api/stats\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/stats/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/stats/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "u6CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAc,QAAQ,GAAG,CAAC,WAAW,CAE3C,GAAI,CAAC,EACH,MAAM,AAAI,KADM,CAEd,kEASJ,IAAI,EAAS,EAAA,CAAA,CAAO,GAAP,KAAe,AAExB,CAAC,IACH,EAAS,EADE,AACF,CAAA,CAAO,GAAP,KAAe,CAAG,CAAE,KAAM,KAAM,QAAS,KAAK,QAGzD,eAAe,EACb,GAAI,EAAO,IAAI,CACb,CADe,GAwBJ,GAvBJ,EAAO,IAAI,CAGf,EAAO,OAAO,EAAE,CAKnB,EAAO,OAAO,CAAG,EAAA,OAAQ,CAAC,OAAO,CAAC,EAJrB,CACX,UAG8C,MAH9B,CAClB,GAEsD,IAAI,CAAC,AAAC,GACnD,EACT,EAGF,GAAI,CACF,EAAO,IAAI,CAAG,MAAM,EAAO,OAAO,AACpC,CAAE,MAAO,EAAG,CAEV,MADA,EAAO,OAAO,CAAG,KACX,CACR,CAEA,OAAO,EAAO,IAAI,AACpB,iDC5CA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAoB,IAAI,EAAA,MAAM,CAClC,CACE,KAAM,CACJ,KAAM,OACN,SAAU,EAAC,EAAM,mBAAmB,CACpC,KAAM,GACN,UAAW,CAAC,IAAK,oCAAoC,AACvD,EACA,MAAO,CACL,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,MAAM,EACN,WAAW,EACX,MAAO,CACL,8CACA,qCACD,AACH,EACA,MAAO,CACL,KAAM,OACN,MAAM,EACN,MAAO,CACL,yBACA,oCACD,AACH,EACA,QAAS,CACP,KAAM,OACN,SAAU,EAAC,EAAM,sBAAsB,CACvC,MAAM,EACN,UAAW,CAAC,IAAK,uCAAuC,AAC1D,EACA,QAAS,CACP,KAAM,OACN,SAAU,CAAC,GAAM,sBAAsB,CACvC,KAAM,GACN,UAAW,CAAC,IAAM,wCAAwC,AAC5D,EACA,OAAQ,CACN,KAAM,QACN,SAAS,CACX,CACF,EACA,CACE,YAAY,EACZ,OAAQ,CAAE,SAAU,EAAK,EACzB,SAAU,CAAE,UAAU,CAAK,CAC7B,GAIF,EAAkB,KAAK,CAAC,CAAE,MAAO,CAAE,GACnC,EAAkB,KAAK,CAAC,CAAE,UAAW,CAAC,CAAE,GACxC,EAAkB,KAAK,CAAC,CAAE,OAAQ,CAAE,GAGpC,EAAkB,OAAO,CAAC,iBAAiB,GAAG,CAAC,WAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAS,CAChD,KAAM,UACN,MAAO,OACP,IAAK,UACL,KAAM,UACN,OAAQ,SACV,EACF,GAGA,EAAkB,GAAG,CAAC,OAAQ,SAAU,CAAI,EAE1C,IAAI,CAAC,IAAI,CAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAc,IAC5C,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAc,IAClD,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAc,IAElD,GACF,GAGA,EAAkB,OAAO,CAAC,cAAc,CAAG,WACzC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAE,QAAQ,CAAM,EAC7C,EAGA,EAAkB,OAAO,CAAC,UAAU,CAAG,SAAU,CAAU,EACzD,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAI,CAAE,QAAQ,CAAK,EAAG,CAAE,KAAK,CAAK,EAClE,EAGA,EAAkB,OAAO,CAAC,SAAS,CAAG,SAAU,EAAgB,EAAE,EAChE,OAAO,IAAI,CAAC,IAAI,GACb,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,KAAK,CAAC,GACN,MAAM,CAAC,sCACZ,QAEoB,EAAA,OAAQ,CAAC,MAAM,CAAC,WAAW,EAAI,CAEpC,CAFoC,OAAQ,CAAC,KAAK,CAAe,cAAe,kDCjG/F,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAmB,IAAI,EAAA,MAAM,CACjC,CACE,MAAO,CACL,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,QAAQ,EACR,MAAM,EACN,WAAW,EACX,MAAO,CACL,8CACA,qCACD,AACH,EACA,KAAM,CACJ,KAAM,OACN,MAAM,EACN,UAAW,CAAC,IAAK,oCAAoC,AACvD,EACA,SAAU,CACR,KAAM,QACN,QAAS,EACX,EACA,aAAc,CACZ,KAAM,KACN,QAAS,KAAK,GAChB,AADmB,EAEnB,eAAgB,CACd,KAAM,IACR,CACF,EACA,CACE,YAAY,EACZ,OAAQ,CAAE,UAAU,CAAK,EACzB,SAAU,CAAE,UAAU,CAAK,CAC7B,GAIF,EAAiB,KAAK,CAAC,CAAE,MAAO,CAAE,EAAG,CAAE,QAAQ,CAAK,GACpD,EAAiB,KAAK,CAAC,CAAE,SAAU,CAAE,GACrC,EAAiB,KAAK,CAAC,CAAE,aAAc,CAAC,CAAE,GAG1C,EAAiB,OAAO,CAAC,sBAAsB,GAAG,CAAC,WACjD,OAAO,IAAI,CAAC,QAAQ,CAAG,SAAW,cACpC,GAGA,EAAiB,OAAO,CAAC,6BAA6B,GAAG,CAAC,WACxD,OAAO,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,QAAS,CACnD,KAAM,UACN,MAAO,OACP,IAAK,SACP,EACF,GAGA,EAAiB,GAAG,CAAC,OAAQ,SAAU,CAAI,EAErC,AAAC,IAAI,CAAC,QAAQ,EAAK,EAAD,EAAK,CAAC,cAAc,EAAE,CAC1C,IAAI,CAAC,cAAc,CAAG,IAAI,IAAA,EAIxB,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,cAAc,EAAE,CACxC,IAAI,CAAC,cAAc,MAAG,CAAA,EAGxB,GACF,GAGA,EAAiB,OAAO,CAAC,cAAc,CAAG,WACxC,OAAO,IAAI,CAAC,cAAc,CAAC,CAAE,SAAU,EAAK,EAC9C,EAGA,EAAiB,OAAO,CAAC,WAAW,CAAG,SAAU,CAAa,EAC5D,OAAO,IAAI,CAAC,gBAAgB,CAC1B,OAAE,CAAM,EACR,CACE,UAAU,EACV,eAAgB,IAAI,IACtB,EACA,CAAE,KAAK,CAAK,EAEhB,EAGA,EAAiB,OAAO,CAAC,WAAW,CAAG,SAAU,CAAa,EAC5D,OAAO,IAAI,CAAC,gBAAgB,CAC1B,OAAE,CAAM,EACR,CACE,UAAU,EACV,OAAQ,CAAE,eAAgB,CAAE,CAC9B,EACA,CAAE,KAAK,EAAM,OAAQ,EAAK,EAE9B,EAGA,EAAiB,OAAO,CAAC,SAAS,CAAG,SAAU,EAAgB,EAAE,EAC/D,OAAO,IAAI,CAAC,IAAI,CAAC,CAAE,UAAU,CAAK,GAC/B,IAAI,CAAC,CAAE,aAAc,CAAC,CAAE,GACxB,KAAK,CAAC,GACN,MAAM,CAAC,0BACZ,EAGA,EAAiB,OAAO,CAAC,eAAe,CAAG,SAAU,CAAgB,EACnE,OAAO,IAAI,CAAC,UAAU,CACpB,CAAE,MAAO,CAAE,IAAK,CAAO,CAAE,EACzB,CACE,UAAU,EACV,eAAgB,IAAI,IACtB,EAEJ,QAEmB,EAAA,OAAQ,CAAC,MAAM,CAAC,UAAU,EAAI,EAAA,AAElC,OAF0C,CAAC,KAAK,CAAc,aAAc,iDC1H3F,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAiB,IAAI,EAAA,MAAM,CAC/B,CACE,UAAW,CACT,KAAM,OACN,SAAU,EAAC,EAAM,yBAAyB,CAC1C,MAAM,EACN,UAAW,CAAC,IAAK,oCAAoC,AACvD,EACA,WAAY,CACV,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,MAAM,EACN,WAAW,EACX,MAAO,CACL,8CACA,qCACD,AACH,EACA,WAAY,CACV,KAAM,OACN,KAAM,GACN,MAAO,CACL,yBACA,oCACD,AACH,EACA,OAAQ,CACN,KAAM,OACN,SAAU,EAAC,EAAM,8BAA8B,CAC/C,IAAK,CAAC,EAAG,6BAA6B,CACtC,IAAK,CAAC,IAAU,gCAAgC,AAClD,EACA,SAAU,CACR,KAAM,OACN,QAAS,MACT,KAAM,CAAC,MAAO,MAAO,MAAM,AAC7B,EACA,QAAS,CACP,KAAM,OACN,SAAU,EAAC,EAAM,sBAAsB,CACvC,KAAM,CAAC,UAAW,SAAU,YAAa,YAAa,cAAc,CACpE,QAAS,SACX,EACA,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,6BAA6B,CAC9C,KAAM,CAAC,SAAU,gBAAiB,OAAQ,SAAS,CACnD,QAAS,QACX,EACA,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,6BAA6B,CAC9C,KAAM,CAAC,UAAW,YAAa,SAAU,WAAW,CACpD,QAAS,SACX,EACA,cAAe,CACb,KAAM,OACN,MAAM,EACN,QAAQ,CACV,EACA,uBAAwB,CACtB,KAAM,EAAA,MAAM,CAAC,KAAK,CAAC,KAAK,AAC1B,EACA,YAAa,CACX,KAAM,QACN,SAAS,CACX,EACA,QAAS,CACP,OAAQ,CACN,KAAM,OACN,MAAM,EACN,UAAW,CAAC,IAAK,8CAA8C,AACjE,EACA,KAAM,CACJ,KAAM,OACN,MAAM,EACN,UAAW,CAAC,GAAI,mCAAmC,AACrD,EACA,MAAO,CACL,KAAM,OACN,MAAM,EACN,UAAW,CAAC,GAAI,oCAAoC,AACtD,EACA,QAAS,CACP,KAAM,OACN,MAAM,EACN,MAAO,CAAC,kBAAmB,+BAA+B,AAC5D,EACA,QAAS,CACP,KAAM,OACN,MAAM,EACN,QAAS,QACT,UAAW,CAAC,GAAI,sCAAsC,AACxD,CACF,EACA,UAAW,CACT,KAAM,OACN,MAAM,EACN,UAAW,GACX,MAAO,CAAC,6BAA8B,kCAAkC,AAC1E,EACA,cAAe,CACb,KAAM,OACN,MAAM,EACN,QAAQ,EACR,QAAQ,CACV,CACF,EACA,CACE,YAAY,EACZ,OAAQ,CAAE,UAAU,CAAK,EACzB,SAAU,CAAE,UAAU,CAAK,CAC7B,GAIF,EAAe,KAAK,CAAC,CAAE,WAAY,CAAE,GACrC,EAAe,KAAK,CAAC,CAAE,cAAe,CAAE,GACxC,EAAe,KAAK,CAAC,CAAE,QAAS,CAAE,GAClC,EAAe,KAAK,CAAC,CAAE,UAAW,CAAC,CAAE,GACrC,EAAe,KAAK,CAAC,CAAE,OAAQ,CAAC,CAAE,GAClC,EAAe,KAAK,CAAC,CAAE,cAAe,CAAE,EAAG,CAAE,QAAQ,CAAK,GAG1D,EAAe,OAAO,CAAC,mBAAmB,GAAG,CAAC,WAC5C,OAAO,IAAI,KAAK,YAAY,CAAC,QAAS,CACpC,MAAO,WACP,SAAU,IAAI,CAAC,QAAQ,AACzB,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CACvB,GAGA,EAAe,OAAO,CAAC,iBAAiB,GAAG,CAAC,WAC1C,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAS,CAChD,KAAM,UACN,MAAO,OACP,IAAK,SACP,EACF,GAGA,EAAe,OAAO,CAAC,sBAAsB,GAAG,CAAC,WAQ/C,MAAO,CAPY,CACjB,QAAS,eACT,OAAQ,eACR,UAAW,qBACX,UAAW,mBACX,YAAa,oBACf,CACiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAI,IAAI,CAAC,OAAO,AACjD,GAGA,EAAe,GAAG,CAAC,OAAQ,SAAU,CAAI,EAEvC,GAA2B,AAAvB,kBAAI,CAAC,aAAa,EAAoB,CAAC,IAAI,CAAC,aAAa,CAAE,CAC7D,IAAM,EAAO,IAAI,OAAO,WAAW,GAC7B,EAAQ,OAAO,IAAI,OAAO,QAAQ,GAAK,GAAG,QAAQ,CAAC,EAAG,KACtD,EAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,EAAG,GAAG,WAAW,GAClE,IAAI,CAAC,aAAa,CAAG,CAAC,GAAG,EAAE,EAAA,EAAO,EAAA,EAAQ,EAAA,CAAQ,AACpD,CAGA,IAAI,CAAC,SAAS,CAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAc,IAEtD,GACF,GAGA,EAAe,OAAO,CAAC,cAAc,CAAG,SAAU,CAAgB,EAEhE,OAAO,IAAI,CAAC,SAAS,CAAC,CACpB,CAAE,OAFU,CAEF,CAFY,SAAE,EAAS,cAAe,WAAY,EAAI,CAAE,cAAe,WAAY,CAE7E,EAChB,CAAE,OAAQ,CAAE,IAAK,KAAM,MAAO,CAAE,KAAM,SAAU,CAAE,CAAE,EACrD,CACH,EAGA,EAAe,OAAO,CAAC,QAAQ,CAAG,WAChC,OAAO,IAAI,CAAC,SAAS,CAAC,CACpB,CAAE,OAAQ,CAAE,cAAe,WAAY,CAAE,EACzC,CACE,OAAQ,CACN,IAAK,WACL,YAAa,CAAE,KAAM,SAAU,EAC/B,MAAO,CAAE,KAAM,CAAE,EACjB,UAAW,CAAE,KAAM,SAAU,CAC/B,CACF,EACD,CACH,EAGA,EAAe,OAAO,CAAC,SAAS,CAAG,SAAU,EAAgB,EAAE,EAC7D,OAAO,IAAI,CAAC,IAAI,CAAC,CAAE,cAAe,WAAY,GAC3C,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,KAAK,CAAC,GACN,MAAM,CAAC,iDACZ,EAGA,EAAe,OAAO,CAAC,YAAY,CAAG,SAAU,EAAgB,EAAE,EAChE,OAAO,IAAI,CAAC,SAAS,CAAC,CACpB,CAAE,OAAQ,CAAE,cAAe,YAAa,aAAa,CAAM,CAAE,EAC7D,CACE,OAAQ,CACN,IAAK,cACL,UAAW,CAAE,OAAQ,YAAa,EAClC,YAAa,CAAE,KAAM,SAAU,EAC/B,cAAe,CAAE,KAAM,CAAE,EACzB,aAAc,CAAE,KAAM,YAAa,CACrC,CACF,EACA,CAAE,MAAO,CAAE,YAAa,CAAC,CAAE,CAAE,EAC7B,CAAE,OAAQ,CAAM,EACjB,CACH,QAEiB,EAAA,OAAQ,CAAC,MAAM,CAAC,QAAQ,EAAI,EAAA,EAE9B,KAFsC,CAAC,KAAK,CAAY,WAAY,kDC7NnF,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAqB,IAAI,EAAA,MAAM,CACnC,CACE,aAAc,CACZ,KAAM,OACN,SAAU,EAAC,EAAM,4BAA4B,CAC7C,MAAM,EACN,UAAW,CAAC,IAAK,oCAAoC,AACvD,EACA,cAAe,CACb,KAAM,OACN,SAAU,CAAC,GAAM,oBAAoB,CACrC,MAAM,EACN,WAAW,EACX,MAAO,CACL,8CACA,qCAEJ,AADG,EAEH,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,2BAA2B,CAC5C,KAAM,GACN,MAAO,CACL,yBACA,oCACD,AACH,EACA,SAAU,CACR,CACE,UAAW,CACT,KAAM,OACN,SAAU,EAAC,EAAM,yBAAyB,AAC5C,EACA,YAAa,CACX,KAAM,OACN,SAAU,EAAC,EAAM,2BAA2B,CAC5C,MAAM,CACR,EACA,SAAU,CACR,KAAM,OACN,SAAU,EAAC,EAAM,uBAAuB,CACxC,IAAK,CAAC,EAAG,8BAA8B,CACvC,IAAK,CAAC,IAAK,6BAA6B,AAC1C,EACA,MAAO,CACL,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,IAAK,CAAC,EAAG,2BACX,AADsC,CAExC,EACD,CACD,YAAa,CACX,KAAM,OACN,SAAU,EAAC,EAAM,2BAA2B,CAC5C,IAAK,CAAC,EAAG,kCAAkC,AAC7C,EACA,gBAAiB,CACf,OAAQ,CACN,KAAM,OACN,SAAU,EAAC,EAAM,6BAA6B,CAC9C,MAAM,EACN,UAAW,CAAC,IAAK,8CAA8C,AACjE,EACA,KAAM,CACJ,KAAM,OACN,SAAU,EAAC,EAAM,mBAAmB,CACpC,MAAM,EACN,UAAW,CAAC,GAAI,mCAAmC,AACrD,EACA,MAAO,CACL,KAAM,OACN,SAAU,EAAC,EAAM,oBAAoB,CACrC,MAAM,EACN,UAAW,CAAC,GAAI,oCAAoC,AACtD,EACA,QAAS,CACP,KAAM,OACN,SAAU,EAAC,EAAM,sBAAsB,CACvC,MAAM,EACN,MAAO,CAAC,kBAAmB,+BAC7B,AAD4D,EAE5D,QAAS,CACP,KAAM,OACN,MAAM,EACN,QAAS,QACT,UAAW,CAAC,GAAI,sCAAsC,AACxD,CACF,EACA,YAAa,CACX,KAAM,OACN,SAAU,EAAC,EAAM,2BAA2B,CAC5C,KAAM,CAAC,UAAW,YAAa,aAAc,UAAW,YAAa,YAAY,CACjF,QAAS,SACX,EACA,cAAe,CACb,KAAM,OACN,SAAU,EAAC,EAAM,6BAA6B,CAC9C,KAAM,CAAC,UAAW,YAAa,SAAU,WAAW,CACpD,QAAS,SACX,EACA,cAAe,CACb,KAAM,OACN,SAAU,CAAC,GAAM,6BAA6B,CAC9C,KAAM,CAAC,MAAO,SAAU,gBAAgB,CACxC,QAAS,KACX,EACA,eAAgB,CACd,KAAM,OACN,MAAM,EACN,QAAQ,CACV,EACA,MAAO,CACL,KAAM,OACN,KAAM,GACN,UAAW,CAAC,IAAK,qCAAqC,AACxD,CACF,EACA,CACE,YAAY,EACZ,OAAQ,CAAE,UAAU,CAAK,EACzB,SAAU,CAAE,UAAU,CAAK,CAC7B,GAIF,EAAmB,KAAK,CAAC,CAAE,cAAe,CAAE,GAC5C,EAAmB,KAAK,CAAC,CAAE,YAAa,CAAE,GAC1C,EAAmB,KAAK,CAAC,CAAE,cAAe,CAAE,GAC5C,EAAmB,KAAK,CAAC,CAAE,UAAW,CAAC,CAAE,GACzC,EAAmB,KAAK,CAAC,CAAE,eAAgB,CAAE,EAAG,CAAE,QAAQ,CAAK,GAG/D,EAAmB,OAAO,CAAC,eAAe,GAAG,CAAC,WAC5C,IAAM,EAAO,IAAI,CAAC,SAAS,CAAC,WAAW,GACjC,EAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAK,GAAG,QAAQ,CAAC,EAAG,KAC1D,EAAK,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC,GAAG,WAAW,GACpD,MAAO,CAAC,GAAG,EAAE,EAAA,EAAO,EAAA,EAAQ,EAAA,CAAI,AAClC,GAGA,EAAmB,OAAO,CAAC,wBAAwB,GAAG,CAAC,WACrD,OAAO,IAAI,KAAK,YAAY,CAAC,QAAS,CACpC,MAAO,WACP,SAAU,KACZ,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAC5B,GAGA,EAAmB,OAAO,CAAC,sBAAsB,GAAG,CAAC,WACnD,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAS,CAChD,KAAM,UACN,MAAO,OACP,IAAK,SACP,EACF,GAGA,EAAmB,OAAO,CAAC,uBAAuB,GAAG,CAAC,WACpD,IAAM,EAAO,IAAI,CAAC,eAAe,CACjC,MAAO,CAAA,EAAG,EAAK,MAAM,CAAC,EAAE,EAAE,EAAK,IAAI,CAAC,EAAE,EAAE,EAAK,KAAK,CAAC,GAAG,EAAE,EAAK,OAAO,CAAC,EAAE,EAAE,EAAK,OAAO,CAAA,CAAE,AACzF,GAGA,EAAmB,OAAO,CAAC,0BAA0B,GAAG,CAAC,WASvD,MAAO,CARW,CAChB,QAAS,UACT,UAAW,YACX,WAAY,aACZ,QAAS,UACT,UAAW,YACX,UAAW,YACb,CACgB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAI,IAAI,CAAC,WAAW,AACxD,GAGA,EAAmB,OAAO,CAAC,4BAA4B,GAAG,CAAC,WAOzD,MAAO,CANW,CAChB,QAAS,UACT,UAAW,YACX,OAAQ,SACR,SAAU,WACZ,CACgB,CAAC,IAAI,CAAC,aAAa,CAAC,EAAI,IAAI,CAAC,aAAa,AAC5D,GAGA,EAAmB,GAAG,CAAC,OAAQ,SAAU,CAAI,EAS3C,GAPI,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAG,GAAG,AAC7C,KAAI,CAAC,WAAW,CAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAO,IACvC,EAAS,EAAQ,KAAK,CAAG,EAAQ,QAAQ,CAC/C,EAAA,EAIoB,YAArB,IAAI,CAAC,WAAW,EAAkB,CAAC,IAAI,CAAC,cAAc,CAAE,CAC1D,IAAM,EAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,EAAG,IAAI,WAAW,GACnE,IAAI,CAAC,cAAc,CAAG,CAAC,GAAG,EAAE,EAAA,CAAQ,AACtC,CAGA,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,aAAc,IACxD,IAAI,CAAC,KAAK,EAAE,CACd,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAc,GAAA,EAGhD,GACF,GAGA,EAAmB,OAAO,CAAC,QAAQ,CAAG,WACpC,OAAO,IAAI,CAAC,SAAS,CAAC,CACpB,CACE,OAAQ,CACN,IAAK,eACL,MAAO,CAAE,KAAM,CAAE,EACjB,YAAa,CAAE,KAAM,cAAe,CACtC,CACF,EACD,CACH,EAGA,EAAmB,OAAO,CAAC,SAAS,CAAG,SAAU,EAAgB,EAAE,EACjE,OAAO,IAAI,CAAC,IAAI,GACb,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,KAAK,CAAC,GACN,MAAM,CAAC,+DACZ,EAGA,EAAmB,OAAO,CAAC,aAAa,CAAG,SAAU,CAAa,EAChE,OAAO,IAAI,CAAC,IAAI,CAAC,CAAE,cAAe,CAAM,GACrC,IAAI,CAAC,CAAE,UAAW,CAAC,CAAE,GACrB,MAAM,CAAC,iBACZ,EAGA,EAAmB,OAAO,CAAC,YAAY,CAAG,SAAU,CAAe,CAAE,CAAc,EACjF,OAAO,IAAI,CAAC,iBAAiB,CAC3B,EACA,CAAE,YAAa,CAAO,EACtB,CAAE,KAAK,CAAK,EAEhB,QAEqB,EAAA,OAAQ,CAAC,MAAM,CAAC,YAAY,EAAI,AAEtC,EAFsC,OAAQ,CAAC,KAAK,CAAgB,eAAgB,2LE1PnG,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,8BDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,MAAM,CAAA,EAAA,EAAA,OAAA,AAAS,IAGf,IAAM,EAAM,IAAI,KACV,EAAgB,IAAI,KAAK,EAAI,OAAO,GAAK,KAAK,GACjC,AAAS,EAD6B,AACzB,EAAT,GADuC,EACvB,GAD4B,AAInE,EAH4C,CAGtC,CACJ,EACA,EACA,AANgD,EAOhD,EACA,CARqD,CASrD,EACA,EACA,AAX0D,EAY1D,EACA,CAb+D,CAc/D,EACA,EACD,CAAG,MAAM,QAAQ,GAAG,CAAC,CAEpB,EAAA,OAAQ,CAAC,SAAS,CAAC,CACjB,CAAE,OAAQ,CAAE,cAAe,WAAY,CAAE,EACzC,CAAE,OAAQ,CAAE,IAAK,KAAM,MAAO,CAAE,KAAM,SAAU,EAAG,MAAO,CAAE,KAAM,CAAE,CAAE,CAAE,EACzE,EAGD,EAAA,OAAQ,CAAC,QAAQ,CAAC,aAAc,CAAE,cAAe,WAAY,GAG7D,EAAA,OAAQ,CAAC,cAAc,CAAC,CACtB,cAAe,YACf,UAAW,CAAE,KAAM,CAAc,CACnC,GAGA,EAAA,OAAQ,CAAC,SAAS,CAAC,CACjB,CAAE,OAAQ,CAAE,cAAe,WAAY,CAAE,EACzC,CACE,OAAQ,CACN,IAAK,WACL,YAAa,CAAE,KAAM,SAAU,EAC/B,MAAO,CAAE,KAAM,CAAE,CACnB,CACF,EACD,EAGD,EAAA,OAAU,CAAC,cAAc,CAAC,CAAE,UAAU,CAAK,GAG3C,EAAA,OAAU,CAAC,cAAc,CAAC,CACxB,UAAU,EACV,aAAc,CAAE,KAAM,CAAc,CACtC,GAGA,EAAA,OAAW,CAAC,cAAc,GAG1B,EAAA,OAAW,CAAC,cAAc,CAAC,CAAE,QAAQ,CAAM,GAG3C,EAAA,OAAW,CAAC,cAAc,CAAC,CACzB,UAAW,CAAE,KAAM,CAAc,CACnC,GAGA,EAAA,OAAY,CAAC,cAAc,GAG3B,EAAA,OAAY,CAAC,cAAc,CAAC,CAC1B,UAAW,CAAE,KAAM,CAAc,CACnC,GAGA,EAAA,OAAY,CAAC,SAAS,CAAC,CACrB,CACE,OAAQ,CACN,IAAK,eACL,MAAO,CAAE,KAAM,CAAE,EACjB,YAAa,CAAE,KAAM,cAAe,CACtC,CACF,EACD,EACF,EAGK,EAAe,IAAI,KAAK,EAAI,OAAO,GAAK,KAAK,GAE7C,CACJ,CAHsD,CAItD,EACA,EAL2D,AAM3D,EACD,CAAG,EAP8D,IAOxD,QAAQ,GAAG,CAAC,CACpB,EAAA,OAAQ,CAAC,cAAc,CAAC,CACtB,cAAe,YACf,UAAW,CAAE,KAAM,EAAc,IAAK,CAAc,CACtD,GAEA,EAAA,OAAU,CAAC,cAAc,CAAC,CACxB,UAAU,EACV,aAAc,CAAE,KAAM,EAAc,IAAK,CAAc,CACzD,GAEA,EAAA,OAAW,CAAC,cAAc,CAAC,CACzB,UAAW,CAAE,KAAM,EAAc,IAAK,CAAc,CACtD,GAEA,EAAA,OAAY,CAAC,cAAc,CAAC,CAC1B,UAAW,CAAE,KAAM,EAAc,IAAK,CAAc,CACtD,GACD,EAGK,EAAkB,CAAC,EAAiB,IACxC,AAAiB,GAAG,CAAhB,EAAqC,KAAd,CAAoB,EAAV,EAC9B,KAAK,KAAK,CAAE,CAAC,EAAU,CAAA,CAAQ,CAAI,EAAY,KAIlD,EAAQ,CACZ,UAAW,CACT,MAAO,CAAc,CAAC,EAAE,EAAE,OAAS,EACnC,MAAO,CAAc,CAAC,EAAE,EAAE,OAAS,EACnC,aAAc,EAAY,MAAM,CAChC,OAAQ,EACR,OAAQ,EAAgB,EAAiB,GACzC,UAAW,EAAmB,GAAG,CAAC,IAAS,CACzC,EADwC,MAC/B,EAAK,GAAG,CACjB,OAAQ,EAAK,WAAW,CACxB,MAAO,EAAK,KAAK,CACjB,WAAY,CAAc,CAAC,EAAE,EAAE,MAC3B,KAAK,KAAK,CAAE,EAAK,WAAW,CAAG,CAAc,CAAC,EAAE,CAAC,KAAK,CAAI,KAC1D,EACN,CAAC,CACH,EAEA,WAAY,CACV,iBAAkB,EAClB,kBAAmB,EACnB,OAAQ,EAAgB,EAAmB,EAC7C,EAEA,SAAU,CACR,MAAO,EACP,OAAQ,EACR,OAAQ,EACR,OAAQ,EAAgB,EAAoB,EAC9C,EAEA,OAAQ,CACN,MAAO,EACP,OAAQ,EACR,OAAQ,EAAgB,EAAc,GACtC,SAAU,EAAe,GAAG,CAAC,GAAS,EACpC,EADmC,KAC3B,EAAK,GAAG,CAChB,MAAO,EAAK,KAAK,CACjB,YAAa,EAAK,WAAW,CAC/B,CAAC,CACH,EAGA,OAAQ,CACN,cAAe,KACf,gBAAiB,IACjB,YAAa,IACb,eAAgB,GAChB,WAAY,EACd,EAGA,eAAgB,CACd,UAAW,EACX,YAAa,EACb,SAAU,EACV,OAAQ,CACV,EAGA,YAAa,CACX,eAAgB,EAAgB,EAAiB,GACjD,iBAAkB,EAAgB,EAAmB,GACrD,cAAe,EAAgB,EAAoB,GACnD,YAAa,EAAgB,EAAc,EAC7C,CACF,EAEA,OAAO,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAO,qCAC7B,CAAE,OAAQ,GAAI,EAGlB,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,0BAA2B,GAClC,EAAA,YAAY,CAAC,IAAI,CACtB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,gDACpB,CAAE,OAAQ,GAAI,EAElB,CACF,CCrMA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,mBACN,SAAU,aACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,uCAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,mBAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,CACtD,UACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,GAC+B,KAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,IAC7C,GAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,oBACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,OAAvD,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,EAAS,OAAO,EACtD,GACA,EAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,IACxC,SACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAA2B,AAA3B,EAA4B,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAC3E,AAD6F,EACrF,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [6]}