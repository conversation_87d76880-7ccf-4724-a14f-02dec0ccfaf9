(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,98183,(e,t,i)=>{"use strict";function r(e){let t={};for(let[i,r]of e.entries()){let e=t[i];void 0===e?t[i]=r:Array.isArray(e)?e.push(r):t[i]=[e,r]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function s(e){let t=new URLSearchParams;for(let[i,r]of Object.entries(e))if(Array.isArray(r))for(let e of r)t.append(i,n(e));else t.set(i,n(r));return t}function a(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];for(let t of i){for(let i of t.keys())e.delete(i);for(let[i,r]of t.entries())e.append(i,r)}return e}Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return s}})},95057,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{formatUrl:function(){return s},formatWithValidation:function(){return o},urlObjectKeys:function(){return a}});let r=e.r(90809)._(e.r(98183)),n=/https?|ftp|gopher|file/;function s(e){let{auth:t,hostname:i}=e,s=e.protocol||"",a=e.pathname||"",o=e.hash||"",l=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:i&&(c=t+(~i.indexOf(":")?"["+i+"]":i),e.port&&(c+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let d=e.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),e.slashes||(!s||n.test(s))&&!1!==c?(c="//"+(c||""),a&&"/"!==a[0]&&(a="/"+a)):c||(c=""),o&&"#"!==o[0]&&(o="#"+o),d&&"?"!==d[0]&&(d="?"+d),""+s+c+(a=a.replace(/[?#]/g,encodeURIComponent))+(d=d.replace("#","%23"))+o}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return s(e)}},18581,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"useMergedRef",{enumerable:!0,get:function(){return n}});let r=e.r(71645);function n(e,t){let i=(0,r.useRef)(null),n=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=i.current;e&&(i.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(i.current=s(e,r)),t&&(n.current=s(t,r))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let i=e(t);return"function"==typeof i?i:()=>e(null)}}("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},18967,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{DecodeError:function(){return f},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return x},NormalizeError:function(){return g},PageNotFoundError:function(){return v},SP:function(){return m},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return c},getLocationOrigin:function(){return o},getURL:function(){return l},isAbsoluteUrl:function(){return a},isResSent:function(){return d},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,i=!1;return function(){for(var r=arguments.length,n=Array(r),s=0;s<r;s++)n[s]=arguments[s];return i||(i=!0,t=e(...n)),t}}let s=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>s.test(e);function o(){let{protocol:e,hostname:t,port:i}=window.location;return e+"//"+t+(i?":"+i:"")}function l(){let{href:e}=window.location,t=o();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function d(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let i=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(i&&d(i))return r;if(!r)throw Object.defineProperty(Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let m="undefined"!=typeof performance,p=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class f extends Error{}class g extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"isLocalURL",{enumerable:!0,get:function(){return s}});let r=e.r(18967),n=e.r(52817);function s(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),i=new URL(e,t);return i.origin===t&&(0,n.hasBasePath)(i.pathname)}catch(e){return!1}}},84508,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},22016,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{default:function(){return g},useLinkStatus:function(){return x}});let r=e.r(90809),n=e.r(43476),s=r._(e.r(71645)),a=e.r(95057),o=e.r(8372),l=e.r(18581),c=e.r(18967),d=e.r(5550);e.r(33525);let u=e.r(91949),h=e.r(73668),m=e.r(99781);e.r(84508);let p=e.r(65165);function f(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function g(e){var t;let i,r,a,[g,x]=(0,s.useOptimistic)(u.IDLE_LINK_STATUS),y=(0,s.useRef)(null),{href:b,as:w,children:j,prefetch:k=null,passHref:P,replace:N,shallow:S,scroll:T,onClick:C,onMouseEnter:A,onTouchStart:M,legacyBehavior:E=!1,onNavigate:R,ref:V,unstable_dynamicOnHover:D,...O}=e;i=j,E&&("string"==typeof i||"number"==typeof i)&&(i=(0,n.jsx)("a",{children:i}));let I=s.default.useContext(o.AppRouterContext),L=!1!==k,z=!1!==k?null===(t=k)||"auto"===t?p.FetchStrategy.PPR:p.FetchStrategy.Full:p.FetchStrategy.PPR,{href:F,as:B}=s.default.useMemo(()=>{let e=f(b);return{href:e,as:w?f(w):e}},[b,w]);E&&(r=s.default.Children.only(i));let _=E?r&&"object"==typeof r&&r.ref:V,U=s.default.useCallback(e=>(null!==I&&(y.current=(0,u.mountLinkInstance)(e,F,I,z,L,x)),()=>{y.current&&((0,u.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,u.unmountPrefetchableInstance)(e)}),[L,F,I,z,x]),W={ref:(0,l.useMergedRef)(U,_),onClick(e){E||"function"!=typeof C||C(e),E&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),I&&(e.defaultPrevented||function(e,t,i,r,n,a,o){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,h.isLocalURL)(t)){n&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),o){let e=!1;if(o({preventDefault:()=>{e=!0}}),e)return}s.default.startTransition(()=>{(0,m.dispatchNavigateAction)(i||t,n?"replace":"push",null==a||a,r.current)})}}(e,F,B,y,N,T,R))},onMouseEnter(e){E||"function"!=typeof A||A(e),E&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),I&&L&&(0,u.onNavigationIntent)(e.currentTarget,!0===D)},onTouchStart:function(e){E||"function"!=typeof M||M(e),E&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),I&&L&&(0,u.onNavigationIntent)(e.currentTarget,!0===D)}};return(0,c.isAbsoluteUrl)(B)?W.href=B:E&&!P&&("a"!==r.type||"href"in r.props)||(W.href=(0,d.addBasePath)(B)),a=E?s.default.cloneElement(r,W):(0,n.jsx)("a",{...O,...W,children:i}),(0,n.jsx)(v.Provider,{value:g,children:a})}let v=(0,s.createContext)(u.IDLE_LINK_STATUS),x=()=>(0,s.useContext)(v);("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},88143,(e,t,i)=>{"use strict";function r(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:n,blurDataURL:s,objectFit:a}=e,o=r?40*r:t,l=n?40*n:i,c=o&&l?"viewBox='0 0 "+o+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},87690,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},8927,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"getImgProps",{enumerable:!0,get:function(){return l}}),e.r(33525);let r=e.r(88143),n=e.r(87690),s=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let c,d,u,{src:h,sizes:m,unoptimized:p=!1,priority:f=!1,loading:g,className:v,quality:x,width:y,height:b,fill:w=!1,style:j,overrideSrc:k,onLoad:P,onLoadingComplete:N,placeholder:S="empty",blurDataURL:T,fetchPriority:C,decoding:A="async",layout:M,objectFit:E,objectPosition:R,lazyBoundary:V,lazyRoot:D,...O}=e,{imgConf:I,showAltText:L,blurComplete:z,defaultLoader:F}=t,B=I||n.imageConfigDefault;if("allSizes"in B)c=B;else{let e=[...B.deviceSizes,...B.imageSizes].sort((e,t)=>e-t),t=B.deviceSizes.sort((e,t)=>e-t),r=null==(i=B.qualities)?void 0:i.sort((e,t)=>e-t);c={...B,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===F)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let _=O.loader||F;delete O.loader,delete O.srcSet;let U="__next_img_default"in _;if(U){if("custom"===c.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=_;_=t=>{let{config:i,...r}=t;return e(r)}}if(M){"fill"===M&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!m&&(m=t)}let W="",H=o(y),G=o(b);if((l=h)&&"object"==typeof l&&(a(l)||void 0!==l.src)){let e=a(h)?h.default:h;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,u=e.blurHeight,T=T||e.blurDataURL,W=e.src,!w)if(H||G){if(H&&!G){let t=H/e.width;G=Math.round(e.height*t)}else if(!H&&G){let t=G/e.height;H=Math.round(e.width*t)}}else H=e.width,G=e.height}let Y=!f&&("lazy"===g||void 0===g);(!(h="string"==typeof h?h:W)||h.startsWith("data:")||h.startsWith("blob:"))&&(p=!0,Y=!1),c.unoptimized&&(p=!0),U&&!c.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(p=!0);let X=o(x),q=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:E,objectPosition:R}:{},L?{}:{color:"transparent"},j),K=z||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:H,heightInt:G,blurWidth:d,blurHeight:u,blurDataURL:T||"",objectFit:q.objectFit})+'")':'url("'+S+'")',$=s.includes(q.objectFit)?"fill"===q.objectFit?"100% 100%":"cover":q.objectFit,Z=K?{backgroundSize:$,backgroundPosition:q.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},J=function(e){let{config:t,src:i,unoptimized:r,width:n,quality:s,sizes:a,loader:o}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:c}=function(e,t,i){let{deviceSizes:r,allSizes:n}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,a),d=l.length-1;return{sizes:a||"w"!==c?a:"100vw",srcSet:l.map((e,r)=>o({config:t,src:i,quality:s,width:e})+" "+("w"===c?e:r+1)+c).join(", "),src:o({config:t,src:i,quality:s,width:l[d]})}}({config:c,src:h,unoptimized:p,width:H,quality:X,sizes:m,loader:_});return{props:{...O,loading:Y?"lazy":g,fetchPriority:C,width:H,height:G,decoding:A,className:v,style:{...q,...Z},sizes:J.sizes,srcSet:J.srcSet,src:k||J.src},meta:{unoptimized:p,priority:f,placeholder:S,fill:w}}}},98879,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"default",{enumerable:!0,get:function(){return o}});let r=e.r(71645),n="undefined"==typeof window,s=n?()=>{}:r.useLayoutEffect,a=n?()=>{}:r.useEffect;function o(e){let{headManager:t,reduceComponentsToState:i}=e;function o(){if(t&&t.mountedInstances){let n=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(i(n,e))}}if(n){var l;null==t||null==(l=t.mountedInstances)||l.add(e.children),o()}return s(()=>{var i;return null==t||null==(i=t.mountedInstances)||i.add(e.children),()=>{var i;null==t||null==(i=t.mountedInstances)||i.delete(e.children)}}),s(()=>(t&&(t._pendingUpdate=o),()=>{t&&(t._pendingUpdate=o)})),a(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},58908,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=e.r(55682)._(e.r(71645)).default.createContext({})},15986,(e,t,i)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||i&&r}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"isInAmpMode",{enumerable:!0,get:function(){return r}})},25633,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{default:function(){return f},defaultHead:function(){return u}});let r=e.r(55682),n=e.r(90809),s=e.r(43476),a=n._(e.r(71645)),o=r._(e.r(98879)),l=e.r(58908),c=e.r(42732),d=e.r(15986);function u(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function h(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}e.r(33525);let m=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:i}=t;return e.reduce(h,[]).reverse().concat(u(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,r={};return n=>{let s=!0,a=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){a=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?s=!1:t.add(n.type);break;case"meta":for(let e=0,t=m.length;e<t;e++){let t=m[e];if(n.props.hasOwnProperty(t))if("charSet"===t)i.has(t)?s=!1:i.add(t);else{let e=n.props[t],i=r[t]||new Set;("name"!==t||!a)&&i.has(e)?s=!1:(i.add(e),r[t]=i)}}}return s}}()).reverse().map((e,t)=>{let i=e.key||t;return a.default.cloneElement(e,{key:i})})}let f=function(e){let{children:t}=e,i=(0,a.useContext)(l.AmpStateContext),r=(0,a.useContext)(c.HeadManagerContext);return(0,s.jsx)(o.default,{reduceComponentsToState:p,headManager:r,inAmpMode:(0,d.isInAmpMode)(i),children:t})};("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},18556,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"ImageConfigContext",{enumerable:!0,get:function(){return s}});let r=e.r(55682)._(e.r(71645)),n=e.r(87690),s=r.default.createContext(n.imageConfigDefault)},65856,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"RouterContext",{enumerable:!0,get:function(){return r}});let r=e.r(55682)._(e.r(71645)).default.createContext(null)},1948,(e,t,i)=>{"use strict";function r(e){var t;let{config:i,src:r,width:n,quality:s}=e,a=s||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+a+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},85437,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),Object.defineProperty(i,"Image",{enumerable:!0,get:function(){return b}});let r=e.r(55682),n=e.r(90809),s=e.r(43476),a=n._(e.r(71645)),o=r._(e.r(74080)),l=r._(e.r(25633)),c=e.r(8927),d=e.r(87690),u=e.r(18556);e.r(33525);let h=e.r(65856),m=r._(e.r(1948)),p=e.r(18581),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,i,r,n,s,a){let o=null==e?void 0:e.src;e&&e["data-loaded-src"]!==o&&(e["data-loaded-src"]=o,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,n=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function v(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let x=(0,a.forwardRef)((e,t)=>{let{src:i,srcSet:r,sizes:n,height:o,width:l,decoding:c,className:d,style:u,fetchPriority:h,placeholder:m,loading:f,unoptimized:x,fill:y,onLoadRef:b,onLoadingCompleteRef:w,setBlurComplete:j,setShowAltText:k,sizesInput:P,onLoad:N,onError:S,...T}=e,C=(0,a.useCallback)(e=>{e&&(S&&(e.src=e.src),e.complete&&g(e,m,b,w,j,x,P))},[i,m,b,w,j,S,x,P]),A=(0,p.useMergedRef)(t,C);return(0,s.jsx)("img",{...T,...v(h),loading:f,width:l,height:o,decoding:c,"data-nimg":y?"fill":"1",className:d,style:u,sizes:n,srcSet:r,src:i,ref:A,onLoad:e=>{g(e.currentTarget,m,b,w,j,x,P)},onError:e=>{k(!0),"empty"!==m&&j(!0),S&&S(e)}})});function y(e){let{isAppRouter:t,imgAttributes:i}=e,r={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...v(i.fetchPriority)};return t&&o.default.preload?(o.default.preload(i.src,r),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...r},"__nimg-"+i.src+i.srcSet+i.sizes)})}let b=(0,a.forwardRef)((e,t)=>{let i=(0,a.useContext)(h.RouterContext),r=(0,a.useContext)(u.ImageConfigContext),n=(0,a.useMemo)(()=>{var e;let t=f||r||d.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:n,qualities:s}},[r]),{onLoad:o,onLoadingComplete:l}=e,p=(0,a.useRef)(o);(0,a.useEffect)(()=>{p.current=o},[o]);let g=(0,a.useRef)(l);(0,a.useEffect)(()=>{g.current=l},[l]);let[v,b]=(0,a.useState)(!1),[w,j]=(0,a.useState)(!1),{props:k,meta:P}=(0,c.getImgProps)(e,{defaultLoader:m.default,imgConf:n,blurComplete:v,showAltText:w});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x,{...k,unoptimized:P.unoptimized,placeholder:P.placeholder,fill:P.fill,onLoadRef:p,onLoadingCompleteRef:g,setBlurComplete:b,setShowAltText:j,sizesInput:e.sizes,ref:t}),P.priority?(0,s.jsx)(y,{isAppRouter:!i,imgAttributes:k}):null]})});("function"==typeof i.default||"object"==typeof i.default&&null!==i.default)&&void 0===i.default.__esModule&&(Object.defineProperty(i.default,"__esModule",{value:!0}),Object.assign(i.default,i),t.exports=i.default)},94909,(e,t,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(i,{default:function(){return l},getImageProps:function(){return o}});let r=e.r(55682),n=e.r(8927),s=e.r(85437),a=r._(e.r(1948));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=s.Image},57688,(e,t,i)=>{t.exports=e.r(94909)},46932,31178,37806,21476,47414,74008,64978,72846,51360,e=>{"use strict";let t;e.s(["motion",()=>ss],46932);var i=e.i(71645);let r=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],n=new Set(r),s=e=>180*e/Math.PI,a=e=>l(s(Math.atan2(e[1],e[0]))),o={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:a,rotateZ:a,skewX:e=>s(Math.atan(e[1])),skewY:e=>s(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},l=e=>((e%=360)<0&&(e+=360),e),c=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),d=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),u={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:c,scaleY:d,scale:e=>(c(e)+d(e))/2,rotateX:e=>l(s(Math.atan2(e[6],e[5]))),rotateY:e=>l(s(Math.atan2(-e[2],e[0]))),rotateZ:a,rotate:a,skewX:e=>s(Math.atan(e[4])),skewY:e=>s(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function h(e){return+!!e.includes("scale")}function m(e,t){let i,r;if(!e||"none"===e)return h(t);let n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=u,r=n;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=o,r=t}if(!r)return h(t);let s=i[t],a=r[1].split(",").map(p);return"function"==typeof s?s(a):a[s]}function p(e){return parseFloat(e.trim())}let f=e=>t=>"string"==typeof t&&t.startsWith(e),g=f("--"),v=f("var(--"),x=e=>!!v(e)&&y.test(e.split("/*")[0].trim()),y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function b(e){let{top:t,left:i,right:r,bottom:n}=e;return{x:{min:i,max:r},y:{min:t,max:n}}}let w=(e,t,i)=>e+(t-e)*i;function j(e){return void 0===e||1===e}function k(e){let{scale:t,scaleX:i,scaleY:r}=e;return!j(t)||!j(i)||!j(r)}function P(e){return k(e)||N(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function N(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function S(e,t,i,r,n){return void 0!==n&&(e=r+n*(e-r)),r+i*(e-r)+t}function T(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,n=arguments.length>4?arguments[4]:void 0;e.min=S(e.min,t,i,r,n),e.max=S(e.max,t,i,r,n)}function C(e,t){let{x:i,y:r}=t;T(e.x,i.translate,i.scale,i.originPoint),T(e.y,r.translate,r.scale,r.originPoint)}function A(e,t){e.min=e.min+t,e.max=e.max+t}function M(e,t,i,r){let n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5,s=w(e.min,e.max,n);T(e,t,i,s,r)}function E(e,t){M(e.x,t.x,t.scaleX,t.scale,t.originX),M(e.y,t.y,t.scaleY,t.scale,t.originY)}function R(e,t){return b(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let V=new Set(["width","height","top","left","right","bottom",...r]),D=(e,t,i)=>i>t?t:i<e?e:i,O={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},I={...O,transform:e=>D(0,1,e)},L={...O,default:1},z=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),F=z("deg"),B=z("%"),_=z("px"),U=z("vh"),W=z("vw"),H={...B,parse:e=>B.parse(e)/100,transform:e=>B.transform(100*e)},G=e=>t=>t.test(e),Y=[O,_,B,F,W,U,{test:e=>"auto"===e,parse:e=>e}],X=e=>Y.find(G(e));e.i(47167);let q=()=>{},K=()=>{},$=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Z=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,J=e=>e===O||e===_,Q=new Set(["x","y","z"]),ee=r.filter(e=>!Q.has(e)),et={width:(e,t)=>{let{x:i}=e,{paddingLeft:r="0",paddingRight:n="0"}=t;return i.max-i.min-parseFloat(r)-parseFloat(n)},height:(e,t)=>{let{y:i}=e,{paddingTop:r="0",paddingBottom:n="0"}=t;return i.max-i.min-parseFloat(r)-parseFloat(n)},top:(e,t)=>{let{top:i}=t;return parseFloat(i)},left:(e,t)=>{let{left:i}=t;return parseFloat(i)},bottom:(e,t)=>{let{y:i}=e,{top:r}=t;return parseFloat(r)+(i.max-i.min)},right:(e,t)=>{let{x:i}=e,{left:r}=t;return parseFloat(r)+(i.max-i.min)},x:(e,t)=>{let{transform:i}=t;return m(i,"x")},y:(e,t)=>{let{transform:i}=t;return m(i,"y")}};et.translateX=et.x,et.translateY=et.y;let ei=e=>e,er={},en=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],es={value:null,addProjectionMetrics:null};function ea(e,t){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=en.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,r=new Set,n=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function c(t){a.has(t)&&(d.schedule(t),e()),l++,t(o)}let d={schedule:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=s&&n?i:r;return t&&a.add(e),o.has(e)||o.add(e),e},cancel:e=>{r.delete(e),a.delete(e)},process:e=>{if(o=e,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(c),t&&es.value&&es.value.frameloop[t].push(l),l=0,i.clear(),n=!1,s&&(s=!1,d.process(e))}};return d}(s,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:c,preUpdate:d,update:u,preRender:h,render:m,postRender:p}=a,f=()=>{let s=er.useManualTiming?n.timestamp:performance.now();i=!1,er.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,o.process(n),l.process(n),c.process(n),d.process(n),u.process(n),h.process(n),m.process(n),p.process(n),n.isProcessing=!1,i&&t&&(r=!1,e(f))};return{schedule:en.reduce((t,s)=>{let o=a[s];return t[s]=function(t){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return!i&&(i=!0,r=!0,n.isProcessing||e(f)),o.schedule(t,s,a)},t},{}),cancel:e=>{for(let t=0;t<en.length;t++)a[en[t]].cancel(e)},state:n,steps:a}}let{schedule:eo,cancel:el,state:ec,steps:ed}=ea("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ei,!0),eu=new Set,eh=!1,em=!1,ep=!1;function ef(){if(em){let e=Array.from(eu).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return ee.forEach(i=>{let r=e.getValue(i);void 0!==r&&(t.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(t=>{var i;let[r,n]=t;null==(i=e.getValue(r))||i.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}em=!1,eh=!1,eu.forEach(e=>e.complete(ep)),eu.clear()}function eg(){eu.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(em=!0)})}class ev{scheduleResolve(){this.state="scheduled",this.isAsync?(eu.add(this),eh||(eh=!0,eo.read(eg),eo.resolveKeyframes(ef))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:r}=this;if(null===e[0]){let n=null==r?void 0:r.get(),s=e[e.length-1];if(void 0!==n)e[0]=n;else if(i&&t){let r=i.readValue(t,s);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=s),r&&void 0===n&&r.set(e[0])}for(let t=1;t<e.length;t++)null!=e[t]||(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),eu.delete(this)}cancel(){"scheduled"===this.state&&(eu.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}constructor(e,t,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}}let ex=e=>/^0[^.\s]+$/u.test(e),ey=e=>Math.round(1e5*e)/1e5,eb=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ew=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ej=(e,t)=>i=>!!("string"==typeof i&&ew.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),ek=(e,t,i)=>r=>{if("string"!=typeof r)return r;let[n,s,a,o]=r.match(eb);return{[e]:parseFloat(n),[t]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},eP={...O,transform:e=>Math.round(D(0,255,e))},eN={test:ej("rgb","red"),parse:ek("red","green","blue"),transform:e=>{let{red:t,green:i,blue:r,alpha:n=1}=e;return"rgba("+eP.transform(t)+", "+eP.transform(i)+", "+eP.transform(r)+", "+ey(I.transform(n))+")"}},eS={test:ej("#"),parse:function(e){let t="",i="",r="",n="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),r=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),r=e.substring(3,4),n=e.substring(4,5),t+=t,i+=i,r+=r,n+=n),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:eN.transform},eT={test:ej("hsl","hue"),parse:ek("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:i,lightness:r,alpha:n=1}=e;return"hsla("+Math.round(t)+", "+B.transform(ey(i))+", "+B.transform(ey(r))+", "+ey(I.transform(n))+")"}},eC={test:e=>eN.test(e)||eS.test(e)||eT.test(e),parse:e=>eN.test(e)?eN.parse(e):eT.test(e)?eT.parse(e):eS.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eN.transform(e):eT.transform(e),getAnimatableNone:e=>{let t=eC.parse(e);return t.alpha=0,eC.transform(t)}},eA=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eM="number",eE="color",eR=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eV(e){let t=e.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,a=t.replace(eR,e=>(eC.test(e)?(r.color.push(s),n.push(eE),i.push(eC.parse(e))):e.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(e)):(r.number.push(s),n.push(eM),i.push(parseFloat(e))),++s,"${}")).split("${}");return{values:i,split:a,indexes:r,types:n}}function eD(e){return eV(e).values}function eO(e){let{split:t,types:i}=eV(e),r=t.length;return e=>{let n="";for(let s=0;s<r;s++)if(n+=t[s],void 0!==e[s]){let t=i[s];t===eM?n+=ey(e[s]):t===eE?n+=eC.transform(e[s]):n+=e[s]}return n}}let eI=e=>"number"==typeof e?0:eC.test(e)?eC.getAnimatableNone(e):e,eL={test:function(e){var t,i;return isNaN(e)&&"string"==typeof e&&((null==(t=e.match(eb))?void 0:t.length)||0)+((null==(i=e.match(eA))?void 0:i.length)||0)>0},parse:eD,createTransformer:eO,getAnimatableNone:function(e){let t=eD(e);return eO(e)(t.map(eI))}},ez=new Set(["brightness","contrast","saturate","opacity"]);function eF(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=i.match(eb)||[];if(!r)return e;let n=i.replace(r,""),s=+!!ez.has(t);return r!==i&&(s*=100),t+"("+s+n+")"}let eB=/\b([a-z-]*)\(.*?\)/gu,e_={...eL,getAnimatableNone:e=>{let t=e.match(eB);return t?t.map(eF).join(" "):e}},eU={...O,transform:Math.round},eW={borderWidth:_,borderTopWidth:_,borderRightWidth:_,borderBottomWidth:_,borderLeftWidth:_,borderRadius:_,radius:_,borderTopLeftRadius:_,borderTopRightRadius:_,borderBottomRightRadius:_,borderBottomLeftRadius:_,width:_,maxWidth:_,height:_,maxHeight:_,top:_,right:_,bottom:_,left:_,padding:_,paddingTop:_,paddingRight:_,paddingBottom:_,paddingLeft:_,margin:_,marginTop:_,marginRight:_,marginBottom:_,marginLeft:_,backgroundPositionX:_,backgroundPositionY:_,rotate:F,rotateX:F,rotateY:F,rotateZ:F,scale:L,scaleX:L,scaleY:L,scaleZ:L,skew:F,skewX:F,skewY:F,distance:_,translateX:_,translateY:_,translateZ:_,x:_,y:_,z:_,perspective:_,transformPerspective:_,opacity:I,originX:H,originY:H,originZ:_,zIndex:eU,fillOpacity:I,strokeOpacity:I,numOctaves:eU},eH={...eW,color:eC,backgroundColor:eC,outlineColor:eC,fill:eC,stroke:eC,borderColor:eC,borderTopColor:eC,borderRightColor:eC,borderBottomColor:eC,borderLeftColor:eC,filter:e_,WebkitFilter:e_},eG=e=>eH[e];function eY(e,t){let i=eG(e);return i!==e_&&(i=eL),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let eX=new Set(["auto","none","0"]);class eq extends ev{readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let r=e[i];if("string"==typeof r&&x(r=r.trim())){let n=function e(t,i){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;K(r<=4,'Max CSS variable fallback depth detected in property "'.concat(t,'". This may indicate a circular fallback dependency.'),"max-css-var-depth");let[n,s]=function(e){let t=Z.exec(e);if(!t)return[,];let[,i,r,n]=t;return["--".concat(null!=i?i:r),n]}(t);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let e=a.trim();return $(e)?parseFloat(e):e}return x(s)?e(s,i,r+1):s}(r,t.current);void 0!==n&&(e[i]=n),i===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!V.has(i)||2!==e.length)return;let[r,n]=e,s=X(r),a=X(n);if(s!==a)if(J(s)&&J(a))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else et[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||ex(r)))&&i.push(t)}i.length&&function(e,t,i){let r,n=0;for(;n<e.length&&!r;){let t=e[n];"string"==typeof t&&!eX.has(t)&&eV(t).values.length&&(r=e[n]),n++}if(r&&i)for(let n of t)e[n]=eY(i,r)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=et[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(i,r).jump(r,!1)}measureEndState(){var e;let{element:t,name:i,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let n=t.getValue(i);n&&n.jump(this.measuredOrigin,!1);let s=r.length-1,a=r[s];r[s]=et[i](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),(null==(e=this.removedTransforms)?void 0:e.length)&&this.removedTransforms.forEach(e=>{let[i,r]=e;t.getValue(i).set(r)}),this.resolveNoneKeyframes()}constructor(e,t,i,r,n){super(e,t,i,r,n,!0)}}let eK=e=>!!(e&&e.getVelocity);function e$(){t=void 0}let eZ={now:()=>(void 0===t&&eZ.set(ec.isProcessing||er.useManualTiming?ec.timestamp:performance.now()),t),set:e=>{t=e,queueMicrotask(e$)}};function eJ(e,t){-1===e.indexOf(t)&&e.push(t)}function eQ(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class e0{add(e){return eJ(this.subscriptions,e),()=>eQ(this.subscriptions,e)}notify(e,t,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}constructor(){this.subscriptions=[]}}let e1={current:void 0};class e2{setCurrent(e){this.current=e,this.updatedAt=eZ.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.current;this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new e0);let i=this.events[e].add(t);return"change"===e?()=>{i(),eo.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;null==(e=this.events.change)||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return e1.current&&e1.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=eZ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,t;null==(e=this.dependents)||e.clear(),null==(t=this.events.destroy)||t.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=e=>{let t=eZ.now();if(this.updatedAt!==t&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev){var i;if(null==(i=this.events.change)||i.notify(this.current),this.dependents)for(let e of this.dependents)e.dirty()}},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}}function e3(e,t){return new e2(e,t)}let e4=[...Y,eC,eL],{schedule:e5}=ea(queueMicrotask,!1),e6={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},e8={};for(let e in e6)e8[e]={isEnabled:t=>e6[e].some(e=>!!t[e])};let e9=()=>({translate:0,scale:1,origin:0,originPoint:0}),e7=()=>({x:e9(),y:e9()}),te=()=>({min:0,max:0}),tt=()=>({x:te(),y:te()}),ti="undefined"!=typeof window,tr={current:null},tn={current:!1},ts=new WeakMap;function ta(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function to(e){return"string"==typeof e||Array.isArray(e)}let tl=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tc=["initial",...tl];function td(e){return ta(e.animate)||tc.some(t=>to(e[t]))}function tu(e){return!!(td(e)||e.variants)}function th(e){let t=[{},{}];return null==e||e.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function tm(e,t,i,r){if("function"==typeof t){let[n,s]=th(r);t=t(void 0!==i?i:e.custom,n,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[n,s]=th(r);t=t(void 0!==i?i:e.custom,n,s)}return t}let tp=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tf{scrapeMotionValuesFromProps(e,t,i){return{}}mount(e){var t;this.current=e,ts.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),tn.current||function(){if(tn.current=!0,ti)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>tr.current=e.matches;e.addEventListener("change",t),t()}else tr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||tr.current),null==(t=this.parent)||t.addChild(this),this.update(this.props,this.presenceContext)}unmount(){var e;for(let t in this.projection&&this.projection.unmount(),el(this.notifyUpdate),el(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),null==(e=this.parent)||e.removeChild(this),this.events)this.events[t].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),null!=this.enteringChildren||(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=n.has(e);r&&this.onBindTransform&&this.onBindTransform();let s=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eo.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{s(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in e8){let t=e8[e];if(!t)continue;let{isEnabled:i,Feature:r}=t;if(!this.features[e]&&r&&i(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):tt()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<tp.length;t++){let i=tp[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(e,t,i){for(let r in t){let n=t[r],s=i[r];if(eK(n))e.addValue(r,n);else if(eK(s))e.addValue(r,e3(n,{owner:e}));else if(s!==n)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(n):t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(r);e.addValue(r,e3(void 0!==t?t:n,{owner:e}))}}for(let r in i)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=e3(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){var i;let r=void 0===this.latestValues[e]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,e))?i:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];if(null!=r){if("string"==typeof r&&($(r)||ex(r)))r=parseFloat(r);else{let i;i=r,!e4.find(G(i))&&eL.test(t)&&(r=eY(e,t))}this.setBaseTarget(e,eK(r)?r.get():r)}return eK(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){var r;let n=tm(this.props,i,null==(r=this.presenceContext)?void 0:r.custom);n&&(t=n[e])}if(i&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||eK(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new e0),this.events[e].add(t)}notify(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];this.events[e]&&this.events[e].notify(...i)}scheduleRenderMicrotask(){e5.render(this.render)}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ev,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=eZ.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,eo.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=td(t),this.isVariantNode=tu(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==o[e]&&eK(t)&&t.set(o[e])}}}class tg extends tf{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:i,style:r}=t;delete i[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eK(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent="".concat(e))}))}constructor(){super(...arguments),this.KeyframeResolver=eq}}let tv=(e,t)=>t&&"number"==typeof e?t.transform(e):e,tx={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ty=r.length;function tb(e,t,i){let{style:s,vars:a,transformOrigin:o}=e,l=!1,c=!1;for(let e in t){let i=t[e];if(n.has(e)){l=!0;continue}if(g(e)){a[e]=i;continue}{let t=tv(i,eW[e]);e.startsWith("origin")?(c=!0,o[e]=t):s[e]=t}}if(!t.transform&&(l||i?s.transform=function(e,t,i){let n="",s=!0;for(let a=0;a<ty;a++){let o=r[a],l=e[o];if(void 0===l)continue;let c=!0;if(!(c="number"==typeof l?l===+!!o.startsWith("scale"):0===parseFloat(l))||i){let e=tv(l,eW[o]);if(!c){s=!1;let t=tx[o]||o;n+="".concat(t,"(").concat(e,") ")}i&&(t[o]=e)}}return n=n.trim(),i?n=i(t,s?"":n):s&&(n="none"),n}(t,e.transform,i):s.transform&&(s.transform="none")),c){let{originX:e="50%",originY:t="50%",originZ:i=0}=o;s.transformOrigin="".concat(e," ").concat(t," ").concat(i)}}function tw(e,t,i,r){let n,{style:s,vars:a}=t,o=e.style;for(n in s)o[n]=s[n];for(n in null==r||r.applyProjectionStyles(o,i),a)o.setProperty(n,a[n])}let tj={};function tk(e,t){let{layout:i,layoutId:r}=t;return n.has(e)||e.startsWith("origin")||(i||void 0!==r)&&(!!tj[e]||"opacity"===e)}function tP(e,t,i){let{style:r}=e,n={};for(let a in r){var s;(eK(r[a])||t.style&&eK(t.style[a])||tk(a,e)||(null==i||null==(s=i.getValue(a))?void 0:s.liveStyle)!==void 0)&&(n[a]=r[a])}return n}class tN extends tg{readValueFromInstance(e,t){var i;if(n.has(t))return(null==(i=this.projection)?void 0:i.isProjecting)?h(t):((e,t)=>{let{transform:i="none"}=getComputedStyle(e);return m(i,t)})(e,t);{let i=window.getComputedStyle(e),r=(g(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,t){let{transformPagePoint:i}=t;return R(e,i)}build(e,t,i){tb(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return tP(e,t,i)}constructor(){super(...arguments),this.type="html",this.renderInstance=tw}}let tS=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tT={offset:"stroke-dashoffset",array:"stroke-dasharray"},tC={offset:"strokeDashoffset",array:"strokeDasharray"};function tA(e,t,i,r,n){var s,a;let{attrX:o,attrY:l,attrScale:c,pathLength:d,pathSpacing:u=1,pathOffset:h=0,...m}=t;if(tb(e,m,r),i){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:p,style:f}=e;p.transform&&(f.transform=p.transform,delete p.transform),(f.transform||p.transformOrigin)&&(f.transformOrigin=null!=(s=p.transformOrigin)?s:"50% 50%",delete p.transformOrigin),f.transform&&(f.transformBox=null!=(a=null==n?void 0:n.transformBox)?a:"fill-box",delete p.transformBox),void 0!==o&&(p.x=o),void 0!==l&&(p.y=l),void 0!==c&&(p.scale=c),void 0!==d&&function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=!(arguments.length>4)||void 0===arguments[4]||arguments[4];e.pathLength=1;let s=n?tT:tC;e[s.offset]=_.transform(-r);let a=_.transform(t),o=_.transform(i);e[s.array]="".concat(a," ").concat(o)}(p,d,u,h,!1)}let tM=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),tE=e=>"string"==typeof e&&"svg"===e.toLowerCase();function tR(e,t,i){let n=tP(e,t,i);for(let i in e)(eK(e[i])||eK(t[i]))&&(n[-1!==r.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return n}class tV extends tg{getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(n.has(t)){let e=eG(t);return e&&e.default||0}return t=tM.has(t)?t:tS(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return tR(e,t,i)}build(e,t,i){tA(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,r){for(let i in tw(e,t,void 0,r),t.attrs)e.setAttribute(tM.has(i)?i:tS(i),t.attrs[i])}mount(e){this.isSVGTag=tE(e.tagName),super.mount(e)}constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tt}}let tD=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tO(e){if("string"!=typeof e||e.includes("-"));else if(tD.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var tI=e.i(43476);e.s(["LayoutGroupContext",()=>tL],31178);let tL=(0,i.createContext)({}),tz=(0,i.createContext)({strict:!1});e.s(["MotionConfigContext",()=>tF],37806);let tF=(0,i.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),tB=(0,i.createContext)({});function t_(e){return Array.isArray(e)?e.join(" "):e}let tU=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tW(e,t,i){for(let r in t)eK(t[r])||tk(r,i)||(e[r]=t[r])}let tH=()=>({...tU(),attrs:{}}),tG=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tY(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||tG.has(e)}let tX=e=>!tY(e);try{!function(e){"function"==typeof e&&(tX=t=>t.startsWith("on")?!tY(t):e(t))}((()=>{let e=Error("Cannot find module '@emotion/is-prop-valid'");throw e.code="MODULE_NOT_FOUND",e})().default)}catch(e){}e.s(["PresenceContext",()=>tq],21476);let tq=(0,i.createContext)(null);function tK(e){let t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}function t$(e){return eK(e)?e.get():e}e.s(["useConstant",()=>tK],47414);let tZ=e=>(t,r)=>{let n=(0,i.useContext)(tB),s=(0,i.useContext)(tq),a=()=>(function(e,t,i,r){let{scrapeMotionValuesFromProps:n,createRenderState:s}=e;return{latestValues:function(e,t,i,r){let n={},s=r(e,{});for(let e in s)n[e]=t$(s[e]);let{initial:a,animate:o}=e,l=td(e),c=tu(e);t&&c&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===o&&(o=t.animate));let d=!!i&&!1===i.initial,u=(d=d||!1===a)?o:a;if(u&&"boolean"!=typeof u&&!ta(u)){let t=Array.isArray(u)?u:[u];for(let i=0;i<t.length;i++){let r=tm(e,t[i]);if(r){let{transitionEnd:e,transition:t,...i}=r;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(t,i,r,n),renderState:s()}})(e,t,n,s);return r?a():tK(a)},tJ=tZ({scrapeMotionValuesFromProps:tP,createRenderState:tU}),tQ=tZ({scrapeMotionValuesFromProps:tR,createRenderState:tH}),t0=Symbol.for("motionComponentSymbol");function t1(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let t2="data-"+tS("framerAppearId"),t3=(0,i.createContext)({});e.s(["useIsomorphicLayoutEffect",()=>t4],74008);let t4=ti?i.useLayoutEffect:i.useEffect;function t5(e){var t,r;let{forwardMotionProps:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;s&&function(e){for(let t in e)e8[t]={...e8[t],...e[t]}}(s);let o=tO(e)?tQ:tJ;function l(t,r){var s;let l,c={...(0,i.useContext)(tF),...t,layoutId:function(e){let{layoutId:t}=e,r=(0,i.useContext)(tL).id;return r&&void 0!==t?r+"-"+t:t}(t)},{isStatic:d}=c,u=function(e){let{initial:t,animate:r}=function(e,t){if(td(e)){let{initial:t,animate:i}=e;return{initial:!1===t||to(t)?t:void 0,animate:to(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(tB));return(0,i.useMemo)(()=>({initial:t,animate:r}),[t_(t),t_(r)])}(t),h=o(t,d);if(!d&&ti){(0,i.useContext)(tz).strict;let t=function(e){let{drag:t,layout:i}=e8;if(!t&&!i)return{};let r={...t,...i};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==i?void 0:i.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);l=t.MeasureLayout,u.visualElement=function(e,t,r,n,s){var a,o,l,c;let{visualElement:d}=(0,i.useContext)(tB),u=(0,i.useContext)(tz),h=(0,i.useContext)(tq),m=(0,i.useContext)(tF).reducedMotion,p=(0,i.useRef)(null);n=n||u.renderer,!p.current&&n&&(p.current=n(e,{visualState:t,parent:d,props:r,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:m}));let f=p.current,g=(0,i.useContext)(t3);f&&!f.projection&&s&&("html"===f.type||"svg"===f.type)&&function(e,t,i,r){let{layoutId:n,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:c,layoutCrossfade:d}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!a||o&&t1(o),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:d,layoutScroll:l,layoutRoot:c})}(p.current,r,s,g);let v=(0,i.useRef)(!1);(0,i.useInsertionEffect)(()=>{f&&v.current&&f.update(r,h)});let x=r[t2],y=(0,i.useRef)(!!x&&!(null==(a=(o=window).MotionHandoffIsComplete)?void 0:a.call(o,x))&&(null==(l=(c=window).MotionHasOptimisedAnimation)?void 0:l.call(c,x)));return t4(()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),f.scheduleRenderMicrotask(),y.current&&f.animationState&&f.animationState.animateChanges())}),(0,i.useEffect)(()=>{f&&(!y.current&&f.animationState&&f.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var e,t;null==(e=(t=window).MotionHandoffMarkAsComplete)||e.call(t,x)}),y.current=!1),f.enteringChildren=void 0)}),f}(e,h,c,a,t.ProjectionNode)}return(0,tI.jsxs)(tB.Provider,{value:u,children:[l&&u.visualElement?(0,tI.jsx)(l,{visualElement:u.visualElement,...c}):null,function(e,t,r,n,s){let{latestValues:a}=n,o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],l=(tO(e)?function(e,t,r,n){let s=(0,i.useMemo)(()=>{let i=tH();return tA(i,t,tE(n),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};tW(t,e.style,e),s.style={...t,...s.style}}return s}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return tW(n,r,e),Object.assign(n,function(e,t){let{transformTemplate:r}=e;return(0,i.useMemo)(()=>{let e=tU();return tb(e,t,r),Object.assign({},e.vars,e.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(t,a,s,e),c=function(e,t,i){let r={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(tX(n)||!0===i&&tY(n)||!t&&!tY(n)||e.draggable&&n.startsWith("onDrag"))&&(r[n]=e[n]);return r}(t,"string"==typeof e,o),d=e!==i.Fragment?{...c,...l,ref:r}:{},{children:u}=t,h=(0,i.useMemo)(()=>eK(u)?u.get():u,[u]);return(0,i.createElement)(e,{...d,children:h})}(e,t,(s=u.visualElement,(0,i.useCallback)(e=>{e&&h.onMount&&h.onMount(e),s&&(e?s.mount(e):s.unmount()),r&&("function"==typeof r?r(e):t1(r)&&(r.current=e))},[s,r])),h,d,n)]})}l.displayName="motion.".concat("string"==typeof e?e:"create(".concat(null!=(r=null!=(t=e.displayName)?t:e.name)?r:"",")"));let c=(0,i.forwardRef)(l);return c[t0]=e,c}function t6(e,t,i){let r=e.getProps();return tm(r,t,void 0!==i?i:r.custom,e)}function t8(e,t){var i,r;return null!=(r=null!=(i=null==e?void 0:e[t])?i:null==e?void 0:e.default)?r:e}let t9=e=>Array.isArray(e);function t7(e,t){let i=e.getValue("willChange");if(eK(i)&&i.add)return i.add(t);if(!i&&er.WillChange){let i=new er.WillChange("auto");e.addValue("willChange",i),i.add(t)}}function ie(e){e.duration=0,e.type}let it=(e,t)=>i=>t(e(i)),ii=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.reduce(it)},ir=e=>1e3*e,is={layout:0,mainThread:0,waapi:0};function ia(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function io(e,t){return i=>i>0?t:e}let il=(e,t,i)=>{let r=e*e,n=i*(t*t-r)+r;return n<0?0:Math.sqrt(n)},ic=[eS,eN,eT];function id(e){let t=ic.find(t=>t.test(e));if(q(!!t,"'".concat(e,"' is not an animatable color. Use the equivalent color code instead."),"color-not-animatable"),!t)return!1;let i=t.parse(e);return t===eT&&(i=function(e){let{hue:t,saturation:i,lightness:r,alpha:n}=e;t/=360,r/=100;let s=0,a=0,o=0;if(i/=100){let e=r<.5?r*(1+i):r+i-r*i,n=2*r-e;s=ia(n,e,t+1/3),a=ia(n,e,t),o=ia(n,e,t-1/3)}else s=a=o=r;return{red:Math.round(255*s),green:Math.round(255*a),blue:Math.round(255*o),alpha:n}}(i)),i}let iu=(e,t)=>{let i=id(e),r=id(t);if(!i||!r)return io(e,t);let n={...i};return e=>(n.red=il(i.red,r.red,e),n.green=il(i.green,r.green,e),n.blue=il(i.blue,r.blue,e),n.alpha=w(i.alpha,r.alpha,e),eN.transform(n))},ih=new Set(["none","hidden"]);function im(e,t){return i=>w(e,t,i)}function ip(e){return"number"==typeof e?im:"string"==typeof e?x(e)?io:eC.test(e)?iu:ix:Array.isArray(e)?ig:"object"==typeof e?eC.test(e)?iu:iv:io}function ig(e,t){let i=[...e],r=i.length,n=e.map((e,i)=>ip(e)(e,t[i]));return e=>{for(let t=0;t<r;t++)i[t]=n[t](e);return i}}function iv(e,t){let i={...e,...t},r={};for(let n in i)void 0!==e[n]&&void 0!==t[n]&&(r[n]=ip(e[n])(e[n],t[n]));return e=>{for(let t in r)i[t]=r[t](e);return i}}let ix=(e,t)=>{let i=eL.createTransformer(t),r=eV(e),n=eV(t);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?ih.has(e)&&!n.values.length||ih.has(t)&&!r.values.length?function(e,t){return ih.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):ii(ig(function(e,t){let i=[],r={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){var n;let a=t.types[s],o=e.indexes[a][r[a]],l=null!=(n=e.values[o])?n:0;i[s]=l,r[a]++}return i}(r,n),n.values),i):(q(!0,"Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition."),"complex-values-different"),io(e,t))};function iy(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?w(e,t,i):ip(e)(e,t)}let ib=e=>{let t=t=>{let{timestamp:i}=t;return e(i)};return{start:function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return eo.update(t,e)},stop:()=>el(t),now:()=>ec.isProcessing?ec.timestamp:eZ.now()}},iw=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r="",n=Math.max(Math.round(t/i),2);for(let t=0;t<n;t++)r+=Math.round(1e4*e(t/(n-1)))/1e4+", ";return"linear(".concat(r.substring(0,r.length-2),")")};function ij(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function ik(e,t,i){var r,n;let s=Math.max(t-5,0);return r=i-e(s),(n=t-s)?1e3/n*r:0}let iP={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iN(e,t){return e*Math.sqrt(1-t*t)}let iS=["duration","bounce"],iT=["stiffness","damping","mass"];function iC(e,t){return t.some(t=>void 0!==e[t])}function iA(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:iP.visualDuration,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:iP.bounce,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:i}:t,{restSpeed:n,restDelta:s}=r,a=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:c,damping:d,mass:u,duration:h,velocity:m,isResolvedFromDuration:p}=function(e){let t={velocity:iP.velocity,stiffness:iP.stiffness,damping:iP.damping,mass:iP.mass,isResolvedFromDuration:!1,...e};if(!iC(e,iT)&&iC(e,iS))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),r=i*i,n=2*D(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:iP.mass,stiffness:r,damping:n}}else{let i=function(e){let t,i,{duration:r=iP.duration,bounce:n=iP.bounce,velocity:s=iP.velocity,mass:a=iP.mass}=e;q(r<=ir(iP.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-n;o=D(iP.minDamping,iP.maxDamping,o),r=D(iP.minDuration,iP.maxDuration,r/1e3),o<1?(t=e=>{let t=e*o,i=t*r;return .001-(t-s)/iN(e,o)*Math.exp(-i)},i=e=>{let i=e*o*r,n=Math.pow(o,2)*Math.pow(e,2)*r,a=Math.exp(-i),l=iN(Math.pow(e,2),o);return(i*s+s-n)*a*(-t(e)+.001>0?-1:1)/l}):(t=e=>-.001+Math.exp(-e*r)*((e-s)*r+1),i=e=>r*r*(s-e)*Math.exp(-e*r));let l=function(e,t,i){let r=i;for(let i=1;i<12;i++)r-=e(r)/t(r);return r}(t,i,5/r);if(r=ir(r),isNaN(l))return{stiffness:iP.stiffness,damping:iP.damping,duration:r};{let e=Math.pow(l,2)*a;return{stiffness:e,damping:2*o*Math.sqrt(a*e),duration:r}}}(e);(t={...t,...i,mass:iP.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-((r.velocity||0)/1e3)}),f=m||0,g=d/(2*Math.sqrt(c*u)),v=o-a,x=Math.sqrt(c/u)/1e3,y=5>Math.abs(v);if(n||(n=y?iP.restSpeed.granular:iP.restSpeed.default),s||(s=y?iP.restDelta.granular:iP.restDelta.default),g<1){let t=iN(x,g);e=e=>o-Math.exp(-g*x*e)*((f+g*x*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)e=e=>o-Math.exp(-x*e)*(v+(f+x*v)*e);else{let t=x*Math.sqrt(g*g-1);e=e=>{let i=Math.exp(-g*x*e),r=Math.min(t*e,300);return o-i*((f+g*x*v)*Math.sinh(r)+t*v*Math.cosh(r))/t}}let b={calculatedDuration:p&&h||null,next:t=>{let i=e(t);if(p)l.done=t>=h;else{let r=0===t?f:0;g<1&&(r=0===t?ir(f):ik(e,t,i));let a=Math.abs(o-i)<=s;l.done=Math.abs(r)<=n&&a}return l.value=l.done?o:i,l},toString:()=>{let e=Math.min(ij(b),2e4),t=iw(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}function iM(e){let t,i,{keyframes:r,velocity:n=0,power:s=.8,timeConstant:a=325,bounceDamping:o=10,bounceStiffness:l=500,modifyTarget:c,min:d,max:u,restDelta:h=.5,restSpeed:m}=e,p=r[0],f={done:!1,value:p},g=s*n,v=p+g,x=void 0===c?v:c(v);x!==v&&(g=x-p);let y=e=>-g*Math.exp(-e/a),b=e=>x+y(e),w=e=>{let t=y(e),i=b(e);f.done=Math.abs(t)<=h,f.value=f.done?x:i},j=e=>{let r;if(r=f.value,void 0!==d&&r<d||void 0!==u&&r>u){var n;t=e,i=iA({keyframes:[f.value,(n=f.value,void 0===d?u:void 0===u||Math.abs(d-n)<Math.abs(u-n)?d:u)],velocity:ik(b,e,f.value),damping:o,stiffness:l,restDelta:h,restSpeed:m})}};return j(0),{calculatedDuration:null,next:e=>{let r=!1;return(i||void 0!==t||(r=!0,w(e),j(e)),void 0!==t&&e>=t)?i.next(e-t):(r||w(e),f)}}}iA.applyToOptions=e=>{let t=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,i=arguments.length>2?arguments[2]:void 0,r=i({...e,keyframes:[0,t]}),n=Math.min(ij(r),2e4);return{type:"keyframes",ease:e=>r.next(n*e).value/t,duration:n/1e3}}(e,100,iA);return e.ease=t.ease,e.duration=ir(t.duration),e.type="keyframes",e};let iE=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function iR(e,t,i,r){return e===t&&i===r?ei:n=>0===n||1===n?n:iE(function(e,t,i,r,n){let s,a,o=0;do(s=iE(a=t+(i-t)/2,r,n)-e)>0?i=a:t=a;while(Math.abs(s)>1e-7&&++o<12)return a}(n,0,1,e,i),t,r)}let iV=iR(.42,0,1,1),iD=iR(0,0,.58,1),iO=iR(.42,0,.58,1),iI=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,iL=e=>t=>1-e(1-t),iz=iR(.33,1.53,.69,.99),iF=iL(iz),iB=iI(iF),i_=e=>(e*=2)<1?.5*iF(e):.5*(2-Math.pow(2,-10*(e-1))),iU=e=>1-Math.sin(Math.acos(e)),iW=iL(iU),iH=iI(iU),iG=e=>Array.isArray(e)&&"number"==typeof e[0],iY={linear:ei,easeIn:iV,easeInOut:iO,easeOut:iD,circIn:iU,circInOut:iH,circOut:iW,backIn:iF,backInOut:iB,backOut:iz,anticipate:i_},iX=e=>{if(iG(e)){K(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,i,r,n]=e;return iR(t,i,r,n)}return"string"==typeof e?(K(void 0!==iY[e],"Invalid easing type '".concat(e,"'"),"invalid-easing-type"),iY[e]):e},iq=(e,t,i)=>{let r=t-e;return 0===r?1:(i-e)/r};function iK(e){var t;let{duration:i=300,keyframes:r,times:n,ease:s="easeInOut"}=e,a=Array.isArray(s)&&"number"!=typeof s[0]?s.map(iX):iX(s),o={done:!1,value:r[0]},l=function(e,t){let{clamp:i=!0,ease:r,mixer:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=e.length;if(K(s===t.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let r=[],n=i||er.mix||iy,s=e.length-1;for(let i=0;i<s;i++){let s=n(e[i],e[i+1]);t&&(s=ii(Array.isArray(t)?t[i]||ei:t,s)),r.push(s)}return r}(t,r,n),l=o.length,c=i=>{if(a&&i<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(i<e[r+1]);r++);let n=iq(e[r],e[r+1],i);return o[r](n)};return i?t=>c(D(e[0],e[s-1],t)):c}((t=n&&n.length===r.length?n:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let r=1;r<=t;r++){let n=iq(0,t,r);e.push(w(i,1,n))}}(t,e.length-1),t}(r),t.map(e=>e*i)),r,{ease:Array.isArray(a)?a:r.map(()=>a||iO).splice(0,r.length-1)});return{calculatedDuration:i,next:e=>(o.value=l(e),o.done=e>=i,o)}}let i$=e=>null!==e;function iZ(e,t,i){let{repeat:r,repeatType:n="loop"}=t,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,a=e.filter(i$),o=s<0||r&&"loop"!==n&&r%2==1?0:a.length-1;return o&&void 0!==i?i:a[o]}let iJ={decay:iM,inertia:iM,tween:iK,keyframes:iK,spring:iA};function iQ(e){"string"==typeof e.type&&(e.type=iJ[e.type])}class i0{get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}constructor(){this.updateFinished()}}let i1=e=>e/100;class i2 extends i0{initAnimation(){let{options:e}=this;iQ(e);let{type:t=iK,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=e,{keyframes:a}=e,o=t||iK;o!==iK&&"number"!=typeof a[0]&&(this.mixKeyframes=ii(i1,iy(a[0],a[1])),a=[0,100]);let l=o({...e,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...e,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=ij(l));let{calculatedDuration:c}=l;this.calculatedDuration=c,this.resolvedDuration=c+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:c,repeat:d,repeatType:u,repeatDelay:h,type:m,onUpdate:p,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let x=this.currentTime,y=i;if(d){let e=Math.min(this.currentTime,r)/a,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,d+1))%2&&("reverse"===u?(i=1-i,h&&(i-=h/a)):"mirror"===u&&(y=s)),x=D(0,1,i)*a}let b=v?{done:!1,value:c[0]}:y.next(x);n&&(b.value=n(b.value));let{done:w}=b;v||null===o||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let j=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return j&&m!==iM&&(b.value=iZ(c,this.options,f,this.speed)),p&&p(b.value),j&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(e){var t;e=ir(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),null==(t=this.driver)||t.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(eZ.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=this.currentTime/1e3)}play(){var e,t;if(this.isStopped)return;let{driver:i=ib,startTime:r}=this.options;this.driver||(this.driver=i(e=>this.tick(e))),null==(e=(t=this.options).onPlay)||e.call(t);let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=null!=r?r:n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(eZ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,t;this.notifyFinished(),this.teardown(),this.state="finished",null==(e=(t=this.options).onComplete)||e.call(t)}cancel(){var e,t;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null==(e=(t=this.options).onCancel)||e.call(t)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,is.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var t;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null==(t=this.driver)||t.stop(),e.observe(this)}constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var e,t;let{motionValue:i}=this.options;i&&i.updatedAt!==eZ.now()&&this.tick(eZ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),null==(e=(t=this.options).onStop)||e.call(t))},is.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}}function i3(e){let t;return()=>(void 0===t&&(t=e()),t)}let i4=i3(()=>void 0!==window.ScrollTimeline),i5={},i6=function(e,t){let i=i3(e);return()=>{var e;return null!=(e=i5[t])?e:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),i8=e=>{let[t,i,r,n]=e;return"cubic-bezier(".concat(t,", ").concat(i,", ").concat(r,", ").concat(n,")")},i9={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i8([0,.65,.55,1]),circOut:i8([.55,0,1,.45]),backIn:i8([.31,.01,.66,-.59]),backOut:i8([.33,1.53,.69,.99])};function i7(e){return"function"==typeof e&&"applyToOptions"in e}class re extends i0{play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,t;null==(e=(t=this.animation).finish)||e.call(t)}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){if(!this.isPseudoElement){var e,t;null==(e=(t=this.animation).commitStyles)||e.call(t)}}get duration(){var e,t;return Number((null==(t=this.animation.effect)||null==(e=t.getComputedTiming)?void 0:e.call(t).duration)||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(e){this.finishedTime=null,this.animation.currentTime=ir(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline(e){let{timeline:t,observe:i}=e;if(this.allowFlatten){var r;null==(r=this.animation.effect)||r.updateTiming({easing:"linear"})}return(this.animation.onfinish=null,t&&i4())?(this.animation.timeline=t,ei):i(this)}constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=e,K("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function(e){let{type:t,...i}=e;return i7(t)&&i6()?t.applyToOptions(i):(null!=i.duration||(i.duration=300),null!=i.ease||(i.ease="easeOut"),i)}(e);this.animation=function(e,t,i){let{delay:r=0,duration:n=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},c=arguments.length>4&&void 0!==arguments[4]?arguments[4]:void 0,d={[t]:i};l&&(d.offset=l);let u=function e(t,i){if(t)return"function"==typeof t?i6()?iw(t,i):"ease-out":iG(t)?i8(t):Array.isArray(t)?t.map(t=>e(t,i)||i9.easeOut):i9[t]}(o,n);Array.isArray(u)&&(d.easing=u),es.value&&is.waapi++;let h={delay:r,duration:n,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};c&&(h.pseudoElement=c);let m=e.animate(d,h);return es.value&&m.finished.finally(()=>{is.waapi--}),m}(t,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let e=iZ(r,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){t.startsWith("--")?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}null==o||o(),this.notifyFinished()}}}let rt={anticipate:i_,backInOut:iB,circInOut:iH};class ri extends re{updateMotionValue(e){var t;let{motionValue:i,onUpdate:r,onComplete:n,element:s,...a}=this.options;if(!i)return;if(void 0!==e)return void i.set(e);let o=new i2({...a,autoplay:!1}),l=ir(null!=(t=this.finishedTime)?t:this.time);i.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}constructor(e){!function(e){"string"==typeof e.ease&&e.ease in rt&&(e.ease=rt[e.ease])}(e),iQ(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}}let rr=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eL.test(e)||"0"===e)&&!e.startsWith("url(")),rn=new Set(["opacity","clipPath","filter","transform"]),rs=i3(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ra extends i0{onKeyframesResolved(e,t,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:c}=i;this.resolvedAt=eZ.now(),!function(e,t,i,r){let n=e[0];if(null===n)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],a=rr(n,t),o=rr(s,t);return q(a===o,"You are trying to animate ".concat(t,' from "').concat(n,'" to "').concat(s,'". "').concat(a?s:n,'" is not an animatable value.'),"value-not-animatable"),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||i7(i))&&r)}(e,n,s,a)&&((er.instantAnimations||!o)&&(null==c||c(iZ(e,i,t))),e[0]=e[e.length-1],ie(i),i.repeat=0);let d={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},u=!l&&function(e){var t;let{motionValue:i,name:r,repeatDelay:n,repeatType:s,damping:a,type:o}=e;if(!((null==i||null==(t=i.owner)?void 0:t.current)instanceof HTMLElement))return!1;let{onUpdate:l,transformTemplate:c}=i.owner.getProps();return rs()&&r&&rn.has(r)&&("transform"!==r||!c)&&!l&&!n&&"mirror"!==s&&0!==a&&"inertia"!==o}(d)?new ri({...d,element:d.motionValue.owner.current}):new i2(d);u.finished.then(()=>this.notifyFinished()).catch(ei),this.pendingTimeline&&(this.stopTimeline=u.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=u}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){if(!this._animation){var e;null==(e=this.keyframeResolver)||e.resume(),ep=!0,eg(),ef(),ep=!1}return this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),null==(e=this.keyframeResolver)||e.cancel()}constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:c,...d}){var u;super(),this.stop=()=>{var e,t;this._animation&&(this._animation.stop(),null==(t=this.stopTimeline)||t.call(this)),null==(e=this.keyframeResolver)||e.cancel()},this.createdAt=eZ.now();let h={autoplay:e,delay:t,type:i,repeat:r,repeatDelay:n,repeatType:s,name:o,motionValue:l,element:c,...d},m=(null==c?void 0:c.KeyframeResolver)||ev;this.keyframeResolver=new m(a,(e,t,i)=>this.onKeyframesResolved(e,t,h,!i),o,l,c),null==(u=this.keyframeResolver)||u.scheduleResolve()}}let ro=e=>null!==e,rl={type:"spring",stiffness:500,damping:25,restSpeed:10},rc={type:"keyframes",duration:.8},rd={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ru=function(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=arguments.length>4?arguments[4]:void 0,a=arguments.length>5?arguments[5]:void 0;return o=>{let l=t8(r,e)||{},c=l.delay||r.delay||0,{elapsed:d=0}=r;d-=ir(c);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-d,onUpdate:e=>{t.set(e),l.onUpdate&&l.onUpdate(e)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:a?void 0:s};!function(e){let{when:t,delay:i,delayChildren:r,staggerChildren:n,staggerDirection:s,repeat:a,repeatType:o,repeatDelay:l,from:c,elapsed:d,...u}=e;return!!Object.keys(u).length}(l)&&Object.assign(u,((e,t)=>{let{keyframes:i}=t;return i.length>2?rc:n.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===i[1]?2*Math.sqrt(550):30,restSpeed:10}:rl:rd})(e,u)),u.duration&&(u.duration=ir(u.duration)),u.repeatDelay&&(u.repeatDelay=ir(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let h=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(ie(u),0===u.delay&&(h=!0)),(er.instantAnimations||er.skipAnimations)&&(h=!0,ie(u),u.delay=0),u.allowFlatten=!l.type&&!l.ease,h&&!a&&void 0!==t.get()){let e=function(e,t,i){let{repeat:r,repeatType:n="loop"}=t,s=e.filter(ro),a=r&&"loop"!==n&&r%2==1?0:s.length-1;return s[a]}(u.keyframes,l);if(void 0!==e)return void eo.update(()=>{u.onUpdate(e),u.onComplete()})}return l.isSync?new i2(u):new ra(u)}};function rh(e,t){let{delay:i=0,transitionOverride:r,type:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:s=e.getDefaultTransition(),transitionEnd:a,...o}=t;r&&(s=r);let l=[],c=n&&e.animationState&&e.animationState.getState()[n];for(let t in o){var d;let r=e.getValue(t,null!=(d=e.latestValues[t])?d:null),n=o[t];if(void 0===n||c&&function(e,t){let{protectedKeys:i,needsAnimating:r}=e,n=i.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,n}(c,t))continue;let a={delay:i,...t8(s||{},t)},u=r.get();if(void 0!==u&&!r.isAnimating&&!Array.isArray(n)&&n===u&&!a.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let i=e.props[t2];if(i){let e=window.MotionHandoffAnimation(i,t,eo);null!==e&&(a.startTime=e,h=!0)}}t7(e,t),r.start(ru(t,r,n,e.shouldReduceMotion&&V.has(t)?{type:!1}:a,e,h));let m=r.animation;m&&l.push(m)}return a&&Promise.all(l).then(()=>{eo.update(()=>{a&&function(e,t){let{transitionEnd:i={},transition:r={},...n}=t6(e,t)||{};for(let t in n={...n,...i}){var s;let i=t9(s=n[t])?s[s.length-1]||0:s;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,e3(i))}}(e,a)})}),l}function rm(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=Array.from(e).sort((e,t)=>e.sortNodePosition(t)).indexOf(t),a=e.size,o=(a-1)*r;return"function"==typeof i?i(s,a):1===n?s*r:o-s*r}function rp(e,t){var i;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=t6(e,t,"exit"===r.type?null==(i=e.presenceContext)?void 0:i.custom:void 0),{transition:s=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(s=r.transitionOverride);let a=n?()=>Promise.all(rh(e,n,r)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?function(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=s;return function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,a=arguments.length>6?arguments[6]:void 0,o=[];for(let l of e.variantChildren)l.notify("AnimationStart",t),o.push(rp(l,t,{...a,delay:i+("function"==typeof r?0:r)+rm(e.variantChildren,l,r,n,s)}).then(()=>l.notify("AnimationComplete",t)));return Promise.all(o)}(e,t,i,n,a,o,r)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([a(),o(r.delay)]);{let[e,t]="beforeChildren"===l?[a,o]:[o,a];return e().then(()=>t())}}function rf(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let r=0;r<i;r++)if(t[r]!==e[r])return!1;return!0}let rg=tc.length,rv=[...tl].reverse(),rx=tl.length;function ry(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rb(){return{animate:ry(!0),whileInView:ry(),whileHover:ry(),whileTap:ry(),whileDrag:ry(),whileFocus:ry(),exit:ry()}}class rw{update(){}constructor(e){this.isMounted=!1,this.node=e}}let rj=0,rk={x:!1,y:!1};function rP(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,i,r),()=>e.removeEventListener(t,i)}let rN=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rS(e){return{point:{x:e.pageX,y:e.pageY}}}function rT(e,t,i,r){return rP(e,t,e=>rN(e)&&i(e,rS(e)),r)}function rC(e){return e.max-e.min}function rA(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=w(t.min,t.max,e.origin),e.scale=rC(i)/rC(t),e.translate=w(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rM(e,t,i,r){rA(e.x,t.x,i.x,r?r.originX:void 0),rA(e.y,t.y,i.y,r?r.originY:void 0)}function rE(e,t,i){e.min=i.min+t.min,e.max=e.min+rC(t)}function rR(e,t,i){e.min=t.min-i.min,e.max=e.min+rC(t)}function rV(e,t,i){rR(e.x,t.x,i.x),rR(e.y,t.y,i.y)}function rD(e){return[e("x"),e("y")]}let rO=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null},rI=(e,t)=>Math.abs(e-t);class rL{updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),el(this.updatePoint)}constructor(e,t,{transformPagePoint:i,contextWindow:r=window,dragSnapToOrigin:n=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=rB(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(rI(e.x,t.x)**2+rI(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!i)return;let{point:r}=e,{timestamp:n}=ec;this.history.push({...r,timestamp:n});let{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rz(t,this.transformPagePoint),eo.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rB("pointercancel"===e.type?this.lastMoveEventInfo:rz(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,s),r&&r(e,s)},!rN(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=r||window;let a=rz(rS(e),this.transformPagePoint),{point:o}=a,{timestamp:l}=ec;this.history=[{...o,timestamp:l}];let{onSessionStart:c}=t;c&&c(e,rB(a,this.history)),this.removeListeners=ii(rT(this.contextWindow,"pointermove",this.handlePointerMove),rT(this.contextWindow,"pointerup",this.handlePointerUp),rT(this.contextWindow,"pointercancel",this.handlePointerUp))}}function rz(e,t){return t?{point:t(e.point)}:e}function rF(e,t){return{x:e.x-t.x,y:e.y-t.y}}function rB(e,t){let{point:i}=e;return{point:i,delta:rF(i,r_(t)),offset:rF(i,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,r=null,n=r_(e);for(;i>=0&&(r=e[i],!(n.timestamp-r.timestamp>ir(.1)));)i--;if(!r)return{x:0,y:0};let s=(n.timestamp-r.timestamp)/1e3;if(0===s)return{x:0,y:0};let a={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function r_(e){return e[e.length-1]}function rU(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function rW(e,t){let i=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,r]=[r,i]),{min:i,max:r}}function rH(e,t,i){return{min:rG(e,t),max:rG(e,i)}}function rG(e,t){return"number"==typeof e?e:e[t]||0}let rY=new WeakMap;class rX{start(e){let{snapToCursor:t=!1,distanceThreshold:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let n=e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rS(e).point)},s=(e,t)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(rk[e])return null;else return rk[e]=!0,()=>{rk[e]=!1};return rk.x||rk.y?null:(rk.x=rk.y=!0,()=>{rk.x=rk.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rD(e=>{let t=this.getAxisMotionValue(e).get()||0;if(B.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[e];r&&(t=rC(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),n&&eo.postRender(()=>n(e,t)),t7(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},a=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},o=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>rD(e=>{var t;return"paused"===this.getAnimationState(e)&&(null==(t=this.getAxisMotionValue(e).animation)?void 0:t.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new rL(e,{onSessionStart:n,onStart:s,onMove:a,onSessionEnd:o,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,distanceThreshold:i,contextWindow:rO(this.visualElement)})}stop(e,t){let i=e||this.latestPointerEvent,r=t||this.latestPanInfo,n=this.isDragging;if(this.cancel(),!n||!r||!i)return;let{velocity:s}=r;this.startAnimation(s);let{onDragEnd:a}=this.getProps();a&&eo.postRender(()=>a(i,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:r}=this.getProps();if(!i||!rq(e,r,this.currentDirection))return;let n=this.getAxisMotionValue(e),s=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(s=function(e,t,i){let{min:r,max:n}=t;return void 0!==r&&e<r?e=i?w(r,e,i.min):Math.max(e,r):void 0!==n&&e>n&&(e=i?w(n,e,i.max):Math.min(e,n)),e}(s,this.constraints[e],this.elastic[e])),n.set(s)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(e=this.visualElement.projection)?void 0:e.layout,n=this.constraints;t&&t1(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,t){let{top:i,left:r,bottom:n,right:s}=t;return{x:rU(e.x,r,s),y:rU(e.y,i,n)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===e?e=0:!0===e&&(e=.35),{x:rH(e,"left","right"),y:rH(e,"top","bottom")}}(i),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&rD(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!t1(t))return!1;let r=t.current;K(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(e,t,i){let r=R(e,i),{scroll:n}=t;return n&&(A(r.x,n.offset.x),A(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),a=(e=n.layout.layoutBox,{x:rW(e.x,s.x),y:rW(e.y,s.y)});if(i){let e=i(function(e){let{x:t,y:i}=e;return{top:i.min,right:t.max,bottom:i.max,left:t.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=b(e))}return a}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(rD(a=>{if(!rq(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let c={type:"inertia",velocity:i?e[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,c)})).then(a)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return t7(this.visualElement,e),i.start(ru(e,i,0,t,this.visualElement,!1))}stopAnimation(){rD(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rD(e=>{var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.pause()})}getAnimationState(e){var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.state}getAxisMotionValue(e){let t="_drag".concat(e.toUpperCase()),i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){rD(t=>{let{drag:i}=this.getProps();if(!rq(t,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(t);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[t];n.set(e[t]-w(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!t1(t)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rD(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();r[e]=function(e,t){let i=.5,r=rC(e),n=rC(t);return n>r?i=iq(t.min,t.max-r,e.min):r>n&&(i=iq(e.min,e.max-n,t.min)),D(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),rD(t=>{if(!rq(t,e,null))return;let i=this.getAxisMotionValue(t),{min:n,max:s}=this.constraints[t];i.set(w(n,s,r[t]))})}addListeners(){if(!this.visualElement.current)return;rY.set(this.visualElement,this);let e=rT(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();t1(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),eo.read(t);let n=rP(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:i}=e;this.isDragging&&i&&(rD(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),e(),r(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:a}}constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=tt(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}}function rq(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}let rK=e=>(t,i)=>{e&&eo.postRender(()=>e(t,i))};var r$=i;function rZ(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=(0,i.useContext)(tq);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:n,register:s}=t,a=(0,i.useId)();(0,i.useEffect)(()=>{if(e)return s(a)},[e]);let o=(0,i.useCallback)(()=>e&&n&&n(a),[a,n,e]);return!r&&n?[!1,o]:[!0]}e.s(["usePresence",()=>rZ],64978);let rJ={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rQ(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let r0={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!_.test(e))return e;else e=parseFloat(e);let i=rQ(e,t.target.x),r=rQ(e,t.target.y);return"".concat(i,"% ").concat(r,"%")}},r1=!1;class r2 extends r$.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=e;for(let e in r4)tj[e]=r4[e],g(e)&&(tj[e].isCSSVariable=!0);n&&(t.group&&t.group.add(n),i&&i.register&&r&&i.register(n),r1&&n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rJ.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r1=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==n?s.willUpdate():this.safeToRemove(),e.isPresent!==n&&(n?s.promote():s.relegate()||eo.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),e5.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:r}=e;r1=!0,r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function r3(e){let[t,i]=rZ(),r=(0,r$.useContext)(tL);return(0,tI.jsx)(r2,{...e,layoutGroup:r,switchLayoutGroup:(0,r$.useContext)(t3),isPresent:t,safeToRemove:i})}let r4={borderRadius:{...r0,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r0,borderTopRightRadius:r0,borderBottomLeftRadius:r0,borderBottomRightRadius:r0,boxShadow:{correct:(e,t)=>{let{treeScale:i,projectionDelta:r}=t,n=eL.parse(e);if(n.length>5)return e;let s=eL.createTransformer(e),a=+("number"!=typeof n[0]),o=r.x.scale*i.x,l=r.y.scale*i.y;n[0+a]/=o,n[1+a]/=l;let c=w(o,l,.5);return"number"==typeof n[2+a]&&(n[2+a]/=c),"number"==typeof n[3+a]&&(n[3+a]/=c),s(n)}}};function r5(e){return"object"==typeof e&&null!==e}function r6(e){return r5(e)&&"ownerSVGElement"in e}let r8=(e,t)=>e.depth-t.depth;class r9{add(e){eJ(this.children,e),this.isDirty=!0}remove(e){eQ(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(r8),this.isDirty=!1,this.children.forEach(e)}constructor(){this.children=[],this.isDirty=!1}}let r7=["TopLeft","TopRight","BottomLeft","BottomRight"],ne=r7.length,nt=e=>"string"==typeof e?parseFloat(e):e,ni=e=>"number"==typeof e||_.test(e);function nr(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nn=na(0,.5,iW),ns=na(.5,.95,ei);function na(e,t,i){return r=>r<e?0:r>t?1:i(iq(e,t,r))}function no(e,t){e.min=t.min,e.max=t.max}function nl(e,t){no(e.x,t.x),no(e.y,t.y)}function nc(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nd(e,t,i,r,n){return e-=t,e=r+1/i*(e-r),void 0!==n&&(e=r+1/n*(e-r)),e}function nu(e,t,i,r,n){let[s,a,o]=i;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,n=arguments.length>4?arguments[4]:void 0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;if(B.test(t)&&(t=parseFloat(t),t=w(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=w(s.min,s.max,r);e===s&&(o-=t),e.min=nd(e.min,t,i,o,n),e.max=nd(e.max,t,i,o,n)}(e,t[s],t[a],t[o],t.scale,r,n)}let nh=["x","scaleX","originX"],nm=["y","scaleY","originY"];function np(e,t,i,r){nu(e.x,t,nh,i?i.x:void 0,r?r.x:void 0),nu(e.y,t,nm,i?i.y:void 0,r?r.y:void 0)}function nf(e){return 0===e.translate&&1===e.scale}function ng(e){return nf(e.x)&&nf(e.y)}function nv(e,t){return e.min===t.min&&e.max===t.max}function nx(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function ny(e,t){return nx(e.x,t.x)&&nx(e.y,t.y)}function nb(e){return rC(e.x)/rC(e.y)}function nw(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nj{add(e){eJ(this.members,e),e.scheduleRender()}remove(e){if(eQ(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}constructor(){this.members=[]}}let nk={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nP=["","X","Y","Z"],nN=0;function nS(e,t,i,r){let{latestValues:n}=t;n[e]&&(i[e]=n[e],t.setStaticValue(e,0),r&&(r[e]=0))}function nT(e){let{attachResizeListener:t,defaultParent:i,measureScroll:r,checkIsScrollRoot:n,resetTransform:s}=e;return class{addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new e0),this.eventHandlers.get(e).add(t)}notifyListeners(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];let n=this.eventHandlers.get(e);n&&n.notify(...i)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){if(this.instance)return;this.isSVG=r6(e)&&!(r6(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=0,n=()=>this.root.updateBlockedByResize=!1;eo.read(()=>{r=window.innerWidth}),t(e,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=eZ.now(),r=t=>{let{timestamp:n}=t,s=n-i;s>=250&&(el(r),e(s-250))};return eo.setup(r,!0),()=>el(r)}(n,250),rJ.hasAnimatedSinceResize&&(rJ.hasAnimatedSinceResize=!1,this.nodes.forEach(nI)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:i,hasRelativeLayoutChanged:r,layout:s}=e;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let a=this.options.transition||n.getDefaultTransition()||nH,{onLayoutAnimationStart:o,onLayoutAnimationComplete:l}=n.getProps(),c=!this.targetLayout||!ny(this.targetLayout,s),d=!i&&r;if(this.options.layoutRoot||this.resumeFrom||d||i&&(c||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...t8(a,"layout"),onPlay:o,onComplete:l};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,d)}else i||nI(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),el(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nF),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let r=i.props[t2];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",eo,!(e||i))}let{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&e(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nV);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nD);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nO),this.nodes.forEach(nC),this.nodes.forEach(nA)):this.nodes.forEach(nD),this.clearAllSnapshots();let e=eZ.now();ec.delta=D(0,1e3/60,e-ec.timestamp),ec.timestamp=e,ec.isProcessing=!0,ed.update.process(ec),ed.preRender.process(ec),ed.render.process(ec),ec.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,e5.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nR),this.sharedNodes.forEach(nB)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eo.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eo.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rC(this.snapshot.measuredBox.x)||rC(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=tt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!s)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!ng(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,n=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||P(this.latestValues)||n)&&(s(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){var e;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),nX((e=r).x),nX(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return tt();let i=t.measureViewportBox();if(!((null==(e=this.scroll)?void 0:e.wasRoot)||this.path.some(nK))){let{scroll:e}=this.root;e&&(A(i.x,e.offset.x),A(i.y,e.offset.y))}return i}removeElementScroll(e){var t;let i=tt();if(nl(i,e),null==(t=this.scroll)?void 0:t.wasRoot)return i;for(let t=0;t<this.path.length;t++){let r=this.path[t],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&nl(i,e),A(i.x,n.offset.x),A(i.y,n.offset.y))}return i}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=tt();nl(i,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&E(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),P(r.latestValues)&&E(i,r.latestValues)}return P(this.latestValues)&&E(i,this.latestValues),i}removeTransform(e){let t=tt();nl(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!P(i.latestValues))continue;k(i.latestValues)&&i.updateSnapshot();let r=tt();nl(r,i.measurePageBox()),np(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return P(this.latestValues)&&np(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ec.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var e,t,i,r;let n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==s;if(!(n||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:o,layoutId:l}=this.options;if(this.layout&&(o||l)){if(this.resolvedRelativeTargetAt=ec.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tt(),this.relativeTargetOrigin=tt(),rV(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nl(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=tt(),this.targetWithTransforms=tt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),t=this.target,i=this.relativeTarget,r=this.relativeParent.target,rE(t.x,i.x,r.x),rE(t.y,i.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nl(this.target,this.layout.layoutBox),C(this.target,this.targetDelta)):nl(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tt(),this.relativeTargetOrigin=tt(),rV(this.relativeTargetOrigin,this.target,e.target),nl(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}es.value&&nk.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||k(this.parent.latestValues)||N(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),i=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===ec.timestamp&&(r=!1),r)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;nl(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;!function(e,t,i){let r,n,s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=i.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){n=(r=i[o]).projectionDelta;let{visualElement:a}=r.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&E(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,C(e,n)),s&&P(r.latestValues)&&E(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=tt());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nc(this.prevProjectionDelta.x,this.projectionDelta.x),nc(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rM(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===a&&this.treeScale.y===o&&nw(this.projectionDelta.x,this.prevProjectionDelta.x)&&nw(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),es.value&&nk.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var e;let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=e7(),this.projectionDelta=e7(),this.projectionDeltaWithTransform=e7()}setAnimationOrigin(e){let t,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},a=e7();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!i;let o=tt(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),c=this.getStack(),d=!c||c.members.length<=1,u=!!(l&&!d&&!0===this.options.crossfade&&!this.path.some(nW));this.animationProgress=0,this.mixTargetDelta=i=>{let r=i/1e3;if(n_(a.x,e.x,r),n_(a.y,e.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var c,h,m,p,f,g;rV(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,f=o,g=r,nU(m.x,p.x,f.x,g),nU(m.y,p.y,f.y,g),t&&(c=this.relativeTarget,h=t,nv(c.x,h.x)&&nv(c.y,h.y))&&(this.isProjectionDirty=!1),t||(t=tt()),nl(t,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,i,r,n,s){var a,o,l,c;n?(e.opacity=w(0,null!=(a=i.opacity)?a:1,nn(r)),e.opacityExit=w(null!=(o=t.opacity)?o:1,0,ns(r))):s&&(e.opacity=w(null!=(l=t.opacity)?l:1,null!=(c=i.opacity)?c:1,r));for(let n=0;n<ne;n++){let s="border".concat(r7[n],"Radius"),a=nr(t,s),o=nr(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||ni(a)===ni(o)?(e[s]=Math.max(w(nt(a),nt(o),r),0),(B.test(o)||B.test(a))&&(e[s]+="%")):e[s]=o)}(t.rotate||i.rotate)&&(e.rotate=w(t.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,u,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){var t,i,r;this.notifyListeners("animationStart"),null==(t=this.currentAnimation)||t.stop(),null==(r=this.resumingFrom)||null==(i=r.currentAnimation)||i.stop(),this.pendingAnimation&&(el(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eo.update(()=>{rJ.hasAnimatedSinceResize=!0,is.layout++,this.motionValue||(this.motionValue=e3(0)),this.currentAnimation=function(e,t,i){let r=eK(e)?e:e3(e);return r.start(ru("",r,t,i)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{is.layout--},onComplete:()=>{is.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:r,latestValues:n}=e;if(t&&i&&r){if(this!==e&&this.layout&&r&&nq(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||tt();let t=rC(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let r=rC(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+r}nl(t,i),E(t,n),rM(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nj),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null==(e=this.getStack())?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null==(e=this.getStack())?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=this.getStack();r&&r.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let r={};i.z&&nS("z",e,r,this.animationValues);for(let t=0;t<nP.length;t++)nS("rotate".concat(nP[t]),e,r,this.animationValues),nS("skew".concat(nP[t]),e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=t$(null==t?void 0:t.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=t$(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!P(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1);return}e.visibility="";let n=r.animationValues||r.latestValues;this.applyTransformsToTarget();let s=function(e,t,i){let r="",n=e.x.translate/t.x,s=e.y.translate/t.y,a=(null==i?void 0:i.z)||0;if((n||s||a)&&(r="translate3d(".concat(n,"px, ").concat(s,"px, ").concat(a,"px) ")),(1!==t.x||1!==t.y)&&(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),i){let{transformPerspective:e,rotate:t,rotateX:n,rotateY:s,skewX:a,skewY:o}=i;e&&(r="perspective(".concat(e,"px) ").concat(r)),t&&(r+="rotate(".concat(t,"deg) ")),n&&(r+="rotateX(".concat(n,"deg) ")),s&&(r+="rotateY(".concat(s,"deg) ")),a&&(r+="skewX(".concat(a,"deg) ")),o&&(r+="skewY(".concat(o,"deg) "))}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(r+="scale(".concat(o,", ").concat(l,")")),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n);i&&(s=i(n,s)),e.transform=s;let{x:a,y:o}=this.projectionDelta;if(e.transformOrigin="".concat(100*a.origin,"% ").concat(100*o.origin,"% 0"),r.animationValues){var l,c;e.opacity=r===this?null!=(c=null!=(l=n.opacity)?l:this.latestValues.opacity)?c:1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit}else e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0;for(let t in tj){if(void 0===n[t])continue;let{correct:i,applyTo:a,isCSSVariable:o}=tj[t],l="none"===s?n[t]:i(n[t],r);if(a){let t=a.length;for(let i=0;i<t;i++)e[a[i]]=l}else o?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?t$(null==t?void 0:t.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null==(t=e.currentAnimation)?void 0:t.stop()}),this.root.nodes.forEach(nV),this.root.sharedNodes.clear()}constructor(e={},t=null==i?void 0:i()){this.id=nN++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,es.value&&(nk.nodes=nk.calculatedTargetDeltas=nk.calculatedProjections=0),this.nodes.forEach(nM),this.nodes.forEach(nL),this.nodes.forEach(nz),this.nodes.forEach(nE),es.addProjectionMetrics&&es.addProjectionMetrics(nk)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new r9)}}}function nC(e){e.updateLayout()}function nA(e){var t;let i=(null==(t=e.resumeFrom)?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&i&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:n}=e.options,s=i.source!==e.layout.source;"size"===n?rD(e=>{let r=s?i.measuredBox[e]:i.layoutBox[e],n=rC(r);r.min=t[e].min,r.max=r.min+n}):nq(n,i.layoutBox,t)&&rD(r=>{let n=s?i.measuredBox[r]:i.layoutBox[r],a=rC(t[r]);n.max=n.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});let a=e7();rM(a,t,i.layoutBox);let o=e7();s?rM(o,e.applyTransform(r,!0),i.measuredBox):rM(o,t,i.layoutBox);let l=!ng(a),c=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let a=tt();rV(a,i.layoutBox,n.layoutBox);let o=tt();rV(o,t,s.layoutBox),ny(a,o)||(c=!0),r.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:i,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:c})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function nM(e){es.value&&nk.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nE(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nR(e){e.clearSnapshot()}function nV(e){e.clearMeasurements()}function nD(e){e.isLayoutDirty=!1}function nO(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function nI(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function nL(e){e.resolveTargetDelta()}function nz(e){e.calcProjection()}function nF(e){e.resetSkewAndRotation()}function nB(e){e.removeLeadSnapshot()}function n_(e,t,i){e.translate=w(t.translate,0,i),e.scale=w(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function nU(e,t,i,r){e.min=w(t.min,i.min,r),e.max=w(t.max,i.max,r)}function nW(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let nH={duration:.45,ease:[.4,0,.1,1]},nG=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),nY=nG("applewebkit/")&&!nG("chrome/")?Math.round:ei;function nX(e){e.min=nY(e.min),e.max=nY(e.max)}function nq(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nb(t)-nb(i)))}function nK(e){var t;return e!==e.root&&(null==(t=e.scroll)?void 0:t.wasRoot)}let n$=nT({attachResizeListener:(e,t)=>rP(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nZ={current:void 0},nJ=nT({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!nZ.current){let e=new n$({});e.mount(window),e.setOptions({layoutScroll:!0}),nZ.current=e}return nZ.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function nQ(e,t){let i=function(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document.querySelectorAll(e);return t?Array.from(t):[]}return Array.from(e)}(e),r=new AbortController;return[i,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function n0(e){return!("touch"===e.pointerType||rk.x||rk.y)}function n1(e,t,i){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&eo.postRender(()=>n(t,rS(t)))}function n2(e){return r5(e)&&"offsetHeight"in e}e.s(["isHTMLElement",()=>n2],72846);let n3=(e,t)=>!!t&&(e===t||n3(e,t.parentElement)),n4=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),n5=new WeakSet;function n6(e){return t=>{"Enter"===t.key&&e(t)}}function n8(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function n9(e){return rN(e)&&!(rk.x||rk.y)}function n7(e,t,i){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&eo.postRender(()=>n(t,rS(t)))}let se=new WeakMap,st=new WeakMap,si=e=>{let t=se.get(e.target);t&&t(e)},sr=e=>{e.forEach(si)},sn={some:0,all:1},ss=function(e,t){if("undefined"==typeof Proxy)return t5;let i=new Map,r=(i,r)=>t5(i,r,e,t);return new Proxy((e,t)=>r(e,t),{get:(n,s)=>"create"===s?r:(i.has(s)||i.set(s,t5(s,void 0,e,t)),i.get(s))})}({animation:{Feature:class extends rw{updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();ta(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null==(e=this.unmountControls)||e.call(this)}constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(t=>{let{animation:i,options:r}=t;return function(e,t){let i,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t))i=Promise.all(t.map(t=>rp(e,t,r)));else if("string"==typeof t)i=rp(e,t,r);else{let n="function"==typeof t?t6(e,t,r.custom):t;i=Promise.all(rh(e,n,r))}return i.then(()=>{e.notify("AnimationComplete",t)})}(e,i,r)})),i=rb(),r=!0,n=t=>(i,r)=>{var n;let s=t6(e,r,"exit"===t?null==(n=e.presenceContext)?void 0:n.custom:void 0);if(s){let{transition:e,transitionEnd:t,...r}=s;i={...i,...r,...t}}return i};function s(s){let{props:a}=e,o=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<rg;e++){let r=tc[e],n=t.props[r];(to(n)||!1===n)&&(i[r]=n)}return i}(e.parent)||{},l=[],c=new Set,d={},u=1/0;for(let t=0;t<rx;t++){var h,m;let p=rv[t],f=i[p],g=void 0!==a[p]?a[p]:o[p],v=to(g),x=p===s?f.isActive:null;!1===x&&(u=t);let y=g===o[p]&&g!==a[p]&&v;if(y&&r&&e.manuallyAnimateOnMount&&(y=!1),f.protectedKeys={...d},!f.isActive&&null===x||!g&&!f.prevProp||ta(g)||"boolean"==typeof g)continue;let b=(h=f.prevProp,"string"==typeof(m=g)?m!==h:!!Array.isArray(m)&&!rf(m,h)),w=b||p===s&&f.isActive&&!y&&v||t>u&&v,j=!1,k=Array.isArray(g)?g:[g],P=k.reduce(n(p),{});!1===x&&(P={});let{prevResolvedValues:N={}}=f,S={...N,...P},T=t=>{w=!0,c.has(t)&&(j=!0,c.delete(t)),f.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in S){let t=P[e],i=N[e];if(!d.hasOwnProperty(e))(t9(t)&&t9(i)?rf(t,i):t===i)?void 0!==t&&c.has(e)?T(e):f.protectedKeys[e]=!0:null!=t?T(e):c.add(e)}f.prevProp=g,f.prevResolvedValues=P,f.isActive&&(d={...d,...P}),r&&e.blockInitialAnimation&&(w=!1);let C=y&&b,A=!C||j;w&&A&&l.push(...k.map(t=>{let i={type:p};if("string"==typeof t&&r&&!C&&e.manuallyAnimateOnMount&&e.parent){let{parent:r}=e,n=t6(r,t);if(r.enteringChildren&&n){let{delayChildren:t}=n.transition||{};i.delay=rm(r.enteringChildren,e,t)}}return{animation:t,options:i}}))}if(c.size){let t={};if("boolean"!=typeof a.initial){let i=t6(e,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(t.transition=i.transition)}c.forEach(i=>{let r=e.getBaseTarget(i),n=e.getValue(i);n&&(n.liveStyle=!0),t[i]=null!=r?r:null}),l.push({animation:t})}let p=!!l.length;return r&&(!1===a.initial||a.initial===a.animate)&&!e.manuallyAnimateOnMount&&(p=!1),r=!1,p?t(l):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){var n;if(i[t].isActive===r)return Promise.resolve();null==(n=e.variantChildren)||n.forEach(e=>{var i;return null==(i=e.animationState)?void 0:i.setActive(t,r)}),i[t].isActive=r;let a=s(t);for(let e in i)i[e].protectedKeys={};return a},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=rb(),r=!0}}}(e))}}},exit:{Feature:class extends rw{update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}constructor(){super(...arguments),this.id=rj++}}},inView:{Feature:class extends rw{startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:r="some",once:n}=e,s={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:sn[r]},a=e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=t?i:r;s&&s(e)};var o=this.node.current;let l=function(e){let{root:t,...i}=e,r=t||document;st.has(r)||st.set(r,{});let n=st.get(r),s=JSON.stringify(i);return n[s]||(n[s]=new IntersectionObserver(sr,{root:t,...i})),n[s]}(s);return se.set(o,a),l.observe(o),()=>{se.delete(o),l.unobserve(o)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:i={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==i[e]}(e,t))&&this.startObserver()}unmount(){}constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}}},tap:{Feature:class extends rw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[r,n,s]=nQ(e,i),a=e=>{let r=e.currentTarget;if(!n9(e))return;n5.add(r);let s=t(r,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),n5.has(r)&&n5.delete(r),n9(e)&&"function"==typeof s&&s(e,{success:t})},o=e=>{a(e,r===window||r===document||i.useGlobalTarget||n3(r,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return r.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",a,n),n2(e))&&(e.addEventListener("focus",e=>((e,t)=>{let i=e.currentTarget;if(!i)return;let r=n6(()=>{if(n5.has(i))return;n8(i,"down");let e=n6(()=>{n8(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>n8(i,"cancel"),t)});i.addEventListener("keydown",r,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),t)})(e,n)),n4.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(n7(this.node,t,"Start"),(e,t)=>{let{success:i}=t;return n7(this.node,e,i?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends rw{onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ii(rP(this.node.current,"focus",()=>this.onFocus()),rP(this.node.current,"blur",()=>this.onBlur()))}unmount(){}constructor(){super(...arguments),this.isActive=!1}}},hover:{Feature:class extends rw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[r,n,s]=nQ(e,i),a=e=>{if(!n0(e))return;let{target:i}=e,r=t(i,e);if("function"!=typeof r||!i)return;let s=e=>{n0(e)&&(r(e),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(e=>{e.addEventListener("pointerenter",a,n)}),s}(e,(e,t)=>(n1(this.node,t,"Start"),e=>n1(this.node,e,"End"))))}unmount(){}}},pan:{Feature:class extends rw{onPointerDown(e){this.session=new rL(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rO(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rK(e),onStart:rK(t),onMove:i,onEnd:(e,t)=>{delete this.session,r&&eo.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=rT(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}constructor(){super(...arguments),this.removePointerDownListener=ei}}},drag:{Feature:class extends rw{mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ei}unmount(){this.removeGroupControls(),this.removeListeners()}constructor(e){super(e),this.removeGroupControls=ei,this.removeListeners=ei,this.controls=new rX(e)}},ProjectionNode:nJ,MeasureLayout:r3},layout:{ProjectionNode:nJ,MeasureLayout:r3}},(e,t)=>tO(e)?new tV(t):new tN(t,{allowProjection:e!==i.Fragment}));e.s(["cn",()=>s4],51360);let sa=(e,t)=>{var i;if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),s=n?sa(e.slice(1),n):void 0;if(s)return s;if(0===t.validators.length)return;let a=e.join("-");return null==(i=t.validators.find(e=>{let{validator:t}=e;return t(a)}))?void 0:i.classGroupId},so=/^\[(.+)\]$/,sl=(e,t,i,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:sc(t,e)).classGroupId=i;return}if("function"==typeof e)return sd(e)?void sl(e(r),t,i,r):void t.validators.push({validator:e,classGroupId:i});Object.entries(e).forEach(e=>{let[n,s]=e;sl(s,sc(t,n),i,r)})})},sc=(e,t)=>{let i=e;return t.split("-").forEach(e=>{i.nextPart.has(e)||i.nextPart.set(e,{nextPart:new Map,validators:[]}),i=i.nextPart.get(e)}),i},sd=e=>e.isThemeGetter,su=/\s+/;function sh(){let e,t,i=0,r="";for(;i<arguments.length;)(e=arguments[i++])&&(t=sm(e))&&(r&&(r+=" "),r+=t);return r}let sm=e=>{let t;if("string"==typeof e)return e;let i="";for(let r=0;r<e.length;r++)e[r]&&(t=sm(e[r]))&&(i&&(i+=" "),i+=t);return i},sp=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},sf=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,sg=/^\((?:(\w[\w-]*):)?(.+)\)$/i,sv=/^\d+\/\d+$/,sx=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,sy=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,sb=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,sw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,sj=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,sk=e=>sv.test(e),sP=e=>!!e&&!Number.isNaN(Number(e)),sN=e=>!!e&&Number.isInteger(Number(e)),sS=e=>e.endsWith("%")&&sP(e.slice(0,-1)),sT=e=>sx.test(e),sC=()=>!0,sA=e=>sy.test(e)&&!sb.test(e),sM=()=>!1,sE=e=>sw.test(e),sR=e=>sj.test(e),sV=e=>!sO(e)&&!s_(e),sD=e=>sq(e,sJ,sM),sO=e=>sf.test(e),sI=e=>sq(e,sQ,sA),sL=e=>sq(e,s0,sP),sz=e=>sq(e,s$,sM),sF=e=>sq(e,sZ,sR),sB=e=>sq(e,s2,sE),s_=e=>sg.test(e),sU=e=>sK(e,sQ),sW=e=>sK(e,s1),sH=e=>sK(e,s$),sG=e=>sK(e,sJ),sY=e=>sK(e,sZ),sX=e=>sK(e,s2,!0),sq=(e,t,i)=>{let r=sf.exec(e);return!!r&&(r[1]?t(r[1]):i(r[2]))},sK=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=sg.exec(e);return!!r&&(r[1]?t(r[1]):i)},s$=e=>"position"===e||"percentage"===e,sZ=e=>"image"===e||"url"===e,sJ=e=>"length"===e||"size"===e||"bg-size"===e,sQ=e=>"length"===e,s0=e=>"number"===e,s1=e=>"family-name"===e,s2=e=>"shadow"===e;Symbol.toStringTag;let s3=function(e){let t,i,r;for(var n=arguments.length,s=Array(n>1?n-1:0),a=1;a<n;a++)s[a-1]=arguments[a];let o=function(n){let a;return i=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,i=new Map,r=new Map,n=(n,s)=>{i.set(n,s),++t>e&&(t=0,r=i,i=new Map)};return{get(e){let t=i.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(n(e,t),t):void 0},set(e,t){i.has(e)?i.set(e,t):n(e,t)}}})((a=s.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:i}=e,r=e=>{let t,i,r=[],n=0,s=0,a=0;for(let i=0;i<e.length;i++){let o=e[i];if(0===n&&0===s){if(":"===o){r.push(e.slice(a,i)),a=i+1;continue}if("/"===o){t=i;continue}}"["===o?n++:"]"===o?n--:"("===o?s++:")"===o&&s--}let o=0===r.length?e:e.substring(a),l=(i=o).endsWith("!")?i.substring(0,i.length-1):i.startsWith("!")?i.substring(1):i;return{modifiers:r,hasImportantModifier:l!==o,baseClassName:l,maybePostfixModifierPosition:t&&t>a?t-a:void 0}};if(t){let e=t+":",i=r;r=t=>t.startsWith(e)?i(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(i){let e=r;r=t=>i({className:t,parseClassName:e})}return r})(a),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let i=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(i.push(...r.sort(),e),r=[]):r.push(e)}),i.push(...r.sort()),i}})(a),...(e=>{let t=(e=>{let{theme:t,classGroups:i}=e,r={nextPart:new Map,validators:[]};for(let e in i)sl(i[e],r,e,t);return r})(e),{conflictingClassGroups:i,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let i=e.split("-");return""===i[0]&&1!==i.length&&i.shift(),sa(i,t)||(e=>{if(so.test(e)){let t=so.exec(e)[1],i=null==t?void 0:t.substring(0,t.indexOf(":"));if(i)return"arbitrary.."+i}})(e)},getConflictingClassGroupIds:(e,t)=>{let n=i[e]||[];return t&&r[e]?[...n,...r[e]]:n}}})(a)}).cache.get,r=t.cache.set,o=l,l(n)};function l(e){let n=i(e);if(n)return n;let s=((e,t)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:s}=t,a=[],o=e.trim().split(su),l="";for(let e=o.length-1;e>=0;e-=1){let t=o[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:h,maybePostfixModifierPosition:m}=i(t);if(c){l=t+(l.length>0?" "+l:l);continue}let p=!!m,f=r(p?h.substring(0,m):h);if(!f){if(!p||!(f=r(h))){l=t+(l.length>0?" "+l:l);continue}p=!1}let g=s(d).join(":"),v=u?g+"!":g,x=v+f;if(a.includes(x))continue;a.push(x);let y=n(f,p);for(let e=0;e<y.length;++e){let t=y[e];a.push(v+t)}l=t+(l.length>0?" "+l:l)}return l})(e,t);return r(e,s),s}return function(){return o(sh.apply(null,arguments))}}(()=>{let e=sp("color"),t=sp("font"),i=sp("text"),r=sp("font-weight"),n=sp("tracking"),s=sp("leading"),a=sp("breakpoint"),o=sp("container"),l=sp("spacing"),c=sp("radius"),d=sp("shadow"),u=sp("inset-shadow"),h=sp("text-shadow"),m=sp("drop-shadow"),p=sp("blur"),f=sp("perspective"),g=sp("aspect"),v=sp("ease"),x=sp("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],b=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...b(),s_,sO],j=()=>["auto","hidden","clip","visible","scroll"],k=()=>["auto","contain","none"],P=()=>[s_,sO,l],N=()=>[sk,"full","auto",...P()],S=()=>[sN,"none","subgrid",s_,sO],T=()=>["auto",{span:["full",sN,s_,sO]},sN,s_,sO],C=()=>[sN,"auto",s_,sO],A=()=>["auto","min","max","fr",s_,sO],M=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],E=()=>["start","end","center","stretch","center-safe","end-safe"],R=()=>["auto",...P()],V=()=>[sk,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...P()],D=()=>[e,s_,sO],O=()=>[...b(),sH,sz,{position:[s_,sO]}],I=()=>["no-repeat",{repeat:["","x","y","space","round"]}],L=()=>["auto","cover","contain",sG,sD,{size:[s_,sO]}],z=()=>[sS,sU,sI],F=()=>["","none","full",c,s_,sO],B=()=>["",sP,sU,sI],_=()=>["solid","dashed","dotted","double"],U=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>[sP,sS,sH,sz],H=()=>["","none",p,s_,sO],G=()=>["none",sP,s_,sO],Y=()=>["none",sP,s_,sO],X=()=>[sP,s_,sO],q=()=>[sk,"full",...P()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[sT],breakpoint:[sT],color:[sC],container:[sT],"drop-shadow":[sT],ease:["in","out","in-out"],font:[sV],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[sT],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[sT],shadow:[sT],spacing:["px",sP],text:[sT],"text-shadow":[sT],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",sk,sO,s_,g]}],container:["container"],columns:[{columns:[sP,sO,s_,o]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:j()}],"overflow-x":[{"overflow-x":j()}],"overflow-y":[{"overflow-y":j()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:N()}],"inset-x":[{"inset-x":N()}],"inset-y":[{"inset-y":N()}],start:[{start:N()}],end:[{end:N()}],top:[{top:N()}],right:[{right:N()}],bottom:[{bottom:N()}],left:[{left:N()}],visibility:["visible","invisible","collapse"],z:[{z:[sN,"auto",s_,sO]}],basis:[{basis:[sk,"full","auto",o,...P()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[sP,sk,"auto","initial","none",sO]}],grow:[{grow:["",sP,s_,sO]}],shrink:[{shrink:["",sP,s_,sO]}],order:[{order:[sN,"first","last","none",s_,sO]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:T()}],"col-start":[{"col-start":C()}],"col-end":[{"col-end":C()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:T()}],"row-start":[{"row-start":C()}],"row-end":[{"row-end":C()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":A()}],"auto-rows":[{"auto-rows":A()}],gap:[{gap:P()}],"gap-x":[{"gap-x":P()}],"gap-y":[{"gap-y":P()}],"justify-content":[{justify:[...M(),"normal"]}],"justify-items":[{"justify-items":[...E(),"normal"]}],"justify-self":[{"justify-self":["auto",...E()]}],"align-content":[{content:["normal",...M()]}],"align-items":[{items:[...E(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...E(),{baseline:["","last"]}]}],"place-content":[{"place-content":M()}],"place-items":[{"place-items":[...E(),"baseline"]}],"place-self":[{"place-self":["auto",...E()]}],p:[{p:P()}],px:[{px:P()}],py:[{py:P()}],ps:[{ps:P()}],pe:[{pe:P()}],pt:[{pt:P()}],pr:[{pr:P()}],pb:[{pb:P()}],pl:[{pl:P()}],m:[{m:R()}],mx:[{mx:R()}],my:[{my:R()}],ms:[{ms:R()}],me:[{me:R()}],mt:[{mt:R()}],mr:[{mr:R()}],mb:[{mb:R()}],ml:[{ml:R()}],"space-x":[{"space-x":P()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":P()}],"space-y-reverse":["space-y-reverse"],size:[{size:V()}],w:[{w:[o,"screen",...V()]}],"min-w":[{"min-w":[o,"screen","none",...V()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[a]},...V()]}],h:[{h:["screen","lh",...V()]}],"min-h":[{"min-h":["screen","lh","none",...V()]}],"max-h":[{"max-h":["screen","lh",...V()]}],"font-size":[{text:["base",i,sU,sI]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,s_,sL]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",sS,sO]}],"font-family":[{font:[sW,sO,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,s_,sO]}],"line-clamp":[{"line-clamp":[sP,"none",s_,sL]}],leading:[{leading:[s,...P()]}],"list-image":[{"list-image":["none",s_,sO]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",s_,sO]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:D()}],"text-color":[{text:D()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[..._(),"wavy"]}],"text-decoration-thickness":[{decoration:[sP,"from-font","auto",s_,sI]}],"text-decoration-color":[{decoration:D()}],"underline-offset":[{"underline-offset":[sP,"auto",s_,sO]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",s_,sO]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",s_,sO]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:O()}],"bg-repeat":[{bg:I()}],"bg-size":[{bg:L()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},sN,s_,sO],radial:["",s_,sO],conic:[sN,s_,sO]},sY,sF]}],"bg-color":[{bg:D()}],"gradient-from-pos":[{from:z()}],"gradient-via-pos":[{via:z()}],"gradient-to-pos":[{to:z()}],"gradient-from":[{from:D()}],"gradient-via":[{via:D()}],"gradient-to":[{to:D()}],rounded:[{rounded:F()}],"rounded-s":[{"rounded-s":F()}],"rounded-e":[{"rounded-e":F()}],"rounded-t":[{"rounded-t":F()}],"rounded-r":[{"rounded-r":F()}],"rounded-b":[{"rounded-b":F()}],"rounded-l":[{"rounded-l":F()}],"rounded-ss":[{"rounded-ss":F()}],"rounded-se":[{"rounded-se":F()}],"rounded-ee":[{"rounded-ee":F()}],"rounded-es":[{"rounded-es":F()}],"rounded-tl":[{"rounded-tl":F()}],"rounded-tr":[{"rounded-tr":F()}],"rounded-br":[{"rounded-br":F()}],"rounded-bl":[{"rounded-bl":F()}],"border-w":[{border:B()}],"border-w-x":[{"border-x":B()}],"border-w-y":[{"border-y":B()}],"border-w-s":[{"border-s":B()}],"border-w-e":[{"border-e":B()}],"border-w-t":[{"border-t":B()}],"border-w-r":[{"border-r":B()}],"border-w-b":[{"border-b":B()}],"border-w-l":[{"border-l":B()}],"divide-x":[{"divide-x":B()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":B()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[..._(),"hidden","none"]}],"divide-style":[{divide:[..._(),"hidden","none"]}],"border-color":[{border:D()}],"border-color-x":[{"border-x":D()}],"border-color-y":[{"border-y":D()}],"border-color-s":[{"border-s":D()}],"border-color-e":[{"border-e":D()}],"border-color-t":[{"border-t":D()}],"border-color-r":[{"border-r":D()}],"border-color-b":[{"border-b":D()}],"border-color-l":[{"border-l":D()}],"divide-color":[{divide:D()}],"outline-style":[{outline:[..._(),"none","hidden"]}],"outline-offset":[{"outline-offset":[sP,s_,sO]}],"outline-w":[{outline:["",sP,sU,sI]}],"outline-color":[{outline:D()}],shadow:[{shadow:["","none",d,sX,sB]}],"shadow-color":[{shadow:D()}],"inset-shadow":[{"inset-shadow":["none",u,sX,sB]}],"inset-shadow-color":[{"inset-shadow":D()}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:D()}],"ring-offset-w":[{"ring-offset":[sP,sI]}],"ring-offset-color":[{"ring-offset":D()}],"inset-ring-w":[{"inset-ring":B()}],"inset-ring-color":[{"inset-ring":D()}],"text-shadow":[{"text-shadow":["none",h,sX,sB]}],"text-shadow-color":[{"text-shadow":D()}],opacity:[{opacity:[sP,s_,sO]}],"mix-blend":[{"mix-blend":[...U(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":U()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[sP]}],"mask-image-linear-from-pos":[{"mask-linear-from":W()}],"mask-image-linear-to-pos":[{"mask-linear-to":W()}],"mask-image-linear-from-color":[{"mask-linear-from":D()}],"mask-image-linear-to-color":[{"mask-linear-to":D()}],"mask-image-t-from-pos":[{"mask-t-from":W()}],"mask-image-t-to-pos":[{"mask-t-to":W()}],"mask-image-t-from-color":[{"mask-t-from":D()}],"mask-image-t-to-color":[{"mask-t-to":D()}],"mask-image-r-from-pos":[{"mask-r-from":W()}],"mask-image-r-to-pos":[{"mask-r-to":W()}],"mask-image-r-from-color":[{"mask-r-from":D()}],"mask-image-r-to-color":[{"mask-r-to":D()}],"mask-image-b-from-pos":[{"mask-b-from":W()}],"mask-image-b-to-pos":[{"mask-b-to":W()}],"mask-image-b-from-color":[{"mask-b-from":D()}],"mask-image-b-to-color":[{"mask-b-to":D()}],"mask-image-l-from-pos":[{"mask-l-from":W()}],"mask-image-l-to-pos":[{"mask-l-to":W()}],"mask-image-l-from-color":[{"mask-l-from":D()}],"mask-image-l-to-color":[{"mask-l-to":D()}],"mask-image-x-from-pos":[{"mask-x-from":W()}],"mask-image-x-to-pos":[{"mask-x-to":W()}],"mask-image-x-from-color":[{"mask-x-from":D()}],"mask-image-x-to-color":[{"mask-x-to":D()}],"mask-image-y-from-pos":[{"mask-y-from":W()}],"mask-image-y-to-pos":[{"mask-y-to":W()}],"mask-image-y-from-color":[{"mask-y-from":D()}],"mask-image-y-to-color":[{"mask-y-to":D()}],"mask-image-radial":[{"mask-radial":[s_,sO]}],"mask-image-radial-from-pos":[{"mask-radial-from":W()}],"mask-image-radial-to-pos":[{"mask-radial-to":W()}],"mask-image-radial-from-color":[{"mask-radial-from":D()}],"mask-image-radial-to-color":[{"mask-radial-to":D()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":b()}],"mask-image-conic-pos":[{"mask-conic":[sP]}],"mask-image-conic-from-pos":[{"mask-conic-from":W()}],"mask-image-conic-to-pos":[{"mask-conic-to":W()}],"mask-image-conic-from-color":[{"mask-conic-from":D()}],"mask-image-conic-to-color":[{"mask-conic-to":D()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:O()}],"mask-repeat":[{mask:I()}],"mask-size":[{mask:L()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",s_,sO]}],filter:[{filter:["","none",s_,sO]}],blur:[{blur:H()}],brightness:[{brightness:[sP,s_,sO]}],contrast:[{contrast:[sP,s_,sO]}],"drop-shadow":[{"drop-shadow":["","none",m,sX,sB]}],"drop-shadow-color":[{"drop-shadow":D()}],grayscale:[{grayscale:["",sP,s_,sO]}],"hue-rotate":[{"hue-rotate":[sP,s_,sO]}],invert:[{invert:["",sP,s_,sO]}],saturate:[{saturate:[sP,s_,sO]}],sepia:[{sepia:["",sP,s_,sO]}],"backdrop-filter":[{"backdrop-filter":["","none",s_,sO]}],"backdrop-blur":[{"backdrop-blur":H()}],"backdrop-brightness":[{"backdrop-brightness":[sP,s_,sO]}],"backdrop-contrast":[{"backdrop-contrast":[sP,s_,sO]}],"backdrop-grayscale":[{"backdrop-grayscale":["",sP,s_,sO]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[sP,s_,sO]}],"backdrop-invert":[{"backdrop-invert":["",sP,s_,sO]}],"backdrop-opacity":[{"backdrop-opacity":[sP,s_,sO]}],"backdrop-saturate":[{"backdrop-saturate":[sP,s_,sO]}],"backdrop-sepia":[{"backdrop-sepia":["",sP,s_,sO]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":P()}],"border-spacing-x":[{"border-spacing-x":P()}],"border-spacing-y":[{"border-spacing-y":P()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",s_,sO]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[sP,"initial",s_,sO]}],ease:[{ease:["linear","initial",v,s_,sO]}],delay:[{delay:[sP,s_,sO]}],animate:[{animate:["none",x,s_,sO]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,s_,sO]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:G()}],"rotate-x":[{"rotate-x":G()}],"rotate-y":[{"rotate-y":G()}],"rotate-z":[{"rotate-z":G()}],scale:[{scale:Y()}],"scale-x":[{"scale-x":Y()}],"scale-y":[{"scale-y":Y()}],"scale-z":[{"scale-z":Y()}],"scale-3d":["scale-3d"],skew:[{skew:X()}],"skew-x":[{"skew-x":X()}],"skew-y":[{"skew-y":X()}],transform:[{transform:[s_,sO,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:q()}],"translate-x":[{"translate-x":q()}],"translate-y":[{"translate-y":q()}],"translate-z":[{"translate-z":q()}],"translate-none":["translate-none"],accent:[{accent:D()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:D()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",s_,sO]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",s_,sO]}],fill:[{fill:["none",...D()]}],"stroke-w":[{stroke:[sP,sU,sI,sL]}],stroke:[{stroke:["none",...D()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function s4(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return s3(function(){for(var e,t,i=0,r="",n=arguments.length;i<n;i++)(e=arguments[i])&&(t=function e(t){var i,r,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(i=0;i<s;i++)t[i]&&(r=e(t[i]))&&(n&&(n+=" "),n+=r)}else for(r in t)t[r]&&(n&&(n+=" "),n+=r);return n}(e))&&(r&&(r+=" "),r+=t);return r}(t))}},59544,e=>{"use strict";e.s(["default",()=>a]);var t=e.i(43476),i=e.i(71645),r=e.i(46932),n=e.i(51360);let s=i.default.forwardRef((e,i)=>{let{className:s,variant:a="primary",size:o="md",isLoading:l=!1,leftIcon:c,rightIcon:d,children:u,disabled:h,...m}=e,p=(0,n.cn)(["inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed","relative overflow-hidden"],{primary:["bg-primary-600 text-white hover:bg-primary-700","focus:ring-primary-500 shadow-lg hover:shadow-xl","active:bg-primary-800"],secondary:["bg-sage-100 text-sage-800 hover:bg-sage-200","focus:ring-sage-500 border border-sage-200","active:bg-sage-300"],outline:["border-2 border-primary-600 text-primary-600 hover:bg-primary-50","focus:ring-primary-500 hover:border-primary-700","active:bg-primary-100"],ghost:["text-gray-700 hover:bg-gray-100","focus:ring-gray-500","active:bg-gray-200"],danger:["bg-red-600 text-white hover:bg-red-700","focus:ring-red-500 shadow-lg hover:shadow-xl","active:bg-red-800"]}[a],{sm:"px-3 py-1.5 text-sm gap-1.5",md:"px-4 py-2 text-base gap-2",lg:"px-6 py-3 text-lg gap-2.5",xl:"px-8 py-4 text-xl gap-3"}[o],s);return(0,t.jsxs)(r.motion.button,{ref:i,className:p,disabled:h||l,whileHover:{scale:h||l?1:1.02},whileTap:{scale:h||l?1:.98},transition:{duration:.1},...m,children:[l&&(0,t.jsx)(r.motion.div,{className:"absolute inset-0 bg-white/20 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.2},children:(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"})}),(0,t.jsxs)("div",{className:(0,n.cn)("flex items-center gap-inherit",l&&"opacity-0"),children:[c&&(0,t.jsx)("span",{className:"flex-shrink-0",children:c}),(0,t.jsx)("span",{children:u}),d&&(0,t.jsx)("span",{className:"flex-shrink-0",children:d})]})]})});s.displayName="Button";let a=s},58234,e=>{"use strict";e.s(["default",()=>o]);var t=e.i(43476),i=e.i(71645),r=e.i(22016),n=e.i(57688),s=e.i(46932),a=e.i(59544);let o=()=>{let e=[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Refund Policy",href:"/refund"},{name:"Cookie Policy",href:"/cookies"},{name:"Transparency",href:"/transparency"},{name:"Annual Reports",href:"/reports"}],o=[{name:"Facebook",href:"https://facebook.com/ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})},{name:"Twitter",href:"https://twitter.com/ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})},{name:"Instagram",href:"https://instagram.com/ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z"})})},{name:"LinkedIn",href:"https://linkedin.com/company/ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})},{name:"YouTube",href:"https://youtube.com/@ayurakshak",icon:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"})})}];return(0,t.jsxs)("footer",{className:"bg-gray-900 text-white",children:[(0,t.jsx)("div",{className:"bg-primary-600",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Stay Connected with Our Mission"}),(0,t.jsx)("p",{className:"text-primary-100 mb-8 max-w-2xl mx-auto",children:"Get updates on our programs, impact stories, and ways you can make a difference in communities across India."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[(0,t.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white"}),(0,t.jsx)(a.default,{variant:"secondary",size:"md",className:"bg-white text-primary-600 hover:bg-gray-100",children:"Subscribe"})]})]})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,t.jsx)("div",{className:"relative w-12 h-12",children:(0,t.jsx)(n.default,{src:"/logo.jpeg",alt:"Ayurakshak Logo",fill:!0,className:"object-contain rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Ayurakshak"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Care · Restore · Protect"})]})]}),(0,t.jsx)("p",{className:"text-gray-300 mb-6 leading-relaxed",children:"Ayurakshak combines traditional Ayurveda with modern outreach to heal communities across India. Through health camps, sustainable livelihoods, and green initiatives, we're building a healthier, more resilient future for all."}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,t.jsx)("p",{children:"📍 123 Wellness Street, New Delhi, India 110001"}),(0,t.jsx)("p",{children:"📞 +91 98765 43210"}),(0,t.jsx)("p",{children:"✉️ <EMAIL>"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:"Quick Links"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{name:"About Us",href:"/about"},{name:"Our Programs",href:"/#programs"},{name:"Impact Stories",href:"/#impact"},{name:"Volunteer",href:"/volunteer"},{name:"Careers",href:"/careers"},{name:"News & Updates",href:"/news"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(r.default,{href:e.href,className:"text-gray-400 hover:text-white transition-colors duration-200",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:"Our Programs"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{name:"Health Camps",href:"/#programs"},{name:"Women Livelihoods",href:"/#programs"},{name:"Education Support",href:"/#programs"},{name:"Emergency Relief",href:"/#programs"},{name:"Environmental Care",href:"/#programs"},{name:"Community Development",href:"/#programs"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(r.default,{href:e.href,className:"text-gray-400 hover:text-white transition-colors duration-200",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:"Support Us"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{name:"Donate",href:"/donate"},{name:"Sponsor a Program",href:"/sponsor"},{name:"Corporate Partnership",href:"/partnership"},{name:"Volunteer",href:"/volunteer"},{name:"Fundraise",href:"/fundraise"},{name:"Gift a Smile",href:"/gift"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(r.default,{href:e.href,className:"text-gray-400 hover:text-white transition-colors duration-200",children:e.name})},e.name))})]})]}),(0,t.jsx)("div",{className:"mt-12 pt-8 border-t border-gray-800",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsx)("div",{className:"flex space-x-6 mb-6 md:mb-0",children:o.map(e=>(0,t.jsx)(s.motion.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors duration-200",whileHover:{scale:1.2},whileTap:{scale:.9},"aria-label":e.name,children:e.icon},e.name))}),(0,t.jsxs)("div",{className:"text-center md:text-right",children:[(0,t.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Registered NGO | 80G Tax Exemption Available"}),(0,t.jsx)("div",{className:"flex flex-wrap justify-center md:justify-end gap-4 text-xs text-gray-500",children:e.map((n,s)=>(0,t.jsxs)(i.default.Fragment,{children:[(0,t.jsx)(r.default,{href:n.href,className:"hover:text-gray-300 transition-colors duration-200",children:n.name}),s<e.length-1&&(0,t.jsx)("span",{children:"•"})]},n.name))})]})]})}),(0,t.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-800 text-center",children:(0,t.jsxs)("p",{className:"text-gray-400 text-sm",children:["© ",new Date().getFullYear()," Ayurakshak. All rights reserved.",(0,t.jsx)("span",{className:"mx-2",children:"•"}),"Developed with ❤️ by"," ",(0,t.jsx)("a",{href:"https://kush-personal-portfolio-my-portfolio.vercel.app/",target:"_blank",rel:"noopener noreferrer",className:"text-primary-400 hover:text-primary-300 transition-colors duration-200",children:"Kush Vardhan"})]})})]})]})}},88653,e=>{"use strict";e.s(["AnimatePresence",()=>v],88653);var t=e.i(43476),i=e.i(71645),r=e.i(31178),n=e.i(47414),s=e.i(74008),a=e.i(21476),o=e.i(72846),l=i,c=e.i(37806);class d extends l.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,i=(0,o.isHTMLElement)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function u(e){let{children:i,isPresent:r,anchorX:n,root:s}=e,a=(0,l.useId)(),o=(0,l.useRef)(null),u=(0,l.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,l.useContext)(c.MotionConfigContext);return(0,l.useInsertionEffect)(()=>{let{width:e,height:t,top:i,left:l,right:c}=u.current;if(r||!o.current||!e||!t)return;o.current.dataset.motionPopId=a;let d=document.createElement("style");h&&(d.nonce=h);let m=null!=s?s:document.head;return m.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===n?"left: ".concat(l):"right: ".concat(c),"px !important;\n            top: ").concat(i,"px !important;\n          }\n        ")),()=>{m.contains(d)&&m.removeChild(d)}},[r]),(0,t.jsx)(d,{isPresent:r,childRef:o,sizeRef:u,children:l.cloneElement(i,{ref:o})})}let h=e=>{let{children:r,initial:s,isPresent:o,onExitComplete:l,custom:c,presenceAffectsLayout:d,mode:h,anchorX:p,root:f}=e,g=(0,n.useConstant)(m),v=(0,i.useId)(),x=!0,y=(0,i.useMemo)(()=>(x=!1,{id:v,initial:s,isPresent:o,custom:c,onExitComplete:e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;l&&l()},register:e=>(g.set(e,!1),()=>g.delete(e))}),[o,g,l]);return d&&x&&(y={...y}),(0,i.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[o]),i.useEffect(()=>{o||g.size||!l||l()},[o]),"popLayout"===h&&(r=(0,t.jsx)(u,{isPresent:o,anchorX:p,root:f,children:r})),(0,t.jsx)(a.PresenceContext.Provider,{value:y,children:r})};function m(){return new Map}var p=e.i(64978);let f=e=>e.key||"";function g(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let v=e=>{let{children:a,custom:o,initial:l=!0,onExitComplete:c,presenceAffectsLayout:d=!0,mode:u="sync",propagate:m=!1,anchorX:v="left",root:x}=e,[y,b]=(0,p.usePresence)(m),w=(0,i.useMemo)(()=>g(a),[a]),j=m&&!y?[]:w.map(f),k=(0,i.useRef)(!0),P=(0,i.useRef)(w),N=(0,n.useConstant)(()=>new Map),[S,T]=(0,i.useState)(w),[C,A]=(0,i.useState)(w);(0,s.useIsomorphicLayoutEffect)(()=>{k.current=!1,P.current=w;for(let e=0;e<C.length;e++){let t=f(C[e]);j.includes(t)?N.delete(t):!0!==N.get(t)&&N.set(t,!1)}},[C,j.length,j.join("-")]);let M=[];if(w!==S){let e=[...w];for(let t=0;t<C.length;t++){let i=C[t],r=f(i);j.includes(r)||(e.splice(t,0,i),M.push(i))}return"wait"===u&&M.length&&(e=M),A(g(e)),T(w),null}let{forceRender:E}=(0,i.useContext)(r.LayoutGroupContext);return(0,t.jsx)(t.Fragment,{children:C.map(e=>{let i=f(e),r=(!m||!!y)&&(w===C||j.includes(i));return(0,t.jsx)(h,{isPresent:r,initial:(!k.current||!!l)&&void 0,custom:o,presenceAffectsLayout:d,mode:u,root:x,onExitComplete:r?void 0:()=>{if(!N.has(i))return;N.set(i,!0);let e=!0;N.forEach(t=>{t||(e=!1)}),e&&(null==E||E(),A(P.current),m&&(null==b||b()),c&&c())},anchorX:v,children:e},i)})})}},3374,e=>{"use strict";e.s(["default",()=>c]);var t=e.i(43476),i=e.i(71645),r=e.i(22016),n=e.i(57688),s=e.i(46932),a=e.i(88653),o=e.i(51360),l=e.i(59544);let c=()=>{let[e,c]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{c(window.scrollY>20)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let h=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Programs",href:"/#programs"},{name:"Products",href:"/products"},{name:"Impact",href:"/#impact"},{name:"Contact",href:"/contact"}];return(0,t.jsxs)(s.motion.header,{className:(0,o.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",e?"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200":"bg-transparent"),initial:{y:-100},animate:{y:0},transition:{duration:.5},children:[(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,t.jsx)(s.motion.div,{className:"flex items-center space-x-3",whileHover:{scale:1.05},transition:{duration:.2},children:(0,t.jsxs)(r.default,{href:"/",className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"relative w-10 h-10 lg:w-12 lg:h-12",children:(0,t.jsx)(n.default,{src:"/logo.jpeg",alt:"Ayurakshak Logo",fill:!0,className:"object-contain rounded-full",priority:!0})}),(0,t.jsxs)("div",{className:"hidden sm:block",children:[(0,t.jsx)("h1",{className:(0,o.cn)("text-xl lg:text-2xl font-bold transition-colors duration-300",e?"text-primary-600":"text-white"),children:"Ayurakshak"}),(0,t.jsx)("p",{className:(0,o.cn)("text-xs lg:text-sm transition-colors duration-300",e?"text-gray-600":"text-white/80"),children:"Care · Restore · Protect"})]})]})}),(0,t.jsx)("nav",{className:"hidden lg:flex items-center space-x-8",children:h.map((i,n)=>(0,t.jsx)(s.motion.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*n},children:(0,t.jsxs)(r.default,{href:i.href,className:(0,o.cn)("text-sm font-medium transition-all duration-200 hover:scale-105","relative group",e?"text-gray-700 hover:text-primary-600":"text-white hover:text-primary-200"),children:[i.name,(0,t.jsx)("span",{className:(0,o.cn)("absolute -bottom-1 left-0 w-0 h-0.5 transition-all duration-300 group-hover:w-full",e?"bg-primary-600":"bg-white")})]})},i.name))}),(0,t.jsx)("div",{className:"hidden lg:flex items-center space-x-4",children:(0,t.jsx)(l.default,{variant:e?"primary":"outline",size:"md",className:(0,o.cn)("transition-all duration-300",!e&&"border-white text-white hover:bg-white hover:text-primary-600"),children:"Donate Now"})}),(0,t.jsx)("button",{className:"lg:hidden p-2 rounded-md transition-colors duration-200",onClick:()=>u(!d),"aria-label":"Toggle mobile menu",children:(0,t.jsxs)("div",{className:"w-6 h-6 relative",children:[(0,t.jsx)("span",{className:(0,o.cn)("absolute block w-full h-0.5 transform transition-all duration-300","top-1.5",e?"bg-gray-700":"bg-white",d&&"rotate-45 top-3")}),(0,t.jsx)("span",{className:(0,o.cn)("absolute block w-full h-0.5 transform transition-all duration-300","top-3",e?"bg-gray-700":"bg-white",d&&"opacity-0")}),(0,t.jsx)("span",{className:(0,o.cn)("absolute block w-full h-0.5 transform transition-all duration-300","top-4.5",e?"bg-gray-700":"bg-white",d&&"-rotate-45 top-3")})]})})]})}),(0,t.jsx)(a.AnimatePresence,{children:d&&(0,t.jsx)(s.motion.div,{className:"lg:hidden bg-white border-t border-gray-200 shadow-lg",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,t.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[h.map((e,i)=>(0,t.jsx)(s.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*i},children:(0,t.jsx)(r.default,{href:e.href,className:"block text-gray-700 hover:text-primary-600 font-medium py-2 transition-colors duration-200",onClick:()=>u(!1),children:e.name})},e.name)),(0,t.jsx)(s.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*h.length},className:"pt-4 border-t border-gray-200",children:(0,t.jsx)(l.default,{variant:"primary",size:"md",className:"w-full",children:"Donate Now"})})]})})})]})}},39964,e=>{"use strict";e.s(["CardContent",()=>a,"CardHeader",()=>n,"CardTitle",()=>s,"default",()=>o]);var t=e.i(43476),i=e.i(46932),r=e.i(51360);let n=e=>{let{children:i,className:n}=e;return(0,t.jsx)("div",{className:(0,r.cn)("mb-4",n),children:i})},s=e=>{let{children:i,className:n,as:s="h3"}=e;return(0,t.jsx)(s,{className:(0,r.cn)("text-xl font-semibold text-gray-900 mb-2",n),children:i})},a=e=>{let{children:i,className:n}=e;return(0,t.jsx)("div",{className:(0,r.cn)("",n),children:i})},o=e=>{let{children:n,className:s,variant:a="default",padding:o="md",hover:l=!1,onClick:c}=e,d=(0,r.cn)(["rounded-xl transition-all duration-300",c&&"cursor-pointer"],{default:["bg-white border border-gray-200",l&&"hover:border-gray-300 hover:shadow-md"],elevated:["bg-white shadow-lg border border-gray-100",l&&"hover:shadow-xl hover:border-gray-200"],outlined:["bg-transparent border-2 border-primary-200",l&&"hover:border-primary-300 hover:bg-primary-50/50"],glass:["bg-white/80 backdrop-blur-sm border border-white/20",l&&"hover:bg-white/90 hover:border-white/30"]}[a],{none:"",sm:"p-4",md:"p-6",lg:"p-8",xl:"p-10"}[o],s),u=i.motion.div;return(0,t.jsx)(u,{className:d,onClick:c,whileHover:l?{y:-2,scale:1.02}:void 0,whileTap:c?{scale:.98}:void 0,transition:{duration:.2},children:n})}},55160,e=>{"use strict";e.s(["default",()=>a]);var t=e.i(43476),i=e.i(57688),r=e.i(46932),n=e.i(39964),s=e.i(59544);let a=()=>(0,t.jsx)("section",{id:"about",className:"py-20 bg-gradient-to-br from-sage-50 to-mint-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,t.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["About ",(0,t.jsx)("span",{className:"text-primary-600",children:"Ayurakshak"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Where ancient wisdom meets modern compassion to heal communities across India"})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center mb-20",children:[(0,t.jsxs)(r.motion.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Healing Through Traditional Wisdom"}),(0,t.jsxs)("div",{className:"space-y-6 text-gray-600 leading-relaxed",children:[(0,t.jsx)("p",{children:"Ayurakshak was born from a simple yet powerful vision: to bridge the gap between traditional Ayurvedic healing and modern healthcare accessibility. Founded by a team of passionate healthcare professionals and social workers, we believe that everyone deserves access to natural, holistic healthcare."}),(0,t.jsx)("p",{children:"Our journey began in rural villages where we witnessed the profound impact of combining ancient Ayurvedic practices with community-centered care. Today, we've grown into a movement that spans across India, touching lives through health camps, women's empowerment programs, and sustainable livelihood initiatives."}),(0,t.jsx)("p",{children:"At Ayurakshak, we don't just treat symptoms – we nurture communities, empower individuals, and create lasting change that ripples through generations. Every program we run, every product we create, and every life we touch is guided by our commitment to holistic wellness and social transformation."})]}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsx)(s.default,{variant:"primary",size:"lg",children:"Learn More About Our Mission"})})]}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"relative",children:[(0,t.jsxs)("div",{className:"relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl",children:[(0,t.jsx)(i.default,{src:"/about/ayurveda-healing.jpg",alt:"Traditional Ayurvedic healing session",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 50vw"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]}),(0,t.jsx)(r.motion.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border border-gray-100",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-primary-600 mb-1",children:"8+"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Years of Service"})]})})]})]}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mb-20",children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-center text-gray-900 mb-12",children:"Our Core Values"}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{icon:"🌿",title:"Traditional Wisdom",description:"Preserving and practicing ancient Ayurvedic knowledge for modern healing"},{icon:"🤝",title:"Community First",description:"Building stronger, healthier communities through collaborative care"},{icon:"🌱",title:"Sustainable Growth",description:"Creating lasting change through environmentally conscious practices"},{icon:"💚",title:"Compassionate Care",description:"Providing healthcare with empathy, dignity, and respect for all"}].map((e,i)=>(0,t.jsx)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*i},viewport:{once:!0},children:(0,t.jsx)(n.default,{variant:"elevated",hover:!0,className:"text-center h-full",children:(0,t.jsxs)(n.CardContent,{className:"p-6",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,t.jsx)("h4",{className:"text-xl font-semibold text-gray-900 mb-3",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]})})},e.title))})]}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"bg-primary-600 rounded-2xl p-8 lg:p-12 text-white",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h3",{className:"text-3xl font-bold mb-4",children:"Our Impact So Far"}),(0,t.jsx)("p",{className:"text-primary-100 text-lg",children:"Every number represents a life touched, a community strengthened, and hope restored"})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-8",children:[{number:"15,000+",label:"Lives Impacted",icon:"👥"},{number:"250+",label:"Villages Reached",icon:"🏘️"},{number:"180+",label:"Health Camps",icon:"🏥"},{number:"2,500+",label:"Women Empowered",icon:"👩"}].map((e,i)=>(0,t.jsxs)(r.motion.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.1*i},viewport:{once:!0},className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl mb-2",children:e.icon}),(0,t.jsx)("div",{className:"text-3xl lg:text-4xl font-bold mb-2",children:e.number}),(0,t.jsx)("div",{className:"text-primary-100",children:e.label})]},e.label))})]})]})})},43412,e=>{"use strict";e.s(["default",()=>a]);var t=e.i(43476),i=e.i(71645),r=e.i(46932),n=e.i(39964),s=e.i(59544);let a=()=>{let[e,a]=(0,i.useState)(500),[o,l]=(0,i.useState)(""),[c,d]=(0,i.useState)("general"),u=()=>o?parseInt(o)||0:e;return(0,t.jsx)("section",{className:"py-20 bg-gradient-to-br from-primary-600 to-primary-800 text-white",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,t.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold mb-6",children:["Make a ",(0,t.jsx)("span",{className:"text-yellow-300",children:"Difference"})]}),(0,t.jsx)("p",{className:"text-xl text-primary-100 max-w-3xl mx-auto leading-relaxed",children:"Your contribution can transform lives and heal communities. Every rupee counts towards building a healthier India."})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-start",children:[(0,t.jsx)(r.motion.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,t.jsxs)(n.default,{variant:"glass",className:"bg-white/10 backdrop-blur-md border-white/20",children:[(0,t.jsx)(n.CardHeader,{children:(0,t.jsx)(n.CardTitle,{className:"text-2xl text-white text-center",children:"Choose Your Contribution"})}),(0,t.jsxs)(n.CardContent,{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-3",children:"Select Purpose"}),(0,t.jsx)("div",{className:"grid grid-cols-1 gap-3",children:[{id:"general",title:"General Fund",description:"Support all our programs and initiatives",icon:"🌟",impact:"Helps us reach more communities"},{id:"health",title:"Health Camps",description:"Mobile healthcare for remote villages",icon:"🏥",impact:"₹500 can provide treatment for 5 families"},{id:"education",title:"Health Education",description:"Awareness programs and workshops",icon:"📚",impact:"₹1000 can educate 50 people"},{id:"livelihoods",title:"Women Empowerment",description:"Skill development and microfinance",icon:"👩‍💼",impact:"₹2500 can train 10 women entrepreneurs"},{id:"emergency",title:"Emergency Relief",description:"Disaster response and urgent care",icon:"🚨",impact:"₹1000 can provide emergency kit for 1 family"}].map(e=>(0,t.jsx)("button",{onClick:()=>d(e.id),className:"p-4 rounded-lg border-2 transition-all duration-200 text-left ".concat(c===e.id?"border-yellow-300 bg-white/20":"border-white/30 hover:border-white/50 bg-white/10"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-semibold text-white",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-white/80",children:e.description})]})]})},e.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-3",children:"Select Amount (₹)"}),(0,t.jsx)("div",{className:"grid grid-cols-3 gap-3 mb-4",children:[100,250,500,1e3,2500,5e3].map(i=>(0,t.jsxs)("button",{onClick:()=>{a(i),l("")},className:"p-3 rounded-lg border-2 transition-all duration-200 font-semibold ".concat(e===i?"border-yellow-300 bg-yellow-300 text-primary-800":"border-white/30 text-white hover:border-white/50"),children:["₹",i]},i))}),(0,t.jsx)("input",{type:"number",placeholder:"Enter custom amount",value:o,onChange:e=>{l(e.target.value),a(0)},className:"w-full p-3 rounded-lg bg-white/10 border-2 border-white/30 text-white placeholder-white/60 focus:border-yellow-300 focus:outline-none"})]}),u()>0&&(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/20 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold text-white mb-2",children:"Your Impact:"}),(0,t.jsxs)("p",{className:"text-white/90",children:["₹",u()," can help"," ",(0,t.jsx)("span",{className:"font-bold text-yellow-300",children:((e,t)=>({general:Math.floor(e/100),health:Math.floor(e/100),education:Math.floor(e/20),livelihoods:Math.floor(e/250),emergency:Math.floor(e/1e3)})[t]||0)(u(),c)})," ","general"===c&&"people through our programs","health"===c&&"families receive healthcare","education"===c&&"people get health education","livelihoods"===c&&"women start businesses","emergency"===c&&"families in emergencies"]})]}),(0,t.jsxs)(s.default,{variant:"secondary",size:"lg",className:"w-full bg-yellow-400 text-primary-800 hover:bg-yellow-300 font-bold text-lg py-4",disabled:0===u(),children:["Donate ₹",u()," Now"]}),(0,t.jsx)("p",{className:"text-xs text-white/70 text-center",children:"🔒 Secure payment • 80G Tax exemption available • 100% transparent usage"})]})]})}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-white mb-6",children:"Why Your Donation Matters"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"bg-yellow-400 rounded-full p-2 mt-1",children:(0,t.jsx)("svg",{className:"w-4 h-4 text-primary-800",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-white",children:"Direct Impact"}),(0,t.jsx)("p",{className:"text-primary-100",children:"100% of your donation goes directly to programs, not administrative costs"})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"bg-yellow-400 rounded-full p-2 mt-1",children:(0,t.jsx)("svg",{className:"w-4 h-4 text-primary-800",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-white",children:"Tax Benefits"}),(0,t.jsx)("p",{className:"text-primary-100",children:"Get 80G tax exemption on your donations as per Indian tax laws"})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"bg-yellow-400 rounded-full p-2 mt-1",children:(0,t.jsx)("svg",{className:"w-4 h-4 text-primary-800",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-white",children:"Transparency"}),(0,t.jsx)("p",{className:"text-primary-100",children:"Regular updates on how your contribution is making a difference"})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white/10 rounded-xl p-6 backdrop-blur-sm",children:[(0,t.jsx)("h4",{className:"text-xl font-bold text-white mb-4",children:"Recent Impact"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-primary-100",children:"Health camps this month"}),(0,t.jsx)("span",{className:"font-bold text-yellow-300",children:"15"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-primary-100",children:"Families helped"}),(0,t.jsx)("span",{className:"font-bold text-yellow-300",children:"1,250"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-primary-100",children:"Women trained"}),(0,t.jsx)("span",{className:"font-bold text-yellow-300",children:"180"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-primary-100",children:"Villages reached"}),(0,t.jsx)("span",{className:"font-bold text-yellow-300",children:"25"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-xl font-bold text-white mb-4",children:"Other Ways to Help"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(s.default,{variant:"outline",className:"w-full border-white text-white hover:bg-white hover:text-primary-800",children:"Become a Volunteer"}),(0,t.jsx)(s.default,{variant:"outline",className:"w-full border-white text-white hover:bg-white hover:text-primary-800",children:"Corporate Partnership"}),(0,t.jsx)(s.default,{variant:"outline",className:"w-full border-white text-white hover:bg-white hover:text-primary-800",children:"Sponsor a Program"})]})]})]})]})]})})}},77258,e=>{"use strict";e.s(["default",()=>o]);var t=e.i(43476),i=e.i(71645),r=e.i(57688),n=e.i(46932),s=e.i(88653),a=e.i(59544);let o=()=>{let[e,o]=(0,i.useState)(0),l=[{id:1,title:"Healing Communities with Traditional Wisdom",subtitle:"Ayurakshak combines ancient Ayurveda with modern outreach to bring healthcare to every corner of India",image:"/hero/health-camp.jpg",cta:"Support Our Mission",stats:{number:"15,000+",label:"Lives Impacted"}},{id:2,title:"Empowering Women Through Sustainable Livelihoods",subtitle:"Creating opportunities for women to build independent, sustainable businesses in their communities",image:"/hero/women-empowerment.jpg",cta:"Join Our Programs",stats:{number:"2,500+",label:"Women Empowered"}},{id:3,title:"Natural Products for Holistic Wellness",subtitle:"Authentic Ayurvedic products crafted with traditional knowledge and modern quality standards",image:"/hero/ayurvedic-products.jpg",cta:"Shop Products",stats:{number:"50+",label:"Natural Products"}}];return(0,i.useEffect)(()=>{let e=setInterval(()=>{o(e=>(e+1)%l.length)},6e3);return()=>clearInterval(e)},[l.length]),(0,t.jsxs)("section",{className:"relative h-screen flex items-center justify-center overflow-hidden",children:[(0,t.jsx)(s.AnimatePresence,{mode:"wait",children:(0,t.jsxs)(n.motion.div,{className:"absolute inset-0 z-0",initial:{opacity:0,scale:1.1},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{duration:1},children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60 z-10"}),(0,t.jsx)(r.default,{src:l[e].image,alt:l[e].title,fill:!0,className:"object-cover",priority:!0,sizes:"100vw"})]},e)}),(0,t.jsxs)("div",{className:"relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,t.jsx)(s.AnimatePresence,{mode:"wait",children:(0,t.jsxs)(n.motion.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:-50},transition:{duration:.8},className:"text-white",children:[(0,t.jsxs)(n.motion.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.2},className:"inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-8",children:[(0,t.jsx)("span",{className:"text-2xl font-bold text-primary-200",children:l[e].stats.number}),(0,t.jsx)("span",{className:"ml-2 text-white/90",children:l[e].stats.label})]}),(0,t.jsx)(n.motion.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.3},className:"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight",children:l[e].title}),(0,t.jsx)(n.motion.p,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-xl md:text-2xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed",children:l[e].subtitle}),(0,t.jsxs)(n.motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.5},className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,t.jsx)(a.default,{variant:"primary",size:"lg",className:"bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg",children:l[e].cta}),(0,t.jsx)(a.default,{variant:"outline",size:"lg",className:"border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg",children:"Learn More"})]})]},e)}),(0,t.jsx)(n.motion.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,t.jsxs)("div",{className:"flex flex-col items-center text-white/70",children:[(0,t.jsx)("span",{className:"text-sm mb-2",children:"Scroll to explore"}),(0,t.jsx)(n.motion.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center",children:(0,t.jsx)("div",{className:"w-1 h-3 bg-white/70 rounded-full mt-2"})})]})})]}),(0,t.jsx)("button",{onClick:()=>{o(e=>(e-1+l.length)%l.length)},className:"absolute left-4 top-1/2 transform -translate-y-1/2 z-30 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200","aria-label":"Previous slide",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,t.jsx)("button",{onClick:()=>{o(e=>(e+1)%l.length)},className:"absolute right-4 top-1/2 transform -translate-y-1/2 z-30 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200","aria-label":"Next slide",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}),(0,t.jsx)("div",{className:"absolute bottom-20 left-1/2 transform -translate-x-1/2 z-30 flex space-x-3",children:l.map((i,r)=>(0,t.jsx)("button",{onClick:()=>o(r),className:"w-3 h-3 rounded-full transition-all duration-300 ".concat(r===e?"bg-white scale-125":"bg-white/50 hover:bg-white/70"),"aria-label":"Go to slide ".concat(r+1)},r))}),(0,t.jsx)("div",{className:"absolute inset-0 z-10 pointer-events-none",children:[...Array(6)].map((e,i)=>(0,t.jsx)(n.motion.div,{className:"absolute w-2 h-2 bg-white/20 rounded-full",style:{left:"".concat(100*Math.random(),"%"),top:"".concat(100*Math.random(),"%")},animate:{y:[0,-20,0],opacity:[.2,.8,.2]},transition:{duration:3+2*Math.random(),repeat:1/0,delay:2*Math.random()}},i))})]})}},90568,e=>{"use strict";e.s(["default",()=>s]);var t=e.i(43476),i=e.i(57688),r=e.i(46932),n=e.i(39964);let s=()=>(0,t.jsx)("section",{id:"impact",className:"py-20 bg-gradient-to-br from-gray-50 to-blue-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,t.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["Our ",(0,t.jsx)("span",{className:"text-primary-600",children:"Impact"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Real stories of transformation, healing, and hope from communities across India"})]}),(0,t.jsx)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-20",children:[{number:"15,000+",label:"Lives Directly Impacted",icon:"👥",color:"text-blue-600"},{number:"250+",label:"Villages Reached",icon:"🏘️",color:"text-green-600"},{number:"2,500+",label:"Women Empowered",icon:"👩",color:"text-purple-600"},{number:"180+",label:"Health Camps Conducted",icon:"🏥",color:"text-red-600"},{number:"50,000+",label:"Medicinal Plants Grown",icon:"🌱",color:"text-emerald-600"},{number:"8",label:"States Covered",icon:"🗺️",color:"text-orange-600"}].map((e,i)=>(0,t.jsxs)(r.motion.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.1*i},viewport:{once:!0},className:"text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300",children:[(0,t.jsx)("div",{className:"text-3xl mb-3",children:e.icon}),(0,t.jsx)("div",{className:"text-2xl lg:text-3xl font-bold mb-2 ".concat(e.color),children:e.number}),(0,t.jsx)("div",{className:"text-sm text-gray-600 leading-tight",children:e.label})]},e.label))}),(0,t.jsxs)("div",{className:"mb-20",children:[(0,t.jsx)(r.motion.h3,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-3xl font-bold text-center text-gray-900 mb-12",children:"Stories of Transformation"}),(0,t.jsx)("div",{className:"space-y-12",children:[{id:1,title:"Transforming Rural Healthcare in Rajasthan",location:"Udaipur District, Rajasthan",image:"/impact/rajasthan-health.jpg",description:"Our mobile health camps have reached 45 villages in Udaipur district, providing free Ayurvedic treatment to over 3,000 families.",stats:{families:"3,000+",villages:"45",camps:"120"},quote:"Ayurakshak brought hope to our village when modern medicine was too expensive.",author:"Kamala Devi, Village Elder"},{id:2,title:"Women Entrepreneurs Leading Change",location:"Varanasi, Uttar Pradesh",image:"/impact/women-entrepreneurs.jpg",description:"Through our livelihood program, 200 women have started their own herbal product businesses, earning sustainable income.",stats:{women:"200+",businesses:"150+",income:"₹50,000"},quote:"I can now support my family and send my children to school with dignity.",author:"Sunita Sharma, Entrepreneur"},{id:3,title:"Emergency Relief During COVID-19",location:"Multiple States",image:"/impact/covid-relief.jpg",description:"During the pandemic, we provided immunity boosters and healthcare support to 10,000 families across 8 states.",stats:{families:"10,000+",states:"8",kits:"25,000"},quote:"When hospitals were full, Ayurakshak was there with natural healing.",author:"Dr. Rajesh Kumar, Community Health Worker"}].map((e,n)=>(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2*n},viewport:{once:!0},className:"grid lg:grid-cols-2 gap-8 items-center ".concat(n%2==1?"lg:grid-flow-col-dense":""),children:[(0,t.jsxs)("div",{className:"relative ".concat(n%2==1?"lg:col-start-2":""),children:[(0,t.jsxs)("div",{className:"relative h-64 lg:h-80 rounded-2xl overflow-hidden shadow-xl",children:[(0,t.jsx)(i.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 50vw"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"})]}),(0,t.jsx)("div",{className:"absolute -bottom-6 left-6 right-6 bg-white rounded-xl shadow-lg p-4",children:(0,t.jsx)("div",{className:"grid grid-cols-3 gap-4 text-center",children:Object.entries(e.stats).map(e=>{let[i,r]=e;return(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-lg font-bold text-primary-600",children:r}),(0,t.jsx)("div",{className:"text-xs text-gray-500 capitalize",children:i})]},i)})})})]}),(0,t.jsxs)("div",{className:"".concat(n%2==1?"lg:col-start-1":""," pt-8 lg:pt-0"),children:[(0,t.jsxs)("div",{className:"inline-flex items-center bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium mb-4",children:["📍 ",e.location]}),(0,t.jsx)("h4",{className:"text-2xl lg:text-3xl font-bold text-gray-900 mb-4",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed mb-6 text-lg",children:e.description}),(0,t.jsxs)("blockquote",{className:"border-l-4 border-primary-500 pl-6 py-4 bg-primary-50 rounded-r-lg",children:[(0,t.jsxs)("p",{className:"text-gray-700 italic mb-2",children:['"',e.quote,'"']}),(0,t.jsxs)("cite",{className:"text-sm font-medium text-primary-700",children:["— ",e.author]})]})]})]},e.id))})]}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-center text-gray-900 mb-12",children:"Recognition & Achievements"}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{icon:"🏆",title:"National Recognition",description:"Awarded Best NGO for Rural Healthcare by Ministry of Health",year:"2023"},{icon:"🌟",title:"UN Partnership",description:"Collaborated with UN Women for sustainable development goals",year:"2022"},{icon:"📜",title:"ISO Certification",description:"Certified for quality management in healthcare delivery",year:"2021"},{icon:"🎖️",title:"State Award",description:"Recognized by Rajasthan Government for community service",year:"2020"}].map((e,i)=>(0,t.jsx)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*i},viewport:{once:!0},children:(0,t.jsx)(n.default,{variant:"elevated",hover:!0,className:"text-center h-full",children:(0,t.jsxs)(n.CardContent,{className:"p-6",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,t.jsx)("div",{className:"text-sm font-medium text-primary-600 mb-2",children:e.year}),(0,t.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description})]})})},e.title))})]})]})})},79485,e=>{"use strict";e.s(["default",()=>o],79485);var t=e.i(43476),i=e.i(71645),r=e.i(46932),n=e.i(59544),s=e.i(51360);let a=i.default.forwardRef((e,i)=>{let{className:n,label:a,error:o,helperText:l,leftIcon:c,rightIcon:d,variant:u="default",inputSize:h="md",id:m,...p}=e,f=m||"input-".concat(Math.random().toString(36).substr(2,9)),g={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"},v=(0,s.cn)(["w-full transition-all duration-200 focus:outline-none","disabled:opacity-50 disabled:cursor-not-allowed"],{default:["border border-gray-300 rounded-lg bg-white","focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20",o&&"border-red-500 focus:border-red-500 focus:ring-red-500/20"],filled:["border-0 rounded-lg bg-gray-100","focus:bg-white focus:ring-2 focus:ring-primary-500/20",o&&"bg-red-50 focus:ring-red-500/20"],outlined:["border-2 border-gray-300 rounded-lg bg-transparent","focus:border-primary-500",o&&"border-red-500 focus:border-red-500"]}[u],{sm:"px-3 py-2 text-sm",md:"px-4 py-3 text-base",lg:"px-5 py-4 text-lg"}[h],c&&"pl-10",d&&"pr-10",n);return(0,t.jsxs)("div",{className:"w-full",children:[a&&(0,t.jsx)("label",{htmlFor:f,className:(0,s.cn)("block text-sm font-medium mb-2",o?"text-red-700":"text-gray-700"),children:a}),(0,t.jsxs)("div",{className:"relative",children:[c&&(0,t.jsx)("div",{className:(0,s.cn)("absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",g[h]),children:c}),(0,t.jsx)("input",{ref:i,id:f,className:v,...p}),d&&(0,t.jsx)("div",{className:(0,s.cn)("absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",g[h]),children:d})]}),(o||l)&&(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:-5},animate:{opacity:1,y:0},transition:{duration:.2},className:"mt-2",children:[o&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),o]}),l&&!o&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:l})]})]})});a.displayName="Input";let o=()=>{let[e,s]=(0,i.useState)(""),[o,l]=(0,i.useState)(""),[c,d]=(0,i.useState)(!1),[u,h]=(0,i.useState)(!1),m=async t=>{if(t.preventDefault(),e){d(!0);try{(await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,name:o})})).ok?(h(!0),s(""),l("")):console.error("Subscription failed")}catch(e){console.error("Subscription error:",e)}finally{d(!1)}}};return u?(0,t.jsx)("section",{className:"py-20 bg-gradient-to-br from-sage-50 to-mint-50",children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,t.jsxs)(r.motion.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6},className:"bg-white rounded-2xl shadow-xl p-12",children:[(0,t.jsx)("div",{className:"text-6xl mb-6",children:"🎉"}),(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Welcome to Our Community!"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"Thank you for subscribing! You'll receive our latest updates and exclusive content soon."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(n.default,{variant:"primary",size:"lg",children:"Explore Our Programs"}),(0,t.jsx)(n.default,{variant:"outline",size:"lg",children:"Shop Products"})]})]})})}):(0,t.jsx)("section",{className:"py-20 bg-gradient-to-br from-sage-50 to-mint-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,t.jsxs)(r.motion.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,t.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["Stay Connected with Our"," ",(0,t.jsx)("span",{className:"text-primary-600",children:"Mission"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Join thousands of supporters who receive our updates on community impact, health tips, and ways to make a difference. Be part of the healing journey."}),(0,t.jsx)("div",{className:"grid md:grid-cols-2 gap-6 mb-8",children:[{icon:"📧",title:"Monthly Updates",description:"Get our monthly newsletter with program updates and impact stories"},{icon:"🎯",title:"Exclusive Content",description:"Access to exclusive health tips and Ayurvedic wellness guides"},{icon:"🎉",title:"Event Invitations",description:"Be the first to know about health camps and community events"},{icon:"💝",title:"Special Offers",description:"Exclusive discounts on our natural products and programs"}].map((e,i)=>(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*i},viewport:{once:!0},className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},e.title))}),(0,t.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,t.jsx)("span",{children:"No spam, ever"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,t.jsx)("span",{children:"Unsubscribe anytime"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,t.jsx)("span",{children:"5,000+ subscribers"})]})]})]}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"bg-white rounded-2xl shadow-xl p-8 lg:p-10",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:"📬"}),(0,t.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Join Our Newsletter"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Get the latest updates delivered to your inbox"})]}),(0,t.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[(0,t.jsx)(a,{type:"text",placeholder:"Your name (optional)",value:o,onChange:e=>l(e.target.value),variant:"filled",inputSize:"lg"}),(0,t.jsx)(a,{type:"email",placeholder:"Enter your email address",value:e,onChange:e=>s(e.target.value),required:!0,variant:"filled",inputSize:"lg"}),(0,t.jsx)(n.default,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:c,disabled:!e||c,children:c?"Subscribing...":"Subscribe Now"})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 text-center mt-6",children:["By subscribing, you agree to our"," ",(0,t.jsx)("a",{href:"/privacy",className:"text-primary-600 hover:underline",children:"Privacy Policy"})," ","and consent to receive updates from Ayurakshak."]}),(0,t.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-100",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 text-center mb-4",children:"Join our community on social media"}),(0,t.jsx)("div",{className:"flex justify-center space-x-4",children:[{name:"Facebook",icon:"📘"},{name:"Instagram",icon:"📷"},{name:"Twitter",icon:"🐦"},{name:"LinkedIn",icon:"💼"}].map(e=>(0,t.jsx)("button",{className:"w-10 h-10 rounded-full bg-gray-100 hover:bg-primary-100 transition-colors duration-200 flex items-center justify-center","aria-label":e.name,children:(0,t.jsx)("span",{className:"text-lg",children:e.icon})},e.name))})]})]})]})})})}},47385,e=>{"use strict";e.s(["default",()=>a]);var t=e.i(43476),i=e.i(57688),r=e.i(46932),n=e.i(39964),s=e.i(59544);let a=()=>(0,t.jsx)("section",{id:"programs",className:"py-20 bg-white",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,t.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["Our ",(0,t.jsx)("span",{className:"text-primary-600",children:"Programs"})]}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Comprehensive initiatives designed to heal, empower, and transform communities across India"})]}),(0,t.jsx)("div",{className:"grid lg:grid-cols-2 gap-8 mb-16",children:[{id:"health-camps",title:"Mobile Health Camps",description:"Bringing Ayurvedic healthcare directly to remote villages and underserved communities across India.",image:"/programs/health-camps.jpg",icon:"🏥",stats:{beneficiaries:"15,000+",locations:"250+"},features:["Free Ayurvedic consultations","Traditional medicine distribution","Health education workshops","Preventive care guidance"],color:"from-green-500 to-emerald-600"},{id:"women-empowerment",title:"Women Livelihood Programs",description:"Empowering women through skill development, entrepreneurship training, and sustainable business opportunities.",image:"/programs/women-empowerment.jpg",icon:"👩‍💼",stats:{beneficiaries:"2,500+",groups:"150+"},features:["Skill development training","Microfinance support","Business mentorship","Market linkage assistance"],color:"from-purple-500 to-pink-600"},{id:"education",title:"Health Education Initiative",description:"Spreading awareness about preventive healthcare, nutrition, and traditional wellness practices.",image:"/programs/education.jpg",icon:"📚",stats:{beneficiaries:"8,000+",workshops:"300+"},features:["Community health workshops","Nutrition education","Hygiene awareness programs","Traditional medicine training"],color:"from-blue-500 to-indigo-600"},{id:"emergency-relief",title:"Emergency Relief Support",description:"Rapid response healthcare support during natural disasters and health emergencies.",image:"/programs/emergency-relief.jpg",icon:"🚨",stats:{responses:"25+",families:"5,000+"},features:["Emergency medical camps","Relief material distribution","Psychological support","Rehabilitation assistance"],color:"from-red-500 to-orange-600"},{id:"environment",title:"Green Health Initiative",description:"Promoting environmental conservation through medicinal plant cultivation and eco-friendly practices.",image:"/programs/environment.jpg",icon:"🌱",stats:{plants:"50,000+",farmers:"800+"},features:["Medicinal plant cultivation","Organic farming training","Environmental awareness","Sustainable practices"],color:"from-green-600 to-teal-600"},{id:"community-development",title:"Community Development",description:"Building resilient communities through integrated development programs and capacity building.",image:"/programs/community-development.jpg",icon:"🏘️",stats:{villages:"180+",leaders:"500+"},features:["Community leadership training","Infrastructure development","Social cohesion programs","Local governance support"],color:"from-yellow-500 to-amber-600"}].map((e,a)=>(0,t.jsx)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*a},viewport:{once:!0},children:(0,t.jsxs)(n.default,{variant:"elevated",hover:!0,className:"h-full overflow-hidden group",children:[(0,t.jsxs)("div",{className:"relative h-48 overflow-hidden",children:[(0,t.jsx)(i.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300",sizes:"(max-width: 768px) 100vw, 50vw"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r ".concat(e.color," opacity-80")}),(0,t.jsx)("div",{className:"absolute top-4 left-4 bg-white/20 backdrop-blur-sm rounded-full p-3",children:(0,t.jsx)("span",{className:"text-2xl",children:e.icon})}),(0,t.jsx)("div",{className:"absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1",children:(0,t.jsxs)("span",{className:"text-sm font-medium text-gray-800",children:[Object.values(e.stats)[0]," impacted"]})})]}),(0,t.jsxs)(n.CardContent,{className:"p-6",children:[(0,t.jsxs)(n.CardHeader,{className:"p-0 mb-4",children:[(0,t.jsx)(n.CardTitle,{className:"text-xl font-bold text-gray-900 mb-2",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]}),(0,t.jsx)("div",{className:"flex gap-6 mb-4",children:Object.entries(e.stats).map(e=>{let[i,r]=e;return(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-primary-600",children:r}),(0,t.jsx)("div",{className:"text-xs text-gray-500 capitalize",children:i})]},i)})}),(0,t.jsx)("div",{className:"space-y-2 mb-6",children:e.features.map((e,i)=>(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)("div",{className:"w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 flex-shrink-0"}),e]},i))}),(0,t.jsx)(s.default,{variant:"outline",size:"sm",className:"w-full",children:"Learn More"})]})]})},e.id))}),(0,t.jsxs)(r.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center bg-gradient-to-r from-primary-50 to-sage-50 rounded-2xl p-8 lg:p-12",children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Want to Support Our Programs?"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-8 max-w-2xl mx-auto",children:"Your contribution can help us expand our reach and impact more lives. Join us in building healthier, more empowered communities."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(s.default,{variant:"primary",size:"lg",children:"Donate Now"}),(0,t.jsx)(s.default,{variant:"outline",size:"lg",children:"Become a Volunteer"})]})]})]})})}]);