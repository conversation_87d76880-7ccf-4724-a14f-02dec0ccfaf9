import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  weight: ["300", "400", "500", "600", "700", "800"],
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: "Ayurakshak - Care · Restore · Protect",
    template: "%s | Ayurakshak",
  },
  description:
    "Ayurakshak combines traditional Ayurveda with modern outreach — health camps, sustainable livelihoods, and green initiatives across India. Join us in healing communities with nature-led care.",
  keywords: [
    "Ayurakshak",
    "Ayurveda",
    "NGO",
    "Healthcare",
    "Traditional Medicine",
    "Health Camps",
    "Natural Healing",
    "Community Health",
    "Herbal Products",
    "Sustainable Livelihoods",
    "India",
    "Naturopathy",
  ],
  authors: [
    {
      name: "Ayurakshak Team",
      url: "https://ayurakshak.org",
    },
    {
      name: "<PERSON><PERSON>",
      url: "https://kush-personal-portfolio-my-portfolio.vercel.app/",
    },
  ],
  creator: "Kush Vardhan",
  publisher: "Ayurakshak",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
  ),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "/",
    title: "Ayurakshak - Care · Restore · Protect",
    description:
      "Healing communities with traditional Ayurveda and modern outreach across India.",
    siteName: "Ayurakshak",
    images: [
      {
        url: "/logo.jpeg",
        width: 1200,
        height: 630,
        alt: "Ayurakshak - Traditional Ayurveda meets modern healthcare",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Ayurakshak - Care · Restore · Protect",
    description:
      "Healing communities with traditional Ayurveda and modern outreach across India.",
    images: ["/logo.jpeg"],
    creator: "@ayurakshak",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/logo.jpeg" sizes="any" />
        <link rel="apple-touch-icon" href="/logo.jpeg" />
        <meta name="theme-color" content="#4a7c59" />
        <meta name="msapplication-TileColor" content="#4a7c59" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=5"
        />
      </head>
      <body
        className={`${inter.variable} ${poppins.variable} font-sans antialiased bg-white text-gray-900`}
      >
        {children}
      </body>
    </html>
  );
}
