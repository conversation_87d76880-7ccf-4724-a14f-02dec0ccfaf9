module.exports=[46058,(a,b,c)=>{"use strict";function d(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(d=function(a){return a?c:b})(a)}c._=function(a,b){if(!b&&a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=d(b);if(c&&c.has(a))return c.get(a);var e={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(e,g,h):e[g]=a[g]}return e.default=a,c&&c.set(a,e),e}},91735,(a,b,c)=>{"use strict";function d(a){let b={};for(let[c,d]of a.entries()){let a=b[c];void 0===a?b[c]=d:Array.isArray(a)?a.push(d):b[c]=[a,d]}return b}function e(a){return"string"==typeof a?a:("number"!=typeof a||isNaN(a))&&"boolean"!=typeof a?"":String(a)}function f(a){let b=new URLSearchParams;for(let[c,d]of Object.entries(a))if(Array.isArray(d))for(let a of d)b.append(c,e(a));else b.set(c,e(d));return b}function g(a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];for(let b of c){for(let c of b.keys())a.delete(c);for(let[c,d]of b.entries())a.append(c,d)}return a}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{assign:function(){return g},searchParamsToUrlQuery:function(){return d},urlQueryToSearchParams:function(){return f}})},43087,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{formatUrl:function(){return f},formatWithValidation:function(){return h},urlObjectKeys:function(){return g}});let d=a.r(46058)._(a.r(91735)),e=/https?|ftp|gopher|file/;function f(a){let{auth:b,hostname:c}=a,f=a.protocol||"",g=a.pathname||"",h=a.hash||"",i=a.query||"",j=!1;b=b?encodeURIComponent(b).replace(/%3A/i,":")+"@":"",a.host?j=b+a.host:c&&(j=b+(~c.indexOf(":")?"["+c+"]":c),a.port&&(j+=":"+a.port)),i&&"object"==typeof i&&(i=String(d.urlQueryToSearchParams(i)));let k=a.search||i&&"?"+i||"";return f&&!f.endsWith(":")&&(f+=":"),a.slashes||(!f||e.test(f))&&!1!==j?(j="//"+(j||""),g&&"/"!==g[0]&&(g="/"+g)):j||(j=""),h&&"#"!==h[0]&&(h="#"+h),k&&"?"!==k[0]&&(k="?"+k),""+f+j+(g=g.replace(/[?#]/g,encodeURIComponent))+(k=k.replace("#","%23"))+h}let g=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function h(a){return f(a)}},8591,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=a.r(72131);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},46272,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return t},MissingStaticPage:function(){return s},NormalizeError:function(){return q},PageNotFoundError:function(){return r},SP:function(){return n},ST:function(){return o},WEB_VITALS:function(){return d},execOnce:function(){return e},getDisplayName:function(){return j},getLocationOrigin:function(){return h},getURL:function(){return i},isAbsoluteUrl:function(){return g},isResSent:function(){return k},loadGetInitialProps:function(){return m},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return u}});let d=["CLS","FCP","FID","INP","LCP","TTFB"];function e(a){let b,c=!1;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return c||(c=!0,b=a(...e)),b}}let f=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,g=a=>f.test(a);function h(){let{protocol:a,hostname:b,port:c}=window.location;return a+"//"+b+(c?":"+c:"")}function i(){let{href:a}=window.location,b=h();return a.substring(b.length)}function j(a){return"string"==typeof a?a:a.displayName||a.name||"Unknown"}function k(a){return a.finished||a.headersSent}function l(a){let b=a.split("?");return b[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(b[1]?"?"+b.slice(1).join("?"):"")}async function m(a,b){let c=b.res||b.ctx&&b.ctx.res;if(!a.getInitialProps)return b.ctx&&b.Component?{pageProps:await m(b.Component,b.ctx)}:{};let d=await a.getInitialProps(b);if(c&&k(c))return d;if(!d)throw Object.defineProperty(Error('"'+j(a)+'.getInitialProps()" should resolve to an object. But found "'+d+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return d}let n="undefined"!=typeof performance,o=n&&["mark","measure","getEntriesByName"].every(a=>"function"==typeof performance[a]);class p extends Error{}class q extends Error{}class r extends Error{constructor(a){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+a}}class s extends Error{constructor(a,b){super(),this.message="Failed to load static file for page: "+a+" "+b}}class t extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function u(a){return JSON.stringify({message:a.message,stack:a.stack})}},8868,(a,b,c)=>{"use strict";function d(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"parsePath",{enumerable:!0,get:function(){return d}})},5407,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"addPathPrefix",{enumerable:!0,get:function(){return e}});let d=a.r(8868);function e(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:e,hash:f}=(0,d.parsePath)(a);return""+b+c+e+f}},46112,(a,b,c)=>{"use strict";function d(a){return a.replace(/\/$/,"")||"/"}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"removeTrailingSlash",{enumerable:!0,get:function(){return d}})},25075,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return f}});let d=a.r(46112),e=a.r(8868),f=a=>{if(!a.startsWith("/"))return a;let{pathname:b,query:c,hash:f}=(0,e.parsePath)(a);return""+(0,d.removeTrailingSlash)(b)+c+f};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},38243,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"addBasePath",{enumerable:!0,get:function(){return f}});let d=a.r(5407),e=a.r(25075);function f(a,b){return(0,e.normalizePathTrailingSlash)((0,d.addPathPrefix)(a,""))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},92434,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"warnOnce",{enumerable:!0,get:function(){return d}});let d=a=>{}},88347,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ACTION_HMR_REFRESH:function(){return i},ACTION_NAVIGATE:function(){return e},ACTION_PREFETCH:function(){return h},ACTION_REFRESH:function(){return d},ACTION_RESTORE:function(){return f},ACTION_SERVER_ACTION:function(){return j},ACTION_SERVER_PATCH:function(){return g},PrefetchCacheEntryStatus:function(){return l},PrefetchKind:function(){return k}});let d="refresh",e="navigate",f="restore",g="server-patch",h="prefetch",i="hmr-refresh",j="server-action";var k=function(a){return a.AUTO="auto",a.FULL="full",a.TEMPORARY="temporary",a}({}),l=function(a){return a.fresh="fresh",a.reusable="reusable",a.expired="expired",a.stale="stale",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},37909,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ACTION_HEADER:function(){return e},FLIGHT_HEADERS:function(){return m},NEXT_ACTION_NOT_FOUND_HEADER:function(){return t},NEXT_DID_POSTPONE_HEADER:function(){return p},NEXT_HMR_REFRESH_HASH_COOKIE:function(){return j},NEXT_HMR_REFRESH_HEADER:function(){return i},NEXT_IS_PRERENDER_HEADER:function(){return s},NEXT_REWRITTEN_PATH_HEADER:function(){return q},NEXT_REWRITTEN_QUERY_HEADER:function(){return r},NEXT_ROUTER_PREFETCH_HEADER:function(){return g},NEXT_ROUTER_SEGMENT_PREFETCH_HEADER:function(){return h},NEXT_ROUTER_STALE_TIME_HEADER:function(){return o},NEXT_ROUTER_STATE_TREE_HEADER:function(){return f},NEXT_RSC_UNION_QUERY:function(){return n},NEXT_URL:function(){return k},RSC_CONTENT_TYPE_HEADER:function(){return l},RSC_HEADER:function(){return d}});let d="rsc",e="next-action",f="next-router-state-tree",g="next-router-prefetch",h="next-router-segment-prefetch",i="next-hmr-refresh",j="__next_hmr_refresh_hash__",k="next-url",l="text/x-component",m=[d,f,g,i,h],n="_rsc",o="x-nextjs-stale-time",p="x-nextjs-postponed",q="x-nextjs-rewritten-path",r="x-nextjs-rewritten-query",s="x-nextjs-prerender",t="x-nextjs-action-not-found";("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},67009,(a,b,c)=>{"use strict";function d(a){return null!==a&&"object"==typeof a&&"then"in a&&"function"==typeof a.then}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isThenable",{enumerable:!0,get:function(){return d}})},90841,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{dispatchAppRouterAction:function(){return g},useActionQueue:function(){return h}});let d=a.r(46058)._(a.r(72131)),e=a.r(67009),f=null;function g(a){if(null===f)throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0});f(a)}function h(a){let[b,c]=d.default.useState(a.state);return f=b=>a.dispatch(b,c),(0,e.isThenable)(b)?(0,d.use)(b):b}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},20611,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"callServer",{enumerable:!0,get:function(){return g}});let d=a.r(72131),e=a.r(88347),f=a.r(90841);async function g(a,b){return new Promise((c,g)=>{(0,d.startTransition)(()=>{(0,f.dispatchAppRouterAction)({type:e.ACTION_SERVER_ACTION,actionId:a,actionArgs:b,resolve:c,reject:g})})})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},1722,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"findSourceMapURL",{enumerable:!0,get:function(){return d}});let d=void 0;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},39118,(a,b,c)=>{"use strict";function d(a){return"("===a[0]&&a.endsWith(")")}function e(a){return a.startsWith("@")&&"@children"!==a}function f(a,b){if(a.includes(g)){let a=JSON.stringify(b);return"{}"!==a?g+"?"+a:g}return a}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DEFAULT_SEGMENT_KEY:function(){return h},PAGE_SEGMENT_KEY:function(){return g},addSearchParamsIfPageSegment:function(){return f},isGroupSegment:function(){return d},isParallelRouteSegment:function(){return e}});let g="__PAGE__",h="__DEFAULT__"},22129,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getFlightDataPartsFromPath:function(){return e},getNextFlightSegmentPath:function(){return f},normalizeFlightData:function(){return g},prepareFlightRouterStateForRequest:function(){return h}});let d=a.r(39118);function e(a){var b;let[c,d,e,f]=a.slice(-4),g=a.slice(0,-4);return{pathToSegment:g.slice(0,-1),segmentPath:g,segment:null!=(b=g[g.length-1])?b:"",tree:c,seedData:d,head:e,isHeadPartial:f,isRootRender:4===a.length}}function f(a){return a.slice(2)}function g(a){return"string"==typeof a?a:a.map(a=>e(a))}function h(a,b){return b?encodeURIComponent(JSON.stringify(a)):encodeURIComponent(JSON.stringify(function a(b){var c,e;let[f,g,h,i,j,k]=b,l="string"==typeof(c=f)&&c.startsWith(d.PAGE_SEGMENT_KEY+"?")?d.PAGE_SEGMENT_KEY:c,m={};for(let[b,c]of Object.entries(g))m[b]=a(c);let n=[l,m,null,(e=i)&&"refresh"!==e?i:null];return void 0!==j&&(n[4]=j),void 0!==k&&(n[5]=k),n}(a)))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},91377,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getAppBuildId:function(){return f},setAppBuildId:function(){return e}});let d="";function e(a){d=a}function f(){return d}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},16661,(a,b,c)=>{"use strict";function d(a){let b=5381;for(let c=0;c<a.length;c++)b=(b<<5)+b+a.charCodeAt(c)|0;return b>>>0}function e(a){return d(a).toString(36).slice(0,5)}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{djb2Hash:function(){return d},hexHash:function(){return e}})},35604,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"computeCacheBustingSearchParam",{enumerable:!0,get:function(){return e}});let d=a.r(16661);function e(a,b,c,e){return(void 0===a||"0"===a)&&void 0===b&&void 0===c&&void 0===e?"":(0,d.hexHash)([a||"0",b||"0",c||"0",e||"0"].join(","))}},25744,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{setCacheBustingSearchParam:function(){return f},setCacheBustingSearchParamWithHash:function(){return g}});let d=a.r(35604),e=a.r(37909),f=(a,b)=>{g(a,(0,d.computeCacheBustingSearchParam)(b[e.NEXT_ROUTER_PREFETCH_HEADER],b[e.NEXT_ROUTER_SEGMENT_PREFETCH_HEADER],b[e.NEXT_ROUTER_STATE_TREE_HEADER],b[e.NEXT_URL]))},g=(a,b)=>{let c=a.search,d=(c.startsWith("?")?c.slice(1):c).split("&").filter(a=>a&&!a.startsWith(""+e.NEXT_RSC_UNION_QUERY+"="));b.length>0?d.push(e.NEXT_RSC_UNION_QUERY+"="+b):d.push(""+e.NEXT_RSC_UNION_QUERY),a.search=d.length?"?"+d.join("&"):""};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},58216,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ROOT_SEGMENT_CACHE_KEY:function(){return f},ROOT_SEGMENT_REQUEST_KEY:function(){return e},appendSegmentCacheKeyPart:function(){return j},appendSegmentRequestKeyPart:function(){return h},convertSegmentPathToStaticExportFilename:function(){return m},createSegmentCacheKeyPart:function(){return i},createSegmentRequestKeyPart:function(){return g}});let d=a.r(39118),e="",f="";function g(a){if("string"==typeof a)return a.startsWith(d.PAGE_SEGMENT_KEY)?d.PAGE_SEGMENT_KEY:"/_not-found"===a?"_not-found":l(a);let b=a[0],c=a[2];return"$"+c+"$"+l(b)}function h(a,b,c){return a+"/"+("children"===b?c:"@"+l(b)+"/"+c)}function i(a,b){return"string"==typeof b?a:a+"$"+l(b[1])}function j(a,b,c){return a+"/"+("children"===b?c:"@"+l(b)+"/"+c)}let k=/^[a-zA-Z0-9\-_@]+$/;function l(a){return k.test(a)?a:"!"+btoa(a).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}function m(a){return"__next"+a.replace(/\//g,".")+".txt"}},23187,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{doesStaticSegmentAppearInURL:function(){return j},getCacheKeyForDynamicParam:function(){return k},getParamValueFromCacheKey:function(){return m},getRenderedPathname:function(){return h},getRenderedSearch:function(){return g},parseDynamicParamFromURLPart:function(){return i},urlToUrlWithoutFlightMarker:function(){return l}});let d=a.r(39118),e=a.r(58216),f=a.r(37909);function g(a){let b=a.headers.get(f.NEXT_REWRITTEN_QUERY_HEADER);return null!==b?""===b?"":"?"+b:l(new URL(a.url)).search}function h(a){let b=a.headers.get(f.NEXT_REWRITTEN_PATH_HEADER);return null!=b?b:l(new URL(a.url)).pathname}function i(a,b,c){switch(a){case"c":case"ci":return c<b.length?b.slice(c).map(a=>encodeURIComponent(a)):[];case"oc":return c<b.length?b.slice(c).map(a=>encodeURIComponent(a)):null;case"d":case"di":if(c>=b.length)return"";return encodeURIComponent(b[c]);default:return""}}function j(a){return!(a===e.ROOT_SEGMENT_REQUEST_KEY||a.startsWith(d.PAGE_SEGMENT_KEY)||"("===a[0]&&a.endsWith(")"))&&a!==d.DEFAULT_SEGMENT_KEY&&"/_not-found"!==a}function k(a,b){return"string"==typeof a?(0,d.addSearchParamsIfPageSegment)(a,Object.fromEntries(new URLSearchParams(b))):null===a?"":a.join("/")}function l(a){let b=new URL(a);return b.searchParams.delete(f.NEXT_RSC_UNION_QUERY),b}function m(a,b){return"c"===b||"oc"===b?a.split("/"):a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},20873,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{createFetch:function(){return q},createFromNextReadableStream:function(){return r},fetchServerResponse:function(){return p}});let d=a.r(38783),e=a.r(37909),f=a.r(20611),g=a.r(1722),h=a.r(88347),i=a.r(22129),j=a.r(91377),k=a.r(25744),l=a.r(23187),m=d.createFromReadableStream;function n(a){return{flightData:(0,l.urlToUrlWithoutFlightMarker)(new URL(a,location.origin)).toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}let o=new AbortController;async function p(a,b){let{flightRouterState:c,nextUrl:d,prefetchKind:f}=b,g={[e.RSC_HEADER]:"1",[e.NEXT_ROUTER_STATE_TREE_HEADER]:(0,i.prepareFlightRouterStateForRequest)(c,b.isHmrRefresh)};f===h.PrefetchKind.AUTO&&(g[e.NEXT_ROUTER_PREFETCH_HEADER]="1"),d&&(g[e.NEXT_URL]=d);try{var k;let b=f?f===h.PrefetchKind.TEMPORARY?"high":"low":"auto",c=await q(a,g,b,o.signal),d=(0,l.urlToUrlWithoutFlightMarker)(new URL(c.url)),m=c.redirected?d:void 0,p=c.headers.get("content-type")||"",s=!!(null==(k=c.headers.get("vary"))?void 0:k.includes(e.NEXT_URL)),t=!!c.headers.get(e.NEXT_DID_POSTPONE_HEADER),u=c.headers.get(e.NEXT_ROUTER_STALE_TIME_HEADER),v=null!==u?1e3*parseInt(u,10):-1;if(!p.startsWith(e.RSC_CONTENT_TYPE_HEADER)||!c.ok||!c.body)return a.hash&&(d.hash=a.hash),n(d.toString());let w=t?function(a){let b=a.getReader();return new ReadableStream({async pull(a){for(;;){let{done:c,value:d}=await b.read();if(!c){a.enqueue(d);continue}return}}})}(c.body):c.body,x=await r(w);if((0,j.getAppBuildId)()!==x.b)return n(c.url);return{flightData:(0,i.normalizeFlightData)(x.f),canonicalUrl:m,couldBeIntercepted:s,prerendered:x.S,postponed:t,staleTime:v}}catch(b){return o.signal.aborted||console.error("Failed to fetch RSC payload for "+a+". Falling back to browser navigation.",b),{flightData:a.toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}}async function q(a,b,c,d){let f=new URL(a);(0,k.setCacheBustingSearchParam)(f,b);let g=await fetch(f,{credentials:"same-origin",headers:b,priority:c||void 0,signal:d}),h=g.redirected,i=new URL(g.url,f);return i.searchParams.delete(e.NEXT_RSC_UNION_QUERY),{url:i.href,redirected:h,ok:g.ok,headers:g.headers,body:g.body,status:g.status}}function r(a){return m(a,{callServer:f.callServer,findSourceMapURL:g.findSourceMapURL})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},74685,(a,b,c)=>{"use strict";function d(a,b){return void 0===b&&(b=!0),a.pathname+a.search+(b?a.hash:"")}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"createHrefFromUrl",{enumerable:!0,get:function(){return d}}),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},70681,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"createRouterCacheKey",{enumerable:!0,get:function(){return e}});let d=a.r(39118);function e(a,b){return(void 0===b&&(b=!1),Array.isArray(a))?a[0]+"|"+a[1]+"|"+a[2]:b&&a.startsWith(d.PAGE_SEGMENT_KEY)?d.PAGE_SEGMENT_KEY:a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},77159,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,d.createRouterCacheKey)(i),k=c.parallelRoutes.get(h);if(!k)return;let l=b.parallelRoutes.get(h);if(l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l)),g)return void l.delete(j);let m=k.get(j),n=l.get(j);n&&m&&(n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes)},l.set(j,n)),a(n,m,(0,e.getNextFlightSegmentPath)(f)))}}});let d=a.r(70681),e=a.r(22129);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},30641,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"matchSegment",{enumerable:!0,get:function(){return d}});let d=(a,b)=>"string"==typeof a?"string"==typeof b&&a===b:"string"!=typeof b&&a[0]===b[0]&&a[1]===b[1];("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},10089,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function a(b,c,f,g,h,i,j){if(0===Object.keys(g[1]).length){c.head=i;return}for(let k in g[1]){let l,m=g[1][k],n=m[0],o=(0,d.createRouterCacheKey)(n),p=null!==h&&void 0!==h[2][k]?h[2][k]:null;if(f){let d=f.parallelRoutes.get(k);if(d){let f,g=(null==j?void 0:j.kind)==="auto"&&j.status===e.PrefetchCacheEntryStatus.reusable,h=new Map(d),l=h.get(o);f=null!==p?{lazyData:null,rsc:p[1],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),navigatedAt:b}:g&&l?{lazyData:l.lazyData,rsc:l.rsc,prefetchRsc:l.prefetchRsc,head:l.head,prefetchHead:l.prefetchHead,parallelRoutes:new Map(l.parallelRoutes),loading:l.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),loading:null,navigatedAt:b},h.set(o,f),a(b,f,l,m,p||null,i,j),c.parallelRoutes.set(k,h);continue}}if(null!==p){let a=p[1],c=p[3];l={lazyData:null,rsc:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:b};let q=c.parallelRoutes.get(k);q?q.set(o,l):c.parallelRoutes.set(k,new Map([[o,l]])),a(b,l,void 0,m,p,i,j)}}}});let d=a.r(70681),e=a.r(88347);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88770,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return e}});let d=a.r(70681);function e(a,b,c){for(let e in c[1]){let f=c[1][e][0],g=(0,d.createRouterCacheKey)(f),h=b.parallelRoutes.get(e);if(h){let b=new Map(h);b.delete(g),a.parallelRoutes.set(e,b)}}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},66013,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return j}});let d=a.r(88770),e=a.r(10089),f=a.r(70681),g=a.r(39118);function h(a,b,c,h,i,j){let{segmentPath:k,seedData:l,tree:m,head:n}=h,o=b,p=c;for(let b=0;b<k.length;b+=2){let c=k[b],h=k[b+1],q=b===k.length-2,r=(0,f.createRouterCacheKey)(h),s=p.parallelRoutes.get(c);if(!s)continue;let t=o.parallelRoutes.get(c);t&&t!==s||(t=new Map(s),o.parallelRoutes.set(c,t));let u=s.get(r),v=t.get(r);if(q){if(l&&(!v||!v.lazyData||v===u)){let b=l[0],c=l[1],f=l[3];v={lazyData:null,rsc:j||b!==g.PAGE_SEGMENT_KEY?c:null,prefetchRsc:null,head:null,prefetchHead:null,loading:f,parallelRoutes:j&&u?new Map(u.parallelRoutes):new Map,navigatedAt:a},u&&j&&(0,d.invalidateCacheByRouterState)(v,u,m),j&&(0,e.fillLazyItemsTillLeafWithHead)(a,v,u,m,l,n,i),t.set(r,v)}continue}v&&u&&(v===u&&(v={lazyData:v.lazyData,rsc:v.rsc,prefetchRsc:v.prefetchRsc,head:v.head,prefetchHead:v.prefetchHead,parallelRoutes:new Map(v.parallelRoutes),loading:v.loading},t.set(r,v)),o=v,p=u)}}function i(a,b,c,d,e){h(a,b,c,d,e,!0)}function j(a,b,c,d,e){h(a,b,c,d,e,!1)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},97768,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"applyFlightData",{enumerable:!0,get:function(){return f}});let d=a.r(10089),e=a.r(66013);function f(a,b,c,f,g){let{tree:h,seedData:i,head:j,isRootRender:k}=f;if(null===i)return!1;if(k){let e=i[1];c.loading=i[3],c.rsc=e,c.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(a,c,b,h,i,j,g)}else c.rsc=b.rsc,c.prefetchRsc=b.prefetchRsc,c.parallelRoutes=new Map(b.parallelRoutes),c.loading=b.loading,(0,e.fillCacheWithNewSubTreeData)(a,c,b,f,g);return!0}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},30719,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{addRefreshMarkerToActiveParallelSegments:function(){return function a(b,c){let[d,e,,g]=b;for(let h in d.includes(f.PAGE_SEGMENT_KEY)&&"refresh"!==g&&(b[2]=c,b[3]="refresh"),e)a(e[h],c)}},refreshInactiveParallelSegments:function(){return g}});let d=a.r(97768),e=a.r(20873),f=a.r(39118);async function g(a){let b=new Set;await h({...a,rootTree:a.updatedTree,fetchedSegments:b})}async function h(a){let{navigatedAt:b,state:c,updatedTree:f,updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k=f,canonicalUrl:l}=a,[,m,n,o]=f,p=[];if(n&&n!==l&&"refresh"===o&&!j.has(n)){j.add(n);let a=(0,e.fetchServerResponse)(new URL(n,location.origin),{flightRouterState:[k[0],k[1],k[2],"refetch"],nextUrl:i?c.nextUrl:null}).then(a=>{let{flightData:c}=a;if("string"!=typeof c)for(let a of c)(0,d.applyFlightData)(b,g,g,a)});p.push(a)}for(let a in m){let d=h({navigatedAt:b,state:c,updatedTree:m[a],updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k,canonicalUrl:l});p.push(d)}await Promise.all(p)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},51748,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function a(b,c,d,i){let j,[k,l,m,n,o]=c;if(1===b.length){let a=h(c,d);return(0,g.addRefreshMarkerToActiveParallelSegments)(a,i),a}let[p,q]=b;if(!(0,f.matchSegment)(p,k))return null;if(2===b.length)j=h(l[q],d);else if(null===(j=a((0,e.getNextFlightSegmentPath)(b),l[q],d,i)))return null;let r=[b[0],{...l,[q]:j},m,n];return o&&(r[4]=!0),(0,g.addRefreshMarkerToActiveParallelSegments)(r,i),r}}});let d=a.r(39118),e=a.r(22129),f=a.r(30641),g=a.r(30719);function h(a,b){let[c,e]=a,[g,i]=b;if(g===d.DEFAULT_SEGMENT_KEY&&c!==d.DEFAULT_SEGMENT_KEY)return a;if((0,f.matchSegment)(c,g)){let b={};for(let a in e)void 0!==i[a]?b[a]=h(e[a],i[a]):b[a]=e[a];for(let a in i)b[a]||(b[a]=i[a]);let d=[c,b];return a[2]&&(d[2]=a[2]),a[3]&&(d[3]=a[3]),a[4]&&(d[4]=a[4]),d}return b}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},9381,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"shouldHardNavigate",{enumerable:!0,get:function(){return function a(b,c){let[f,g]=c,[h,i]=b;return(0,e.matchSegment)(h,f)?!(b.length<=2)&&a((0,d.getNextFlightSegmentPath)(b),g[i]):!!Array.isArray(h)}}});let d=a.r(22129),e=a.r(30641);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},71094,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function a(b,c){let d=b[0],e=c[0];if(Array.isArray(d)&&Array.isArray(e)){if(d[0]!==e[0]||d[2]!==e[2])return!0}else if(d!==e)return!0;if(b[4])return!c[4];if(c[4])return!0;let f=Object.values(b[1])[0],g=Object.values(c[1])[0];return!f||!g||a(f,g)}}}),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},19924,(a,b,c)=>{"use strict";function d(a){return a.startsWith("/")?a:"/"+a}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"ensureLeadingSlash",{enumerable:!0,get:function(){return d}})},53808,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{normalizeAppPath:function(){return f},normalizeRscURL:function(){return g}});let d=a.r(19924),e=a.r(39118);function f(a){return(0,d.ensureLeadingSlash)(a.split("/").reduce((a,b,c,d)=>!b||(0,e.isGroupSegment)(b)||"@"===b[0]||("page"===b||"route"===b)&&c===d.length-1?a:a+"/"+b,""))}function g(a){return a.replace(/\.rsc($|\?)/,"$1")}},18099,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{INTERCEPTION_ROUTE_MARKERS:function(){return e},extractInterceptionRouteInformation:function(){return g},isInterceptionRouteAppPath:function(){return f}});let d=a.r(53808),e=["(..)(..)","(.)","(..)","(...)"];function f(a){return void 0!==a.split("/").find(a=>e.find(b=>a.startsWith(b)))}function g(a){let b,c,f;for(let d of a.split("/"))if(c=e.find(a=>d.startsWith(a))){[b,f]=a.split(c,2);break}if(!b||!c||!f)throw Object.defineProperty(Error("Invalid interception route: "+a+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(b=(0,d.normalizeAppPath)(b),c){case"(.)":f="/"===b?"/"+f:b+"/"+f;break;case"(..)":if("/"===b)throw Object.defineProperty(Error("Invalid interception route: "+a+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});f=b.split("/").slice(0,-1).concat(f).join("/");break;case"(...)":f="/"+f;break;case"(..)(..)":let g=b.split("/");if(g.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+a+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});f=g.slice(0,-2).concat(f).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:b,interceptedRoute:f}}},37042,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{computeChangedPath:function(){return j},extractPathFromFlightRouterState:function(){return i},getSelectedParams:function(){return function a(b,c){for(let d of(void 0===c&&(c={}),Object.values(b[1]))){let b=d[0],f=Array.isArray(b),g=f?b[1]:b;!g||g.startsWith(e.PAGE_SEGMENT_KEY)||(f&&("c"===b[2]||"oc"===b[2])?c[b[0]]=b[1].split("/"):f&&(c[b[0]]=b[1]),c=a(d,c))}return c}}});let d=a.r(18099),e=a.r(39118),f=a.r(30641),g=a=>"string"==typeof a?"children"===a?"":a:a[1];function h(a){return a.reduce((a,b)=>{let c;return""===(b="/"===(c=b)[0]?c.slice(1):c)||(0,e.isGroupSegment)(b)?a:a+"/"+b},"")||"/"}function i(a){var b;let c=Array.isArray(a[0])?a[0][1]:a[0];if(c===e.DEFAULT_SEGMENT_KEY||d.INTERCEPTION_ROUTE_MARKERS.some(a=>c.startsWith(a)))return;if(c.startsWith(e.PAGE_SEGMENT_KEY))return"";let f=[g(c)],j=null!=(b=a[1])?b:{},k=j.children?i(j.children):void 0;if(void 0!==k)f.push(k);else for(let[a,b]of Object.entries(j)){if("children"===a)continue;let c=i(b);void 0!==c&&f.push(c)}return h(f)}function j(a,b){let c=function a(b,c){let[e,h]=b,[j,k]=c,l=g(e),m=g(j);if(d.INTERCEPTION_ROUTE_MARKERS.some(a=>l.startsWith(a)||m.startsWith(a)))return"";if(!(0,f.matchSegment)(e,j)){var n;return null!=(n=i(c))?n:""}for(let b in h)if(k[b]){let c=a(h[b],k[b]);if(null!==c)return g(j)+"/"+c}return null}(a,b);return null==c||"/"===c?c:h(c.split("/"))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88632,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"handleMutable",{enumerable:!0,get:function(){return f}});let d=a.r(37042);function e(a){return void 0!==a}function f(a,b){var c,f;let g=null==(c=b.shouldScroll)||c,h=a.nextUrl;if(e(b.patchedTree)){let c=(0,d.computeChangedPath)(a.tree,b.patchedTree);c?h=c:h||(h=a.canonicalUrl)}return{canonicalUrl:e(b.canonicalUrl)?b.canonicalUrl===a.canonicalUrl?a.canonicalUrl:b.canonicalUrl:a.canonicalUrl,pushRef:{pendingPush:e(b.pendingPush)?b.pendingPush:a.pushRef.pendingPush,mpaNavigation:e(b.mpaNavigation)?b.mpaNavigation:a.pushRef.mpaNavigation,preserveCustomHistoryState:e(b.preserveCustomHistoryState)?b.preserveCustomHistoryState:a.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!g&&(!!e(null==b?void 0:b.scrollableSegments)||a.focusAndScrollRef.apply),onlyHashChange:b.onlyHashChange||!1,hashFragment:g?b.hashFragment&&""!==b.hashFragment?decodeURIComponent(b.hashFragment.slice(1)):a.focusAndScrollRef.hashFragment:null,segmentPaths:g?null!=(f=null==b?void 0:b.scrollableSegments)?f:a.focusAndScrollRef.segmentPaths:[]},cache:b.cache?b.cache:a.cache,prefetchCache:b.prefetchCache?b.prefetchCache:a.prefetchCache,tree:e(b.patchedTree)?b.patchedTree:a.tree,nextUrl:h}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},4662,(a,b,c)=>{"use strict";c._=function(a,b){if(!Object.prototype.hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}},91296,(a,b,c)=>{"use strict";var d=0;c._=function(a){return"__private_"+d+++"_"+a}},81083,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"PromiseQueue",{enumerable:!0,get:function(){return j}});let d=a.r(4662),e=a.r(91296);var f=e._("_maxConcurrency"),g=e._("_runningCount"),h=e._("_queue"),i=e._("_processNext");class j{enqueue(a){let b,c,e=new Promise((a,d)=>{b=a,c=d}),f=async()=>{try{d._(this,g)[g]++;let c=await a();b(c)}catch(a){c(a)}finally{d._(this,g)[g]--,d._(this,i)[i]()}};return d._(this,h)[h].push({promiseFn:e,task:f}),d._(this,i)[i](),e}bump(a){let b=d._(this,h)[h].findIndex(b=>b.promiseFn===a);if(b>-1){let a=d._(this,h)[h].splice(b,1)[0];d._(this,h)[h].unshift(a),d._(this,i)[i](!0)}}constructor(a=5){Object.defineProperty(this,i,{value:k}),Object.defineProperty(this,f,{writable:!0,value:void 0}),Object.defineProperty(this,g,{writable:!0,value:void 0}),Object.defineProperty(this,h,{writable:!0,value:void 0}),d._(this,f)[f]=a,d._(this,g)[g]=0,d._(this,h)[h]=[]}}function k(a){if(void 0===a&&(a=!1),(d._(this,g)[g]<d._(this,f)[f]||a)&&d._(this,h)[h].length>0){var b;null==(b=d._(this,h)[h].shift())||b.task()}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},98629,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DYNAMIC_STALETIME_MS:function(){return m},STATIC_STALETIME_MS:function(){return n},createSeededPrefetchCacheEntry:function(){return j},getOrCreatePrefetchCacheEntry:function(){return i},prunePrefetchCache:function(){return l}});let d=a.r(20873),e=a.r(88347),f=a.r(35399);function g(a,b,c){let d=a.pathname;return(b&&(d+=a.search),c)?""+c+"%"+d:d}function h(a,b,c){return g(a,b===e.PrefetchKind.FULL,c)}function i(a){let{url:b,nextUrl:c,tree:d,prefetchCache:f,kind:h,allowAliasing:i=!0}=a,j=function(a,b,c,d,f){for(let h of(void 0===b&&(b=e.PrefetchKind.TEMPORARY),[c,null])){let c=g(a,!0,h),i=g(a,!1,h),j=a.search?c:i,k=d.get(j);if(k&&f){if(k.url.pathname===a.pathname&&k.url.search!==a.search)return{...k,aliased:!0};return k}let l=d.get(i);if(f&&a.search&&b!==e.PrefetchKind.FULL&&l&&!l.key.includes("%"))return{...l,aliased:!0}}if(b!==e.PrefetchKind.FULL&&f){for(let b of d.values())if(b.url.pathname===a.pathname&&!b.key.includes("%"))return{...b,aliased:!0}}}(b,h,c,f,i);return j?(j.status=o(j),j.kind!==e.PrefetchKind.FULL&&h===e.PrefetchKind.FULL&&j.data.then(a=>{if(!(Array.isArray(a.flightData)&&a.flightData.some(a=>a.isRootRender&&null!==a.seedData)))return k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:null!=h?h:e.PrefetchKind.TEMPORARY})}),h&&j.kind===e.PrefetchKind.TEMPORARY&&(j.kind=h),j):k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:h||e.PrefetchKind.TEMPORARY})}function j(a){let{nextUrl:b,tree:c,prefetchCache:d,url:f,data:g,kind:i}=a,j=g.couldBeIntercepted?h(f,i,b):h(f,i),k={treeAtTimeOfPrefetch:c,data:Promise.resolve(g),kind:i,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:g.staleTime,key:j,status:e.PrefetchCacheEntryStatus.fresh,url:f};return d.set(j,k),k}function k(a){let{url:b,kind:c,tree:g,nextUrl:i,prefetchCache:j}=a,k=h(b,c),l=f.prefetchQueue.enqueue(()=>(0,d.fetchServerResponse)(b,{flightRouterState:g,nextUrl:i,prefetchKind:c}).then(a=>{let c;if(a.couldBeIntercepted&&(c=function(a){let{url:b,nextUrl:c,prefetchCache:d,existingCacheKey:e}=a,f=d.get(e);if(!f)return;let g=h(b,f.kind,c);return d.set(g,{...f,key:g}),d.delete(e),g}({url:b,existingCacheKey:k,nextUrl:i,prefetchCache:j})),a.prerendered){let b=j.get(null!=c?c:k);b&&(b.kind=e.PrefetchKind.FULL,-1!==a.staleTime&&(b.staleTime=a.staleTime))}return a})),m={treeAtTimeOfPrefetch:g,data:l,kind:c,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:k,status:e.PrefetchCacheEntryStatus.fresh,url:b};return j.set(k,m),m}function l(a){for(let[b,c]of a)o(c)===e.PrefetchCacheEntryStatus.expired&&a.delete(b)}let m=1e3*Number("0"),n=1e3*Number("300");function o(a){let{kind:b,prefetchTime:c,lastUsedTime:d}=a;return Date.now()<(null!=d?d:c)+m?d?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.fresh:b===e.PrefetchKind.AUTO&&Date.now()<c+n?e.PrefetchCacheEntryStatus.stale:b===e.PrefetchKind.FULL&&Date.now()<c+n?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.expired}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},35399,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{prefetchQueue:function(){return f},prefetchReducer:function(){return g}});let d=a.r(81083),e=a.r(98629),f=new d.PromiseQueue(5),g=function(a,b){(0,e.prunePrefetchCache)(a.prefetchCache);let{url:c}=b;return(0,e.getOrCreatePrefetchCacheEntry)({url:c,nextUrl:a.nextUrl,prefetchCache:a.prefetchCache,kind:b.kind,tree:a.tree,allowAliasing:!0}),a};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},33354,(a,b,c)=>{"use strict";c._=function(a){return a&&a.__esModule?a:{default:a}}},51842,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return d}});let d=/[\w-]+-Google|Google-[\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i},19269,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{HTML_LIMITED_BOT_UA_RE:function(){return d.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return f},getBotType:function(){return i},isBot:function(){return h}});let d=a.r(51842),e=/Googlebot(?!-)|Googlebot$/i,f=d.HTML_LIMITED_BOT_UA_RE.source;function g(a){return d.HTML_LIMITED_BOT_UA_RE.test(a)}function h(a){return e.test(a)||g(a)}function i(a){return e.test(a)?"dom":g(a)?"html":void 0}},72724,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"AppRouterAnnouncer",{enumerable:!0,get:function(){return g}});let d=a.r(72131),e=a.r(35112),f="next-route-announcer";function g(a){let{tree:b}=a,[c,g]=(0,d.useState)(null);(0,d.useEffect)(()=>(g(function(){var a;let b=document.getElementsByName(f)[0];if(null==b||null==(a=b.shadowRoot)?void 0:a.childNodes[0])return b.shadowRoot.childNodes[0];{let a=document.createElement(f);a.style.cssText="position:absolute";let b=document.createElement("div");return b.ariaLive="assertive",b.id="__next-route-announcer__",b.role="alert",b.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",a.attachShadow({mode:"open"}).appendChild(b),document.body.appendChild(a),b}}()),()=>{let a=document.getElementsByTagName(f)[0];(null==a?void 0:a.isConnected)&&document.body.removeChild(a)}),[]);let[h,i]=(0,d.useState)(""),j=(0,d.useRef)(void 0);return(0,d.useEffect)(()=>{let a="";if(document.title)a=document.title;else{let b=document.querySelector("h1");b&&(a=b.innerText||b.textContent||"")}void 0!==j.current&&j.current!==a&&i(a),j.current=a},[b]),c?(0,e.createPortal)(h,c):null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},68789,(a,b,c)=>{"use strict";function d(a){return Array.isArray(a)?a[1]:a}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getSegmentValue",{enumerable:!0,get:function(){return d}}),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},15128,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"RedirectStatusCode",{enumerable:!0,get:function(){return d}});var d=function(a){return a[a.SeeOther=303]="SeeOther",a[a.TemporaryRedirect=307]="TemporaryRedirect",a[a.PermanentRedirect=308]="PermanentRedirect",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},11026,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{REDIRECT_ERROR_CODE:function(){return e},RedirectType:function(){return f},isRedirectError:function(){return g}});let d=a.r(15128),e="NEXT_REDIRECT";var f=function(a){return a.push="push",a.replace="replace",a}({});function g(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let b=a.digest.split(";"),[c,f]=b,g=b.slice(2,-2).join(";"),h=Number(b.at(-2));return c===e&&("replace"===f||"push"===f)&&"string"==typeof g&&!isNaN(h)&&h in d.RedirectStatusCode}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},22099,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=a.r(15128),e=a.r(11026),f=a.r(20635).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},8535,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{HTTPAccessErrorStatus:function(){return d},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return f},getAccessFallbackErrorTypeByStatus:function(){return i},getAccessFallbackHTTPStatus:function(){return h},isHTTPAccessFallbackError:function(){return g}});let d={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},e=new Set(Object.values(d)),f="NEXT_HTTP_ERROR_FALLBACK";function g(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let[b,c]=a.digest.split(";");return b===f&&e.has(Number(c))}function h(a){return Number(a.digest.split(";")[1])}function i(a){switch(a){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},28102,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"notFound",{enumerable:!0,get:function(){return e}});let d=""+a.r(8535).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},64177,(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"forbidden",{enumerable:!0,get:function(){return d}}),a.r(8535).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88213,(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unauthorized",{enumerable:!0,get:function(){return d}}),a.r(8535).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},35710,(a,b,c)=>{"use strict";function d(a){return"object"==typeof a&&null!==a&&"digest"in a&&a.digest===e}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{isHangingPromiseRejectionError:function(){return d},makeDevtoolsIOAwarePromise:function(){return j},makeHangingPromise:function(){return h}});let e="HANGING_PROMISE_REJECTION";class f extends Error{constructor(a,b){super(`During prerendering, ${b} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${b} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route "${a}".`),this.route=a,this.expression=b,this.digest=e}}let g=new WeakMap;function h(a,b,c){if(a.aborted)return Promise.reject(new f(b,c));{let d=new Promise((d,e)=>{let h=e.bind(null,new f(b,c)),i=g.get(a);if(i)i.push(h);else{let b=[h];g.set(a,b),a.addEventListener("abort",()=>{for(let a=0;a<b.length;a++)b[a]()},{once:!0})}});return d.catch(i),d}}function i(){}function j(a){return new Promise(b=>{setTimeout(()=>{b(a)},0)})}},31101,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isPostpone",{enumerable:!0,get:function(){return e}});let d=Symbol.for("react.postpone");function e(a){return"object"==typeof a&&null!==a&&a.$$typeof===d}},41997,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{BailoutToCSRError:function(){return e},isBailoutToCSRError:function(){return f}});let d="BAILOUT_TO_CLIENT_SIDE_RENDERING";class e extends Error{constructor(a){super("Bail out to client-side rendering: "+a),this.reason=a,this.digest=d}}function f(a){return"object"==typeof a&&null!==a&&"digest"in a&&a.digest===d}},77747,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isNextRouterError",{enumerable:!0,get:function(){return f}});let d=a.r(8535),e=a.r(11026);function f(a){return(0,e.isRedirectError)(a)||(0,d.isHTTPAccessFallbackError)(a)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},3326,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DynamicServerError:function(){return e},isDynamicServerError:function(){return f}});let d="DYNAMIC_SERVER_USAGE";class e extends Error{constructor(a){super("Dynamic server usage: "+a),this.description=a,this.digest=d}}function f(a){return"object"==typeof a&&null!==a&&"digest"in a&&"string"==typeof a.digest&&a.digest===d}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},99392,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{StaticGenBailoutError:function(){return e},isStaticGenBailoutError:function(){return f}});let d="NEXT_STATIC_GEN_BAILOUT";class e extends Error{constructor(...a){super(...a),this.code=d}}function f(a){return"object"==typeof a&&null!==a&&"code"in a&&a.code===d}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},83590,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{METADATA_BOUNDARY_NAME:function(){return d},OUTLET_BOUNDARY_NAME:function(){return f},ROOT_LAYOUT_BOUNDARY_NAME:function(){return g},VIEWPORT_BOUNDARY_NAME:function(){return e}});let d="__next_metadata_boundary__",e="__next_viewport_boundary__",f="__next_outlet_boundary__",g="__next_root_layout_boundary__"},76383,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{atLeastOneTask:function(){return f},scheduleImmediate:function(){return e},scheduleOnNextTick:function(){return d},waitAtLeastOneReactRenderTask:function(){return g}});let d=a=>{Promise.resolve().then(()=>{process.nextTick(a)})},e=a=>{setImmediate(a)};function f(){return new Promise(a=>e(a))}function g(){return new Promise(a=>setImmediate(a))}},88644,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"InvariantError",{enumerable:!0,get:function(){return d}});class d extends Error{constructor(a,b){super("Invariant: "+(a.endsWith(".")?a:a+".")+" This is a bug in Next.js.",b),this.name="InvariantError"}}},54110,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{Postpone:function(){return A},PreludeState:function(){return V},abortAndThrowOnSynchronousRequestDataAccess:function(){return x},abortOnSynchronousPlatformIOAccess:function(){return v},accessedDynamicData:function(){return I},annotateDynamicAccess:function(){return N},consumeDynamicAccess:function(){return J},createDynamicTrackingState:function(){return o},createDynamicValidationState:function(){return p},createHangingInputAbortSignal:function(){return M},createRenderInBrowserAbortSignal:function(){return L},delayUntilRuntimeStage:function(){return Y},formatDynamicAPIAccesses:function(){return K},getFirstDynamicReason:function(){return q},isDynamicPostpone:function(){return D},isPrerenderInterruptedError:function(){return H},logDisallowedDynamicError:function(){return W},markCurrentScopeAsDynamic:function(){return r},postponeWithTracking:function(){return B},throwIfDisallowedDynamic:function(){return X},throwToInterruptStaticGeneration:function(){return s},trackAllowedDynamicAccess:function(){return U},trackDynamicDataInDynamicRender:function(){return t},trackSynchronousPlatformIOAccessInDev:function(){return w},trackSynchronousRequestDataAccessInDev:function(){return z},useDynamicRouteParams:function(){return O},warnOnSyncDynamicError:function(){return y}});let d=function(a){return a&&a.__esModule?a:{default:a}}(a.r(72131)),e=a.r(3326),f=a.r(99392),g=a.r(32319),h=a.r(56704),i=a.r(35710),j=a.r(83590),k=a.r(76383),l=a.r(41997),m=a.r(88644),n="function"==typeof d.default.unstable_postpone;function o(a){return{isDebugDynamicAccesses:a,dynamicAccesses:[],syncDynamicErrorWithStack:null}}function p(){return{hasSuspenseAboveBody:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasAllowedDynamic:!1,dynamicErrors:[]}}function q(a){var b;return null==(b=a.dynamicAccesses[0])?void 0:b.expression}function r(a,b,c){if(b)switch(b.type){case"cache":case"unstable-cache":case"private-cache":return}if(!a.forceDynamic&&!a.forceStatic){if(a.dynamicShouldError)throw Object.defineProperty(new f.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${c}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender-ppr":return B(a.route,c,b.dynamicTracking);case"prerender-legacy":b.revalidate=0;let d=Object.defineProperty(new e.DynamicServerError(`Route ${a.route} couldn't be rendered statically because it used ${c}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw a.dynamicUsageDescription=c,a.dynamicUsageStack=d.stack,d}}}function s(a,b,c){let d=Object.defineProperty(new e.DynamicServerError(`Route ${b.route} couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw c.revalidate=0,b.dynamicUsageDescription=a,b.dynamicUsageStack=d.stack,d}function t(a){switch(a.type){case"cache":case"unstable-cache":case"private-cache":return}}function u(a,b,c){let d=G(`Route ${a} needs to bail out of prerendering at this point because it used ${b}.`);c.controller.abort(d);let e=c.dynamicTracking;e&&e.dynamicAccesses.push({stack:e.isDebugDynamicAccesses?Error().stack:void 0,expression:b})}function v(a,b,c,d){let e=d.dynamicTracking;u(a,b,d),e&&null===e.syncDynamicErrorWithStack&&(e.syncDynamicErrorWithStack=c)}function w(a){a.prerenderPhase=!1}function x(a,b,c,d){if(!1===d.controller.signal.aborted){u(a,b,d);let e=d.dynamicTracking;e&&null===e.syncDynamicErrorWithStack&&(e.syncDynamicErrorWithStack=c)}throw G(`Route ${a} needs to bail out of prerendering at this point because it used ${b}.`)}function y(a){a.syncDynamicErrorWithStack&&console.error(a.syncDynamicErrorWithStack)}let z=w;function A({reason:a,route:b}){let c=g.workUnitAsyncStorage.getStore();B(b,a,c&&"prerender-ppr"===c.type?c.dynamicTracking:null)}function B(a,b,c){(function(){if(!n)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:b}),d.default.unstable_postpone(C(a,b))}function C(a,b){return`Route ${a} needs to bail out of prerendering at this point because it used ${b}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function D(a){return"object"==typeof a&&null!==a&&"string"==typeof a.message&&E(a.message)}function E(a){return a.includes("needs to bail out of prerendering at this point because it used")&&a.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===E(C("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let F="NEXT_PRERENDER_INTERRUPTED";function G(a){let b=Object.defineProperty(Error(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return b.digest=F,b}function H(a){return"object"==typeof a&&null!==a&&a.digest===F&&"name"in a&&"message"in a&&a instanceof Error}function I(a){return a.length>0}function J(a,b){return a.dynamicAccesses.push(...b.dynamicAccesses),a.dynamicAccesses}function K(a){return a.filter(a=>"string"==typeof a.stack&&a.stack.length>0).map(({expression:a,stack:b})=>(b=b.split("\n").slice(4).filter(a=>!(a.includes("node_modules/next/")||a.includes(" (<anonymous>)")||a.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${a}:
${b}`))}function L(){let a=new AbortController;return a.abort(Object.defineProperty(new l.BailoutToCSRError("Render in Browser"),"__NEXT_ERROR_CODE",{value:"E721",enumerable:!1,configurable:!0})),a.signal}function M(a){switch(a.type){case"prerender":case"prerender-runtime":let b=new AbortController;if(a.cacheSignal)a.cacheSignal.inputReady().then(()=>{b.abort()});else{let c=(0,g.getRuntimeStagePromise)(a);c?c.then(()=>(0,k.scheduleOnNextTick)(()=>b.abort())):(0,k.scheduleOnNextTick)(()=>b.abort())}return b.signal;case"prerender-client":case"prerender-ppr":case"prerender-legacy":case"request":case"cache":case"private-cache":case"unstable-cache":return}}function N(a,b){let c=b.dynamicTracking;c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:a})}function O(a){let b=h.workAsyncStorage.getStore(),c=g.workUnitAsyncStorage.getStore();if(b&&c)switch(c.type){case"prerender-client":case"prerender":{let e=c.fallbackRouteParams;e&&e.size>0&&d.default.use((0,i.makeHangingPromise)(c.renderSignal,b.route,a));break}case"prerender-ppr":{let d=c.fallbackRouteParams;if(d&&d.size>0)return B(b.route,a,c.dynamicTracking);break}case"prerender-runtime":throw Object.defineProperty(new m.InvariantError(`\`${a}\` was called during a runtime prerender. Next.js should be preventing ${a} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E771",enumerable:!1,configurable:!0});case"cache":case"private-cache":throw Object.defineProperty(new m.InvariantError(`\`${a}\` was called inside a cache scope. Next.js should be preventing ${a} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E745",enumerable:!1,configurable:!0})}}let P=/\n\s+at Suspense \(<anonymous>\)/,Q=RegExp(`\\n\\s+at Suspense \\(<anonymous>\\)(?:(?!\\n\\s+at (?:body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6) \\(<anonymous>\\))[\\s\\S])*?\\n\\s+at ${j.ROOT_LAYOUT_BOUNDARY_NAME} \\([^\\n]*\\)`),R=RegExp(`\\n\\s+at ${j.METADATA_BOUNDARY_NAME}[\\n\\s]`),S=RegExp(`\\n\\s+at ${j.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),T=RegExp(`\\n\\s+at ${j.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function U(a,b,c,d){if(!T.test(b)){if(R.test(b)){c.hasDynamicMetadata=!0;return}if(S.test(b)){c.hasDynamicViewport=!0;return}if(Q.test(b)){c.hasAllowedDynamic=!0,c.hasSuspenseAboveBody=!0;return}else if(P.test(b)){c.hasAllowedDynamic=!0;return}else{if(d.syncDynamicErrorWithStack)return void c.dynamicErrors.push(d.syncDynamicErrorWithStack);let e=function(a,b){let c=Object.defineProperty(Error(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return c.stack=c.name+": "+a+b,c}(`Route "${a.route}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`,b);return void c.dynamicErrors.push(e)}}}var V=function(a){return a[a.Full=0]="Full",a[a.Empty=1]="Empty",a[a.Errored=2]="Errored",a}({});function W(a,b){console.error(b),a.dev||(a.hasReadableErrorStacks?console.error(`To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \`next dev\`, then open "${a.route}" in your browser to investigate the error.`):console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:
  - Start the app in development mode by running \`next dev\`, then open "${a.route}" in your browser to investigate the error.
  - Rerun the production build with \`next build --debug-prerender\` to generate better stack traces.`))}function X(a,b,c,d){if(0!==b){if(c.hasSuspenseAboveBody)return;if(d.syncDynamicErrorWithStack)throw W(a,d.syncDynamicErrorWithStack),new f.StaticGenBailoutError;let e=c.dynamicErrors;if(e.length>0){for(let b=0;b<e.length;b++)W(a,e[b]);throw new f.StaticGenBailoutError}if(c.hasDynamicViewport)throw console.error(`Route "${a.route}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or uncached external data (\`fetch(...)\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`),new f.StaticGenBailoutError;if(1===b)throw console.error(`Route "${a.route}" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`),new f.StaticGenBailoutError}else if(!1===c.hasAllowedDynamic&&c.hasDynamicMetadata)throw console.error(`Route "${a.route}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or uncached external data (\`fetch(...)\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`),new f.StaticGenBailoutError}function Y(a,b){return a.runtimeStagePromise?a.runtimeStagePromise.then(()=>b):b}},44753,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=a.r(35710),e=a.r(31101),f=a.r(41997),g=a.r(77747),h=a.r(54110),i=a.r(3326);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},47083,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=a.r(44753).unstable_rethrow;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},21066,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_isUnrecognizedActionError:function(){return l},unstable_rethrow:function(){return i.unstable_rethrow}});let d=a.r(22099),e=a.r(11026),f=a.r(28102),g=a.r(64177),h=a.r(88213),i=a.r(47083);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}function l(){throw Object.defineProperty(Error("`unstable_isUnrecognizedActionError` can only be used on the client."),"__NEXT_ERROR_CODE",{value:"E776",enumerable:!1,configurable:!0})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},57997,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{UnrecognizedActionError:function(){return d},unstable_isUnrecognizedActionError:function(){return e}});class d extends Error{constructor(...a){super(...a),this.name="UnrecognizedActionError"}}function e(a){return!!(a&&"object"==typeof a&&a instanceof d)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},15204,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"bailoutToClientRendering",{enumerable:!0,get:function(){return g}});let d=a.r(41997),e=a.r(56704),f=a.r(32319);function g(a){let b=e.workAsyncStorage.getStore();if(null==b?void 0:b.forceStatic)return;let c=f.workUnitAsyncStorage.getStore();if(c)switch(c.type){case"prerender":case"prerender-runtime":case"prerender-client":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(new d.BailoutToCSRError(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},74137,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ReadonlyURLSearchParams:function(){return i.ReadonlyURLSearchParams},RedirectType:function(){return i.RedirectType},ServerInsertedHTMLContext:function(){return j.ServerInsertedHTMLContext},forbidden:function(){return i.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return i.unauthorized},unstable_isUnrecognizedActionError:function(){return k.unstable_isUnrecognizedActionError},unstable_rethrow:function(){return i.unstable_rethrow},useParams:function(){return p},usePathname:function(){return n},useRouter:function(){return o},useSearchParams:function(){return m},useSelectedLayoutSegment:function(){return r},useSelectedLayoutSegments:function(){return q},useServerInsertedHTML:function(){return j.useServerInsertedHTML}});let d=a.r(72131),e=a.r(9270),f=a.r(36313),g=a.r(68789),h=a.r(39118),i=a.r(21066),j=a.r(18341),k=a.r(57997),l=a.r(54110).useDynamicRouteParams;function m(){let b=(0,d.useContext)(f.SearchParamsContext),c=(0,d.useMemo)(()=>b?new i.ReadonlyURLSearchParams(b):null,[b]);{let{bailoutToClientRendering:b}=a.r(15204);b("useSearchParams()")}return c}function n(){return null==l||l("usePathname()"),(0,d.useContext)(f.PathnameContext)}function o(){let a=(0,d.useContext)(e.AppRouterContext);if(null===a)throw Object.defineProperty(Error("invariant expected app router to be mounted"),"__NEXT_ERROR_CODE",{value:"E238",enumerable:!1,configurable:!0});return a}function p(){return null==l||l("useParams()"),(0,d.useContext)(f.PathParamsContext)}function q(a){void 0===a&&(a="children"),null==l||l("useSelectedLayoutSegments()");let b=(0,d.useContext)(e.LayoutRouterContext);return b?function a(b,c,d,e){let f;if(void 0===d&&(d=!0),void 0===e&&(e=[]),d)f=b[1][c];else{var i;let a=b[1];f=null!=(i=a.children)?i:Object.values(a)[0]}if(!f)return e;let j=f[0],k=(0,g.getSegmentValue)(j);return!k||k.startsWith(h.PAGE_SEGMENT_KEY)?e:(e.push(k),a(f,c,!1,e))}(b.parentTree,a):null}function r(a){void 0===a&&(a="children"),null==l||l("useSelectedLayoutSegment()");let b=q(a);if(!b||0===b.length)return null;let c="children"===a?b[0]:b[b.length-1];return c===h.DEFAULT_SEGMENT_KEY?null:c}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},57774,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{RedirectBoundary:function(){return l},RedirectErrorBoundary:function(){return k}});let d=a.r(46058),e=a.r(87924),f=d._(a.r(72131)),g=a.r(74137),h=a.r(22099),i=a.r(11026);function j(a){let{redirect:b,reset:c,redirectType:d}=a,e=(0,g.useRouter)();return(0,f.useEffect)(()=>{f.default.startTransition(()=>{d===i.RedirectType.push?e.push(b,{}):e.replace(b,{}),c()})},[b,d,c,e]),null}class k extends f.default.Component{static getDerivedStateFromError(a){if((0,i.isRedirectError)(a))return{redirect:(0,h.getURLFromRedirectError)(a),redirectType:(0,h.getRedirectTypeFromError)(a)};throw a}render(){let{redirect:a,redirectType:b}=this.state;return null!==a&&null!==b?(0,e.jsx)(j,{redirect:a,redirectType:b,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(a){super(a),this.state={redirect:null,redirectType:null}}}function l(a){let{children:b}=a,c=(0,g.useRouter)();return(0,e.jsx)(k,{router:c,children:b})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},99138,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"findHeadInCache",{enumerable:!0,get:function(){return f}});let d=a.r(39118),e=a.r(70681);function f(a,b){return function a(b,c,f,g){if(0===Object.keys(c).length)return[b,f,g];let h=Object.keys(c).filter(a=>"children"!==a);for(let g of("children"in c&&h.unshift("children"),h)){let[h,i]=c[g];if(h===d.DEFAULT_SEGMENT_KEY)continue;let j=b.parallelRoutes.get(g);if(!j)continue;let k=(0,e.createRouterCacheKey)(h),l=(0,e.createRouterCacheKey)(h,!0),m=j.get(k);if(!m)continue;let n=a(m,i,f+"/"+k,f+"/"+l);if(n)return n}return null}(a,b,"","")}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},72471,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unresolvedThenable",{enumerable:!0,get:function(){return d}});let d={then:()=>{}};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},11679,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"pathHasPrefix",{enumerable:!0,get:function(){return e}});let d=a.r(8868);function e(a,b){if("string"!=typeof a)return!1;let{pathname:c}=(0,d.parsePath)(a);return c===b||c.startsWith(b+"/")}},78897,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"hasBasePath",{enumerable:!0,get:function(){return e}});let d=a.r(11679);function e(a){return(0,d.pathHasPrefix)(a,"")}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},16749,(a,b,c)=>{"use strict";function d(a){return a}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"removeBasePath",{enumerable:!0,get:function(){return d}}),a.r(78897),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},18274,(a,b,c)=>{"use strict";function d(a){return!1}function e(){}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{handleHardNavError:function(){return d},useNavFailureHandler:function(){return e}}),a.r(72131),a.r(74685),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88807,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{GracefulDegradeBoundary:function(){return f},default:function(){return g}});let d=a.r(87924),e=a.r(72131);class f extends e.Component{static getDerivedStateFromError(a){return{hasError:!0}}componentDidMount(){let a=this.htmlRef.current;this.state.hasError&&a&&Object.entries(this.htmlAttributes).forEach(b=>{let[c,d]=b;a.setAttribute(c,d)})}render(){let{hasError:a}=this.state;return a?(0,d.jsx)("html",{ref:this.htmlRef,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:this.rootHtml}}):this.props.children}constructor(a){super(a),this.state={hasError:!1},this.rootHtml="",this.htmlAttributes={},this.htmlRef=(0,e.createRef)()}}let g=f;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},94722,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"useUntrackedPathname",{enumerable:!0,get:function(){return f}});let d=a.r(72131),e=a.r(36313);function f(){return!function(){{let{workUnitAsyncStorage:b}=a.r(32319),c=b.getStore();if(!c)return!1;switch(c.type){case"prerender":case"prerender-client":case"prerender-ppr":let d=c.fallbackRouteParams;return!!d&&d.size>0}return!1}}()?(0,d.useContext)(e.PathnameContext):null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},80981,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ErrorBoundary:function(){return k},ErrorBoundaryHandler:function(){return j}});let d=a.r(33354),e=a.r(87924),f=d._(a.r(72131)),g=a.r(94722),h=a.r(77747);a.r(18274);let i=a.r(51234);a.r(19269);class j extends f.default.Component{static getDerivedStateFromError(a){if((0,h.isNextRouterError)(a))throw a;return{error:a}}static getDerivedStateFromProps(a,b){let{error:c}=b;return a.pathname!==b.previousPathname&&b.error?{error:null,previousPathname:a.pathname}:{error:b.error,previousPathname:a.pathname}}render(){return this.state.error&&1?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(i.HandleISRError,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,e.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(a){super(a),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function k(a){let{errorComponent:b,errorStyles:c,errorScripts:d,children:f}=a,h=(0,g.useUntrackedPathname)();return b?(0,e.jsx)(j,{pathname:h,errorComponent:b,errorStyles:c,errorScripts:d,children:f}):(0,e.jsx)(e.Fragment,{children:f})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},86293,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return f}}),a.r(33354);let d=a.r(87924);a.r(72131),a.r(88807);let e=a.r(80981);function f(a){let{children:b,errorComponent:c,errorStyles:f,errorScripts:g}=a;return(0,d.jsx)(e.ErrorBoundary,{errorComponent:c,errorStyles:f,errorScripts:g,children:b})}a.r(19269),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},50822,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{MetadataBoundary:function(){return f},OutletBoundary:function(){return h},RootLayoutBoundary:function(){return i},ViewportBoundary:function(){return g}});let d=a.r(83590),e={[d.METADATA_BOUNDARY_NAME]:function({children:a}){return a},[d.VIEWPORT_BOUNDARY_NAME]:function({children:a}){return a},[d.OUTLET_BOUNDARY_NAME]:function({children:a}){return a},[d.ROOT_LAYOUT_BOUNDARY_NAME]:function({children:a}){return a}},f=e[d.METADATA_BOUNDARY_NAME.slice(0)],g=e[d.VIEWPORT_BOUNDARY_NAME.slice(0)],h=e[d.OUTLET_BOUNDARY_NAME.slice(0)],i=e[d.ROOT_LAYOUT_BOUNDARY_NAME.slice(0)]},451,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{createEmptyCacheNode:function(){return G},createPrefetchURL:function(){return E},default:function(){return K},isExternalURL:function(){return D}});let d=a.r(33354),e=a.r(46058),f=a.r(87924),g=e._(a.r(72131)),h=a.r(9270),i=a.r(88347),j=a.r(74685),k=a.r(36313),l=a.r(90841),m=a.r(19269),n=a.r(38243),o=a.r(72724),p=a.r(57774),q=a.r(99138),r=a.r(72471),s=a.r(16749),t=a.r(78897),u=a.r(37042),v=a.r(18274),w=a.r(44637),x=a.r(22099),y=a.r(11026);a.r(16441);let z=d._(a.r(86293)),A=d._(a.r(40622)),B=a.r(50822),C={};function D(a){return a.origin!==window.location.origin}function E(a){let b;if((0,m.isBot)(window.navigator.userAgent))return null;try{b=new URL((0,n.addBasePath)(a),window.location.href)}catch(b){throw Object.defineProperty(Error("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return D(b)?null:b}function F(a){let{appRouterState:b}=a;return(0,g.useInsertionEffect)(()=>{let{tree:a,pushRef:c,canonicalUrl:d}=b,e={...c.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:a};c.pendingPush&&(0,j.createHrefFromUrl)(new URL(window.location.href))!==d?(c.pendingPush=!1,window.history.pushState(e,"",d)):window.history.replaceState(e,"",d)},[b]),(0,g.useEffect)(()=>{},[b.nextUrl,b.tree]),null}function G(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function H(a){null==a&&(a={});let b=window.history.state,c=null==b?void 0:b.__NA;c&&(a.__NA=c);let d=null==b?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;return d&&(a.__PRIVATE_NEXTJS_INTERNALS_TREE=d),a}function I(a){let{headCacheNode:b}=a,c=null!==b?b.head:null,d=null!==b?b.prefetchHead:null,e=null!==d?d:c;return(0,g.useDeferredValue)(c,e)}function J(a){let b,{actionQueue:c,assetPrefix:d,globalError:e}=a,j=(0,l.useActionQueue)(c),{canonicalUrl:m}=j,{searchParams:n,pathname:v}=(0,g.useMemo)(()=>{let a=new URL(m,"http://n");return{searchParams:a.searchParams,pathname:(0,t.hasBasePath)(a.pathname)?(0,s.removeBasePath)(a.pathname):a.pathname}},[m]);(0,g.useEffect)(()=>{function a(a){var b;a.persisted&&(null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(C.pendingMpaPath=void 0,(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",a),()=>{window.removeEventListener("pageshow",a)}},[]),(0,g.useEffect)(()=>{function a(a){let b="reason"in a?a.reason:a.error;if((0,y.isRedirectError)(b)){a.preventDefault();let c=(0,x.getURLFromRedirectError)(b);(0,x.getRedirectTypeFromError)(b)===y.RedirectType.push?w.publicAppRouterInstance.push(c,{}):w.publicAppRouterInstance.replace(c,{})}}return window.addEventListener("error",a),window.addEventListener("unhandledrejection",a),()=>{window.removeEventListener("error",a),window.removeEventListener("unhandledrejection",a)}},[]);let{pushRef:A}=j;if(A.mpaNavigation){if(C.pendingMpaPath!==m){let a=window.location;A.pendingPush?a.assign(m):a.replace(m),C.pendingMpaPath=m}throw r.unresolvedThenable}(0,g.useEffect)(()=>{let a=window.history.pushState.bind(window.history),b=window.history.replaceState.bind(window.history),c=a=>{var b;let c=window.location.href,d=null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,g.startTransition)(()=>{(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=a?a:c,c),tree:d})})};window.history.pushState=function(b,d,e){return(null==b?void 0:b.__NA)||(null==b?void 0:b._N)||(b=H(b),e&&c(e)),a(b,d,e)},window.history.replaceState=function(a,d,e){return(null==a?void 0:a.__NA)||(null==a?void 0:a._N)||(a=H(a),e&&c(e)),b(a,d,e)};let d=a=>{if(a.state){if(!a.state.__NA)return void window.location.reload();(0,g.startTransition)(()=>{(0,w.dispatchTraverseAction)(window.location.href,a.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",d),()=>{window.history.pushState=a,window.history.replaceState=b,window.removeEventListener("popstate",d)}},[]);let{cache:D,tree:E,nextUrl:G,focusAndScrollRef:J}=j,K=(0,g.useMemo)(()=>(0,q.findHeadInCache)(D,E[1]),[D,E]),L=(0,g.useMemo)(()=>(0,u.getSelectedParams)(E),[E]),M=(0,g.useMemo)(()=>({parentTree:E,parentCacheNode:D,parentSegmentPath:null,url:m}),[E,D,m]),O=(0,g.useMemo)(()=>({tree:E,focusAndScrollRef:J,nextUrl:G}),[E,J,G]);if(null!==K){let[a,c,d]=K;b=(0,f.jsx)(I,{headCacheNode:a},d)}else b=null;let P=(0,f.jsxs)(p.RedirectBoundary,{children:[b,(0,f.jsx)(B.RootLayoutBoundary,{children:D.rsc}),(0,f.jsx)(o.AppRouterAnnouncer,{tree:E})]});return P=(0,f.jsx)(z.default,{errorComponent:e[0],errorStyles:e[1],children:P}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(F,{appRouterState:j}),(0,f.jsx)(N,{}),(0,f.jsx)(k.PathParamsContext.Provider,{value:L,children:(0,f.jsx)(k.PathnameContext.Provider,{value:v,children:(0,f.jsx)(k.SearchParamsContext.Provider,{value:n,children:(0,f.jsx)(h.GlobalLayoutRouterContext.Provider,{value:O,children:(0,f.jsx)(h.AppRouterContext.Provider,{value:w.publicAppRouterInstance,children:(0,f.jsx)(h.LayoutRouterContext.Provider,{value:M,children:P})})})})})})]})}function K(a){let{actionQueue:b,globalErrorState:c,assetPrefix:d}=a;(0,v.useNavFailureHandler)();let e=(0,f.jsx)(J,{actionQueue:b,assetPrefix:d,globalError:c});return(0,f.jsx)(z.default,{errorComponent:A.default,children:e})}let L=new Set,M=new Set;function N(){let[,a]=g.default.useState(0),b=L.size;return(0,g.useEffect)(()=>{let c=()=>a(a=>a+1);return M.add(c),b!==L.size&&c(),()=>{M.delete(c)}},[b,a]),[...L].map((a,b)=>(0,f.jsx)("link",{rel:"stylesheet",href:""+a,precedence:"next"},b))}globalThis._N_E_STYLE_LOAD=function(a){let b=L.size;return L.add(a),L.size!==b&&M.forEach(a=>a()),Promise.resolve()},("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},57051,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{abortTask:function(){return o},listenForDynamicRequest:function(){return n},startPPRNavigation:function(){return j},updateCacheNodeOnPopstateRestoration:function(){return function a(b,c){let d=c[1],e=b.parallelRoutes,g=new Map(e);for(let b in d){let c=d[b],h=c[0],i=(0,f.createRouterCacheKey)(h),j=e.get(b);if(void 0!==j){let d=j.get(i);if(void 0!==d){let e=a(d,c),f=new Map(j);f.set(i,e),g.set(b,f)}}}let h=b.rsc,i=r(h)&&"pending"===h.status;return{lazyData:null,rsc:h,head:b.head,prefetchHead:i?b.prefetchHead:[null,null],prefetchRsc:i?b.prefetchRsc:null,loading:b.loading,parallelRoutes:g,navigatedAt:b.navigatedAt}}}});let d=a.r(39118),e=a.r(30641),f=a.r(70681),g=a.r(71094),h=a.r(98629),i={route:null,node:null,dynamicRequestTree:null,children:null};function j(a,b,c,g,h,j,m,n,o){return function a(b,c,g,h,j,m,n,o,p,q,r){let s=g[1],t=h[1],u=null!==m?m[2]:null;j||!0===h[4]&&(j=!0);let v=c.parallelRoutes,w=new Map(v),x={},y=null,z=!1,A={};for(let c in t){let g,h=t[c],l=s[c],m=v.get(c),B=null!==u?u[c]:null,C=h[0],D=q.concat([c,C]),E=(0,f.createRouterCacheKey)(C),F=void 0!==l?l[0]:void 0,G=void 0!==m?m.get(E):void 0;if(null!==(g=C===d.DEFAULT_SEGMENT_KEY?void 0!==l?{route:l,node:null,dynamicRequestTree:null,children:null}:k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):p&&0===Object.keys(h[1]).length?k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):void 0!==l&&void 0!==F&&(0,e.matchSegment)(C,F)&&void 0!==G&&void 0!==l?a(b,G,l,h,j,B,n,o,p,D,r):k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r))){if(null===g.route)return i;null===y&&(y=new Map),y.set(c,g);let a=g.node;if(null!==a){let b=new Map(m);b.set(E,a),w.set(c,b)}let b=g.route;x[c]=b;let d=g.dynamicRequestTree;null!==d?(z=!0,A[c]=d):A[c]=b}else x[c]=h,A[c]=h}if(null===y)return null;let B={lazyData:null,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,loading:c.loading,parallelRoutes:w,navigatedAt:b};return{route:l(h,x),node:B,dynamicRequestTree:z?l(h,A):null,children:y}}(a,b,c,g,!1,h,j,m,n,[],o)}function k(a,b,c,d,e,j,k,n,o,p){return!e&&(void 0===b||(0,g.isNavigatingToNewRootLayout)(b,c))?i:function a(b,c,d,e,g,i,j,k){let n,o,p,q,r=c[1],s=0===Object.keys(r).length;if(void 0!==d&&d.navigatedAt+h.DYNAMIC_STALETIME_MS>b)n=d.rsc,o=d.loading,p=d.head,q=d.navigatedAt;else if(null===e)return m(b,c,null,g,i,j,k);else if(n=e[1],o=e[3],p=s?g:null,q=b,e[4]||i&&s)return m(b,c,e,g,i,j,k);let t=null!==e?e[2]:null,u=new Map,v=void 0!==d?d.parallelRoutes:null,w=new Map(v),x={},y=!1;if(s)k.push(j);else for(let c in r){let d=r[c],e=null!==t?t[c]:null,h=null!==v?v.get(c):void 0,l=d[0],m=j.concat([c,l]),n=(0,f.createRouterCacheKey)(l),o=a(b,d,void 0!==h?h.get(n):void 0,e,g,i,m,k);u.set(c,o);let p=o.dynamicRequestTree;null!==p?(y=!0,x[c]=p):x[c]=d;let q=o.node;if(null!==q){let a=new Map;a.set(n,q),w.set(c,a)}}return{route:c,node:{lazyData:null,rsc:n,prefetchRsc:null,head:p,prefetchHead:null,loading:o,parallelRoutes:w,navigatedAt:q},dynamicRequestTree:y?l(c,x):null,children:u}}(a,c,d,j,k,n,o,p)}function l(a,b){let c=[a[0],b];return 2 in a&&(c[2]=a[2]),3 in a&&(c[3]=a[3]),4 in a&&(c[4]=a[4]),c}function m(a,b,c,d,e,g,h){let i=l(b,b[1]);return i[3]="refetch",{route:b,node:function a(b,c,d,e,g,h,i){let j=c[1],k=null!==d?d[2]:null,l=new Map;for(let c in j){let d=j[c],m=null!==k?k[c]:null,n=d[0],o=h.concat([c,n]),p=(0,f.createRouterCacheKey)(n),q=a(b,d,void 0===m?null:m,e,g,o,i),r=new Map;r.set(p,q),l.set(c,r)}let m=0===l.size;m&&i.push(h);let n=null!==d?d[1]:null,o=null!==d?d[3]:null;return{lazyData:null,parallelRoutes:l,prefetchRsc:void 0!==n?n:null,prefetchHead:m?e:[null,null],loading:void 0!==o?o:null,rsc:s(),head:m?s():null,navigatedAt:b}}(a,b,c,d,e,g,h),dynamicRequestTree:i,children:null}}function n(a,b){b.then(b=>{let{flightData:c}=b;if("string"!=typeof c){for(let b of c){let{segmentPath:c,tree:d,seedData:g,head:h}=b;g&&function(a,b,c,d,g){let h=a;for(let a=0;a<b.length;a+=2){let c=b[a],d=b[a+1],f=h.children;if(null!==f){let a=f.get(c);if(void 0!==a){let b=a.route[0];if((0,e.matchSegment)(d,b)){h=a;continue}}}return}!function a(b,c,d,g){if(null===b.dynamicRequestTree)return;let h=b.children,i=b.node;if(null===h){null!==i&&(function a(b,c,d,g,h){let i=c[1],j=d[1],k=g[2],l=b.parallelRoutes;for(let b in i){let c=i[b],d=j[b],g=k[b],m=l.get(b),n=c[0],o=(0,f.createRouterCacheKey)(n),q=void 0!==m?m.get(o):void 0;void 0!==q&&(void 0!==d&&(0,e.matchSegment)(n,d[0])&&null!=g?a(q,c,d,g,h):p(c,q,null))}let m=b.rsc,n=g[1];null===m?b.rsc=n:r(m)&&m.resolve(n);let o=b.head;r(o)&&o.resolve(h)}(i,b.route,c,d,g),b.dynamicRequestTree=null);return}let j=c[1],k=d[2];for(let b in c){let c=j[b],d=k[b],f=h.get(b);if(void 0!==f){let b=f.route[0];if((0,e.matchSegment)(c[0],b)&&null!=d)return a(f,c,d,g)}}}(h,c,d,g)}(a,c,d,g,h)}o(a,null)}},b=>{o(a,b)})}function o(a,b){let c=a.node;if(null===c)return;let d=a.children;if(null===d)p(a.route,c,b);else for(let a of d.values())o(a,b);a.dynamicRequestTree=null}function p(a,b,c){let d=a[1],e=b.parallelRoutes;for(let a in d){let b=d[a],g=e.get(a);if(void 0===g)continue;let h=b[0],i=(0,f.createRouterCacheKey)(h),j=g.get(i);void 0!==j&&p(b,j,c)}let g=b.rsc;r(g)&&(null===c?g.resolve(null):g.reject(c));let h=b.head;r(h)&&h.resolve(null)}let q=Symbol();function r(a){return a&&a.tag===q}function s(){let a,b,c=new Promise((c,d)=>{a=c,b=d});return c.status="pending",c.resolve=b=>{"pending"===c.status&&(c.status="fulfilled",c.value=b,a(b))},c.reject=a=>{"pending"===c.status&&(c.status="rejected",c.reason=a,b(a))},c.tag=q,c}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},45496,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,e.createRouterCacheKey)(i),k=c.parallelRoutes.get(h),l=b.parallelRoutes.get(h);l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l));let m=null==k?void 0:k.get(j),n=l.get(j);if(g){n&&n.lazyData&&n!==m||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!n||!m){n||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes),loading:n.loading},l.set(j,n)),a(n,m,(0,d.getNextFlightSegmentPath)(f))}}});let d=a.r(22129),e=a.r(70681);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},17522,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{addSearchParamsToPageSegments:function(){return m},handleAliasedPrefetchEntry:function(){return l}});let d=a.r(39118),e=a.r(451),f=a.r(51748),g=a.r(74685),h=a.r(70681),i=a.r(66013),j=a.r(88632),k=a.r(23053);function l(a,b,c,l,n){let o,p=b.tree,q=b.cache,r=(0,g.createHrefFromUrl)(l),s=[];if("string"==typeof c)return!1;for(let b of c){if(!function a(b){if(!b)return!1;let c=b[2];if(b[3])return!0;for(let b in c)if(a(c[b]))return!0;return!1}(b.seedData))continue;let c=b.tree;c=m(c,Object.fromEntries(l.searchParams));let{seedData:g,isRootRender:j,pathToSegment:n}=b,t=["",...n];c=m(c,Object.fromEntries(l.searchParams));let u=(0,f.applyRouterStatePatchToTree)(t,p,c,r),v=(0,e.createEmptyCacheNode)();if(j&&g){let b=g[1];v.loading=g[3],v.rsc=b,function a(b,c,e,f,g){if(0!==Object.keys(f[1]).length)for(let i in f[1]){let j,k=f[1][i],l=k[0],m=(0,h.createRouterCacheKey)(l),n=null!==g&&void 0!==g[2][i]?g[2][i]:null;if(null!==n){let a=n[1],c=n[3];j={lazyData:null,rsc:l.includes(d.PAGE_SEGMENT_KEY)?null:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else j={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let o=c.parallelRoutes.get(i);o?o.set(m,j):c.parallelRoutes.set(i,new Map([[m,j]])),a(b,j,e,k,n)}}(a,v,q,c,g)}else v.rsc=q.rsc,v.prefetchRsc=q.prefetchRsc,v.loading=q.loading,v.parallelRoutes=new Map(q.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(a,v,q,b);for(let a of(u&&(p=u,q=v,o=!0),(0,k.generateSegmentsFromPatch)(c))){let c=[...b.pathToSegment,...a];c[c.length-1]!==d.DEFAULT_SEGMENT_KEY&&s.push(c)}}return!!o&&(n.patchedTree=p,n.cache=q,n.canonicalUrl=r,n.hashFragment=l.hash,n.scrollableSegments=s,(0,j.handleMutable)(b,n))}function m(a,b){let[c,e,...f]=a;if(c.includes(d.PAGE_SEGMENT_KEY))return[(0,d.addSearchParamsIfPageSegment)(c,b),e,...f];let g={};for(let[a,c]of Object.entries(e))g[a]=m(c,b);return[c,g,...f]}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},29442,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{FetchStrategy:function(){return p},NavigationResultTag:function(){return n},PrefetchPriority:function(){return o},cancelPrefetchTask:function(){return j},createCacheKey:function(){return m},getCurrentCacheVersion:function(){return h},isPrefetchTaskDirty:function(){return l},navigate:function(){return f},prefetch:function(){return e},reschedulePrefetchTask:function(){return k},revalidateEntireCache:function(){return g},schedulePrefetchTask:function(){return i}});let d=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},e=d,f=d,g=d,h=d,i=d,j=d,k=d,l=d,m=d;var n=function(a){return a[a.MPA=0]="MPA",a[a.Success=1]="Success",a[a.NoOp=2]="NoOp",a[a.Async=3]="Async",a}({}),o=function(a){return a[a.Intent=2]="Intent",a[a.Default=1]="Default",a[a.Background=0]="Background",a}({}),p=function(a){return a[a.LoadingBoundary=0]="LoadingBoundary",a[a.PPR=1]="PPR",a[a.PPRRuntime=2]="PPRRuntime",a[a.Full=3]="Full",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},23053,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{generateSegmentsFromPatch:function(){return u},handleExternalUrl:function(){return t},navigateReducer:function(){return function a(b,c){let{url:v,isExternalUrl:w,navigateType:x,shouldScroll:y,allowAliasing:z}=c,A={},{hash:B}=v,C=(0,e.createHrefFromUrl)(v),D="push"===x;if((0,q.prunePrefetchCache)(b.prefetchCache),A.preserveCustomHistoryState=!1,A.pendingPush=D,w)return t(b,A,v.toString(),D);if(document.getElementById("__next-page-redirect"))return t(b,A,C,D);let E=(0,q.getOrCreatePrefetchCacheEntry)({url:v,nextUrl:b.nextUrl,tree:b.tree,prefetchCache:b.prefetchCache,allowAliasing:z}),{treeAtTimeOfPrefetch:F,data:G}=E;return m.prefetchQueue.bump(G),G.then(m=>{let{flightData:q,canonicalUrl:w,postponed:x}=m,z=Date.now(),G=!1;if(E.lastUsedTime||(E.lastUsedTime=z,G=!0),E.aliased){let d=new URL(v.href);w&&(d.pathname=w.pathname);let e=(0,s.handleAliasedPrefetchEntry)(z,b,q,d,A);return!1===e?a(b,{...c,allowAliasing:!1}):e}if("string"==typeof q)return t(b,A,q,D);let H=w?(0,e.createHrefFromUrl)(w):C;if(B&&b.canonicalUrl.split("#",1)[0]===H.split("#",1)[0])return A.onlyHashChange=!0,A.canonicalUrl=H,A.shouldScroll=y,A.hashFragment=B,A.scrollableSegments=[],(0,k.handleMutable)(b,A);let I=b.tree,J=b.cache,K=[];for(let a of q){let{pathToSegment:c,seedData:e,head:k,isHeadPartial:m,isRootRender:q}=a,s=a.tree,w=["",...c],y=(0,g.applyRouterStatePatchToTree)(w,I,s,C);if(null===y&&(y=(0,g.applyRouterStatePatchToTree)(w,F,s,C)),null!==y){if(e&&q&&x){let a=(0,p.startPPRNavigation)(z,J,I,s,e,k,m,!1,K);if(null!==a){if(null===a.route)return t(b,A,C,D);y=a.route;let c=a.node;null!==c&&(A.cache=c);let e=a.dynamicRequestTree;if(null!==e){let c=(0,d.fetchServerResponse)(new URL(H,v.origin),{flightRouterState:e,nextUrl:b.nextUrl});(0,p.listenForDynamicRequest)(a,c)}}else y=s}else{if((0,i.isNavigatingToNewRootLayout)(I,y))return t(b,A,C,D);let d=(0,n.createEmptyCacheNode)(),e=!1;for(let b of(E.status!==j.PrefetchCacheEntryStatus.stale||G?e=(0,l.applyFlightData)(z,J,d,a,E):(e=function(a,b,c,d){let e=!1;for(let f of(a.rsc=b.rsc,a.prefetchRsc=b.prefetchRsc,a.loading=b.loading,a.parallelRoutes=new Map(b.parallelRoutes),u(d).map(a=>[...c,...a])))(0,r.clearCacheNodeDataForSegmentPath)(a,b,f),e=!0;return e}(d,J,c,s),E.lastUsedTime=z),(0,h.shouldHardNavigate)(w,I)?(d.rsc=J.rsc,d.prefetchRsc=J.prefetchRsc,(0,f.invalidateCacheBelowFlightSegmentPath)(d,J,c),A.cache=d):e&&(A.cache=d,J=d),u(s))){let a=[...c,...b];a[a.length-1]!==o.DEFAULT_SEGMENT_KEY&&K.push(a)}}I=y}}return A.patchedTree=I,A.canonicalUrl=H,A.scrollableSegments=K,A.hashFragment=B,A.shouldScroll=y,(0,k.handleMutable)(b,A)},()=>b)}}});let d=a.r(20873),e=a.r(74685),f=a.r(77159),g=a.r(51748),h=a.r(9381),i=a.r(71094),j=a.r(88347),k=a.r(88632),l=a.r(97768),m=a.r(35399),n=a.r(451),o=a.r(39118),p=a.r(57051),q=a.r(98629),r=a.r(45496),s=a.r(17522);function t(a,b,c,d){return b.mpaNavigation=!0,b.canonicalUrl=c,b.pendingPush=d,b.scrollableSegments=void 0,(0,k.handleMutable)(a,b)}function u(a){let b=[],[c,d]=a;if(0===Object.keys(d).length)return[[c]];for(let[a,e]of Object.entries(d))for(let d of u(e))""===c?b.push([a,...d]):b.push([c,a,...d]);return b}a.r(29442),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},95828,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"serverPatchReducer",{enumerable:!0,get:function(){return k}});let d=a.r(74685),e=a.r(51748),f=a.r(71094),g=a.r(23053),h=a.r(97768),i=a.r(88632),j=a.r(451);function k(a,b){let{serverResponse:{flightData:c,canonicalUrl:k},navigatedAt:l}=b,m={};if(m.preserveCustomHistoryState=!1,"string"==typeof c)return(0,g.handleExternalUrl)(a,m,c,a.pushRef.pendingPush);let n=a.tree,o=a.cache;for(let b of c){let{segmentPath:c,tree:i}=b,p=(0,e.applyRouterStatePatchToTree)(["",...c],n,i,a.canonicalUrl);if(null===p)return a;if((0,f.isNavigatingToNewRootLayout)(n,p))return(0,g.handleExternalUrl)(a,m,a.canonicalUrl,a.pushRef.pendingPush);let q=k?(0,d.createHrefFromUrl)(k):void 0;q&&(m.canonicalUrl=q);let r=(0,j.createEmptyCacheNode)();(0,h.applyFlightData)(l,o,r,b),m.patchedTree=p,m.cache=r,o=r,n=p}return(0,i.handleMutable)(a,m)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},52869,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"restoreReducer",{enumerable:!0,get:function(){return f}});let d=a.r(74685),e=a.r(37042);function f(a,b){var c;let{url:f,tree:g}=b,h=(0,d.createHrefFromUrl)(f),i=g||a.tree,j=a.cache;return{canonicalUrl:h,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:a.focusAndScrollRef,cache:j,prefetchCache:a.prefetchCache,tree:i,nextUrl:null!=(c=(0,e.extractPathFromFlightRouterState)(i))?c:f.pathname}}a.r(57051),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88850,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"handleSegmentMismatch",{enumerable:!0,get:function(){return e}});let d=a.r(23053);function e(a,b,c){return(0,d.handleExternalUrl)(a,{},a.canonicalUrl,!0)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},54050,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"hasInterceptionRouteInCurrentTree",{enumerable:!0,get:function(){return function a(b){let[c,e]=b;if(Array.isArray(c)&&("di"===c[2]||"ci"===c[2])||"string"==typeof c&&(0,d.isInterceptionRouteAppPath)(c))return!0;if(e){for(let b in e)if(a(e[b]))return!0}return!1}}});let d=a.r(18099);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},84496,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"refreshReducer",{enumerable:!0,get:function(){return o}});let d=a.r(20873),e=a.r(74685),f=a.r(51748),g=a.r(71094),h=a.r(23053),i=a.r(88632),j=a.r(10089),k=a.r(451),l=a.r(88850),m=a.r(54050),n=a.r(30719);function o(a,b){let{origin:c}=b,o={},p=a.canonicalUrl,q=a.tree;o.preserveCustomHistoryState=!1;let r=(0,k.createEmptyCacheNode)(),s=(0,m.hasInterceptionRouteInCurrentTree)(a.tree);r.lazyData=(0,d.fetchServerResponse)(new URL(p,c),{flightRouterState:[q[0],q[1],q[2],"refetch"],nextUrl:s?a.nextUrl:null});let t=Date.now();return r.lazyData.then(async c=>{let{flightData:d,canonicalUrl:k}=c;if("string"==typeof d)return(0,h.handleExternalUrl)(a,o,d,a.pushRef.pendingPush);for(let c of(r.lazyData=null,d)){let{tree:d,seedData:i,head:m,isRootRender:u}=c;if(!u)return console.log("REFRESH FAILED"),a;let v=(0,f.applyRouterStatePatchToTree)([""],q,d,a.canonicalUrl);if(null===v)return(0,l.handleSegmentMismatch)(a,b,d);if((0,g.isNavigatingToNewRootLayout)(q,v))return(0,h.handleExternalUrl)(a,o,p,a.pushRef.pendingPush);let w=k?(0,e.createHrefFromUrl)(k):void 0;if(k&&(o.canonicalUrl=w),null!==i){let a=i[1],b=i[3];r.rsc=a,r.prefetchRsc=null,r.loading=b,(0,j.fillLazyItemsTillLeafWithHead)(t,r,void 0,d,i,m,void 0),o.prefetchCache=new Map}await (0,n.refreshInactiveParallelSegments)({navigatedAt:t,state:a,updatedTree:v,updatedCache:r,includeNextUrl:s,canonicalUrl:o.canonicalUrl||a.canonicalUrl}),o.cache=r,o.patchedTree=v,q=v}return(0,i.handleMutable)(a,o)},()=>a)}a.r(29442),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},41970,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"hmrRefreshReducer",{enumerable:!0,get:function(){return d}}),a.r(20873),a.r(74685),a.r(51748),a.r(71094),a.r(23053),a.r(88632),a.r(97768),a.r(451),a.r(88850),a.r(54050);let d=function(a,b){return a};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},63333,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"assignLocation",{enumerable:!0,get:function(){return e}});let d=a.r(38243);function e(a,b){if(a.startsWith(".")){let c=b.origin+b.pathname;return new URL((c.endsWith("/")?c:c+"/")+a)}return new URL((0,d.addBasePath)(a),b.href)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},22713,(a,b,c)=>{"use strict";function d(a){let b=parseInt(a.slice(0,2),16),c=b>>1&63,d=Array(6);for(let a=0;a<6;a++){let b=c>>5-a&1;d[a]=1===b}return{type:1==(b>>7&1)?"use-cache":"server-action",usedArgs:d,hasRestArgs:1==(1&b)}}function e(a,b){let c=Array(a.length);for(let d=0;d<a.length;d++)(d<6&&b.usedArgs[d]||d>=6&&b.hasRestArgs)&&(c[d]=a[d]);return c}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{extractInfoFromServerReferenceId:function(){return d},omitUnusedArgs:function(){return e}})},41633,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"serverActionReducer",{enumerable:!0,get:function(){return E}});let d=a.r(20611),e=a.r(1722),f=a.r(37909),g=a.r(57997),h=a.r(38783),i=a.r(88347),j=a.r(63333),k=a.r(74685),l=a.r(23053),m=a.r(51748),n=a.r(71094),o=a.r(88632),p=a.r(10089),q=a.r(451),r=a.r(54050),s=a.r(88850),t=a.r(30719),u=a.r(22129),v=a.r(22099),w=a.r(11026),x=a.r(98629),y=a.r(16749),z=a.r(78897),A=a.r(22713);a.r(29442);let B=h.createFromFetch;async function C(a,b,c){let i,k,l,m,{actionId:n,actionArgs:o}=c,p=(0,h.createTemporaryReferenceSet)(),q=(0,A.extractInfoFromServerReferenceId)(n),r="use-cache"===q.type?(0,A.omitUnusedArgs)(o,q):o,s=await (0,h.encodeReply)(r,{temporaryReferences:p}),t=await fetch(a.canonicalUrl,{method:"POST",headers:{Accept:f.RSC_CONTENT_TYPE_HEADER,[f.ACTION_HEADER]:n,[f.NEXT_ROUTER_STATE_TREE_HEADER]:(0,u.prepareFlightRouterStateForRequest)(a.tree),...{},...b?{[f.NEXT_URL]:b}:{}},body:s});if("1"===t.headers.get(f.NEXT_ACTION_NOT_FOUND_HEADER))throw Object.defineProperty(new g.UnrecognizedActionError('Server Action "'+n+'" was not found on the server. \nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action'),"__NEXT_ERROR_CODE",{value:"E715",enumerable:!1,configurable:!0});let v=t.headers.get("x-action-redirect"),[x,y]=(null==v?void 0:v.split(";"))||[];switch(y){case"push":i=w.RedirectType.push;break;case"replace":i=w.RedirectType.replace;break;default:i=void 0}let z=!!t.headers.get(f.NEXT_IS_PRERENDER_HEADER);try{let a=JSON.parse(t.headers.get("x-action-revalidated")||"[[],0,0]");k={paths:a[0]||[],tag:!!a[1],cookie:a[2]}}catch(a){k=D}let C=x?(0,j.assignLocation)(x,new URL(a.canonicalUrl,window.location.href)):void 0,E=t.headers.get("content-type"),F=!!(E&&E.startsWith(f.RSC_CONTENT_TYPE_HEADER));if(!F&&!C)throw Object.defineProperty(Error(t.status>=400&&"text/plain"===E?await t.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if(F){let a=await B(Promise.resolve(t),{callServer:d.callServer,findSourceMapURL:e.findSourceMapURL,temporaryReferences:p});l=C?void 0:a.a,m=(0,u.normalizeFlightData)(a.f)}else l=void 0,m=void 0;return{actionResult:l,actionFlightData:m,redirectLocation:C,redirectType:i,revalidatedParts:k,isPrerender:z}}let D={paths:[],tag:!1,cookie:!1};function E(a,b){let{resolve:c,reject:d}=b,e={},f=a.tree;e.preserveCustomHistoryState=!1;let g=a.nextUrl&&(0,r.hasInterceptionRouteInCurrentTree)(a.tree)?a.nextUrl:null,h=Date.now();return C(a,g,b).then(async j=>{let r,{actionResult:u,actionFlightData:A,redirectLocation:B,redirectType:C,isPrerender:D,revalidatedParts:E}=j;if(B&&(C===w.RedirectType.replace?(a.pushRef.pendingPush=!1,e.pendingPush=!1):(a.pushRef.pendingPush=!0,e.pendingPush=!0),e.canonicalUrl=r=(0,k.createHrefFromUrl)(B,!1)),!A)return(c(u),B)?(0,l.handleExternalUrl)(a,e,B.href,a.pushRef.pendingPush):a;if("string"==typeof A)return c(u),(0,l.handleExternalUrl)(a,e,A,a.pushRef.pendingPush);let F=E.paths.length>0||E.tag||E.cookie;for(let d of A){let{tree:i,seedData:j,head:k,isRootRender:o}=d;if(!o)return console.log("SERVER ACTION APPLY FAILED"),c(u),a;let v=(0,m.applyRouterStatePatchToTree)([""],f,i,r||a.canonicalUrl);if(null===v)return c(u),(0,s.handleSegmentMismatch)(a,b,i);if((0,n.isNavigatingToNewRootLayout)(f,v))return c(u),(0,l.handleExternalUrl)(a,e,r||a.canonicalUrl,a.pushRef.pendingPush);if(null!==j){let b=j[1],c=(0,q.createEmptyCacheNode)();c.rsc=b,c.prefetchRsc=null,c.loading=j[3],(0,p.fillLazyItemsTillLeafWithHead)(h,c,void 0,i,j,k,void 0),e.cache=c,e.prefetchCache=new Map,F&&await (0,t.refreshInactiveParallelSegments)({navigatedAt:h,state:a,updatedTree:v,updatedCache:c,includeNextUrl:!!g,canonicalUrl:e.canonicalUrl||a.canonicalUrl})}e.patchedTree=v,f=v}return B&&r?(F||((0,x.createSeededPrefetchCacheEntry)({url:B,data:{flightData:A,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:a.tree,prefetchCache:a.prefetchCache,nextUrl:a.nextUrl,kind:D?i.PrefetchKind.FULL:i.PrefetchKind.AUTO}),e.prefetchCache=a.prefetchCache),d((0,v.getRedirectError)((0,z.hasBasePath)(r)?(0,y.removeBasePath)(r):r,C||w.RedirectType.push))):c(u),(0,o.handleMutable)(a,e)},b=>(d(b),a))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},54940,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"reducer",{enumerable:!0,get:function(){return d}}),a.r(88347),a.r(23053),a.r(95828),a.r(52869),a.r(84496),a.r(35399),a.r(41970),a.r(41633);let d=function(a,b){return a};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},44637,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{createMutableActionQueue:function(){return o},dispatchNavigateAction:function(){return q},dispatchTraverseAction:function(){return r},getCurrentAppRouterState:function(){return p},publicAppRouterInstance:function(){return s}});let d=a.r(88347),e=a.r(54940),f=a.r(72131),g=a.r(67009);a.r(29442);let h=a.r(90841),i=a.r(38243),j=a.r(451),k=a.r(35399),l=a.r(16441);function m(a,b){null!==a.pending&&(a.pending=a.pending.next,null!==a.pending?n({actionQueue:a,action:a.pending,setState:b}):a.needsRefresh&&(a.needsRefresh=!1,a.dispatch({type:d.ACTION_REFRESH,origin:window.location.origin},b)))}async function n(a){let{actionQueue:b,action:c,setState:d}=a,e=b.state;b.pending=c;let f=c.payload,h=b.action(e,f);function i(a){c.discarded||(b.state=a,m(b,d),c.resolve(a))}(0,g.isThenable)(h)?h.then(i,a=>{m(b,d),c.reject(a)}):i(h)}function o(a,b){let c={state:a,dispatch:(a,b)=>(function(a,b,c){let e={resolve:c,reject:()=>{}};if(b.type!==d.ACTION_RESTORE){let a=new Promise((a,b)=>{e={resolve:a,reject:b}});(0,f.startTransition)(()=>{c(a)})}let g={payload:b,next:null,resolve:e.resolve,reject:e.reject};null===a.pending?(a.last=g,n({actionQueue:a,action:g,setState:c})):b.type===d.ACTION_NAVIGATE||b.type===d.ACTION_RESTORE?(a.pending.discarded=!0,g.next=a.pending.next,a.pending.payload.type===d.ACTION_SERVER_ACTION&&(a.needsRefresh=!0),n({actionQueue:a,action:g,setState:c})):(null!==a.last&&(a.last.next=g),a.last=g)})(c,a,b),action:async(a,b)=>(0,e.reducer)(a,b),pending:null,last:null,onRouterTransitionStart:null!==b&&"function"==typeof b.onRouterTransitionStart?b.onRouterTransitionStart:null};return c}function p(){return null}function q(a,b,c,e){let f=new URL((0,i.addBasePath)(a),location.href);(0,l.setLinkForCurrentNavigation)(e);(0,h.dispatchAppRouterAction)({type:d.ACTION_NAVIGATE,url:f,isExternalUrl:(0,j.isExternalURL)(f),locationSearch:location.search,shouldScroll:c,navigateType:b,allowAliasing:!0})}function r(a,b){(0,h.dispatchAppRouterAction)({type:d.ACTION_RESTORE,url:new URL(a),tree:b})}let s={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(a,b)=>{let c=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),e=(0,j.createPrefetchURL)(a);if(null!==e){var f;(0,k.prefetchReducer)(c.state,{type:d.ACTION_PREFETCH,url:e,kind:null!=(f=null==b?void 0:b.kind)?f:d.PrefetchKind.FULL})}},replace:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"replace",null==(c=null==b?void 0:b.scroll)||c,null)})},push:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"push",null==(c=null==b?void 0:b.scroll)||c,null)})},refresh:()=>{(0,f.startTransition)(()=>{(0,h.dispatchAppRouterAction)({type:d.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},16441,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{IDLE_LINK_STATUS:function(){return i},PENDING_LINK_STATUS:function(){return h},mountFormInstance:function(){return r},mountLinkInstance:function(){return q},onLinkVisibilityChanged:function(){return t},onNavigationIntent:function(){return u},pingVisibleLinks:function(){return w},setLinkForCurrentNavigation:function(){return j},unmountLinkForCurrentNavigation:function(){return k},unmountPrefetchableInstance:function(){return s}}),a.r(44637);let d=a.r(451),e=a.r(29442),f=a.r(72131);a.r(88347),a.r(88644);let g=null,h={pending:!0},i={pending:!1};function j(a){(0,f.startTransition)(()=>{null==g||g.setOptimisticLinkStatus(i),null==a||a.setOptimisticLinkStatus(h),g=a})}function k(a){g===a&&(g=null)}let l="function"==typeof WeakMap?new WeakMap:new Map,m=new Set,n="function"==typeof IntersectionObserver?new IntersectionObserver(function(a){for(let b of a){let a=b.intersectionRatio>0;t(b.target,a)}},{rootMargin:"200px"}):null;function o(a,b){void 0!==l.get(a)&&s(a),l.set(a,b),null!==n&&n.observe(a)}function p(a){try{return(0,d.createPrefetchURL)(a)}catch(b){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),null}}function q(a,b,c,d,e,f){if(e){let e=p(b);if(null!==e){let b={router:c,fetchStrategy:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:f};return o(a,b),b}}return{router:c,fetchStrategy:d,isVisible:!1,prefetchTask:null,prefetchHref:null,setOptimisticLinkStatus:f}}function r(a,b,c,d){let e=p(b);null!==e&&o(a,{router:c,fetchStrategy:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:null})}function s(a){let b=l.get(a);if(void 0!==b){l.delete(a),m.delete(b);let c=b.prefetchTask;null!==c&&(0,e.cancelPrefetchTask)(c)}null!==n&&n.unobserve(a)}function t(a,b){let c=l.get(a);void 0!==c&&(c.isVisible=b,b?m.add(c):m.delete(c),v(c,e.PrefetchPriority.Default))}function u(a,b){let c=l.get(a);void 0!==c&&void 0!==c&&v(c,e.PrefetchPriority.Intent)}function v(a,b){let c=a.prefetchTask;if(!a.isVisible){null!==c&&(0,e.cancelPrefetchTask)(c);return}}function w(a,b){for(let c of m){let d=c.prefetchTask;if(null!==d&&!(0,e.isPrefetchTaskDirty)(d,a,b))continue;null!==d&&(0,e.cancelPrefetchTask)(d);let f=(0,e.createCacheKey)(c.prefetchHref,a);c.prefetchTask=(0,e.schedulePrefetchTask)(f,b,c.fetchStrategy,e.PrefetchPriority.Default,null)}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},11762,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isLocalURL",{enumerable:!0,get:function(){return f}});let d=a.r(46272),e=a.r(78897);function f(a){if(!(0,d.isAbsoluteUrl)(a))return!0;try{let b=(0,d.getLocationOrigin)(),c=new URL(a,b);return c.origin===b&&(0,e.hasBasePath)(c.pathname)}catch(a){return!1}}},9833,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"errorOnce",{enumerable:!0,get:function(){return d}});let d=a=>{}},38246,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return q},useLinkStatus:function(){return s}});let d=a.r(46058),e=a.r(87924),f=d._(a.r(72131)),g=a.r(43087),h=a.r(9270),i=a.r(8591),j=a.r(46272),k=a.r(38243);a.r(92434);let l=a.r(16441),m=a.r(11762),n=a.r(44637);a.r(9833);let o=a.r(29442);function p(a){return"string"==typeof a?a:(0,g.formatUrl)(a)}function q(a){var b;let c,d,g,[q,s]=(0,f.useOptimistic)(l.IDLE_LINK_STATUS),t=(0,f.useRef)(null),{href:u,as:v,children:w,prefetch:x=null,passHref:y,replace:z,shallow:A,scroll:B,onClick:C,onMouseEnter:D,onTouchStart:E,legacyBehavior:F=!1,onNavigate:G,ref:H,unstable_dynamicOnHover:I,...J}=a;c=w,F&&("string"==typeof c||"number"==typeof c)&&(c=(0,e.jsx)("a",{children:c}));let K=f.default.useContext(h.AppRouterContext),L=!1!==x,M=!1!==x?null===(b=x)||"auto"===b?o.FetchStrategy.PPR:o.FetchStrategy.Full:o.FetchStrategy.PPR,{href:N,as:O}=f.default.useMemo(()=>{let a=p(u);return{href:a,as:v?p(v):a}},[u,v]);F&&(d=f.default.Children.only(c));let P=F?d&&"object"==typeof d&&d.ref:H,Q=f.default.useCallback(a=>(null!==K&&(t.current=(0,l.mountLinkInstance)(a,N,K,M,L,s)),()=>{t.current&&((0,l.unmountLinkForCurrentNavigation)(t.current),t.current=null),(0,l.unmountPrefetchableInstance)(a)}),[L,N,K,M,s]),R={ref:(0,i.useMergedRef)(Q,P),onClick(a){F||"function"!=typeof C||C(a),F&&d.props&&"function"==typeof d.props.onClick&&d.props.onClick(a),K&&(a.defaultPrevented||function(a,b,c,d,e,g,h){let{nodeName:i}=a.currentTarget;if(!("A"===i.toUpperCase()&&function(a){let b=a.currentTarget.getAttribute("target");return b&&"_self"!==b||a.metaKey||a.ctrlKey||a.shiftKey||a.altKey||a.nativeEvent&&2===a.nativeEvent.which}(a)||a.currentTarget.hasAttribute("download"))){if(!(0,m.isLocalURL)(b)){e&&(a.preventDefault(),location.replace(b));return}if(a.preventDefault(),h){let a=!1;if(h({preventDefault:()=>{a=!0}}),a)return}f.default.startTransition(()=>{(0,n.dispatchNavigateAction)(c||b,e?"replace":"push",null==g||g,d.current)})}}(a,N,O,t,z,B,G))},onMouseEnter(a){F||"function"!=typeof D||D(a),F&&d.props&&"function"==typeof d.props.onMouseEnter&&d.props.onMouseEnter(a),K&&L&&(0,l.onNavigationIntent)(a.currentTarget,!0===I)},onTouchStart:function(a){F||"function"!=typeof E||E(a),F&&d.props&&"function"==typeof d.props.onTouchStart&&d.props.onTouchStart(a),K&&L&&(0,l.onNavigationIntent)(a.currentTarget,!0===I)}};return(0,j.isAbsoluteUrl)(O)?R.href=O:F&&!y&&("a"!==d.type||"href"in d.props)||(R.href=(0,k.addBasePath)(O)),g=F?f.default.cloneElement(d,R):(0,e.jsx)("a",{...J,...R,children:c}),(0,e.jsx)(r.Provider,{value:q,children:g})}let r=(0,f.createContext)(l.IDLE_LINK_STATUS),s=()=>(0,f.useContext)(r);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},4987,(a,b,c)=>{"use strict";function d(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getImageBlurSvg",{enumerable:!0,get:function(){return d}})},345,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{VALID_LOADERS:function(){return d},imageConfigDefault:function(){return e}});let d=["default","imgix","cloudinary","akamai","custom"],e={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},94915,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getImgProps",{enumerable:!0,get:function(){return i}}),a.r(92434);let d=a.r(4987),e=a.r(345),f=["-moz-initial","fill","none","scale-down",void 0];function g(a){return void 0!==a.default}function h(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function i(a,b){var c,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=b,O=K||e.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),d=null==(c=O.qualities)?void 0:c.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:d}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=h(t),T=h(u);if((i=m)&&"object"==typeof i&&(g(i)||void 0!==i.src)){let a=g(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=h(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,d.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=f.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}},94613,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return f}});let d=a.r(72131),e=()=>{};function f(a){var b;let{headManager:c,reduceComponentsToState:f}=a;function g(){if(c&&c.mountedInstances){let b=d.Children.toArray(Array.from(c.mountedInstances).filter(Boolean));c.updateHead(f(b,a))}}return null==c||null==(b=c.mountedInstances)||b.add(a.children),g(),e(()=>{var b;return null==c||null==(b=c.mountedInstances)||b.add(a.children),()=>{var b;null==c||null==(b=c.mountedInstances)||b.delete(a.children)}}),e(()=>(c&&(c._pendingUpdate=g),()=>{c&&(c._pendingUpdate=g)})),null}},59231,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AmpContext},92966,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HeadManagerContext},57268,(a,b,c)=>{"use strict";function d(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isInAmpMode",{enumerable:!0,get:function(){return d}})},58018,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return p},defaultHead:function(){return l}});let d=a.r(33354),e=a.r(46058),f=a.r(87924),g=e._(a.r(72131)),h=d._(a.r(94613)),i=a.r(59231),j=a.r(92966),k=a.r(57268);function l(a){void 0===a&&(a=!1);let b=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function m(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===g.default.Fragment?a.concat(g.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}a.r(92434);let n=["name","httpEquiv","charSet","itemProp"];function o(a,b){let{inAmpMode:c}=b;return a.reduce(m,[]).reverse().concat(l(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=n.length;a<b;a++){let b=n[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let c=a.key||b;return g.default.cloneElement(a,{key:c})})}let p=function(a){let{children:b}=a,c=(0,g.useContext)(i.AmpStateContext),d=(0,g.useContext)(j.HeadManagerContext);return(0,f.jsx)(h.default,{reduceComponentsToState:o,headManager:d,inAmpMode:(0,k.isInAmpMode)(c),children:b})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},4486,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ImageConfigContext},53773,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.RouterContext},2305,(a,b,c)=>{"use strict";function d(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return e}}),d.__next_img_default=!0;let e=d},67161,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"Image",{enumerable:!0,get:function(){return u}});let d=a.r(33354),e=a.r(46058),f=a.r(87924),g=e._(a.r(72131)),h=d._(a.r(35112)),i=d._(a.r(58018)),j=a.r(94915),k=a.r(345),l=a.r(4486);a.r(92434);let m=a.r(53773),n=d._(a.r(2305)),o=a.r(8591),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function q(a,b,c,d,e,f,g){let h=null==a?void 0:a.src;a&&a["data-loaded-src"]!==h&&(a["data-loaded-src"]=h,("decode"in a?a.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(a.parentElement&&a.isConnected){if("empty"!==b&&e(!0),null==c?void 0:c.current){let b=new Event("load");Object.defineProperty(b,"target",{writable:!1,value:a});let d=!1,e=!1;c.current({...b,nativeEvent:b,currentTarget:a,target:a,isDefaultPrevented:()=>d,isPropagationStopped:()=>e,persist:()=>{},preventDefault:()=>{d=!0,b.preventDefault()},stopPropagation:()=>{e=!0,b.stopPropagation()}})}(null==d?void 0:d.current)&&d.current(a)}}))}function r(a){return g.use?{fetchPriority:a}:{fetchpriority:a}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let s=(0,g.forwardRef)((a,b)=>{let{src:c,srcSet:d,sizes:e,height:h,width:i,decoding:j,className:k,style:l,fetchPriority:m,placeholder:n,loading:p,unoptimized:s,fill:t,onLoadRef:u,onLoadingCompleteRef:v,setBlurComplete:w,setShowAltText:x,sizesInput:y,onLoad:z,onError:A,...B}=a,C=(0,g.useCallback)(a=>{a&&(A&&(a.src=a.src),a.complete&&q(a,n,u,v,w,s,y))},[c,n,u,v,w,A,s,y]),D=(0,o.useMergedRef)(b,C);return(0,f.jsx)("img",{...B,...r(m),loading:p,width:i,height:h,decoding:j,"data-nimg":t?"fill":"1",className:k,style:l,sizes:e,srcSet:d,src:c,ref:D,onLoad:a=>{q(a.currentTarget,n,u,v,w,s,y)},onError:a=>{x(!0),"empty"!==n&&w(!0),A&&A(a)}})});function t(a){let{isAppRouter:b,imgAttributes:c}=a,d={as:"image",imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:c.crossOrigin,referrerPolicy:c.referrerPolicy,...r(c.fetchPriority)};return b&&h.default.preload?(h.default.preload(c.src,d),null):(0,f.jsx)(i.default,{children:(0,f.jsx)("link",{rel:"preload",href:c.srcSet?void 0:c.src,...d},"__nimg-"+c.src+c.srcSet+c.sizes)})}let u=(0,g.forwardRef)((a,b)=>{let c=(0,g.useContext)(m.RouterContext),d=(0,g.useContext)(l.ImageConfigContext),e=(0,g.useMemo)(()=>{var a;let b=p||d||k.imageConfigDefault,c=[...b.deviceSizes,...b.imageSizes].sort((a,b)=>a-b),e=b.deviceSizes.sort((a,b)=>a-b),f=null==(a=b.qualities)?void 0:a.sort((a,b)=>a-b);return{...b,allSizes:c,deviceSizes:e,qualities:f}},[d]),{onLoad:h,onLoadingComplete:i}=a,o=(0,g.useRef)(h);(0,g.useEffect)(()=>{o.current=h},[h]);let q=(0,g.useRef)(i);(0,g.useEffect)(()=>{q.current=i},[i]);let[r,u]=(0,g.useState)(!1),[v,w]=(0,g.useState)(!1),{props:x,meta:y}=(0,j.getImgProps)(a,{defaultLoader:n.default,imgConf:e,blurComplete:r,showAltText:v});return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(s,{...x,unoptimized:y.unoptimized,placeholder:y.placeholder,fill:y.fill,onLoadRef:o,onLoadingCompleteRef:q,setBlurComplete:u,setShowAltText:w,sizesInput:a.sizes,ref:b}),y.priority?(0,f.jsx)(t,{isAppRouter:!c,imgAttributes:x}):null]})});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},33095,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return i},getImageProps:function(){return h}});let d=a.r(33354),e=a.r(94915),f=a.r(67161),g=d._(a.r(2305));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},71987,(a,b,c)=>{b.exports=a.r(33095)},46271,86723,65802,14800,74290,1703,20410,91128,77845,a=>{"use strict";let b;a.s(["motion",()=>fc],46271);var c=a.i(72131);let d=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],e=new Set(d),f=a=>180*a/Math.PI,g=a=>i(f(Math.atan2(a[1],a[0]))),h={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:g,rotateZ:g,skewX:a=>f(Math.atan(a[1])),skewY:a=>f(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},i=a=>((a%=360)<0&&(a+=360),a),j=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),k=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),l={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:j,scaleY:k,scale:a=>(j(a)+k(a))/2,rotateX:a=>i(f(Math.atan2(a[6],a[5]))),rotateY:a=>i(f(Math.atan2(-a[2],a[0]))),rotateZ:g,rotate:g,skewX:a=>f(Math.atan(a[4])),skewY:a=>f(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function m(a){return+!!a.includes("scale")}function n(a,b){let c,d;if(!a||"none"===a)return m(b);let e=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(e)c=l,d=e;else{let b=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);c=h,d=b}if(!d)return m(b);let f=c[b],g=d[1].split(",").map(o);return"function"==typeof f?f(g):g[f]}function o(a){return parseFloat(a.trim())}let p=a=>b=>"string"==typeof b&&b.startsWith(a),q=p("--"),r=p("var(--"),s=a=>!!r(a)&&t.test(a.split("/*")[0].trim()),t=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function u({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}let v=(a,b,c)=>a+(b-a)*c;function w(a){return void 0===a||1===a}function x({scale:a,scaleX:b,scaleY:c}){return!w(a)||!w(b)||!w(c)}function y(a){return x(a)||z(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function z(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}function A(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function B(a,b=0,c=1,d,e){a.min=A(a.min,b,c,d,e),a.max=A(a.max,b,c,d,e)}function C(a,{x:b,y:c}){B(a.x,b.translate,b.scale,b.originPoint),B(a.y,c.translate,c.scale,c.originPoint)}function D(a,b){a.min=a.min+b,a.max=a.max+b}function E(a,b,c,d,e=.5){let f=v(a.min,a.max,e);B(a,b,c,f,d)}function F(a,b){E(a.x,b.x,b.scaleX,b.scale,b.originX),E(a.y,b.y,b.scaleY,b.scale,b.originY)}function G(a,b){return u(function(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}(a.getBoundingClientRect(),b))}let H=new Set(["width","height","top","left","right","bottom",...d]),I=(a,b,c)=>c>b?b:c<a?a:c,J={test:a=>"number"==typeof a,parse:parseFloat,transform:a=>a},K={...J,transform:a=>I(0,1,a)},L={...J,default:1},M=a=>({test:b=>"string"==typeof b&&b.endsWith(a)&&1===b.split(" ").length,parse:parseFloat,transform:b=>`${b}${a}`}),N=M("deg"),O=M("%"),P=M("px"),Q=M("vh"),R=M("vw"),S={...O,parse:a=>O.parse(a)/100,transform:a=>O.transform(100*a)},T=a=>b=>b.test(a),U=[J,P,O,N,R,Q,{test:a=>"auto"===a,parse:a=>a}],V=a=>U.find(T(a)),W=()=>{},X=()=>{},Y=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),Z=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,$=a=>a===J||a===P,_=new Set(["x","y","z"]),aa=d.filter(a=>!_.has(a)),ab={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:(a,{transform:b})=>n(b,"x"),y:(a,{transform:b})=>n(b,"y")};ab.translateX=ab.x,ab.translateY=ab.y;let ac=a=>a,ad={},ae=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],af={value:null,addProjectionMetrics:null};function ag(a,b){let c=!1,d=!0,e={delta:0,timestamp:0,isProcessing:!1},f=()=>c=!0,g=ae.reduce((a,c)=>(a[c]=function(a,b){let c=new Set,d=new Set,e=!1,f=!1,g=new WeakSet,h={delta:0,timestamp:0,isProcessing:!1},i=0;function j(b){g.has(b)&&(k.schedule(b),a()),i++,b(h)}let k={schedule:(a,b=!1,f=!1)=>{let h=f&&e?c:d;return b&&g.add(a),h.has(a)||h.add(a),a},cancel:a=>{d.delete(a),g.delete(a)},process:a=>{if(h=a,e){f=!0;return}e=!0,[c,d]=[d,c],c.forEach(j),b&&af.value&&af.value.frameloop[b].push(i),i=0,c.clear(),e=!1,f&&(f=!1,k.process(a))}};return k}(f,b?c:void 0),a),{}),{setup:h,read:i,resolveKeyframes:j,preUpdate:k,update:l,preRender:m,render:n,postRender:o}=g,p=()=>{let f=ad.useManualTiming?e.timestamp:performance.now();c=!1,ad.useManualTiming||(e.delta=d?1e3/60:Math.max(Math.min(f-e.timestamp,40),1)),e.timestamp=f,e.isProcessing=!0,h.process(e),i.process(e),j.process(e),k.process(e),l.process(e),m.process(e),n.process(e),o.process(e),e.isProcessing=!1,c&&b&&(d=!1,a(p))};return{schedule:ae.reduce((b,f)=>{let h=g[f];return b[f]=(b,f=!1,g=!1)=>(!c&&(c=!0,d=!0,e.isProcessing||a(p)),h.schedule(b,f,g)),b},{}),cancel:a=>{for(let b=0;b<ae.length;b++)g[ae[b]].cancel(a)},state:e,steps:g}}let{schedule:ah,cancel:ai,state:aj,steps:ak}=ag("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:ac,!0),al=new Set,am=!1,an=!1,ao=!1;function ap(){if(an){let a=Array.from(al).filter(a=>a.needsMeasurement),b=new Set(a.map(a=>a.element)),c=new Map;b.forEach(a=>{let b=function(a){let b=[];return aa.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}(a);b.length&&(c.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),b.forEach(a=>{a.render();let b=c.get(a);b&&b.forEach(([b,c])=>{a.getValue(b)?.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}an=!1,am=!1,al.forEach(a=>a.complete(ao)),al.clear()}function aq(){al.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(an=!0)})}class ar{constructor(a,b,c,d,e,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(al.add(this),am||(am=!0,ah.read(aq),ah.resolveKeyframes(ap))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:d}=this;if(null===a[0]){let e=d?.get(),f=a[a.length-1];if(void 0!==e)a[0]=e;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),d&&void 0===e&&d.set(a[0])}for(let b=1;b<a.length;b++)a[b]??(a[b]=a[b-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),al.delete(this)}cancel(){"scheduled"===this.state&&(al.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let as=a=>/^0[^.\s]+$/u.test(a),at=a=>Math.round(1e5*a)/1e5,au=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,av=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,aw=(a,b)=>c=>!!("string"==typeof c&&av.test(c)&&c.startsWith(a)||b&&null!=c&&Object.prototype.hasOwnProperty.call(c,b)),ax=(a,b,c)=>d=>{if("string"!=typeof d)return d;let[e,f,g,h]=d.match(au);return{[a]:parseFloat(e),[b]:parseFloat(f),[c]:parseFloat(g),alpha:void 0!==h?parseFloat(h):1}},ay={...J,transform:a=>Math.round(I(0,255,a))},az={test:aw("rgb","red"),parse:ax("red","green","blue"),transform:({red:a,green:b,blue:c,alpha:d=1})=>"rgba("+ay.transform(a)+", "+ay.transform(b)+", "+ay.transform(c)+", "+at(K.transform(d))+")"},aA={test:aw("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:az.transform},aB={test:aw("hsl","hue"),parse:ax("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:d=1})=>"hsla("+Math.round(a)+", "+O.transform(at(b))+", "+O.transform(at(c))+", "+at(K.transform(d))+")"},aC={test:a=>az.test(a)||aA.test(a)||aB.test(a),parse:a=>az.test(a)?az.parse(a):aB.test(a)?aB.parse(a):aA.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?az.transform(a):aB.transform(a),getAnimatableNone:a=>{let b=aC.parse(a);return b.alpha=0,aC.transform(b)}},aD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,aE="number",aF="color",aG=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function aH(a){let b=a.toString(),c=[],d={color:[],number:[],var:[]},e=[],f=0,g=b.replace(aG,a=>(aC.test(a)?(d.color.push(f),e.push(aF),c.push(aC.parse(a))):a.startsWith("var(")?(d.var.push(f),e.push("var"),c.push(a)):(d.number.push(f),e.push(aE),c.push(parseFloat(a))),++f,"${}")).split("${}");return{values:c,split:g,indexes:d,types:e}}function aI(a){return aH(a).values}function aJ(a){let{split:b,types:c}=aH(a),d=b.length;return a=>{let e="";for(let f=0;f<d;f++)if(e+=b[f],void 0!==a[f]){let b=c[f];b===aE?e+=at(a[f]):b===aF?e+=aC.transform(a[f]):e+=a[f]}return e}}let aK=a=>"number"==typeof a?0:aC.test(a)?aC.getAnimatableNone(a):a,aL={test:function(a){return isNaN(a)&&"string"==typeof a&&(a.match(au)?.length||0)+(a.match(aD)?.length||0)>0},parse:aI,createTransformer:aJ,getAnimatableNone:function(a){let b=aI(a);return aJ(a)(b.map(aK))}},aM=new Set(["brightness","contrast","saturate","opacity"]);function aN(a){let[b,c]=a.slice(0,-1).split("(");if("drop-shadow"===b)return a;let[d]=c.match(au)||[];if(!d)return a;let e=c.replace(d,""),f=+!!aM.has(b);return d!==c&&(f*=100),b+"("+f+e+")"}let aO=/\b([a-z-]*)\(.*?\)/gu,aP={...aL,getAnimatableNone:a=>{let b=a.match(aO);return b?b.map(aN).join(" "):a}},aQ={...J,transform:Math.round},aR={borderWidth:P,borderTopWidth:P,borderRightWidth:P,borderBottomWidth:P,borderLeftWidth:P,borderRadius:P,radius:P,borderTopLeftRadius:P,borderTopRightRadius:P,borderBottomRightRadius:P,borderBottomLeftRadius:P,width:P,maxWidth:P,height:P,maxHeight:P,top:P,right:P,bottom:P,left:P,padding:P,paddingTop:P,paddingRight:P,paddingBottom:P,paddingLeft:P,margin:P,marginTop:P,marginRight:P,marginBottom:P,marginLeft:P,backgroundPositionX:P,backgroundPositionY:P,rotate:N,rotateX:N,rotateY:N,rotateZ:N,scale:L,scaleX:L,scaleY:L,scaleZ:L,skew:N,skewX:N,skewY:N,distance:P,translateX:P,translateY:P,translateZ:P,x:P,y:P,z:P,perspective:P,transformPerspective:P,opacity:K,originX:S,originY:S,originZ:P,zIndex:aQ,fillOpacity:K,strokeOpacity:K,numOctaves:aQ},aS={...aR,color:aC,backgroundColor:aC,outlineColor:aC,fill:aC,stroke:aC,borderColor:aC,borderTopColor:aC,borderRightColor:aC,borderBottomColor:aC,borderLeftColor:aC,filter:aP,WebkitFilter:aP},aT=a=>aS[a];function aU(a,b){let c=aT(a);return c!==aP&&(c=aL),c.getAnimatableNone?c.getAnimatableNone(b):void 0}let aV=new Set(["auto","none","0"]);class aW extends ar{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&s(d=d.trim())){let e=function a(b,c,d=1){X(d<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[e,f]=function(a){let b=Z.exec(a);if(!b)return[,];let[,c,d,e]=b;return[`--${c??d}`,e]}(b);if(!e)return;let g=window.getComputedStyle(c).getPropertyValue(e);if(g){let a=g.trim();return Y(a)?parseFloat(a):a}return s(f)?a(f,c,d+1):f}(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!H.has(c)||2!==a.length)return;let[d,e]=a,f=V(d),g=V(e);if(f!==g)if($(f)&&$(g))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else ab[c]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++){var d;(null===a[b]||("number"==typeof(d=a[b])?0===d:null===d||"none"===d||"0"===d||as(d)))&&c.push(b)}c.length&&function(a,b,c){let d,e=0;for(;e<a.length&&!d;){let b=a[e];"string"==typeof b&&!aV.has(b)&&aH(b).values.length&&(d=a[e]),e++}if(d&&c)for(let e of b)a[e]=aU(c,d)}(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ab[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){let{element:a,name:b,unresolvedKeyframes:c}=this;if(!a||!a.current)return;let d=a.getValue(b);d&&d.jump(this.measuredOrigin,!1);let e=c.length-1,f=c[e];c[e]=ab[b](a.measureViewportBox(),window.getComputedStyle(a.current)),null!==f&&void 0===this.finalKeyframe&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([b,c])=>{a.getValue(b).set(c)}),this.resolveNoneKeyframes()}}let aX=a=>!!(a&&a.getVelocity);function aY(){b=void 0}let aZ={now:()=>(void 0===b&&aZ.set(aj.isProcessing||ad.useManualTiming?aj.timestamp:performance.now()),b),set:a=>{b=a,queueMicrotask(aY)}};function a$(a,b){-1===a.indexOf(b)&&a.push(b)}function a_(a,b){let c=a.indexOf(b);c>-1&&a.splice(c,1)}class a0{constructor(){this.subscriptions=[]}add(a){return a$(this.subscriptions,a),()=>a_(this.subscriptions,a)}notify(a,b,c){let d=this.subscriptions.length;if(d)if(1===d)this.subscriptions[0](a,b,c);else for(let e=0;e<d;e++){let d=this.subscriptions[e];d&&d(a,b,c)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let a1={current:void 0};class a2{constructor(a,b={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=a=>{let b=aZ.now();if(this.updatedAt!==b&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let a of this.dependents)a.dirty()},this.hasAnimated=!1,this.setCurrent(a),this.owner=b.owner}setCurrent(a){this.current=a,this.updatedAt=aZ.now(),null===this.canTrackVelocity&&void 0!==a&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,b){this.events[a]||(this.events[a]=new a0);let c=this.events[a].add(b);return"change"===a?()=>{c(),ah.read(()=>{this.events.change.getSize()||this.stop()})}:c}clearListeners(){for(let a in this.events)this.events[a].clear()}attach(a,b){this.passiveEffect=a,this.stopPassiveEffect=b}set(a){this.passiveEffect?this.passiveEffect(a,this.updateAndNotify):this.updateAndNotify(a)}setWithVelocity(a,b,c){this.set(b),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-c}jump(a,b=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,b&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return a1.current&&a1.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var a;let b=aZ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||b-this.updatedAt>30)return 0;let c=Math.min(this.updatedAt-this.prevUpdatedAt,30);return a=parseFloat(this.current)-parseFloat(this.prevFrameValue),c?1e3/c*a:0}start(a){return this.stop(),new Promise(b=>{this.hasAnimated=!0,this.animation=a(b),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function a3(a,b){return new a2(a,b)}let a4=[...U,aC,aL],{schedule:a5}=ag(queueMicrotask,!1),a6={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},a7={};for(let a in a6)a7[a]={isEnabled:b=>a6[a].some(a=>!!b[a])};let a8=()=>({translate:0,scale:1,origin:0,originPoint:0}),a9=()=>({x:a8(),y:a8()}),ba=()=>({min:0,max:0}),bb=()=>({x:ba(),y:ba()}),bc={current:null},bd={current:!1},be=new WeakMap;function bf(a){return null!==a&&"object"==typeof a&&"function"==typeof a.start}function bg(a){return"string"==typeof a||Array.isArray(a)}let bh=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],bi=["initial",...bh];function bj(a){return bf(a.animate)||bi.some(b=>bg(a[b]))}function bk(a){return!!(bj(a)||a.variants)}function bl(a){let b=[{},{}];return a?.values.forEach((a,c)=>{b[0][c]=a.get(),b[1][c]=a.getVelocity()}),b}function bm(a,b,c,d){if("function"==typeof b){let[e,f]=bl(d);b=b(void 0!==c?c:a.custom,e,f)}if("string"==typeof b&&(b=a.variants&&a.variants[b]),"function"==typeof b){let[e,f]=bl(d);b=b(void 0!==c?c:a.custom,e,f)}return b}let bn=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class bo{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:d,blockInitialAnimation:e,visualState:f},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ar,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=aZ.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,ah.render(this.render,!1,!0))};let{latestValues:h,renderState:i}=f;this.latestValues=h,this.baseTarget={...h},this.initialValues=b.initial?{...h}:{},this.renderState=i,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=d,this.options=g,this.blockInitialAnimation=!!e,this.isControllingVariants=bj(b),this.isVariantNode=bk(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:j,...k}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in k){let b=k[a];void 0!==h[a]&&aX(b)&&b.set(h[a])}}mount(a){this.current=a,be.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),bd.current||(bd.current=!0),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||bc.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in this.projection&&this.projection.unmount(),ai(this.notifyUpdate),ai(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}addChild(a){this.children.add(a),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(a)}removeChild(a){this.children.delete(a),this.enteringChildren&&this.enteringChildren.delete(a)}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=e.has(a);d&&this.onBindTransform&&this.onBindTransform();let f=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&ah.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{f(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in a7){let b=a7[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):bb()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,b){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=b;for(let b=0;b<bn.length;b++){let c=bn[b];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);let d=a["on"+c];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=function(a,b,c){for(let d in b){let e=b[d],f=c[d];if(aX(e))a.addValue(d,e);else if(aX(f))a.addValue(d,a3(e,{owner:a}));else if(f!==e)if(a.hasValue(d)){let b=a.getValue(d);!0===b.liveStyle?b.jump(e):b.hasAnimated||b.set(e)}else{let b=a.getStaticValue(d);a.addValue(d,a3(void 0!==b?b:e,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=a3(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){let c=void 0===this.latestValues[a]&&this.current?this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];if(null!=c){if("string"==typeof c&&(Y(c)||as(c)))c=parseFloat(c);else{let d;d=c,!a4.find(T(d))&&aL.test(b)&&(c=aU(a,b))}this.setBaseTarget(a,aX(c)?c.get():c)}return aX(c)?c.get():c}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){let b,{initial:c}=this.props;if("string"==typeof c||"object"==typeof c){let d=bm(this.props,c,this.presenceContext?.custom);d&&(b=d[a])}if(c&&void 0!==b)return b;let d=this.getBaseTargetFromProps(this.props,a);return void 0===d||aX(d)?void 0!==this.initialValues[a]&&void 0===b?void 0:this.baseTarget[a]:d}on(a,b){return this.events[a]||(this.events[a]=new a0),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}scheduleRenderMicrotask(){a5.render(this.render)}}class bp extends bo{constructor(){super(...arguments),this.KeyframeResolver=aW}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;aX(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}let bq=(a,b)=>b&&"number"==typeof a?b.transform(a):a,br={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},bs=d.length;function bt(a,b,c){let{style:f,vars:g,transformOrigin:h}=a,i=!1,j=!1;for(let a in b){let c=b[a];if(e.has(a)){i=!0;continue}if(q(a)){g[a]=c;continue}{let b=bq(c,aR[a]);a.startsWith("origin")?(j=!0,h[a]=b):f[a]=b}}if(!b.transform&&(i||c?f.transform=function(a,b,c){let e="",f=!0;for(let g=0;g<bs;g++){let h=d[g],i=a[h];if(void 0===i)continue;let j=!0;if(!(j="number"==typeof i?i===+!!h.startsWith("scale"):0===parseFloat(i))||c){let a=bq(i,aR[h]);if(!j){f=!1;let b=br[h]||h;e+=`${b}(${a}) `}c&&(b[h]=a)}}return e=e.trim(),c?e=c(b,f?"":e):f&&(e="none"),e}(b,a.transform,c):f.transform&&(f.transform="none")),j){let{originX:a="50%",originY:b="50%",originZ:c=0}=h;f.transformOrigin=`${a} ${b} ${c}`}}function bu(a,{style:b,vars:c},d,e){let f,g=a.style;for(f in b)g[f]=b[f];for(f in e?.applyProjectionStyles(g,d),c)g.setProperty(f,c[f])}let bv={};function bw(a,{layout:b,layoutId:c}){return e.has(a)||a.startsWith("origin")||(b||void 0!==c)&&(!!bv[a]||"opacity"===a)}function bx(a,b,c){let{style:d}=a,e={};for(let f in d)(aX(d[f])||b.style&&aX(b.style[f])||bw(f,a)||c?.getValue(f)?.liveStyle!==void 0)&&(e[f]=d[f]);return e}class by extends bp{constructor(){super(...arguments),this.type="html",this.renderInstance=bu}readValueFromInstance(a,b){if(e.has(b))return this.projection?.isProjecting?m(b):((a,b)=>{let{transform:c="none"}=getComputedStyle(a);return n(c,b)})(a,b);{let c=window.getComputedStyle(a),d=(q(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return G(a,b)}build(a,b,c){bt(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return bx(a,b,c)}}let bz=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),bA={offset:"stroke-dashoffset",array:"stroke-dasharray"},bB={offset:"strokeDashoffset",array:"strokeDasharray"};function bC(a,{attrX:b,attrY:c,attrScale:d,pathLength:e,pathSpacing:f=1,pathOffset:g=0,...h},i,j,k){if(bt(a,h,j),i){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};let{attrs:l,style:m}=a;l.transform&&(m.transform=l.transform,delete l.transform),(m.transform||l.transformOrigin)&&(m.transformOrigin=l.transformOrigin??"50% 50%",delete l.transformOrigin),m.transform&&(m.transformBox=k?.transformBox??"fill-box",delete l.transformBox),void 0!==b&&(l.x=b),void 0!==c&&(l.y=c),void 0!==d&&(l.scale=d),void 0!==e&&function(a,b,c=1,d=0,e=!0){a.pathLength=1;let f=e?bA:bB;a[f.offset]=P.transform(-d);let g=P.transform(b),h=P.transform(c);a[f.array]=`${g} ${h}`}(l,e,f,g,!1)}let bD=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),bE=a=>"string"==typeof a&&"svg"===a.toLowerCase();function bF(a,b,c){let e=bx(a,b,c);for(let c in a)(aX(a[c])||aX(b[c]))&&(e[-1!==d.indexOf(c)?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c]=a[c]);return e}class bG extends bp{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=bb}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(e.has(b)){let a=aT(b);return a&&a.default||0}return b=bD.has(b)?b:bz(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return bF(a,b,c)}build(a,b,c){bC(a,b,this.isSVGTag,c.transformTemplate,c.style)}renderInstance(a,b,c,d){for(let c in bu(a,b,void 0,d),b.attrs)a.setAttribute(bD.has(c)?c:bz(c),b.attrs[c])}mount(a){this.isSVGTag=bE(a.tagName),super.mount(a)}}let bH=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function bI(a){if("string"!=typeof a||a.includes("-"));else if(bH.indexOf(a)>-1||/[A-Z]/u.test(a))return!0;return!1}var bJ=a.i(87924);a.s(["LayoutGroupContext",()=>bK],86723);let bK=(0,c.createContext)({});(0,c.createContext)({strict:!1}),a.s(["MotionConfigContext",()=>bL],65802);let bL=(0,c.createContext)({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"}),bM=(0,c.createContext)({});function bN(a){return Array.isArray(a)?a.join(" "):a}let bO=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function bP(a,b,c){for(let d in b)aX(b[d])||bw(d,c)||(a[d]=b[d])}let bQ=()=>({...bO(),attrs:{}}),bR=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function bS(a){return a.startsWith("while")||a.startsWith("drag")&&"draggable"!==a||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||bR.has(a)}let bT=a=>!bS(a);try{!function(a){"function"==typeof a&&(bT=b=>b.startsWith("on")?!bS(b):a(b))}((()=>{let a=Error("Cannot find module '@emotion/is-prop-valid'");throw a.code="MODULE_NOT_FOUND",a})().default)}catch{}a.s(["PresenceContext",()=>bU],14800);let bU=(0,c.createContext)(null);function bV(a){let b=(0,c.useRef)(null);return null===b.current&&(b.current=a()),b.current}function bW(a){return aX(a)?a.get():a}a.s(["useConstant",()=>bV],74290);let bX=a=>(b,d)=>{let e=(0,c.useContext)(bM),f=(0,c.useContext)(bU),g=()=>(function({scrapeMotionValuesFromProps:a,createRenderState:b},c,d,e){return{latestValues:function(a,b,c,d){let e={},f=d(a,{});for(let a in f)e[a]=bW(f[a]);let{initial:g,animate:h}=a,i=bj(a),j=bk(a);b&&j&&!i&&!1!==a.inherit&&(void 0===g&&(g=b.initial),void 0===h&&(h=b.animate));let k=!!c&&!1===c.initial,l=(k=k||!1===g)?h:g;if(l&&"boolean"!=typeof l&&!bf(l)){let b=Array.isArray(l)?l:[l];for(let c=0;c<b.length;c++){let d=bm(a,b[c]);if(d){let{transitionEnd:a,transition:b,...c}=d;for(let a in c){let b=c[a];if(Array.isArray(b)){let a=k?b.length-1:0;b=b[a]}null!==b&&(e[a]=b)}for(let b in a)e[b]=a[b]}}}return e}(c,d,e,a),renderState:b()}})(a,b,e,f);return d?g():bV(g)},bY=bX({scrapeMotionValuesFromProps:bx,createRenderState:bO}),bZ=bX({scrapeMotionValuesFromProps:bF,createRenderState:bQ}),b$=Symbol.for("motionComponentSymbol");function b_(a){return a&&"object"==typeof a&&Object.prototype.hasOwnProperty.call(a,"current")}let b0="data-"+bz("framerAppearId"),b1=(0,c.createContext)({});a.s(["useIsomorphicLayoutEffect",()=>b2],1703);let b2=c.useEffect;function b3(a,{forwardMotionProps:b=!1}={},d,e){d&&function(a){for(let b in a)a7[b]={...a7[b],...a[b]}}(d);let f=bI(a)?bZ:bY;function g(d,e){var g;let h,i={...(0,c.useContext)(bL),...d,layoutId:function({layoutId:a}){let b=(0,c.useContext)(bK).id;return b&&void 0!==a?b+"-"+a:a}(d)},{isStatic:j}=i,k=function(a){let{initial:b,animate:d}=function(a,b){if(bj(a)){let{initial:b,animate:c}=a;return{initial:!1===b||bg(b)?b:void 0,animate:bg(c)?c:void 0}}return!1!==a.inherit?b:{}}(a,(0,c.useContext)(bM));return(0,c.useMemo)(()=>({initial:b,animate:d}),[bN(b),bN(d)])}(d),l=f(d,j);return(0,bJ.jsxs)(bM.Provider,{value:k,children:[h&&k.visualElement?(0,bJ.jsx)(h,{visualElement:k.visualElement,...i}):null,function(a,b,d,{latestValues:e},f,g=!1){let h=(bI(a)?function(a,b,d,e){let f=(0,c.useMemo)(()=>{let c=bQ();return bC(c,b,bE(e),a.transformTemplate,a.style),{...c.attrs,style:{...c.style}}},[b]);if(a.style){let b={};bP(b,a.style,a),f.style={...b,...f.style}}return f}:function(a,b){let d={},e=function(a,b){let d=a.style||{},e={};return bP(e,d,a),Object.assign(e,function({transformTemplate:a},b){return(0,c.useMemo)(()=>{let c=bO();return bt(c,b,a),Object.assign({},c.vars,c.style)},[b])}(a,b)),e}(a,b);return a.drag&&!1!==a.dragListener&&(d.draggable=!1,e.userSelect=e.WebkitUserSelect=e.WebkitTouchCallout="none",e.touchAction=!0===a.drag?"none":`pan-${"x"===a.drag?"y":"x"}`),void 0===a.tabIndex&&(a.onTap||a.onTapStart||a.whileTap)&&(d.tabIndex=0),d.style=e,d})(b,e,f,a),i=function(a,b,c){let d={};for(let e in a)("values"!==e||"object"!=typeof a.values)&&(bT(e)||!0===c&&bS(e)||!b&&!bS(e)||a.draggable&&e.startsWith("onDrag"))&&(d[e]=a[e]);return d}(b,"string"==typeof a,g),j=a!==c.Fragment?{...i,...h,ref:d}:{},{children:k}=b,l=(0,c.useMemo)(()=>aX(k)?k.get():k,[k]);return(0,c.createElement)(a,{...j,children:l})}(a,d,(g=k.visualElement,(0,c.useCallback)(a=>{a&&l.onMount&&l.onMount(a),g&&(a?g.mount(a):g.unmount()),e&&("function"==typeof e?e(a):b_(e)&&(e.current=a))},[g,e])),l,j,b)]})}g.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;let h=(0,c.forwardRef)(g);return h[b$]=a,h}function b4(a,b,c){let d=a.getProps();return bm(d,b,void 0!==c?c:d.custom,a)}function b5(a,b){return a?.[b]??a?.default??a}let b6=a=>Array.isArray(a);function b7(a,b){let c=a.getValue("willChange");if(aX(c)&&c.add)return c.add(b);if(!c&&ad.WillChange){let c=new ad.WillChange("auto");a.addValue("willChange",c),c.add(b)}}function b8(a){a.duration=0,a.type}let b9=(a,b)=>c=>b(a(c)),ca=(...a)=>a.reduce(b9),cb=a=>1e3*a,cc={layout:0,mainThread:0,waapi:0};function cd(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function ce(a,b){return c=>c>0?b:a}let cf=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},cg=[aA,az,aB];function ch(a){let b=cg.find(b=>b.test(a));if(W(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!b)return!1;let c=b.parse(a);return b===aB&&(c=function({hue:a,saturation:b,lightness:c,alpha:d}){a/=360,c/=100;let e=0,f=0,g=0;if(b/=100){let d=c<.5?c*(1+b):c+b-c*b,h=2*c-d;e=cd(h,d,a+1/3),f=cd(h,d,a),g=cd(h,d,a-1/3)}else e=f=g=c;return{red:Math.round(255*e),green:Math.round(255*f),blue:Math.round(255*g),alpha:d}}(c)),c}let ci=(a,b)=>{let c=ch(a),d=ch(b);if(!c||!d)return ce(a,b);let e={...c};return a=>(e.red=cf(c.red,d.red,a),e.green=cf(c.green,d.green,a),e.blue=cf(c.blue,d.blue,a),e.alpha=v(c.alpha,d.alpha,a),az.transform(e))},cj=new Set(["none","hidden"]);function ck(a,b){return c=>v(a,b,c)}function cl(a){return"number"==typeof a?ck:"string"==typeof a?s(a)?ce:aC.test(a)?ci:co:Array.isArray(a)?cm:"object"==typeof a?aC.test(a)?ci:cn:ce}function cm(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>cl(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function cn(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=cl(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let co=(a,b)=>{let c=aL.createTransformer(b),d=aH(a),e=aH(b);return d.indexes.var.length===e.indexes.var.length&&d.indexes.color.length===e.indexes.color.length&&d.indexes.number.length>=e.indexes.number.length?cj.has(a)&&!e.values.length||cj.has(b)&&!d.values.length?function(a,b){return cj.has(a)?c=>c<=0?a:b:c=>c>=1?b:a}(a,b):ca(cm(function(a,b){let c=[],d={color:0,var:0,number:0};for(let e=0;e<b.values.length;e++){let f=b.types[e],g=a.indexes[f][d[f]],h=a.values[g]??0;c[e]=h,d[f]++}return c}(d,e),e.values),c):(W(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),ce(a,b))};function cp(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?v(a,b,c):cl(a)(a,b)}let cq=a=>{let b=({timestamp:b})=>a(b);return{start:(a=!0)=>ah.update(b,a),stop:()=>ai(b),now:()=>aj.isProcessing?aj.timestamp:aZ.now()}},cr=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=Math.round(1e4*a(b/(e-1)))/1e4+", ";return`linear(${d.substring(0,d.length-2)})`};function cs(a){let b=0,c=a.next(b);for(;!c.done&&b<2e4;)b+=50,c=a.next(b);return b>=2e4?1/0:b}function ct(a,b,c){var d,e;let f=Math.max(b-5,0);return d=c-a(f),(e=b-f)?1e3/e*d:0}let cu={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function cv(a,b){return a*Math.sqrt(1-b*b)}let cw=["duration","bounce"],cx=["stiffness","damping","mass"];function cy(a,b){return b.some(b=>void 0!==a[b])}function cz(a=cu.visualDuration,b=cu.bounce){let c,d="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:b}:a,{restSpeed:e,restDelta:f}=d,g=d.keyframes[0],h=d.keyframes[d.keyframes.length-1],i={done:!1,value:g},{stiffness:j,damping:k,mass:l,duration:m,velocity:n,isResolvedFromDuration:o}=function(a){let b={velocity:cu.velocity,stiffness:cu.stiffness,damping:cu.damping,mass:cu.mass,isResolvedFromDuration:!1,...a};if(!cy(a,cx)&&cy(a,cw))if(a.visualDuration){let c=2*Math.PI/(1.2*a.visualDuration),d=c*c,e=2*I(.05,1,1-(a.bounce||0))*Math.sqrt(d);b={...b,mass:cu.mass,stiffness:d,damping:e}}else{let c=function({duration:a=cu.duration,bounce:b=cu.bounce,velocity:c=cu.velocity,mass:d=cu.mass}){let e,f;W(a<=cb(cu.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let g=1-b;g=I(cu.minDamping,cu.maxDamping,g),a=I(cu.minDuration,cu.maxDuration,a/1e3),g<1?(e=b=>{let d=b*g,e=d*a;return .001-(d-c)/cv(b,g)*Math.exp(-e)},f=b=>{let d=b*g*a,f=Math.pow(g,2)*Math.pow(b,2)*a,h=Math.exp(-d),i=cv(Math.pow(b,2),g);return(d*c+c-f)*h*(-e(b)+.001>0?-1:1)/i}):(e=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),f=b=>a*a*(c-b)*Math.exp(-b*a));let h=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(e,f,5/a);if(a=cb(a),isNaN(h))return{stiffness:cu.stiffness,damping:cu.damping,duration:a};{let b=Math.pow(h,2)*d;return{stiffness:b,damping:2*g*Math.sqrt(d*b),duration:a}}}(a);(b={...b,...c,mass:cu.mass}).isResolvedFromDuration=!0}return b}({...d,velocity:-((d.velocity||0)/1e3)}),p=n||0,q=k/(2*Math.sqrt(j*l)),r=h-g,s=Math.sqrt(j/l)/1e3,t=5>Math.abs(r);if(e||(e=t?cu.restSpeed.granular:cu.restSpeed.default),f||(f=t?cu.restDelta.granular:cu.restDelta.default),q<1){let a=cv(s,q);c=b=>h-Math.exp(-q*s*b)*((p+q*s*r)/a*Math.sin(a*b)+r*Math.cos(a*b))}else if(1===q)c=a=>h-Math.exp(-s*a)*(r+(p+s*r)*a);else{let a=s*Math.sqrt(q*q-1);c=b=>{let c=Math.exp(-q*s*b),d=Math.min(a*b,300);return h-c*((p+q*s*r)*Math.sinh(d)+a*r*Math.cosh(d))/a}}let u={calculatedDuration:o&&m||null,next:a=>{let b=c(a);if(o)i.done=a>=m;else{let d=0===a?p:0;q<1&&(d=0===a?cb(p):ct(c,a,b));let g=Math.abs(h-b)<=f;i.done=Math.abs(d)<=e&&g}return i.value=i.done?h:b,i},toString:()=>{let a=Math.min(cs(u),2e4),b=cr(b=>u.next(a*b).value,a,30);return a+"ms "+b},toTransition:()=>{}};return u}function cA({keyframes:a,velocity:b=0,power:c=.8,timeConstant:d=325,bounceDamping:e=10,bounceStiffness:f=500,modifyTarget:g,min:h,max:i,restDelta:j=.5,restSpeed:k}){let l,m,n=a[0],o={done:!1,value:n},p=c*b,q=n+p,r=void 0===g?q:g(q);r!==q&&(p=r-n);let s=a=>-p*Math.exp(-a/d),t=a=>r+s(a),u=a=>{let b=s(a),c=t(a);o.done=Math.abs(b)<=j,o.value=o.done?r:c},v=a=>{let b;if(b=o.value,void 0!==h&&b<h||void 0!==i&&b>i){var c;l=a,m=cz({keyframes:[o.value,(c=o.value,void 0===h?i:void 0===i||Math.abs(h-c)<Math.abs(i-c)?h:i)],velocity:ct(t,a,o.value),damping:e,stiffness:f,restDelta:j,restSpeed:k})}};return v(0),{calculatedDuration:null,next:a=>{let b=!1;return(m||void 0!==l||(b=!0,u(a),v(a)),void 0!==l&&a>=l)?m.next(a-l):(b||u(a),o)}}}cz.applyToOptions=a=>{let b=function(a,b=100,c){let d=c({...a,keyframes:[0,b]}),e=Math.min(cs(d),2e4);return{type:"keyframes",ease:a=>d.next(e*a).value/b,duration:e/1e3}}(a,100,cz);return a.ease=b.ease,a.duration=cb(b.duration),a.type="keyframes",a};let cB=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function cC(a,b,c,d){return a===b&&c===d?ac:e=>0===e||1===e?e:cB(function(a,b,c,d,e){let f,g,h=0;do(f=cB(g=b+(c-b)/2,d,e)-a)>0?c=g:b=g;while(Math.abs(f)>1e-7&&++h<12)return g}(e,0,1,a,c),b,d)}let cD=cC(.42,0,1,1),cE=cC(0,0,.58,1),cF=cC(.42,0,.58,1),cG=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2,cH=a=>b=>1-a(1-b),cI=cC(.33,1.53,.69,.99),cJ=cH(cI),cK=cG(cJ),cL=a=>(a*=2)<1?.5*cJ(a):.5*(2-Math.pow(2,-10*(a-1))),cM=a=>1-Math.sin(Math.acos(a)),cN=cH(cM),cO=cG(cM),cP=a=>Array.isArray(a)&&"number"==typeof a[0],cQ={linear:ac,easeIn:cD,easeInOut:cF,easeOut:cE,circIn:cM,circInOut:cO,circOut:cN,backIn:cJ,backInOut:cK,backOut:cI,anticipate:cL},cR=a=>{if(cP(a)){X(4===a.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[b,c,d,e]=a;return cC(b,c,d,e)}return"string"==typeof a?(X(void 0!==cQ[a],`Invalid easing type '${a}'`,"invalid-easing-type"),cQ[a]):a},cS=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d};function cT({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){var e;let f=Array.isArray(d)&&"number"!=typeof d[0]?d.map(cR):cR(d),g={done:!1,value:b[0]},h=function(a,b,{clamp:c=!0,ease:d,mixer:e}={}){let f=a.length;if(X(f===b.length,"Both input and output ranges must be the same length","range-length"),1===f)return()=>b[0];if(2===f&&b[0]===b[1])return()=>b[1];let g=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),b=[...b].reverse());let h=function(a,b,c){let d=[],e=c||ad.mix||cp,f=a.length-1;for(let c=0;c<f;c++){let f=e(a[c],a[c+1]);b&&(f=ca(Array.isArray(b)?b[c]||ac:b,f)),d.push(f)}return d}(b,d,e),i=h.length,j=c=>{if(g&&c<a[0])return b[0];let d=0;if(i>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=cS(a[d],a[d+1],c);return h[d](e)};return c?b=>j(I(a[0],a[f-1],b)):j}((e=c&&c.length===b.length?c:function(a){let b=[0];return!function(a,b){let c=a[a.length-1];for(let d=1;d<=b;d++){let e=cS(0,b,d);a.push(v(c,1,e))}}(b,a.length-1),b}(b),e.map(b=>b*a)),b,{ease:Array.isArray(f)?f:b.map(()=>f||cF).splice(0,b.length-1)});return{calculatedDuration:a,next:b=>(g.value=h(b),g.done=b>=a,g)}}let cU=a=>null!==a;function cV(a,{repeat:b,repeatType:c="loop"},d,e=1){let f=a.filter(cU),g=e<0||b&&"loop"!==c&&b%2==1?0:f.length-1;return g&&void 0!==d?d:f[g]}let cW={decay:cA,inertia:cA,tween:cT,keyframes:cT,spring:cz};function cX(a){"string"==typeof a.type&&(a.type=cW[a.type])}class cY{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,b){return this.finished.then(a,b)}}let cZ=a=>a/100;class c$ extends cY{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:a}=this.options;a&&a.updatedAt!==aZ.now()&&this.tick(aZ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},cc.mainThread++,this.options=a,this.initAnimation(),this.play(),!1===a.autoplay&&this.pause()}initAnimation(){let{options:a}=this;cX(a);let{type:b=cT,repeat:c=0,repeatDelay:d=0,repeatType:e,velocity:f=0}=a,{keyframes:g}=a,h=b||cT;h!==cT&&"number"!=typeof g[0]&&(this.mixKeyframes=ca(cZ,cp(g[0],g[1])),g=[0,100]);let i=h({...a,keyframes:g});"mirror"===e&&(this.mirroredGenerator=h({...a,keyframes:[...g].reverse(),velocity:-f})),null===i.calculatedDuration&&(i.calculatedDuration=cs(i));let{calculatedDuration:j}=i;this.calculatedDuration=j,this.resolvedDuration=j+d,this.totalDuration=this.resolvedDuration*(c+1)-d,this.generator=i}updateTime(a){let b=Math.round(a-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=b}tick(a,b=!1){let{generator:c,totalDuration:d,mixKeyframes:e,mirroredGenerator:f,resolvedDuration:g,calculatedDuration:h}=this;if(null===this.startTime)return c.next(0);let{delay:i=0,keyframes:j,repeat:k,repeatType:l,repeatDelay:m,type:n,onUpdate:o,finalKeyframe:p}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-d/this.speed,this.startTime)),b?this.currentTime=a:this.updateTime(a);let q=this.currentTime-i*(this.playbackSpeed>=0?1:-1),r=this.playbackSpeed>=0?q<0:q>d;this.currentTime=Math.max(q,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let s=this.currentTime,t=c;if(k){let a=Math.min(this.currentTime,d)/g,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,k+1))%2&&("reverse"===l?(c=1-c,m&&(c-=m/g)):"mirror"===l&&(t=f)),s=I(0,1,c)*g}let u=r?{done:!1,value:j[0]}:t.next(s);e&&(u.value=e(u.value));let{done:v}=u;r||null===h||(v=this.playbackSpeed>=0?this.currentTime>=d:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&v);return w&&n!==cA&&(u.value=cV(j,this.options,p,this.speed)),o&&o(u.value),w&&this.finish(),u}then(a,b){return this.finished.then(a,b)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(a){a=cb(a),this.currentTime=a,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(aZ.now());let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:a=cq,startTime:b}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),this.options.onPlay?.();let c=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=c):null!==this.holdTime?this.startTime=c-this.holdTime:this.startTime||(this.startTime=b??c),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(aZ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,cc.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),a.observe(this)}}function c_(a){let b;return()=>(void 0===b&&(b=a()),b)}let c0=c_(()=>void 0!==window.ScrollTimeline),c1={},c2=function(a,b){let c=c_(a);return()=>c1[b]??c()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing"),c3=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`,c4={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:c3([0,.65,.55,1]),circOut:c3([.55,0,1,.45]),backIn:c3([.31,.01,.66,-.59]),backOut:c3([.33,1.53,.69,.99])};function c5(a){return"function"==typeof a&&"applyToOptions"in a}class c6 extends cY{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;let{element:b,name:c,keyframes:d,pseudoElement:e,allowFlatten:f=!1,finalKeyframe:g,onComplete:h}=a;this.isPseudoElement=!!e,this.allowFlatten=f,this.options=a,X("string"!=typeof a.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let i=function({type:a,...b}){return c5(a)&&c2()?a.applyToOptions(b):(b.duration??(b.duration=300),b.ease??(b.ease="easeOut"),b)}(a);this.animation=function(a,b,c,{delay:d=0,duration:e=300,repeat:f=0,repeatType:g="loop",ease:h="easeOut",times:i}={},j){let k={[b]:c};i&&(k.offset=i);let l=function a(b,c){if(b)return"function"==typeof b?c2()?cr(b,c):"ease-out":cP(b)?c3(b):Array.isArray(b)?b.map(b=>a(b,c)||c4.easeOut):c4[b]}(h,e);Array.isArray(l)&&(k.easing=l),af.value&&cc.waapi++;let m={delay:d,duration:e,easing:Array.isArray(l)?"linear":l,fill:"both",iterations:f+1,direction:"reverse"===g?"alternate":"normal"};j&&(m.pseudoElement=j);let n=a.animate(k,m);return af.value&&n.finished.finally(()=>{cc.waapi--}),n}(b,c,d,i,e),!1===i.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!e){let a=cV(d,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(a):function(a,b,c){b.startsWith("--")?a.style.setProperty(b,c):a.style[b]=c}(b,c,a),this.animation.cancel()}h?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:a}=this;"idle"!==a&&"finished"!==a&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(a){this.finishedTime=null,this.animation.currentTime=cb(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:b}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,a&&c0())?(this.animation.timeline=a,ac):b(this)}}let c7={anticipate:cL,backInOut:cK,circInOut:cO};class c8 extends c6{constructor(a){!function(a){"string"==typeof a.ease&&a.ease in c7&&(a.ease=c7[a.ease])}(a),cX(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){let{motionValue:b,onUpdate:c,onComplete:d,element:e,...f}=this.options;if(!b)return;if(void 0!==a)return void b.set(a);let g=new c$({...f,autoplay:!1}),h=cb(this.finishedTime??this.time);b.setWithVelocity(g.sample(h-10).value,g.sample(h).value,10),g.stop()}}let c9=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(aL.test(a)||"0"===a)&&!a.startsWith("url(")),da=new Set(["opacity","clipPath","filter","transform"]),db=c_(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class dc extends cY{constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:f="loop",keyframes:g,name:h,motionValue:i,element:j,...k}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=aZ.now();let l={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:f,name:h,motionValue:i,element:j,...k},m=j?.KeyframeResolver||ar;this.keyframeResolver=new m(g,(a,b,c)=>this.onKeyframesResolved(a,b,l,!c),h,i,j),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(a,b,c,d){this.keyframeResolver=void 0;let{name:e,type:f,velocity:g,delay:h,isHandoff:i,onUpdate:j}=c;this.resolvedAt=aZ.now(),!function(a,b,c,d){let e=a[0];if(null===e)return!1;if("display"===b||"visibility"===b)return!0;let f=a[a.length-1],g=c9(e,b),h=c9(f,b);return W(g===h,`You are trying to animate ${b} from "${e}" to "${f}". "${g?f:e}" is not an animatable value.`,"value-not-animatable"),!!g&&!!h&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||c5(c))&&d)}(a,e,f,g)&&((ad.instantAnimations||!h)&&j?.(cV(a,c,b)),a[0]=a[a.length-1],b8(c),c.repeat=0);let k={startTime:d?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:b,...c,keyframes:a},l=!i&&function(a){let{motionValue:b,name:c,repeatDelay:d,repeatType:e,damping:f,type:g}=a;if(!(b?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:h,transformTemplate:i}=b.owner.getProps();return db()&&c&&da.has(c)&&("transform"!==c||!i)&&!h&&!d&&"mirror"!==e&&0!==f&&"inertia"!==g}(k)?new c8({...k,element:k.motionValue.owner.current}):new c$(k);l.finished.then(()=>this.notifyFinished()).catch(ac),this.pendingTimeline&&(this.stopTimeline=l.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=l}get finished(){return this._animation?this.animation.finished:this._finished}then(a,b){return this.finished.finally(a).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ao=!0,aq(),ap(),ao=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let dd=a=>null!==a,de={type:"spring",stiffness:500,damping:25,restSpeed:10},df={type:"keyframes",duration:.8},dg={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},dh=(a,b,c,d={},f,g)=>h=>{let i=b5(d,a)||{},j=i.delay||d.delay||0,{elapsed:k=0}=d;k-=cb(j);let l={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...i,delay:-k,onUpdate:a=>{b.set(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{h(),i.onComplete&&i.onComplete()},name:a,motionValue:b,element:g?void 0:f};!function({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}(i)&&Object.assign(l,((a,{keyframes:b})=>b.length>2?df:e.has(a)?a.startsWith("scale")?{type:"spring",stiffness:550,damping:0===b[1]?2*Math.sqrt(550):30,restSpeed:10}:de:dg)(a,l)),l.duration&&(l.duration=cb(l.duration)),l.repeatDelay&&(l.repeatDelay=cb(l.repeatDelay)),void 0!==l.from&&(l.keyframes[0]=l.from);let m=!1;if(!1!==l.type&&(0!==l.duration||l.repeatDelay)||(b8(l),0===l.delay&&(m=!0)),(ad.instantAnimations||ad.skipAnimations)&&(m=!0,b8(l),l.delay=0),l.allowFlatten=!i.type&&!i.ease,m&&!g&&void 0!==b.get()){let a=function(a,{repeat:b,repeatType:c="loop"},d){let e=a.filter(dd),f=b&&"loop"!==c&&b%2==1?0:e.length-1;return e[f]}(l.keyframes,i);if(void 0!==a)return void ah.update(()=>{l.onUpdate(a),l.onComplete()})}return i.isSync?new c$(l):new dc(l)};function di(a,b,{delay:c=0,transitionOverride:d,type:e}={}){let{transition:f=a.getDefaultTransition(),transitionEnd:g,...h}=b;d&&(f=d);let i=[],j=e&&a.animationState&&a.animationState.getState()[e];for(let b in h){let d=a.getValue(b,a.latestValues[b]??null),e=h[b];if(void 0===e||j&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(j,b))continue;let g={delay:c,...b5(f||{},b)},k=d.get();if(void 0!==k&&!d.isAnimating&&!Array.isArray(e)&&e===k&&!g.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let c=a.props[b0];if(c){let a=window.MotionHandoffAnimation(c,b,ah);null!==a&&(g.startTime=a,l=!0)}}b7(a,b),d.start(dh(b,d,e,a.shouldReduceMotion&&H.has(b)?{type:!1}:g,a,l));let m=d.animation;m&&i.push(m)}return g&&Promise.all(i).then(()=>{ah.update(()=>{g&&function(a,b){let{transitionEnd:c={},transition:d={},...e}=b4(a,b)||{};for(let b in e={...e,...c}){var f;let c=b6(f=e[b])?f[f.length-1]||0:f;a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,a3(c))}}(a,g)})}),i}function dj(a,b,c,d=0,e=1){let f=Array.from(a).sort((a,b)=>a.sortNodePosition(b)).indexOf(b),g=a.size,h=(g-1)*d;return"function"==typeof c?c(f,g):1===e?f*d:h-f*d}function dk(a,b,c={}){let d=b4(a,b,"exit"===c.type?a.presenceContext?.custom:void 0),{transition:e=a.getDefaultTransition()||{}}=d||{};c.transitionOverride&&(e=c.transitionOverride);let f=d?()=>Promise.all(di(a,d,c)):()=>Promise.resolve(),g=a.variantChildren&&a.variantChildren.size?(d=0)=>{let{delayChildren:f=0,staggerChildren:g,staggerDirection:h}=e;return function(a,b,c=0,d=0,e=0,f=1,g){let h=[];for(let i of a.variantChildren)i.notify("AnimationStart",b),h.push(dk(i,b,{...g,delay:c+("function"==typeof d?0:d)+dj(a.variantChildren,i,d,e,f)}).then(()=>i.notify("AnimationComplete",b)));return Promise.all(h)}(a,b,d,f,g,h,c)}:()=>Promise.resolve(),{when:h}=e;if(!h)return Promise.all([f(),g(c.delay)]);{let[a,b]="beforeChildren"===h?[f,g]:[g,f];return a().then(()=>b())}}function dl(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}let dm=bi.length,dn=[...bh].reverse(),dp=bh.length;function dq(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function dr(){return{animate:dq(!0),whileInView:dq(),whileHover:dq(),whileTap:dq(),whileDrag:dq(),whileFocus:dq(),exit:dq()}}class ds{constructor(a){this.isMounted=!1,this.node=a}update(){}}let dt=0,du={x:!1,y:!1};function dv(a,b,c,d={passive:!0}){return a.addEventListener(b,c,d),()=>a.removeEventListener(b,c)}let dw=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary;function dx(a){return{point:{x:a.pageX,y:a.pageY}}}function dy(a,b,c,d){return dv(a,b,a=>dw(a)&&c(a,dx(a)),d)}function dz(a){return a.max-a.min}function dA(a,b,c,d=.5){a.origin=d,a.originPoint=v(b.min,b.max,a.origin),a.scale=dz(c)/dz(b),a.translate=v(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function dB(a,b,c,d){dA(a.x,b.x,c.x,d?d.originX:void 0),dA(a.y,b.y,c.y,d?d.originY:void 0)}function dC(a,b,c){a.min=c.min+b.min,a.max=a.min+dz(b)}function dD(a,b,c){a.min=b.min-c.min,a.max=a.min+dz(b)}function dE(a,b,c){dD(a.x,b.x,c.x),dD(a.y,b.y,c.y)}function dF(a){return[a("x"),a("y")]}let dG=({current:a})=>a?a.ownerDocument.defaultView:null,dH=(a,b)=>Math.abs(a-b);class dI{constructor(a,b,{transformPagePoint:c,contextWindow:d=window,dragSnapToOrigin:e=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=dL(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=function(a,b){return Math.sqrt(dH(a.x,b.x)**2+dH(a.y,b.y)**2)}(a.offset,{x:0,y:0})>=this.distanceThreshold;if(!b&&!c)return;let{point:d}=a,{timestamp:e}=aj;this.history.push({...d,timestamp:e});let{onStart:f,onMove:g}=this.handlers;b||(f&&f(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=dJ(b,this.transformPagePoint),ah.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=dL("pointercancel"===a.type?this.lastMoveEventInfo:dJ(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!dw(a))return;this.dragSnapToOrigin=e,this.handlers=b,this.transformPagePoint=c,this.distanceThreshold=f,this.contextWindow=d||window;let g=dJ(dx(a),this.transformPagePoint),{point:h}=g,{timestamp:i}=aj;this.history=[{...h,timestamp:i}];let{onSessionStart:j}=b;j&&j(a,dL(g,this.history)),this.removeListeners=ca(dy(this.contextWindow,"pointermove",this.handlePointerMove),dy(this.contextWindow,"pointerup",this.handlePointerUp),dy(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),ai(this.updatePoint)}}function dJ(a,b){return b?{point:b(a.point)}:a}function dK(a,b){return{x:a.x-b.x,y:a.y-b.y}}function dL({point:a},b){return{point:a,delta:dK(a,dM(b)),offset:dK(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,e=dM(a);for(;c>=0&&(d=a[c],!(e.timestamp-d.timestamp>cb(.1)));)c--;if(!d)return{x:0,y:0};let f=(e.timestamp-d.timestamp)/1e3;if(0===f)return{x:0,y:0};let g={x:(e.x-d.x)/f,y:(e.y-d.y)/f};return g.x===1/0&&(g.x=0),g.y===1/0&&(g.y=0),g}(b,.1)}}function dM(a){return a[a.length-1]}function dN(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function dO(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function dP(a,b,c){return{min:dQ(a,b),max:dQ(a,c)}}function dQ(a,b){return"number"==typeof a?a:a[b]||0}let dR=new WeakMap;class dS{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=bb(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=a}start(a,{snapToCursor:b=!1,distanceThreshold:c}={}){let{presenceContext:d}=this.visualElement;if(d&&!1===d.isPresent)return;let e=a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor(dx(a).point)},f=(a,b)=>{let{drag:c,dragPropagation:d,onDragStart:e}=this.getProps();if(c&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(a){if("x"===a||"y"===a)if(du[a])return null;else return du[a]=!0,()=>{du[a]=!1};return du.x||du.y?null:(du.x=du.y=!0,()=>{du.x=du.y=!1})}(c),!this.openDragLock))return;this.latestPointerEvent=a,this.latestPanInfo=b,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),dF(a=>{let b=this.getAxisMotionValue(a).get()||0;if(O.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=dz(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),e&&ah.postRender(()=>e(a,b)),b7(this.visualElement,"transform");let{animationState:f}=this.visualElement;f&&f.setActive("whileDrag",!0)},g=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b;let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},h=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b,this.stop(a,b),this.latestPointerEvent=null,this.latestPanInfo=null},i=()=>dF(a=>"paused"===this.getAnimationState(a)&&this.getAxisMotionValue(a).animation?.play()),{dragSnapToOrigin:j}=this.getProps();this.panSession=new dI(a,{onSessionStart:e,onStart:f,onMove:g,onSessionEnd:h,resumeAnimation:i},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:j,distanceThreshold:c,contextWindow:dG(this.visualElement)})}stop(a,b){let c=a||this.latestPointerEvent,d=b||this.latestPanInfo,e=this.isDragging;if(this.cancel(),!e||!d||!c)return;let{velocity:f}=d;this.startAnimation(f);let{onDragEnd:g}=this.getProps();g&&ah.postRender(()=>g(c,d))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!dT(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=function(a,{min:b,max:c},d){return void 0!==b&&a<b?a=d?v(b,a,d.min):Math.max(a,b):void 0!==c&&a>c&&(a=d?v(c,a,d.max):Math.min(a,c)),a}(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){let{dragConstraints:a,dragElastic:b}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,d=this.constraints;a&&b_(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&c?this.constraints=function(a,{top:b,left:c,bottom:d,right:e}){return{x:dN(a.x,c,e),y:dN(a.y,b,d)}}(c.layoutBox,a):this.constraints=!1,this.elastic=function(a=.35){return!1===a?a=0:!0===a&&(a=.35),{x:dP(a,"left","right"),y:dP(a,"top","bottom")}}(b),d!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&dF(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=function(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}(c.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){var a;let{dragConstraints:b,onMeasureDragConstraints:c}=this.getProps();if(!b||!b_(b))return!1;let d=b.current;X(null!==d,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:e}=this.visualElement;if(!e||!e.layout)return!1;let f=function(a,b,c){let d=G(a,c),{scroll:e}=b;return e&&(D(d.x,e.offset.x),D(d.y,e.offset.y)),d}(d,e.root,this.visualElement.getTransformPagePoint()),g=(a=e.layout.layoutBox,{x:dO(a.x,f.x),y:dO(a.y,f.y)});if(c){let a=c(function({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}(g));this.hasMutatedConstraints=!!a,a&&(g=u(a))}return g}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all(dF(g=>{if(!dT(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return b7(this.visualElement,a),c.start(dh(a,c,0,b,this.visualElement,!1))}stopAnimation(){dF(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){dF(a=>this.getAxisMotionValue(a).animation?.pause())}getAnimationState(a){return this.getAxisMotionValue(a).animation?.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){dF(b=>{let{drag:c}=this.getProps();if(!dT(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,e=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:f}=d.layout.layoutBox[b];e.set(a[b]-v(c,f,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!b_(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};dF(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=function(a,b){let c=.5,d=dz(a),e=dz(b);return e>d?c=cS(b.min,b.max-d,a.min):d>e&&(c=cS(a.min,a.max-e,b.min)),I(0,1,c)}({min:c,max:c},this.constraints[a])}});let{transformTemplate:e}=this.visualElement.getProps();this.visualElement.current.style.transform=e?e({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),dF(b=>{if(!dT(b,a,null))return;let c=this.getAxisMotionValue(b),{min:e,max:f}=this.constraints[b];c.set(v(e,f,d[b]))})}addListeners(){if(!this.visualElement.current)return;dR.set(this.visualElement,this);let a=dy(this.visualElement.current,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),b=()=>{let{dragConstraints:a}=this.getProps();b_(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",b);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),ah.read(b);let e=dv(window,"resize",()=>this.scalePositionWithinConstraints()),f=c.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&(dF(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{e(),a(),d(),f&&f()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=.35,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function dT(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}let dU=a=>(b,c)=>{a&&ah.postRender(()=>a(b,c))};var dV=c;function dW(a=!0){let b=(0,c.useContext)(bU);if(null===b)return[!0,null];let{isPresent:d,onExitComplete:e,register:f}=b,g=(0,c.useId)();(0,c.useEffect)(()=>{if(a)return f(g)},[a]);let h=(0,c.useCallback)(()=>a&&e&&e(g),[g,e,a]);return!d&&e?[!1,h]:[!0]}a.s(["usePresence",()=>dW],20410);let dX={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function dY(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let dZ={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!P.test(a))return a;else a=parseFloat(a);let c=dY(a,b.target.x),d=dY(a,b.target.y);return`${c}% ${d}%`}},d$=!1;class d_ extends dV.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c,layoutId:d}=this.props,{projection:e}=a;for(let a in d1)bv[a]=d1[a],q(a)&&(bv[a].isCSSVariable=!0);e&&(b.group&&b.group.add(e),c&&c.register&&d&&c.register(e),d$&&e.root.didUpdate(),e.addEventListener("animationComplete",()=>{this.safeToRemove()}),e.setOptions({...e.options,onExitComplete:()=>this.safeToRemove()})),dX.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:e}=this.props,{projection:f}=c;return f&&(f.isPresent=e,d$=!0,d||a.layoutDependency!==b||void 0===b||a.isPresent!==e?f.willUpdate():this.safeToRemove(),a.isPresent!==e&&(e?f.promote():f.relegate()||ah.postRender(()=>{let a=f.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),a5.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;d$=!0,d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function d0(a){let[b,c]=dW(),d=(0,dV.useContext)(bK);return(0,bJ.jsx)(d_,{...a,layoutGroup:d,switchLayoutGroup:(0,dV.useContext)(b1),isPresent:b,safeToRemove:c})}let d1={borderRadius:{...dZ,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:dZ,borderTopRightRadius:dZ,borderBottomLeftRadius:dZ,borderBottomRightRadius:dZ,boxShadow:{correct:(a,{treeScale:b,projectionDelta:c})=>{let d=aL.parse(a);if(d.length>5)return a;let e=aL.createTransformer(a),f=+("number"!=typeof d[0]),g=c.x.scale*b.x,h=c.y.scale*b.y;d[0+f]/=g,d[1+f]/=h;let i=v(g,h,.5);return"number"==typeof d[2+f]&&(d[2+f]/=i),"number"==typeof d[3+f]&&(d[3+f]/=i),e(d)}}};function d2(a){return"object"==typeof a&&null!==a}function d3(a){return d2(a)&&"ownerSVGElement"in a}let d4=(a,b)=>a.depth-b.depth;class d5{constructor(){this.children=[],this.isDirty=!1}add(a){a$(this.children,a),this.isDirty=!0}remove(a){a_(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(d4),this.isDirty=!1,this.children.forEach(a)}}let d6=["TopLeft","TopRight","BottomLeft","BottomRight"],d7=d6.length,d8=a=>"string"==typeof a?parseFloat(a):a,d9=a=>"number"==typeof a||P.test(a);function ea(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let eb=ed(0,.5,cN),ec=ed(.5,.95,ac);function ed(a,b,c){return d=>d<a?0:d>b?1:c(cS(a,b,d))}function ee(a,b){a.min=b.min,a.max=b.max}function ef(a,b){ee(a.x,b.x),ee(a.y,b.y)}function eg(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}function eh(a,b,c,d,e){return a-=b,a=d+1/c*(a-d),void 0!==e&&(a=d+1/e*(a-d)),a}function ei(a,b,[c,d,e],f,g){!function(a,b=0,c=1,d=.5,e,f=a,g=a){if(O.test(b)&&(b=parseFloat(b),b=v(g.min,g.max,b/100)-g.min),"number"!=typeof b)return;let h=v(f.min,f.max,d);a===f&&(h-=b),a.min=eh(a.min,b,c,h,e),a.max=eh(a.max,b,c,h,e)}(a,b[c],b[d],b[e],b.scale,f,g)}let ej=["x","scaleX","originX"],ek=["y","scaleY","originY"];function el(a,b,c,d){ei(a.x,b,ej,c?c.x:void 0,d?d.x:void 0),ei(a.y,b,ek,c?c.y:void 0,d?d.y:void 0)}function em(a){return 0===a.translate&&1===a.scale}function en(a){return em(a.x)&&em(a.y)}function eo(a,b){return a.min===b.min&&a.max===b.max}function ep(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function eq(a,b){return ep(a.x,b.x)&&ep(a.y,b.y)}function er(a){return dz(a.x)/dz(a.y)}function es(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}class et{constructor(){this.members=[]}add(a){a$(this.members,a),a.scheduleRender()}remove(a){if(a_(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let eu={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ev=["","X","Y","Z"],ew=0;function ex(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function ey({attachResizeListener:a,defaultParent:b,measureScroll:c,checkIsScrollRoot:d,resetTransform:e}){return class{constructor(a={},c=b?.()){this.id=ew++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,af.value&&(eu.nodes=eu.calculatedTargetDeltas=eu.calculatedProjections=0),this.nodes.forEach(eB),this.nodes.forEach(eI),this.nodes.forEach(eJ),this.nodes.forEach(eC),af.addProjectionMetrics&&af.addProjectionMetrics(eu)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new d5)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new a0),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b){if(this.instance)return;this.isSVG=d3(b)&&!(d3(b)&&"svg"===b.tagName),this.instance=b;let{layoutId:c,layout:d,visualElement:e}=this.options;if(e&&!e.current&&e.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(d||c)&&(this.isLayoutDirty=!0),a){let c,d=0,e=()=>this.root.updateBlockedByResize=!1;ah.read(()=>{d=window.innerWidth}),a(b,()=>{let a=window.innerWidth;a!==d&&(d=a,this.root.updateBlockedByResize=!0,c&&c(),c=function(a,b){let c=aZ.now(),d=({timestamp:b})=>{let e=b-c;e>=250&&(ai(d),a(e-250))};return ah.setup(d,!0),()=>ai(d)}(e,250),dX.hasAnimatedSinceResize&&(dX.hasAnimatedSinceResize=!1,this.nodes.forEach(eH)))})}c&&this.root.registerSharedNode(c,this),!1!==this.options.animate&&e&&(c||d)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeLayoutChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let f=this.options.transition||e.getDefaultTransition()||eP,{onLayoutAnimationStart:g,onLayoutAnimationComplete:h}=e.getProps(),i=!this.targetLayout||!eq(this.targetLayout,d),j=!b&&c;if(this.options.layoutRoot||this.resumeFrom||j||b&&(i||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let b={...b5(f,"layout"),onPlay:g,onComplete:h};(e.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(a,j)}else b||eH(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ai(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eK),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let d=c.props[b0];if(window.MotionHasOptimisedAnimation(d,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(d,"transform",ah,!(a||c))}let{parent:e}=b;e&&!e.hasCheckedOptimisedAppear&&a(e)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(eE);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(eF);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(eG),this.nodes.forEach(ez),this.nodes.forEach(eA)):this.nodes.forEach(eF),this.clearAllSnapshots();let a=aZ.now();aj.delta=I(0,1e3/60,a-aj.timestamp),aj.timestamp=a,aj.isProcessing=!0,ak.update.process(aj),ak.preRender.process(aj),ak.render.process(aj),aj.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,a5.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eD),this.sharedNodes.forEach(eL)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ah.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ah.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||dz(this.snapshot.measuredBox.x)||dz(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=bb(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b&&this.instance){let b=d(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:c(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!e)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!en(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,f=d!==this.prevTransformTemplateValue;a&&this.instance&&(b||y(this.latestValues)||f)&&(e(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),eS((b=d).x),eS(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return bb();let b=a.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(eU))){let{scroll:a}=this.root;a&&(D(b.x,a.offset.x),D(b.y,a.offset.y))}return b}removeElementScroll(a){let b=bb();if(ef(b,a),this.scroll?.wasRoot)return b;for(let c=0;c<this.path.length;c++){let d=this.path[c],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&ef(b,a),D(b.x,e.offset.x),D(b.y,e.offset.y))}return b}applyTransform(a,b=!1){let c=bb();ef(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&F(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),y(d.latestValues)&&F(c,d.latestValues)}return y(this.latestValues)&&F(c,this.latestValues),c}removeTransform(a){let b=bb();ef(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!y(c.latestValues))continue;x(c.latestValues)&&c.updateSnapshot();let d=bb();ef(d,c.measurePageBox()),el(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return y(this.latestValues)&&el(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==aj.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){let b=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=b.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=b.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=b.isSharedProjectionDirty);let c=!!this.resumingFrom||this!==b;if(!(a||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:d,layoutId:e}=this.options;if(this.layout&&(d||e)){if(this.resolvedRelativeTargetAt=aj.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bb(),this.relativeTargetOrigin=bb(),dE(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),ef(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=bb(),this.targetWithTransforms=bb()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var f,g,h;this.forceRelativeParentToResolveTarget(),f=this.target,g=this.relativeTarget,h=this.relativeParent.target,dC(f.x,g.x,h.x),dC(f.y,g.y,h.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ef(this.target,this.layout.layoutBox),C(this.target,this.targetDelta)):ef(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=bb(),this.relativeTargetOrigin=bb(),dE(this.relativeTargetOrigin,this.target,a.target),ef(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}af.value&&eu.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||x(this.parent.latestValues)||z(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let a=this.getLead(),b=!!this.resumingFrom||this!==a,c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),b&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===aj.timestamp&&(c=!1),c)return;let{layout:d,layoutId:e}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||e))return;ef(this.layoutCorrected,this.layout.layoutBox);let f=this.treeScale.x,g=this.treeScale.y;!function(a,b,c,d=!1){let e,f,g=c.length;if(g){b.x=b.y=1;for(let h=0;h<g;h++){f=(e=c[h]).projectionDelta;let{visualElement:g}=e.options;(!g||!g.props.style||"contents"!==g.props.style.display)&&(d&&e.options.layoutScroll&&e.scroll&&e!==e.root&&F(a,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),f&&(b.x*=f.x.scale,b.y*=f.y.scale,C(a,f)),d&&y(e.latestValues)&&F(a,e.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}(this.layoutCorrected,this.treeScale,this.path,b),a.layout&&!a.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=bb());let{target:h}=a;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(eg(this.prevProjectionDelta.x,this.projectionDelta.x),eg(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),dB(this.projectionDelta,this.layoutCorrected,h,this.latestValues),this.treeScale.x===f&&this.treeScale.y===g&&es(this.projectionDelta.x,this.prevProjectionDelta.x)&&es(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h)),af.value&&eu.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.visualElement?.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=a9(),this.projectionDelta=a9(),this.projectionDeltaWithTransform=a9()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=a9();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=bb(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some(eO));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;if(eM(g.x,a.x,d),eM(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var j,m,n,o,p,q;dE(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),n=this.relativeTarget,o=this.relativeTargetOrigin,p=h,q=d,eN(n.x,o.x,p.x,q),eN(n.y,o.y,p.y,q),c&&(j=this.relativeTarget,m=c,eo(j.x,m.x)&&eo(j.y,m.y))&&(this.isProjectionDirty=!1),c||(c=bb()),ef(c,this.relativeTarget)}i&&(this.animationValues=f,function(a,b,c,d,e,f){e?(a.opacity=v(0,c.opacity??1,eb(d)),a.opacityExit=v(b.opacity??1,0,ec(d))):f&&(a.opacity=v(b.opacity??1,c.opacity??1,d));for(let e=0;e<d7;e++){let f=`border${d6[e]}Radius`,g=ea(b,f),h=ea(c,f);(void 0!==g||void 0!==h)&&(g||(g=0),h||(h=0),0===g||0===h||d9(g)===d9(h)?(a[f]=Math.max(v(d8(g),d8(h),d),0),(O.test(h)||O.test(g))&&(a[f]+="%")):a[f]=h)}(b.rotate||c.rotate)&&(a.rotate=v(b.rotate||0,c.rotate||0,d))}(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ai(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ah.update(()=>{dX.hasAnimatedSinceResize=!0,cc.layout++,this.motionValue||(this.motionValue=a3(0)),this.currentAnimation=function(a,b,c){let d=aX(a)?a:a3(a);return d.start(dh("",d,b,c)),d.animation}(this.motionValue,[0,1e3],{...a,velocity:0,isSync:!0,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onStop:()=>{cc.layout--},onComplete:()=>{cc.layout--,a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&eT(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||bb();let b=dz(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=dz(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}ef(b,c),F(b,e),dB(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new et),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){let{layoutId:a}=this.options;return a&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:a}=this.options;return a?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(b=!0),!b)return;let d={};c.z&&ex("z",a,d,this.animationValues);for(let b=0;b<ev.length;b++)ex(`rotate${ev[b]}`,a,d,this.animationValues),ex(`skew${ev[b]}`,a,d,this.animationValues);for(let b in a.render(),d)a.setStaticValue(b,d[b]),this.animationValues&&(this.animationValues[b]=d[b]);a.scheduleRender()}applyProjectionStyles(a,b){if(!this.instance||this.isSVG)return;if(!this.isVisible){a.visibility="hidden";return}let c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,a.visibility="",a.opacity="",a.pointerEvents=bW(b?.pointerEvents)||"",a.transform=c?c(this.latestValues,""):"none";return}let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=bW(b?.pointerEvents)||""),this.hasProjected&&!y(this.latestValues)&&(a.transform=c?c({},""):"none",this.hasProjected=!1);return}a.visibility="";let e=d.animationValues||d.latestValues;this.applyTransformsToTarget();let f=function(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=c?.z||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}(this.projectionDeltaWithTransform,this.treeScale,e);c&&(f=c(e,f)),a.transform=f;let{x:g,y:h}=this.projectionDelta;for(let b in a.transformOrigin=`${100*g.origin}% ${100*h.origin}% 0`,d.animationValues?a.opacity=d===this?e.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:e.opacityExit:a.opacity=d===this?void 0!==e.opacity?e.opacity:"":void 0!==e.opacityExit?e.opacityExit:0,bv){if(void 0===e[b])continue;let{correct:c,applyTo:g,isCSSVariable:h}=bv[b],i="none"===f?e[b]:c(e[b],d);if(g){let b=g.length;for(let c=0;c<b;c++)a[g[c]]=i}else h?this.options.visualElement.renderState.vars[b]=i:a[b]=i}this.options.layoutId&&(a.pointerEvents=d===this?bW(b?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>a.currentAnimation?.stop()),this.root.nodes.forEach(eE),this.root.sharedNodes.clear()}}}function ez(a){a.updateLayout()}function eA(a){let b=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&b&&a.hasListeners("didUpdate")){let{layoutBox:c,measuredBox:d}=a.layout,{animationType:e}=a.options,f=b.source!==a.layout.source;"size"===e?dF(a=>{let d=f?b.measuredBox[a]:b.layoutBox[a],e=dz(d);d.min=c[a].min,d.max=d.min+e}):eT(e,b.layoutBox,c)&&dF(d=>{let e=f?b.measuredBox[d]:b.layoutBox[d],g=dz(c[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=a9();dB(g,c,b.layoutBox);let h=a9();f?dB(h,a.applyTransform(d,!0),b.measuredBox):dB(h,c,b.layoutBox);let i=!en(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=bb();dE(g,b.layoutBox,e.layoutBox);let h=bb();dE(h,c,f.layoutBox),eq(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:c,snapshot:b,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeLayoutChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function eB(a){af.value&&eu.nodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function eC(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function eD(a){a.clearSnapshot()}function eE(a){a.clearMeasurements()}function eF(a){a.isLayoutDirty=!1}function eG(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function eH(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function eI(a){a.resolveTargetDelta()}function eJ(a){a.calcProjection()}function eK(a){a.resetSkewAndRotation()}function eL(a){a.removeLeadSnapshot()}function eM(a,b,c){a.translate=v(b.translate,0,c),a.scale=v(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function eN(a,b,c,d){a.min=v(b.min,c.min,d),a.max=v(b.max,c.max,d)}function eO(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let eP={duration:.45,ease:[.4,0,.1,1]},eQ=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),eR=eQ("applewebkit/")&&!eQ("chrome/")?Math.round:ac;function eS(a){a.min=eR(a.min),a.max=eR(a.max)}function eT(a,b,c){return"position"===a||"preserve-aspect"===a&&!(.2>=Math.abs(er(b)-er(c)))}function eU(a){return a!==a.root&&a.scroll?.wasRoot}let eV=ey({attachResizeListener:(a,b)=>dv(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),eW={current:void 0},eX=ey({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!eW.current){let a=new eV({});a.mount(window),a.setOptions({layoutScroll:!0}),eW.current=a}return eW.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position});function eY(a,b){let c=function(a,b,c){if(a instanceof EventTarget)return[a];if("string"==typeof a){let b=document,c=(void 0)??b.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}(a),d=new AbortController;return[c,{passive:!0,...b,signal:d.signal},()=>d.abort()]}function eZ(a){return!("touch"===a.pointerType||du.x||du.y)}function e$(a,b,c){let{props:d}=a;a.animationState&&d.whileHover&&a.animationState.setActive("whileHover","Start"===c);let e=d["onHover"+c];e&&ah.postRender(()=>e(b,dx(b)))}function e_(a){return d2(a)&&"offsetHeight"in a}a.s(["isHTMLElement",()=>e_],91128);let e0=(a,b)=>!!b&&(a===b||e0(a,b.parentElement)),e1=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),e2=new WeakSet;function e3(a){return b=>{"Enter"===b.key&&a(b)}}function e4(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}function e5(a){return dw(a)&&!(du.x||du.y)}function e6(a,b,c){let{props:d}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&d.whileTap&&a.animationState.setActive("whileTap","Start"===c);let e=d["onTap"+("End"===c?"":c)];e&&ah.postRender(()=>e(b,dx(b)))}let e7=new WeakMap,e8=new WeakMap,e9=a=>{let b=e7.get(a.target);b&&b(a)},fa=a=>{a.forEach(e9)},fb={some:0,all:1},fc=function(a,b){if("undefined"==typeof Proxy)return b3;let c=new Map,d=(c,d)=>b3(c,d,a,b);return new Proxy((a,b)=>d(a,b),{get:(e,f)=>"create"===f?d:(c.has(f)||c.set(f,b3(f,void 0,a,b)),c.get(f))})}({animation:{Feature:class extends ds{constructor(a){super(a),a.animationState||(a.animationState=function(a){let b=b=>Promise.all(b.map(({animation:b,options:c})=>(function(a,b,c={}){let d;if(a.notify("AnimationStart",b),Array.isArray(b))d=Promise.all(b.map(b=>dk(a,b,c)));else if("string"==typeof b)d=dk(a,b,c);else{let e="function"==typeof b?b4(a,b,c.custom):b;d=Promise.all(di(a,e,c))}return d.then(()=>{a.notify("AnimationComplete",b)})})(a,b,c))),c=dr(),d=!0,e=b=>(c,d)=>{let e=b4(a,d,"exit"===b?a.presenceContext?.custom:void 0);if(e){let{transition:a,transitionEnd:b,...d}=e;c={...c,...d,...b}}return c};function f(f){let{props:g}=a,h=function a(b){if(!b)return;if(!b.isControllingVariants){let c=b.parent&&a(b.parent)||{};return void 0!==b.props.initial&&(c.initial=b.props.initial),c}let c={};for(let a=0;a<dm;a++){let d=bi[a],e=b.props[d];(bg(e)||!1===e)&&(c[d]=e)}return c}(a.parent)||{},i=[],j=new Set,k={},l=1/0;for(let b=0;b<dp;b++){var m,n;let o=dn[b],p=c[o],q=void 0!==g[o]?g[o]:h[o],r=bg(q),s=o===f?p.isActive:null;!1===s&&(l=b);let t=q===h[o]&&q!==g[o]&&r;if(t&&d&&a.manuallyAnimateOnMount&&(t=!1),p.protectedKeys={...k},!p.isActive&&null===s||!q&&!p.prevProp||bf(q)||"boolean"==typeof q)continue;let u=(m=p.prevProp,"string"==typeof(n=q)?n!==m:!!Array.isArray(n)&&!dl(n,m)),v=u||o===f&&p.isActive&&!t&&r||b>l&&r,w=!1,x=Array.isArray(q)?q:[q],y=x.reduce(e(o),{});!1===s&&(y={});let{prevResolvedValues:z={}}=p,A={...z,...y},B=b=>{v=!0,j.has(b)&&(w=!0,j.delete(b)),p.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in A){let b=y[a],c=z[a];if(!k.hasOwnProperty(a))(b6(b)&&b6(c)?dl(b,c):b===c)?void 0!==b&&j.has(a)?B(a):p.protectedKeys[a]=!0:null!=b?B(a):j.add(a)}p.prevProp=q,p.prevResolvedValues=y,p.isActive&&(k={...k,...y}),d&&a.blockInitialAnimation&&(v=!1);let C=t&&u,D=!C||w;v&&D&&i.push(...x.map(b=>{let c={type:o};if("string"==typeof b&&d&&!C&&a.manuallyAnimateOnMount&&a.parent){let{parent:d}=a,e=b4(d,b);if(d.enteringChildren&&e){let{delayChildren:b}=e.transition||{};c.delay=dj(d.enteringChildren,a,b)}}return{animation:b,options:c}}))}if(j.size){let b={};if("boolean"!=typeof g.initial){let c=b4(a,Array.isArray(g.initial)?g.initial[0]:g.initial);c&&c.transition&&(b.transition=c.transition)}j.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=d??null}),i.push({animation:b})}let o=!!i.length;return d&&(!1===g.initial||g.initial===g.animate)&&!a.manuallyAnimateOnMount&&(o=!1),d=!1,o?b(i):Promise.resolve()}return{animateChanges:f,setActive:function(b,d){if(c[b].isActive===d)return Promise.resolve();a.variantChildren?.forEach(a=>a.animationState?.setActive(b,d)),c[b].isActive=d;let e=f(b);for(let a in c)c[a].protectedKeys={};return e},setAnimateFunction:function(c){b=c(a)},getState:()=>c,reset:()=>{c=dr(),d=!0}}}(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();bf(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends ds{constructor(){super(...arguments),this.id=dt++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>{b(this.id)})}mount(){let{register:a,onExitComplete:b}=this.node.presenceContext||{};b&&b(this.id),a&&(this.unmount=a(this.id))}unmount(){}}},inView:{Feature:class extends ds{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:b,margin:c,amount:d="some",once:e}=a,f={root:b?b.current:void 0,rootMargin:c,threshold:"number"==typeof d?d:fb[d]},g=a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,e&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=b?c:d;f&&f(a)};var h=this.node.current;let i=function({root:a,...b}){let c=a||document;e8.has(c)||e8.set(c,{});let d=e8.get(c),e=JSON.stringify(b);return d[e]||(d[e]=new IntersectionObserver(fa,{root:a,...b})),d[e]}(f);return e7.set(h,g),i.observe(h),()=>{e7.delete(h),i.unobserve(h)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends ds{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=eY(a,c),g=a=>{let d=a.currentTarget;if(!e5(a))return;e2.add(d);let f=b(d,a),g=(a,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",i),e2.has(d)&&e2.delete(d),e5(a)&&"function"==typeof f&&f(a,{success:b})},h=a=>{g(a,d===window||d===document||c.useGlobalTarget||e0(d,a.target))},i=a=>{g(a,!1)};window.addEventListener("pointerup",h,e),window.addEventListener("pointercancel",i,e)};return d.forEach(a=>{((c.useGlobalTarget?window:a).addEventListener("pointerdown",g,e),e_(a))&&(a.addEventListener("focus",a=>((a,b)=>{let c=a.currentTarget;if(!c)return;let d=e3(()=>{if(e2.has(c))return;e4(c,"down");let a=e3(()=>{e4(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>e4(c,"cancel"),b)});c.addEventListener("keydown",d,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",d),b)})(a,e)),e1.has(a.tagName)||-1!==a.tabIndex||a.hasAttribute("tabindex")||(a.tabIndex=0))}),f}(a,(a,b)=>(e6(this.node,b,"Start"),(a,{success:b})=>e6(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends ds{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ca(dv(this.node.current,"focus",()=>this.onFocus()),dv(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends ds{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=eY(a,c),g=a=>{if(!eZ(a))return;let{target:c}=a,d=b(c,a);if("function"!=typeof d||!c)return;let f=a=>{eZ(a)&&(d(a),c.removeEventListener("pointerleave",f))};c.addEventListener("pointerleave",f,e)};return d.forEach(a=>{a.addEventListener("pointerenter",g,e)}),f}(a,(a,b)=>(e$(this.node,b,"Start"),a=>e$(this.node,a,"End"))))}unmount(){}}},pan:{Feature:class extends ds{constructor(){super(...arguments),this.removePointerDownListener=ac}onPointerDown(a){this.session=new dI(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:dG(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:b,onPan:c,onPanEnd:d}=this.node.getProps();return{onSessionStart:dU(a),onStart:dU(b),onMove:c,onEnd:(a,b)=>{delete this.session,d&&ah.postRender(()=>d(a,b))}}}mount(){this.removePointerDownListener=dy(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends ds{constructor(a){super(a),this.removeGroupControls=ac,this.removeListeners=ac,this.controls=new dS(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ac}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:eX,MeasureLayout:d0},layout:{ProjectionNode:eX,MeasureLayout:d0}},(a,b)=>bI(a)?new bG(b):new by(b,{allowProjection:a!==c.Fragment}));a.s(["cn",()=>f1],77845);let fd=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),e=d?fd(a.slice(1),d):void 0;if(e)return e;if(0===b.validators.length)return;let f=a.join("-");return b.validators.find(({validator:a})=>a(f))?.classGroupId},fe=/^\[(.+)\]$/,ff=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:fg(b,a)).classGroupId=c;return}if("function"==typeof a)return fh(a)?void ff(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{ff(e,fg(b,a),c,d)})})},fg=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},fh=a=>a.isThemeGetter,fi=/\s+/;function fj(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=fk(a))&&(d&&(d+=" "),d+=b);return d}let fk=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=fk(a[d]))&&(c&&(c+=" "),c+=b);return c},fl=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},fm=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,fn=/^\((?:(\w[\w-]*):)?(.+)\)$/i,fo=/^\d+\/\d+$/,fp=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,fq=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,fr=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,fs=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ft=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,fu=a=>fo.test(a),fv=a=>!!a&&!Number.isNaN(Number(a)),fw=a=>!!a&&Number.isInteger(Number(a)),fx=a=>a.endsWith("%")&&fv(a.slice(0,-1)),fy=a=>fp.test(a),fz=()=>!0,fA=a=>fq.test(a)&&!fr.test(a),fB=()=>!1,fC=a=>fs.test(a),fD=a=>ft.test(a),fE=a=>!fG(a)&&!fM(a),fF=a=>fT(a,fX,fB),fG=a=>fm.test(a),fH=a=>fT(a,fY,fA),fI=a=>fT(a,fZ,fv),fJ=a=>fT(a,fV,fB),fK=a=>fT(a,fW,fD),fL=a=>fT(a,f_,fC),fM=a=>fn.test(a),fN=a=>fU(a,fY),fO=a=>fU(a,f$),fP=a=>fU(a,fV),fQ=a=>fU(a,fX),fR=a=>fU(a,fW),fS=a=>fU(a,f_,!0),fT=(a,b,c)=>{let d=fm.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},fU=(a,b,c=!1)=>{let d=fn.exec(a);return!!d&&(d[1]?b(d[1]):c)},fV=a=>"position"===a||"percentage"===a,fW=a=>"image"===a||"url"===a,fX=a=>"length"===a||"size"===a||"bg-size"===a,fY=a=>"length"===a,fZ=a=>"number"===a,f$=a=>"family-name"===a,f_=a=>"shadow"===a;Symbol.toStringTag;let f0=function(a,...b){let c,d,e,f=function(h){let i;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(i),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(i),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)ff(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),fd(c,b)||(a=>{if(fe.test(a)){let b=fe.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(i)}).cache.get,e=c.cache.set,f=g,g(h)};function g(a){let b=d(a);if(b)return b;let f=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(fi),i="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:j,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(j){i=b+(i.length>0?" "+i:i);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){i=b+(i.length>0?" "+i:i);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}i=b+(i.length>0?" "+i:i)}return i})(a,c);return e(a,f),f}return function(){return f(fj.apply(null,arguments))}}(()=>{let a=fl("color"),b=fl("font"),c=fl("text"),d=fl("font-weight"),e=fl("tracking"),f=fl("leading"),g=fl("breakpoint"),h=fl("container"),i=fl("spacing"),j=fl("radius"),k=fl("shadow"),l=fl("inset-shadow"),m=fl("text-shadow"),n=fl("drop-shadow"),o=fl("blur"),p=fl("perspective"),q=fl("aspect"),r=fl("ease"),s=fl("animate"),t=()=>["auto","avoid","all","avoid-page","page","left","right","column"],u=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...u(),fM,fG],w=()=>["auto","hidden","clip","visible","scroll"],x=()=>["auto","contain","none"],y=()=>[fM,fG,i],z=()=>[fu,"full","auto",...y()],A=()=>[fw,"none","subgrid",fM,fG],B=()=>["auto",{span:["full",fw,fM,fG]},fw,fM,fG],C=()=>[fw,"auto",fM,fG],D=()=>["auto","min","max","fr",fM,fG],E=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],G=()=>["auto",...y()],H=()=>[fu,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...y()],I=()=>[a,fM,fG],J=()=>[...u(),fP,fJ,{position:[fM,fG]}],K=()=>["no-repeat",{repeat:["","x","y","space","round"]}],L=()=>["auto","cover","contain",fQ,fF,{size:[fM,fG]}],M=()=>[fx,fN,fH],N=()=>["","none","full",j,fM,fG],O=()=>["",fv,fN,fH],P=()=>["solid","dashed","dotted","double"],Q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],R=()=>[fv,fx,fP,fJ],S=()=>["","none",o,fM,fG],T=()=>["none",fv,fM,fG],U=()=>["none",fv,fM,fG],V=()=>[fv,fM,fG],W=()=>[fu,"full",...y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[fy],breakpoint:[fy],color:[fz],container:[fy],"drop-shadow":[fy],ease:["in","out","in-out"],font:[fE],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[fy],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[fy],shadow:[fy],spacing:["px",fv],text:[fy],"text-shadow":[fy],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",fu,fG,fM,q]}],container:["container"],columns:[{columns:[fv,fG,fM,h]}],"break-after":[{"break-after":t()}],"break-before":[{"break-before":t()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:x()}],"overscroll-x":[{"overscroll-x":x()}],"overscroll-y":[{"overscroll-y":x()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[fw,"auto",fM,fG]}],basis:[{basis:[fu,"full","auto",h,...y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[fv,fu,"auto","initial","none",fG]}],grow:[{grow:["",fv,fM,fG]}],shrink:[{shrink:["",fv,fM,fG]}],order:[{order:[fw,"first","last","none",fM,fG]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:B()}],"col-start":[{"col-start":C()}],"col-end":[{"col-end":C()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:B()}],"row-start":[{"row-start":C()}],"row-end":[{"row-end":C()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:y()}],"gap-x":[{"gap-x":y()}],"gap-y":[{"gap-y":y()}],"justify-content":[{justify:[...E(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...E()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":E()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:y()}],px:[{px:y()}],py:[{py:y()}],ps:[{ps:y()}],pe:[{pe:y()}],pt:[{pt:y()}],pr:[{pr:y()}],pb:[{pb:y()}],pl:[{pl:y()}],m:[{m:G()}],mx:[{mx:G()}],my:[{my:G()}],ms:[{ms:G()}],me:[{me:G()}],mt:[{mt:G()}],mr:[{mr:G()}],mb:[{mb:G()}],ml:[{ml:G()}],"space-x":[{"space-x":y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":y()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[h,"screen",...H()]}],"min-w":[{"min-w":[h,"screen","none",...H()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",c,fN,fH]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,fM,fI]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",fx,fG]}],"font-family":[{font:[fO,fG,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,fM,fG]}],"line-clamp":[{"line-clamp":[fv,"none",fM,fI]}],leading:[{leading:[f,...y()]}],"list-image":[{"list-image":["none",fM,fG]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",fM,fG]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:I()}],"text-color":[{text:I()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...P(),"wavy"]}],"text-decoration-thickness":[{decoration:[fv,"from-font","auto",fM,fH]}],"text-decoration-color":[{decoration:I()}],"underline-offset":[{"underline-offset":[fv,"auto",fM,fG]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",fM,fG]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",fM,fG]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:J()}],"bg-repeat":[{bg:K()}],"bg-size":[{bg:L()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},fw,fM,fG],radial:["",fM,fG],conic:[fw,fM,fG]},fR,fK]}],"bg-color":[{bg:I()}],"gradient-from-pos":[{from:M()}],"gradient-via-pos":[{via:M()}],"gradient-to-pos":[{to:M()}],"gradient-from":[{from:I()}],"gradient-via":[{via:I()}],"gradient-to":[{to:I()}],rounded:[{rounded:N()}],"rounded-s":[{"rounded-s":N()}],"rounded-e":[{"rounded-e":N()}],"rounded-t":[{"rounded-t":N()}],"rounded-r":[{"rounded-r":N()}],"rounded-b":[{"rounded-b":N()}],"rounded-l":[{"rounded-l":N()}],"rounded-ss":[{"rounded-ss":N()}],"rounded-se":[{"rounded-se":N()}],"rounded-ee":[{"rounded-ee":N()}],"rounded-es":[{"rounded-es":N()}],"rounded-tl":[{"rounded-tl":N()}],"rounded-tr":[{"rounded-tr":N()}],"rounded-br":[{"rounded-br":N()}],"rounded-bl":[{"rounded-bl":N()}],"border-w":[{border:O()}],"border-w-x":[{"border-x":O()}],"border-w-y":[{"border-y":O()}],"border-w-s":[{"border-s":O()}],"border-w-e":[{"border-e":O()}],"border-w-t":[{"border-t":O()}],"border-w-r":[{"border-r":O()}],"border-w-b":[{"border-b":O()}],"border-w-l":[{"border-l":O()}],"divide-x":[{"divide-x":O()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":O()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...P(),"hidden","none"]}],"divide-style":[{divide:[...P(),"hidden","none"]}],"border-color":[{border:I()}],"border-color-x":[{"border-x":I()}],"border-color-y":[{"border-y":I()}],"border-color-s":[{"border-s":I()}],"border-color-e":[{"border-e":I()}],"border-color-t":[{"border-t":I()}],"border-color-r":[{"border-r":I()}],"border-color-b":[{"border-b":I()}],"border-color-l":[{"border-l":I()}],"divide-color":[{divide:I()}],"outline-style":[{outline:[...P(),"none","hidden"]}],"outline-offset":[{"outline-offset":[fv,fM,fG]}],"outline-w":[{outline:["",fv,fN,fH]}],"outline-color":[{outline:I()}],shadow:[{shadow:["","none",k,fS,fL]}],"shadow-color":[{shadow:I()}],"inset-shadow":[{"inset-shadow":["none",l,fS,fL]}],"inset-shadow-color":[{"inset-shadow":I()}],"ring-w":[{ring:O()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:I()}],"ring-offset-w":[{"ring-offset":[fv,fH]}],"ring-offset-color":[{"ring-offset":I()}],"inset-ring-w":[{"inset-ring":O()}],"inset-ring-color":[{"inset-ring":I()}],"text-shadow":[{"text-shadow":["none",m,fS,fL]}],"text-shadow-color":[{"text-shadow":I()}],opacity:[{opacity:[fv,fM,fG]}],"mix-blend":[{"mix-blend":[...Q(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Q()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[fv]}],"mask-image-linear-from-pos":[{"mask-linear-from":R()}],"mask-image-linear-to-pos":[{"mask-linear-to":R()}],"mask-image-linear-from-color":[{"mask-linear-from":I()}],"mask-image-linear-to-color":[{"mask-linear-to":I()}],"mask-image-t-from-pos":[{"mask-t-from":R()}],"mask-image-t-to-pos":[{"mask-t-to":R()}],"mask-image-t-from-color":[{"mask-t-from":I()}],"mask-image-t-to-color":[{"mask-t-to":I()}],"mask-image-r-from-pos":[{"mask-r-from":R()}],"mask-image-r-to-pos":[{"mask-r-to":R()}],"mask-image-r-from-color":[{"mask-r-from":I()}],"mask-image-r-to-color":[{"mask-r-to":I()}],"mask-image-b-from-pos":[{"mask-b-from":R()}],"mask-image-b-to-pos":[{"mask-b-to":R()}],"mask-image-b-from-color":[{"mask-b-from":I()}],"mask-image-b-to-color":[{"mask-b-to":I()}],"mask-image-l-from-pos":[{"mask-l-from":R()}],"mask-image-l-to-pos":[{"mask-l-to":R()}],"mask-image-l-from-color":[{"mask-l-from":I()}],"mask-image-l-to-color":[{"mask-l-to":I()}],"mask-image-x-from-pos":[{"mask-x-from":R()}],"mask-image-x-to-pos":[{"mask-x-to":R()}],"mask-image-x-from-color":[{"mask-x-from":I()}],"mask-image-x-to-color":[{"mask-x-to":I()}],"mask-image-y-from-pos":[{"mask-y-from":R()}],"mask-image-y-to-pos":[{"mask-y-to":R()}],"mask-image-y-from-color":[{"mask-y-from":I()}],"mask-image-y-to-color":[{"mask-y-to":I()}],"mask-image-radial":[{"mask-radial":[fM,fG]}],"mask-image-radial-from-pos":[{"mask-radial-from":R()}],"mask-image-radial-to-pos":[{"mask-radial-to":R()}],"mask-image-radial-from-color":[{"mask-radial-from":I()}],"mask-image-radial-to-color":[{"mask-radial-to":I()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":u()}],"mask-image-conic-pos":[{"mask-conic":[fv]}],"mask-image-conic-from-pos":[{"mask-conic-from":R()}],"mask-image-conic-to-pos":[{"mask-conic-to":R()}],"mask-image-conic-from-color":[{"mask-conic-from":I()}],"mask-image-conic-to-color":[{"mask-conic-to":I()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:J()}],"mask-repeat":[{mask:K()}],"mask-size":[{mask:L()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",fM,fG]}],filter:[{filter:["","none",fM,fG]}],blur:[{blur:S()}],brightness:[{brightness:[fv,fM,fG]}],contrast:[{contrast:[fv,fM,fG]}],"drop-shadow":[{"drop-shadow":["","none",n,fS,fL]}],"drop-shadow-color":[{"drop-shadow":I()}],grayscale:[{grayscale:["",fv,fM,fG]}],"hue-rotate":[{"hue-rotate":[fv,fM,fG]}],invert:[{invert:["",fv,fM,fG]}],saturate:[{saturate:[fv,fM,fG]}],sepia:[{sepia:["",fv,fM,fG]}],"backdrop-filter":[{"backdrop-filter":["","none",fM,fG]}],"backdrop-blur":[{"backdrop-blur":S()}],"backdrop-brightness":[{"backdrop-brightness":[fv,fM,fG]}],"backdrop-contrast":[{"backdrop-contrast":[fv,fM,fG]}],"backdrop-grayscale":[{"backdrop-grayscale":["",fv,fM,fG]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[fv,fM,fG]}],"backdrop-invert":[{"backdrop-invert":["",fv,fM,fG]}],"backdrop-opacity":[{"backdrop-opacity":[fv,fM,fG]}],"backdrop-saturate":[{"backdrop-saturate":[fv,fM,fG]}],"backdrop-sepia":[{"backdrop-sepia":["",fv,fM,fG]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":y()}],"border-spacing-x":[{"border-spacing-x":y()}],"border-spacing-y":[{"border-spacing-y":y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",fM,fG]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[fv,"initial",fM,fG]}],ease:[{ease:["linear","initial",r,fM,fG]}],delay:[{delay:[fv,fM,fG]}],animate:[{animate:["none",s,fM,fG]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,fM,fG]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:T()}],"rotate-x":[{"rotate-x":T()}],"rotate-y":[{"rotate-y":T()}],"rotate-z":[{"rotate-z":T()}],scale:[{scale:U()}],"scale-x":[{"scale-x":U()}],"scale-y":[{"scale-y":U()}],"scale-z":[{"scale-z":U()}],"scale-3d":["scale-3d"],skew:[{skew:V()}],"skew-x":[{"skew-x":V()}],"skew-y":[{"skew-y":V()}],transform:[{transform:[fM,fG,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:W()}],"translate-x":[{"translate-x":W()}],"translate-y":[{"translate-y":W()}],"translate-z":[{"translate-z":W()}],"translate-none":["translate-none"],accent:[{accent:I()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:I()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",fM,fG]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":y()}],"scroll-mx":[{"scroll-mx":y()}],"scroll-my":[{"scroll-my":y()}],"scroll-ms":[{"scroll-ms":y()}],"scroll-me":[{"scroll-me":y()}],"scroll-mt":[{"scroll-mt":y()}],"scroll-mr":[{"scroll-mr":y()}],"scroll-mb":[{"scroll-mb":y()}],"scroll-ml":[{"scroll-ml":y()}],"scroll-p":[{"scroll-p":y()}],"scroll-px":[{"scroll-px":y()}],"scroll-py":[{"scroll-py":y()}],"scroll-ps":[{"scroll-ps":y()}],"scroll-pe":[{"scroll-pe":y()}],"scroll-pt":[{"scroll-pt":y()}],"scroll-pr":[{"scroll-pr":y()}],"scroll-pb":[{"scroll-pb":y()}],"scroll-pl":[{"scroll-pl":y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",fM,fG]}],fill:[{fill:["none",...I()]}],"stroke-w":[{stroke:[fv,fN,fH,fI]}],stroke:[{stroke:["none",...I()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function f1(...a){return f0(function(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}(a))}},96438,a=>{"use strict";a.s(["default",()=>g]);var b=a.i(87924),c=a.i(72131),d=a.i(46271),e=a.i(77845);let f=c.default.forwardRef(({className:a,variant:c="primary",size:f="md",isLoading:g=!1,leftIcon:h,rightIcon:i,children:j,disabled:k,...l},m)=>{let n=(0,e.cn)(["inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed","relative overflow-hidden"],{primary:["bg-primary-600 text-white hover:bg-primary-700","focus:ring-primary-500 shadow-lg hover:shadow-xl","active:bg-primary-800"],secondary:["bg-sage-100 text-sage-800 hover:bg-sage-200","focus:ring-sage-500 border border-sage-200","active:bg-sage-300"],outline:["border-2 border-primary-600 text-primary-600 hover:bg-primary-50","focus:ring-primary-500 hover:border-primary-700","active:bg-primary-100"],ghost:["text-gray-700 hover:bg-gray-100","focus:ring-gray-500","active:bg-gray-200"],danger:["bg-red-600 text-white hover:bg-red-700","focus:ring-red-500 shadow-lg hover:shadow-xl","active:bg-red-800"]}[c],{sm:"px-3 py-1.5 text-sm gap-1.5",md:"px-4 py-2 text-base gap-2",lg:"px-6 py-3 text-lg gap-2.5",xl:"px-8 py-4 text-xl gap-3"}[f],a);return(0,b.jsxs)(d.motion.button,{ref:m,className:n,disabled:k||g,whileHover:{scale:k||g?1:1.02},whileTap:{scale:k||g?1:.98},transition:{duration:.1},...l,children:[g&&(0,b.jsx)(d.motion.div,{className:"absolute inset-0 bg-white/20 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.2},children:(0,b.jsx)("div",{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"})}),(0,b.jsxs)("div",{className:(0,e.cn)("flex items-center gap-inherit",g&&"opacity-0"),children:[h&&(0,b.jsx)("span",{className:"flex-shrink-0",children:h}),(0,b.jsx)("span",{children:j}),i&&(0,b.jsx)("span",{className:"flex-shrink-0",children:i})]})]})});f.displayName="Button";let g=f},25250,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(87924),c=a.i(72131),d=a.i(38246),e=a.i(71987),f=a.i(46271),g=a.i(96438);let h=()=>{let a=[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Refund Policy",href:"/refund"},{name:"Cookie Policy",href:"/cookies"},{name:"Transparency",href:"/transparency"},{name:"Annual Reports",href:"/reports"}],h=[{name:"Facebook",href:"https://facebook.com/ayurakshak",icon:(0,b.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})},{name:"Twitter",href:"https://twitter.com/ayurakshak",icon:(0,b.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})},{name:"Instagram",href:"https://instagram.com/ayurakshak",icon:(0,b.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z"})})},{name:"LinkedIn",href:"https://linkedin.com/company/ayurakshak",icon:(0,b.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})},{name:"YouTube",href:"https://youtube.com/@ayurakshak",icon:(0,b.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{d:"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"})})}];return(0,b.jsxs)("footer",{className:"bg-gray-900 text-white",children:[(0,b.jsx)("div",{className:"bg-primary-600",children:(0,b.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Stay Connected with Our Mission"}),(0,b.jsx)("p",{className:"text-primary-100 mb-8 max-w-2xl mx-auto",children:"Get updates on our programs, impact stories, and ways you can make a difference in communities across India."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[(0,b.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white"}),(0,b.jsx)(g.default,{variant:"secondary",size:"md",className:"bg-white text-primary-600 hover:bg-gray-100",children:"Subscribe"})]})]})})}),(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,b.jsxs)("div",{className:"lg:col-span-2",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,b.jsx)("div",{className:"relative w-12 h-12",children:(0,b.jsx)(e.default,{src:"/logo.jpeg",alt:"Ayurakshak Logo",fill:!0,className:"object-contain rounded-full"})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Ayurakshak"}),(0,b.jsx)("p",{className:"text-gray-400",children:"Care · Restore · Protect"})]})]}),(0,b.jsx)("p",{className:"text-gray-300 mb-6 leading-relaxed",children:"Ayurakshak combines traditional Ayurveda with modern outreach to heal communities across India. Through health camps, sustainable livelihoods, and green initiatives, we're building a healthier, more resilient future for all."}),(0,b.jsxs)("div",{className:"space-y-2 text-sm text-gray-400",children:[(0,b.jsx)("p",{children:"📍 123 Wellness Street, New Delhi, India 110001"}),(0,b.jsx)("p",{children:"📞 +91 98765 43210"}),(0,b.jsx)("p",{children:"✉️ <EMAIL>"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:"Quick Links"}),(0,b.jsx)("ul",{className:"space-y-3",children:[{name:"About Us",href:"/about"},{name:"Our Programs",href:"/#programs"},{name:"Impact Stories",href:"/#impact"},{name:"Volunteer",href:"/volunteer"},{name:"Careers",href:"/careers"},{name:"News & Updates",href:"/news"}].map(a=>(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:a.href,className:"text-gray-400 hover:text-white transition-colors duration-200",children:a.name})},a.name))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:"Our Programs"}),(0,b.jsx)("ul",{className:"space-y-3",children:[{name:"Health Camps",href:"/#programs"},{name:"Women Livelihoods",href:"/#programs"},{name:"Education Support",href:"/#programs"},{name:"Emergency Relief",href:"/#programs"},{name:"Environmental Care",href:"/#programs"},{name:"Community Development",href:"/#programs"}].map(a=>(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:a.href,className:"text-gray-400 hover:text-white transition-colors duration-200",children:a.name})},a.name))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:"Support Us"}),(0,b.jsx)("ul",{className:"space-y-3",children:[{name:"Donate",href:"/donate"},{name:"Sponsor a Program",href:"/sponsor"},{name:"Corporate Partnership",href:"/partnership"},{name:"Volunteer",href:"/volunteer"},{name:"Fundraise",href:"/fundraise"},{name:"Gift a Smile",href:"/gift"}].map(a=>(0,b.jsx)("li",{children:(0,b.jsx)(d.default,{href:a.href,className:"text-gray-400 hover:text-white transition-colors duration-200",children:a.name})},a.name))})]})]}),(0,b.jsx)("div",{className:"mt-12 pt-8 border-t border-gray-800",children:(0,b.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,b.jsx)("div",{className:"flex space-x-6 mb-6 md:mb-0",children:h.map(a=>(0,b.jsx)(f.motion.a,{href:a.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors duration-200",whileHover:{scale:1.2},whileTap:{scale:.9},"aria-label":a.name,children:a.icon},a.name))}),(0,b.jsxs)("div",{className:"text-center md:text-right",children:[(0,b.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Registered NGO | 80G Tax Exemption Available"}),(0,b.jsx)("div",{className:"flex flex-wrap justify-center md:justify-end gap-4 text-xs text-gray-500",children:a.map((e,f)=>(0,b.jsxs)(c.default.Fragment,{children:[(0,b.jsx)(d.default,{href:e.href,className:"hover:text-gray-300 transition-colors duration-200",children:e.name}),f<a.length-1&&(0,b.jsx)("span",{children:"•"})]},e.name))})]})]})}),(0,b.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-800 text-center",children:(0,b.jsxs)("p",{className:"text-gray-400 text-sm",children:["© ",new Date().getFullYear()," Ayurakshak. All rights reserved.",(0,b.jsx)("span",{className:"mx-2",children:"•"}),"Developed with ❤️ by"," ",(0,b.jsx)("a",{href:"https://kush-personal-portfolio-my-portfolio.vercel.app/",target:"_blank",rel:"noopener noreferrer",className:"text-primary-400 hover:text-primary-300 transition-colors duration-200",children:"Kush Vardhan"})]})})]})]})}},62036,a=>{"use strict";a.s(["AnimatePresence",()=>r],62036);var b=a.i(87924),c=a.i(72131),d=a.i(86723),e=a.i(74290),f=a.i(1703),g=a.i(14800),h=a.i(91128),i=c,j=a.i(65802);class k extends i.Component{getSnapshotBeforeUpdate(a){let b=this.props.childRef.current;if(b&&a.isPresent&&!this.props.isPresent){let a=b.offsetParent,c=(0,h.isHTMLElement)(a)&&a.offsetWidth||0,d=this.props.sizeRef.current;d.height=b.offsetHeight||0,d.width=b.offsetWidth||0,d.top=b.offsetTop,d.left=b.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function l({children:a,isPresent:c,anchorX:d,root:e}){let f=(0,i.useId)(),g=(0,i.useRef)(null),h=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,i.useContext)(j.MotionConfigContext);return(0,i.useInsertionEffect)(()=>{let{width:a,height:b,top:i,left:j,right:k}=h.current;if(c||!g.current||!a||!b)return;let m="left"===d?`left: ${j}`:`right: ${k}`;g.current.dataset.motionPopId=f;let n=document.createElement("style");l&&(n.nonce=l);let o=e??document.head;return o.appendChild(n),n.sheet&&n.sheet.insertRule(`
          [data-motion-pop-id="${f}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${b}px !important;
            ${m}px !important;
            top: ${i}px !important;
          }
        `),()=>{o.contains(n)&&o.removeChild(n)}},[c]),(0,b.jsx)(k,{isPresent:c,childRef:g,sizeRef:h,children:i.cloneElement(a,{ref:g})})}let m=({children:a,initial:d,isPresent:f,onExitComplete:h,custom:i,presenceAffectsLayout:j,mode:k,anchorX:m,root:o})=>{let p=(0,e.useConstant)(n),q=(0,c.useId)(),r=!0,s=(0,c.useMemo)(()=>(r=!1,{id:q,initial:d,isPresent:f,custom:i,onExitComplete:a=>{for(let b of(p.set(a,!0),p.values()))if(!b)return;h&&h()},register:a=>(p.set(a,!1),()=>p.delete(a))}),[f,p,h]);return j&&r&&(s={...s}),(0,c.useMemo)(()=>{p.forEach((a,b)=>p.set(b,!1))},[f]),c.useEffect(()=>{f||p.size||!h||h()},[f]),"popLayout"===k&&(a=(0,b.jsx)(l,{isPresent:f,anchorX:m,root:o,children:a})),(0,b.jsx)(g.PresenceContext.Provider,{value:s,children:a})};function n(){return new Map}var o=a.i(20410);let p=a=>a.key||"";function q(a){let b=[];return c.Children.forEach(a,a=>{(0,c.isValidElement)(a)&&b.push(a)}),b}let r=({children:a,custom:g,initial:h=!0,onExitComplete:i,presenceAffectsLayout:j=!0,mode:k="sync",propagate:l=!1,anchorX:n="left",root:r})=>{let[s,t]=(0,o.usePresence)(l),u=(0,c.useMemo)(()=>q(a),[a]),v=l&&!s?[]:u.map(p),w=(0,c.useRef)(!0),x=(0,c.useRef)(u),y=(0,e.useConstant)(()=>new Map),[z,A]=(0,c.useState)(u),[B,C]=(0,c.useState)(u);(0,f.useIsomorphicLayoutEffect)(()=>{w.current=!1,x.current=u;for(let a=0;a<B.length;a++){let b=p(B[a]);v.includes(b)?y.delete(b):!0!==y.get(b)&&y.set(b,!1)}},[B,v.length,v.join("-")]);let D=[];if(u!==z){let a=[...u];for(let b=0;b<B.length;b++){let c=B[b],d=p(c);v.includes(d)||(a.splice(b,0,c),D.push(c))}return"wait"===k&&D.length&&(a=D),C(q(a)),A(u),null}let{forceRender:E}=(0,c.useContext)(d.LayoutGroupContext);return(0,b.jsx)(b.Fragment,{children:B.map(a=>{let c=p(a),d=(!l||!!s)&&(u===B||v.includes(c));return(0,b.jsx)(m,{isPresent:d,initial:(!w.current||!!h)&&void 0,custom:g,presenceAffectsLayout:j,mode:k,root:r,onExitComplete:d?void 0:()=>{if(!y.has(c))return;y.set(c,!0);let a=!0;y.forEach(b=>{b||(a=!1)}),a&&(E?.(),C(x.current),l&&t?.(),i&&i())},anchorX:n,children:a},c)})})}},43218,a=>{"use strict";a.s(["default",()=>j]);var b=a.i(87924),c=a.i(72131),d=a.i(38246),e=a.i(71987),f=a.i(46271),g=a.i(62036),h=a.i(77845),i=a.i(96438);let j=()=>{let[a,j]=(0,c.useState)(!1),[k,l]=(0,c.useState)(!1);(0,c.useEffect)(()=>{let a=()=>{j(window.scrollY>20)};return window.addEventListener("scroll",a),()=>window.removeEventListener("scroll",a)},[]);let m=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Programs",href:"/#programs"},{name:"Products",href:"/products"},{name:"Impact",href:"/#impact"},{name:"Contact",href:"/contact"}];return(0,b.jsxs)(f.motion.header,{className:(0,h.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",a?"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200":"bg-transparent"),initial:{y:-100},animate:{y:0},transition:{duration:.5},children:[(0,b.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,b.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,b.jsx)(f.motion.div,{className:"flex items-center space-x-3",whileHover:{scale:1.05},transition:{duration:.2},children:(0,b.jsxs)(d.default,{href:"/",className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:"relative w-10 h-10 lg:w-12 lg:h-12",children:(0,b.jsx)(e.default,{src:"/logo.jpeg",alt:"Ayurakshak Logo",fill:!0,className:"object-contain rounded-full",priority:!0})}),(0,b.jsxs)("div",{className:"hidden sm:block",children:[(0,b.jsx)("h1",{className:(0,h.cn)("text-xl lg:text-2xl font-bold transition-colors duration-300",a?"text-primary-600":"text-white"),children:"Ayurakshak"}),(0,b.jsx)("p",{className:(0,h.cn)("text-xs lg:text-sm transition-colors duration-300",a?"text-gray-600":"text-white/80"),children:"Care · Restore · Protect"})]})]})}),(0,b.jsx)("nav",{className:"hidden lg:flex items-center space-x-8",children:m.map((c,e)=>(0,b.jsx)(f.motion.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*e},children:(0,b.jsxs)(d.default,{href:c.href,className:(0,h.cn)("text-sm font-medium transition-all duration-200 hover:scale-105","relative group",a?"text-gray-700 hover:text-primary-600":"text-white hover:text-primary-200"),children:[c.name,(0,b.jsx)("span",{className:(0,h.cn)("absolute -bottom-1 left-0 w-0 h-0.5 transition-all duration-300 group-hover:w-full",a?"bg-primary-600":"bg-white")})]})},c.name))}),(0,b.jsx)("div",{className:"hidden lg:flex items-center space-x-4",children:(0,b.jsx)(i.default,{variant:a?"primary":"outline",size:"md",className:(0,h.cn)("transition-all duration-300",!a&&"border-white text-white hover:bg-white hover:text-primary-600"),children:"Donate Now"})}),(0,b.jsx)("button",{className:"lg:hidden p-2 rounded-md transition-colors duration-200",onClick:()=>l(!k),"aria-label":"Toggle mobile menu",children:(0,b.jsxs)("div",{className:"w-6 h-6 relative",children:[(0,b.jsx)("span",{className:(0,h.cn)("absolute block w-full h-0.5 transform transition-all duration-300","top-1.5",a?"bg-gray-700":"bg-white",k&&"rotate-45 top-3")}),(0,b.jsx)("span",{className:(0,h.cn)("absolute block w-full h-0.5 transform transition-all duration-300","top-3",a?"bg-gray-700":"bg-white",k&&"opacity-0")}),(0,b.jsx)("span",{className:(0,h.cn)("absolute block w-full h-0.5 transform transition-all duration-300","top-4.5",a?"bg-gray-700":"bg-white",k&&"-rotate-45 top-3")})]})})]})}),(0,b.jsx)(g.AnimatePresence,{children:k&&(0,b.jsx)(f.motion.div,{className:"lg:hidden bg-white border-t border-gray-200 shadow-lg",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,b.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[m.map((a,c)=>(0,b.jsx)(f.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*c},children:(0,b.jsx)(d.default,{href:a.href,className:"block text-gray-700 hover:text-primary-600 font-medium py-2 transition-colors duration-200",onClick:()=>l(!1),children:a.name})},a.name)),(0,b.jsx)(f.motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*m.length},className:"pt-4 border-t border-gray-200",children:(0,b.jsx)(i.default,{variant:"primary",size:"md",className:"w-full",children:"Donate Now"})})]})})})]})}},62067,a=>{"use strict";a.s(["CardContent",()=>g,"CardHeader",()=>e,"CardTitle",()=>f,"default",()=>h]);var b=a.i(87924),c=a.i(46271),d=a.i(77845);let e=({children:a,className:c})=>(0,b.jsx)("div",{className:(0,d.cn)("mb-4",c),children:a}),f=({children:a,className:c,as:e="h3"})=>(0,b.jsx)(e,{className:(0,d.cn)("text-xl font-semibold text-gray-900 mb-2",c),children:a}),g=({children:a,className:c})=>(0,b.jsx)("div",{className:(0,d.cn)("",c),children:a}),h=({children:a,className:e,variant:f="default",padding:g="md",hover:h=!1,onClick:i})=>{let j=(0,d.cn)(["rounded-xl transition-all duration-300",i&&"cursor-pointer"],{default:["bg-white border border-gray-200",h&&"hover:border-gray-300 hover:shadow-md"],elevated:["bg-white shadow-lg border border-gray-100",h&&"hover:shadow-xl hover:border-gray-200"],outlined:["bg-transparent border-2 border-primary-200",h&&"hover:border-primary-300 hover:bg-primary-50/50"],glass:["bg-white/80 backdrop-blur-sm border border-white/20",h&&"hover:bg-white/90 hover:border-white/30"]}[f],{none:"",sm:"p-4",md:"p-6",lg:"p-8",xl:"p-10"}[g],e),k=c.motion.div;return(0,b.jsx)(k,{className:j,onClick:i,whileHover:h?{y:-2,scale:1.02}:void 0,whileTap:i?{scale:.98}:void 0,transition:{duration:.2},children:a})}},60614,a=>{"use strict";a.s(["default",()=>g]);var b=a.i(87924),c=a.i(71987),d=a.i(46271),e=a.i(62067),f=a.i(96438);let g=()=>(0,b.jsx)("section",{id:"about",className:"py-20 bg-gradient-to-br from-sage-50 to-mint-50",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,b.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["About ",(0,b.jsx)("span",{className:"text-primary-600",children:"Ayurakshak"})]}),(0,b.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Where ancient wisdom meets modern compassion to heal communities across India"})]}),(0,b.jsxs)("div",{className:"grid lg:grid-cols-2 gap-16 items-center mb-20",children:[(0,b.jsxs)(d.motion.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,b.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Healing Through Traditional Wisdom"}),(0,b.jsxs)("div",{className:"space-y-6 text-gray-600 leading-relaxed",children:[(0,b.jsx)("p",{children:"Ayurakshak was born from a simple yet powerful vision: to bridge the gap between traditional Ayurvedic healing and modern healthcare accessibility. Founded by a team of passionate healthcare professionals and social workers, we believe that everyone deserves access to natural, holistic healthcare."}),(0,b.jsx)("p",{children:"Our journey began in rural villages where we witnessed the profound impact of combining ancient Ayurvedic practices with community-centered care. Today, we've grown into a movement that spans across India, touching lives through health camps, women's empowerment programs, and sustainable livelihood initiatives."}),(0,b.jsx)("p",{children:"At Ayurakshak, we don't just treat symptoms – we nurture communities, empower individuals, and create lasting change that ripples through generations. Every program we run, every product we create, and every life we touch is guided by our commitment to holistic wellness and social transformation."})]}),(0,b.jsx)("div",{className:"mt-8",children:(0,b.jsx)(f.default,{variant:"primary",size:"lg",children:"Learn More About Our Mission"})})]}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"relative",children:[(0,b.jsxs)("div",{className:"relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl",children:[(0,b.jsx)(c.default,{src:"/about/ayurveda-healing.jpg",alt:"Traditional Ayurvedic healing session",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 50vw"}),(0,b.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]}),(0,b.jsx)(d.motion.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border border-gray-100",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-3xl font-bold text-primary-600 mb-1",children:"8+"}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:"Years of Service"})]})})]})]}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mb-20",children:[(0,b.jsx)("h3",{className:"text-3xl font-bold text-center text-gray-900 mb-12",children:"Our Core Values"}),(0,b.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{icon:"🌿",title:"Traditional Wisdom",description:"Preserving and practicing ancient Ayurvedic knowledge for modern healing"},{icon:"🤝",title:"Community First",description:"Building stronger, healthier communities through collaborative care"},{icon:"🌱",title:"Sustainable Growth",description:"Creating lasting change through environmentally conscious practices"},{icon:"💚",title:"Compassionate Care",description:"Providing healthcare with empathy, dignity, and respect for all"}].map((a,c)=>(0,b.jsx)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*c},viewport:{once:!0},children:(0,b.jsx)(e.default,{variant:"elevated",hover:!0,className:"text-center h-full",children:(0,b.jsxs)(e.CardContent,{className:"p-6",children:[(0,b.jsx)("div",{className:"text-4xl mb-4",children:a.icon}),(0,b.jsx)("h4",{className:"text-xl font-semibold text-gray-900 mb-3",children:a.title}),(0,b.jsx)("p",{className:"text-gray-600 leading-relaxed",children:a.description})]})})},a.title))})]}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"bg-primary-600 rounded-2xl p-8 lg:p-12 text-white",children:[(0,b.jsxs)("div",{className:"text-center mb-12",children:[(0,b.jsx)("h3",{className:"text-3xl font-bold mb-4",children:"Our Impact So Far"}),(0,b.jsx)("p",{className:"text-primary-100 text-lg",children:"Every number represents a life touched, a community strengthened, and hope restored"})]}),(0,b.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-8",children:[{number:"15,000+",label:"Lives Impacted",icon:"👥"},{number:"250+",label:"Villages Reached",icon:"🏘️"},{number:"180+",label:"Health Camps",icon:"🏥"},{number:"2,500+",label:"Women Empowered",icon:"👩"}].map((a,c)=>(0,b.jsxs)(d.motion.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.1*c},viewport:{once:!0},className:"text-center",children:[(0,b.jsx)("div",{className:"text-3xl mb-2",children:a.icon}),(0,b.jsx)("div",{className:"text-3xl lg:text-4xl font-bold mb-2",children:a.number}),(0,b.jsx)("div",{className:"text-primary-100",children:a.label})]},a.label))})]})]})})},70112,a=>{"use strict";a.s(["default",()=>g]);var b=a.i(87924),c=a.i(72131),d=a.i(46271),e=a.i(62067),f=a.i(96438);let g=()=>{let[a,g]=(0,c.useState)(500),[h,i]=(0,c.useState)(""),[j,k]=(0,c.useState)("general"),l=()=>h?parseInt(h)||0:a;return(0,b.jsx)("section",{className:"py-20 bg-gradient-to-br from-primary-600 to-primary-800 text-white",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,b.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold mb-6",children:["Make a ",(0,b.jsx)("span",{className:"text-yellow-300",children:"Difference"})]}),(0,b.jsx)("p",{className:"text-xl text-primary-100 max-w-3xl mx-auto leading-relaxed",children:"Your contribution can transform lives and heal communities. Every rupee counts towards building a healthier India."})]}),(0,b.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-start",children:[(0,b.jsx)(d.motion.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,b.jsxs)(e.default,{variant:"glass",className:"bg-white/10 backdrop-blur-md border-white/20",children:[(0,b.jsx)(e.CardHeader,{children:(0,b.jsx)(e.CardTitle,{className:"text-2xl text-white text-center",children:"Choose Your Contribution"})}),(0,b.jsxs)(e.CardContent,{className:"space-y-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white font-medium mb-3",children:"Select Purpose"}),(0,b.jsx)("div",{className:"grid grid-cols-1 gap-3",children:[{id:"general",title:"General Fund",description:"Support all our programs and initiatives",icon:"🌟",impact:"Helps us reach more communities"},{id:"health",title:"Health Camps",description:"Mobile healthcare for remote villages",icon:"🏥",impact:"₹500 can provide treatment for 5 families"},{id:"education",title:"Health Education",description:"Awareness programs and workshops",icon:"📚",impact:"₹1000 can educate 50 people"},{id:"livelihoods",title:"Women Empowerment",description:"Skill development and microfinance",icon:"👩‍💼",impact:"₹2500 can train 10 women entrepreneurs"},{id:"emergency",title:"Emergency Relief",description:"Disaster response and urgent care",icon:"🚨",impact:"₹1000 can provide emergency kit for 1 family"}].map(a=>(0,b.jsx)("button",{onClick:()=>k(a.id),className:`p-4 rounded-lg border-2 transition-all duration-200 text-left ${j===a.id?"border-yellow-300 bg-white/20":"border-white/30 hover:border-white/50 bg-white/10"}`,children:(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("span",{className:"text-2xl",children:a.icon}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("h4",{className:"font-semibold text-white",children:a.title}),(0,b.jsx)("p",{className:"text-sm text-white/80",children:a.description})]})]})},a.id))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-white font-medium mb-3",children:"Select Amount (₹)"}),(0,b.jsx)("div",{className:"grid grid-cols-3 gap-3 mb-4",children:[100,250,500,1e3,2500,5e3].map(c=>(0,b.jsxs)("button",{onClick:()=>{g(c),i("")},className:`p-3 rounded-lg border-2 transition-all duration-200 font-semibold ${a===c?"border-yellow-300 bg-yellow-300 text-primary-800":"border-white/30 text-white hover:border-white/50"}`,children:["₹",c]},c))}),(0,b.jsx)("input",{type:"number",placeholder:"Enter custom amount",value:h,onChange:a=>{i(a.target.value),g(0)},className:"w-full p-3 rounded-lg bg-white/10 border-2 border-white/30 text-white placeholder-white/60 focus:border-yellow-300 focus:outline-none"})]}),l()>0&&(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/20 rounded-lg p-4",children:[(0,b.jsx)("h4",{className:"font-semibold text-white mb-2",children:"Your Impact:"}),(0,b.jsxs)("p",{className:"text-white/90",children:["₹",l()," can help"," ",(0,b.jsx)("span",{className:"font-bold text-yellow-300",children:((a,b)=>({general:Math.floor(a/100),health:Math.floor(a/100),education:Math.floor(a/20),livelihoods:Math.floor(a/250),emergency:Math.floor(a/1e3)})[b]||0)(l(),j)})," ","general"===j&&"people through our programs","health"===j&&"families receive healthcare","education"===j&&"people get health education","livelihoods"===j&&"women start businesses","emergency"===j&&"families in emergencies"]})]}),(0,b.jsxs)(f.default,{variant:"secondary",size:"lg",className:"w-full bg-yellow-400 text-primary-800 hover:bg-yellow-300 font-bold text-lg py-4",disabled:0===l(),children:["Donate ₹",l()," Now"]}),(0,b.jsx)("p",{className:"text-xs text-white/70 text-center",children:"🔒 Secure payment • 80G Tax exemption available • 100% transparent usage"})]})]})}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-3xl font-bold text-white mb-6",children:"Why Your Donation Matters"}),(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,b.jsx)("div",{className:"bg-yellow-400 rounded-full p-2 mt-1",children:(0,b.jsx)("svg",{className:"w-4 h-4 text-primary-800",fill:"currentColor",viewBox:"0 0 20 20",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-semibold text-white",children:"Direct Impact"}),(0,b.jsx)("p",{className:"text-primary-100",children:"100% of your donation goes directly to programs, not administrative costs"})]})]}),(0,b.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,b.jsx)("div",{className:"bg-yellow-400 rounded-full p-2 mt-1",children:(0,b.jsx)("svg",{className:"w-4 h-4 text-primary-800",fill:"currentColor",viewBox:"0 0 20 20",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-semibold text-white",children:"Tax Benefits"}),(0,b.jsx)("p",{className:"text-primary-100",children:"Get 80G tax exemption on your donations as per Indian tax laws"})]})]}),(0,b.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,b.jsx)("div",{className:"bg-yellow-400 rounded-full p-2 mt-1",children:(0,b.jsx)("svg",{className:"w-4 h-4 text-primary-800",fill:"currentColor",viewBox:"0 0 20 20",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-semibold text-white",children:"Transparency"}),(0,b.jsx)("p",{className:"text-primary-100",children:"Regular updates on how your contribution is making a difference"})]})]})]})]}),(0,b.jsxs)("div",{className:"bg-white/10 rounded-xl p-6 backdrop-blur-sm",children:[(0,b.jsx)("h4",{className:"text-xl font-bold text-white mb-4",children:"Recent Impact"}),(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsx)("span",{className:"text-primary-100",children:"Health camps this month"}),(0,b.jsx)("span",{className:"font-bold text-yellow-300",children:"15"})]}),(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsx)("span",{className:"text-primary-100",children:"Families helped"}),(0,b.jsx)("span",{className:"font-bold text-yellow-300",children:"1,250"})]}),(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsx)("span",{className:"text-primary-100",children:"Women trained"}),(0,b.jsx)("span",{className:"font-bold text-yellow-300",children:"180"})]}),(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsx)("span",{className:"text-primary-100",children:"Villages reached"}),(0,b.jsx)("span",{className:"font-bold text-yellow-300",children:"25"})]})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"text-xl font-bold text-white mb-4",children:"Other Ways to Help"}),(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsx)(f.default,{variant:"outline",className:"w-full border-white text-white hover:bg-white hover:text-primary-800",children:"Become a Volunteer"}),(0,b.jsx)(f.default,{variant:"outline",className:"w-full border-white text-white hover:bg-white hover:text-primary-800",children:"Corporate Partnership"}),(0,b.jsx)(f.default,{variant:"outline",className:"w-full border-white text-white hover:bg-white hover:text-primary-800",children:"Sponsor a Program"})]})]})]})]})]})})}},9264,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(87924),c=a.i(72131),d=a.i(71987),e=a.i(46271),f=a.i(62036),g=a.i(96438);let h=()=>{let[a,h]=(0,c.useState)(0),i=[{id:1,title:"Healing Communities with Traditional Wisdom",subtitle:"Ayurakshak combines ancient Ayurveda with modern outreach to bring healthcare to every corner of India",image:"/hero/health-camp.jpg",cta:"Support Our Mission",stats:{number:"15,000+",label:"Lives Impacted"}},{id:2,title:"Empowering Women Through Sustainable Livelihoods",subtitle:"Creating opportunities for women to build independent, sustainable businesses in their communities",image:"/hero/women-empowerment.jpg",cta:"Join Our Programs",stats:{number:"2,500+",label:"Women Empowered"}},{id:3,title:"Natural Products for Holistic Wellness",subtitle:"Authentic Ayurvedic products crafted with traditional knowledge and modern quality standards",image:"/hero/ayurvedic-products.jpg",cta:"Shop Products",stats:{number:"50+",label:"Natural Products"}}];return(0,c.useEffect)(()=>{let a=setInterval(()=>{h(a=>(a+1)%i.length)},6e3);return()=>clearInterval(a)},[i.length]),(0,b.jsxs)("section",{className:"relative h-screen flex items-center justify-center overflow-hidden",children:[(0,b.jsx)(f.AnimatePresence,{mode:"wait",children:(0,b.jsxs)(e.motion.div,{className:"absolute inset-0 z-0",initial:{opacity:0,scale:1.1},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{duration:1},children:[(0,b.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60 z-10"}),(0,b.jsx)(d.default,{src:i[a].image,alt:i[a].title,fill:!0,className:"object-cover",priority:!0,sizes:"100vw"})]},a)}),(0,b.jsxs)("div",{className:"relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,b.jsx)(f.AnimatePresence,{mode:"wait",children:(0,b.jsxs)(e.motion.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:-50},transition:{duration:.8},className:"text-white",children:[(0,b.jsxs)(e.motion.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6,delay:.2},className:"inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-8",children:[(0,b.jsx)("span",{className:"text-2xl font-bold text-primary-200",children:i[a].stats.number}),(0,b.jsx)("span",{className:"ml-2 text-white/90",children:i[a].stats.label})]}),(0,b.jsx)(e.motion.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.3},className:"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight",children:i[a].title}),(0,b.jsx)(e.motion.p,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-xl md:text-2xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed",children:i[a].subtitle}),(0,b.jsxs)(e.motion.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.5},className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,b.jsx)(g.default,{variant:"primary",size:"lg",className:"bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg",children:i[a].cta}),(0,b.jsx)(g.default,{variant:"outline",size:"lg",className:"border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 text-lg",children:"Learn More"})]})]},a)}),(0,b.jsx)(e.motion.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,b.jsxs)("div",{className:"flex flex-col items-center text-white/70",children:[(0,b.jsx)("span",{className:"text-sm mb-2",children:"Scroll to explore"}),(0,b.jsx)(e.motion.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center",children:(0,b.jsx)("div",{className:"w-1 h-3 bg-white/70 rounded-full mt-2"})})]})})]}),(0,b.jsx)("button",{onClick:()=>{h(a=>(a-1+i.length)%i.length)},className:"absolute left-4 top-1/2 transform -translate-y-1/2 z-30 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200","aria-label":"Previous slide",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,b.jsx)("button",{onClick:()=>{h(a=>(a+1)%i.length)},className:"absolute right-4 top-1/2 transform -translate-y-1/2 z-30 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-200","aria-label":"Next slide",children:(0,b.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}),(0,b.jsx)("div",{className:"absolute bottom-20 left-1/2 transform -translate-x-1/2 z-30 flex space-x-3",children:i.map((c,d)=>(0,b.jsx)("button",{onClick:()=>h(d),className:`w-3 h-3 rounded-full transition-all duration-300 ${d===a?"bg-white scale-125":"bg-white/50 hover:bg-white/70"}`,"aria-label":`Go to slide ${d+1}`},d))}),(0,b.jsx)("div",{className:"absolute inset-0 z-10 pointer-events-none",children:[...Array(6)].map((a,c)=>(0,b.jsx)(e.motion.div,{className:"absolute w-2 h-2 bg-white/20 rounded-full",style:{left:`${100*Math.random()}%`,top:`${100*Math.random()}%`},animate:{y:[0,-20,0],opacity:[.2,.8,.2]},transition:{duration:3+2*Math.random(),repeat:1/0,delay:2*Math.random()}},c))})]})}},21859,a=>{"use strict";a.s(["default",()=>f]);var b=a.i(87924),c=a.i(71987),d=a.i(46271),e=a.i(62067);let f=()=>(0,b.jsx)("section",{id:"impact",className:"py-20 bg-gradient-to-br from-gray-50 to-blue-50",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,b.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["Our ",(0,b.jsx)("span",{className:"text-primary-600",children:"Impact"})]}),(0,b.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Real stories of transformation, healing, and hope from communities across India"})]}),(0,b.jsx)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-20",children:[{number:"15,000+",label:"Lives Directly Impacted",icon:"👥",color:"text-blue-600"},{number:"250+",label:"Villages Reached",icon:"🏘️",color:"text-green-600"},{number:"2,500+",label:"Women Empowered",icon:"👩",color:"text-purple-600"},{number:"180+",label:"Health Camps Conducted",icon:"🏥",color:"text-red-600"},{number:"50,000+",label:"Medicinal Plants Grown",icon:"🌱",color:"text-emerald-600"},{number:"8",label:"States Covered",icon:"🗺️",color:"text-orange-600"}].map((a,c)=>(0,b.jsxs)(d.motion.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.1*c},viewport:{once:!0},className:"text-center bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300",children:[(0,b.jsx)("div",{className:"text-3xl mb-3",children:a.icon}),(0,b.jsx)("div",{className:`text-2xl lg:text-3xl font-bold mb-2 ${a.color}`,children:a.number}),(0,b.jsx)("div",{className:"text-sm text-gray-600 leading-tight",children:a.label})]},a.label))}),(0,b.jsxs)("div",{className:"mb-20",children:[(0,b.jsx)(d.motion.h3,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-3xl font-bold text-center text-gray-900 mb-12",children:"Stories of Transformation"}),(0,b.jsx)("div",{className:"space-y-12",children:[{id:1,title:"Transforming Rural Healthcare in Rajasthan",location:"Udaipur District, Rajasthan",image:"/impact/rajasthan-health.jpg",description:"Our mobile health camps have reached 45 villages in Udaipur district, providing free Ayurvedic treatment to over 3,000 families.",stats:{families:"3,000+",villages:"45",camps:"120"},quote:"Ayurakshak brought hope to our village when modern medicine was too expensive.",author:"Kamala Devi, Village Elder"},{id:2,title:"Women Entrepreneurs Leading Change",location:"Varanasi, Uttar Pradesh",image:"/impact/women-entrepreneurs.jpg",description:"Through our livelihood program, 200 women have started their own herbal product businesses, earning sustainable income.",stats:{women:"200+",businesses:"150+",income:"₹50,000"},quote:"I can now support my family and send my children to school with dignity.",author:"Sunita Sharma, Entrepreneur"},{id:3,title:"Emergency Relief During COVID-19",location:"Multiple States",image:"/impact/covid-relief.jpg",description:"During the pandemic, we provided immunity boosters and healthcare support to 10,000 families across 8 states.",stats:{families:"10,000+",states:"8",kits:"25,000"},quote:"When hospitals were full, Ayurakshak was there with natural healing.",author:"Dr. Rajesh Kumar, Community Health Worker"}].map((a,e)=>(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2*e},viewport:{once:!0},className:`grid lg:grid-cols-2 gap-8 items-center ${e%2==1?"lg:grid-flow-col-dense":""}`,children:[(0,b.jsxs)("div",{className:`relative ${e%2==1?"lg:col-start-2":""}`,children:[(0,b.jsxs)("div",{className:"relative h-64 lg:h-80 rounded-2xl overflow-hidden shadow-xl",children:[(0,b.jsx)(c.default,{src:a.image,alt:a.title,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 50vw"}),(0,b.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"})]}),(0,b.jsx)("div",{className:"absolute -bottom-6 left-6 right-6 bg-white rounded-xl shadow-lg p-4",children:(0,b.jsx)("div",{className:"grid grid-cols-3 gap-4 text-center",children:Object.entries(a.stats).map(([a,c])=>(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"text-lg font-bold text-primary-600",children:c}),(0,b.jsx)("div",{className:"text-xs text-gray-500 capitalize",children:a})]},a))})})]}),(0,b.jsxs)("div",{className:`${e%2==1?"lg:col-start-1":""} pt-8 lg:pt-0`,children:[(0,b.jsxs)("div",{className:"inline-flex items-center bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium mb-4",children:["📍 ",a.location]}),(0,b.jsx)("h4",{className:"text-2xl lg:text-3xl font-bold text-gray-900 mb-4",children:a.title}),(0,b.jsx)("p",{className:"text-gray-600 leading-relaxed mb-6 text-lg",children:a.description}),(0,b.jsxs)("blockquote",{className:"border-l-4 border-primary-500 pl-6 py-4 bg-primary-50 rounded-r-lg",children:[(0,b.jsxs)("p",{className:"text-gray-700 italic mb-2",children:['"',a.quote,'"']}),(0,b.jsxs)("cite",{className:"text-sm font-medium text-primary-700",children:["— ",a.author]})]})]})]},a.id))})]}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[(0,b.jsx)("h3",{className:"text-3xl font-bold text-center text-gray-900 mb-12",children:"Recognition & Achievements"}),(0,b.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{icon:"🏆",title:"National Recognition",description:"Awarded Best NGO for Rural Healthcare by Ministry of Health",year:"2023"},{icon:"🌟",title:"UN Partnership",description:"Collaborated with UN Women for sustainable development goals",year:"2022"},{icon:"📜",title:"ISO Certification",description:"Certified for quality management in healthcare delivery",year:"2021"},{icon:"🎖️",title:"State Award",description:"Recognized by Rajasthan Government for community service",year:"2020"}].map((a,c)=>(0,b.jsx)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*c},viewport:{once:!0},children:(0,b.jsx)(e.default,{variant:"elevated",hover:!0,className:"text-center h-full",children:(0,b.jsxs)(e.CardContent,{className:"p-6",children:[(0,b.jsx)("div",{className:"text-4xl mb-4",children:a.icon}),(0,b.jsx)("div",{className:"text-sm font-medium text-primary-600 mb-2",children:a.year}),(0,b.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:a.title}),(0,b.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:a.description})]})})},a.title))})]})]})})},37193,a=>{"use strict";a.s(["default",()=>h],37193);var b=a.i(87924),c=a.i(72131),d=a.i(46271),e=a.i(96438),f=a.i(77845);let g=c.default.forwardRef(({className:a,label:c,error:e,helperText:g,leftIcon:h,rightIcon:i,variant:j="default",inputSize:k="md",id:l,...m},n)=>{let o=l||`input-${Math.random().toString(36).substr(2,9)}`,p={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"},q=(0,f.cn)(["w-full transition-all duration-200 focus:outline-none","disabled:opacity-50 disabled:cursor-not-allowed"],{default:["border border-gray-300 rounded-lg bg-white","focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20",e&&"border-red-500 focus:border-red-500 focus:ring-red-500/20"],filled:["border-0 rounded-lg bg-gray-100","focus:bg-white focus:ring-2 focus:ring-primary-500/20",e&&"bg-red-50 focus:ring-red-500/20"],outlined:["border-2 border-gray-300 rounded-lg bg-transparent","focus:border-primary-500",e&&"border-red-500 focus:border-red-500"]}[j],{sm:"px-3 py-2 text-sm",md:"px-4 py-3 text-base",lg:"px-5 py-4 text-lg"}[k],h&&"pl-10",i&&"pr-10",a);return(0,b.jsxs)("div",{className:"w-full",children:[c&&(0,b.jsx)("label",{htmlFor:o,className:(0,f.cn)("block text-sm font-medium mb-2",e?"text-red-700":"text-gray-700"),children:c}),(0,b.jsxs)("div",{className:"relative",children:[h&&(0,b.jsx)("div",{className:(0,f.cn)("absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",p[k]),children:h}),(0,b.jsx)("input",{ref:n,id:o,className:q,...m}),i&&(0,b.jsx)("div",{className:(0,f.cn)("absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",p[k]),children:i})]}),(e||g)&&(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:-5},animate:{opacity:1,y:0},transition:{duration:.2},className:"mt-2",children:[e&&(0,b.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,b.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),e]}),g&&!e&&(0,b.jsx)("p",{className:"text-sm text-gray-500",children:g})]})]})});g.displayName="Input";let h=()=>{let[a,f]=(0,c.useState)(""),[h,i]=(0,c.useState)(""),[j,k]=(0,c.useState)(!1),[l,m]=(0,c.useState)(!1),n=async b=>{if(b.preventDefault(),a){k(!0);try{(await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,name:h})})).ok?(m(!0),f(""),i("")):console.error("Subscription failed")}catch(a){console.error("Subscription error:",a)}finally{k(!1)}}};return l?(0,b.jsx)("section",{className:"py-20 bg-gradient-to-br from-sage-50 to-mint-50",children:(0,b.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,b.jsxs)(d.motion.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6},className:"bg-white rounded-2xl shadow-xl p-12",children:[(0,b.jsx)("div",{className:"text-6xl mb-6",children:"🎉"}),(0,b.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Welcome to Our Community!"}),(0,b.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"Thank you for subscribing! You'll receive our latest updates and exclusive content soon."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,b.jsx)(e.default,{variant:"primary",size:"lg",children:"Explore Our Programs"}),(0,b.jsx)(e.default,{variant:"outline",size:"lg",children:"Shop Products"})]})]})})}):(0,b.jsx)("section",{className:"py-20 bg-gradient-to-br from-sage-50 to-mint-50",children:(0,b.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,b.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,b.jsxs)(d.motion.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,b.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["Stay Connected with Our"," ",(0,b.jsx)("span",{className:"text-primary-600",children:"Mission"})]}),(0,b.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:"Join thousands of supporters who receive our updates on community impact, health tips, and ways to make a difference. Be part of the healing journey."}),(0,b.jsx)("div",{className:"grid md:grid-cols-2 gap-6 mb-8",children:[{icon:"📧",title:"Monthly Updates",description:"Get our monthly newsletter with program updates and impact stories"},{icon:"🎯",title:"Exclusive Content",description:"Access to exclusive health tips and Ayurvedic wellness guides"},{icon:"🎉",title:"Event Invitations",description:"Be the first to know about health camps and community events"},{icon:"💝",title:"Special Offers",description:"Exclusive discounts on our natural products and programs"}].map((a,c)=>(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*c},viewport:{once:!0},className:"flex items-start space-x-3",children:[(0,b.jsx)("div",{className:"text-2xl",children:a.icon}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:a.title}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:a.description})]})]},a.title))}),(0,b.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("svg",{className:"w-4 h-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,b.jsx)("span",{children:"No spam, ever"})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("svg",{className:"w-4 h-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,b.jsx)("span",{children:"Unsubscribe anytime"})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("svg",{className:"w-4 h-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20",children:(0,b.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),(0,b.jsx)("span",{children:"5,000+ subscribers"})]})]})]}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"bg-white rounded-2xl shadow-xl p-8 lg:p-10",children:[(0,b.jsxs)("div",{className:"text-center mb-8",children:[(0,b.jsx)("div",{className:"text-4xl mb-4",children:"📬"}),(0,b.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Join Our Newsletter"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Get the latest updates delivered to your inbox"})]}),(0,b.jsxs)("form",{onSubmit:n,className:"space-y-6",children:[(0,b.jsx)(g,{type:"text",placeholder:"Your name (optional)",value:h,onChange:a=>i(a.target.value),variant:"filled",inputSize:"lg"}),(0,b.jsx)(g,{type:"email",placeholder:"Enter your email address",value:a,onChange:a=>f(a.target.value),required:!0,variant:"filled",inputSize:"lg"}),(0,b.jsx)(e.default,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:j,disabled:!a||j,children:j?"Subscribing...":"Subscribe Now"})]}),(0,b.jsxs)("p",{className:"text-xs text-gray-500 text-center mt-6",children:["By subscribing, you agree to our"," ",(0,b.jsx)("a",{href:"/privacy",className:"text-primary-600 hover:underline",children:"Privacy Policy"})," ","and consent to receive updates from Ayurakshak."]}),(0,b.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-100",children:[(0,b.jsx)("p",{className:"text-sm text-gray-600 text-center mb-4",children:"Join our community on social media"}),(0,b.jsx)("div",{className:"flex justify-center space-x-4",children:[{name:"Facebook",icon:"📘"},{name:"Instagram",icon:"📷"},{name:"Twitter",icon:"🐦"},{name:"LinkedIn",icon:"💼"}].map(a=>(0,b.jsx)("button",{className:"w-10 h-10 rounded-full bg-gray-100 hover:bg-primary-100 transition-colors duration-200 flex items-center justify-center","aria-label":a.name,children:(0,b.jsx)("span",{className:"text-lg",children:a.icon})},a.name))})]})]})]})})})}},47110,a=>{"use strict";a.s(["default",()=>g]);var b=a.i(87924),c=a.i(71987),d=a.i(46271),e=a.i(62067),f=a.i(96438);let g=()=>(0,b.jsx)("section",{id:"programs",className:"py-20 bg-white",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,b.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["Our ",(0,b.jsx)("span",{className:"text-primary-600",children:"Programs"})]}),(0,b.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Comprehensive initiatives designed to heal, empower, and transform communities across India"})]}),(0,b.jsx)("div",{className:"grid lg:grid-cols-2 gap-8 mb-16",children:[{id:"health-camps",title:"Mobile Health Camps",description:"Bringing Ayurvedic healthcare directly to remote villages and underserved communities across India.",image:"/programs/health-camps.jpg",icon:"🏥",stats:{beneficiaries:"15,000+",locations:"250+"},features:["Free Ayurvedic consultations","Traditional medicine distribution","Health education workshops","Preventive care guidance"],color:"from-green-500 to-emerald-600"},{id:"women-empowerment",title:"Women Livelihood Programs",description:"Empowering women through skill development, entrepreneurship training, and sustainable business opportunities.",image:"/programs/women-empowerment.jpg",icon:"👩‍💼",stats:{beneficiaries:"2,500+",groups:"150+"},features:["Skill development training","Microfinance support","Business mentorship","Market linkage assistance"],color:"from-purple-500 to-pink-600"},{id:"education",title:"Health Education Initiative",description:"Spreading awareness about preventive healthcare, nutrition, and traditional wellness practices.",image:"/programs/education.jpg",icon:"📚",stats:{beneficiaries:"8,000+",workshops:"300+"},features:["Community health workshops","Nutrition education","Hygiene awareness programs","Traditional medicine training"],color:"from-blue-500 to-indigo-600"},{id:"emergency-relief",title:"Emergency Relief Support",description:"Rapid response healthcare support during natural disasters and health emergencies.",image:"/programs/emergency-relief.jpg",icon:"🚨",stats:{responses:"25+",families:"5,000+"},features:["Emergency medical camps","Relief material distribution","Psychological support","Rehabilitation assistance"],color:"from-red-500 to-orange-600"},{id:"environment",title:"Green Health Initiative",description:"Promoting environmental conservation through medicinal plant cultivation and eco-friendly practices.",image:"/programs/environment.jpg",icon:"🌱",stats:{plants:"50,000+",farmers:"800+"},features:["Medicinal plant cultivation","Organic farming training","Environmental awareness","Sustainable practices"],color:"from-green-600 to-teal-600"},{id:"community-development",title:"Community Development",description:"Building resilient communities through integrated development programs and capacity building.",image:"/programs/community-development.jpg",icon:"🏘️",stats:{villages:"180+",leaders:"500+"},features:["Community leadership training","Infrastructure development","Social cohesion programs","Local governance support"],color:"from-yellow-500 to-amber-600"}].map((a,g)=>(0,b.jsx)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*g},viewport:{once:!0},children:(0,b.jsxs)(e.default,{variant:"elevated",hover:!0,className:"h-full overflow-hidden group",children:[(0,b.jsxs)("div",{className:"relative h-48 overflow-hidden",children:[(0,b.jsx)(c.default,{src:a.image,alt:a.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300",sizes:"(max-width: 768px) 100vw, 50vw"}),(0,b.jsx)("div",{className:`absolute inset-0 bg-gradient-to-r ${a.color} opacity-80`}),(0,b.jsx)("div",{className:"absolute top-4 left-4 bg-white/20 backdrop-blur-sm rounded-full p-3",children:(0,b.jsx)("span",{className:"text-2xl",children:a.icon})}),(0,b.jsx)("div",{className:"absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1",children:(0,b.jsxs)("span",{className:"text-sm font-medium text-gray-800",children:[Object.values(a.stats)[0]," impacted"]})})]}),(0,b.jsxs)(e.CardContent,{className:"p-6",children:[(0,b.jsxs)(e.CardHeader,{className:"p-0 mb-4",children:[(0,b.jsx)(e.CardTitle,{className:"text-xl font-bold text-gray-900 mb-2",children:a.title}),(0,b.jsx)("p",{className:"text-gray-600 leading-relaxed",children:a.description})]}),(0,b.jsx)("div",{className:"flex gap-6 mb-4",children:Object.entries(a.stats).map(([a,c])=>(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-lg font-bold text-primary-600",children:c}),(0,b.jsx)("div",{className:"text-xs text-gray-500 capitalize",children:a})]},a))}),(0,b.jsx)("div",{className:"space-y-2 mb-6",children:a.features.map((a,c)=>(0,b.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,b.jsx)("div",{className:"w-1.5 h-1.5 bg-primary-500 rounded-full mr-3 flex-shrink-0"}),a]},c))}),(0,b.jsx)(f.default,{variant:"outline",size:"sm",className:"w-full",children:"Learn More"})]})]})},a.id))}),(0,b.jsxs)(d.motion.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center bg-gradient-to-r from-primary-50 to-sage-50 rounded-2xl p-8 lg:p-12",children:[(0,b.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Want to Support Our Programs?"}),(0,b.jsx)("p",{className:"text-lg text-gray-600 mb-8 max-w-2xl mx-auto",children:"Your contribution can help us expand our reach and impact more lives. Join us in building healthier, more empowered communities."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,b.jsx)(f.default,{variant:"primary",size:"lg",children:"Donate Now"}),(0,b.jsx)(f.default,{variant:"outline",size:"lg",children:"Become a Volunteer"})]})]})]})})}];

//# sourceMappingURL=_09b8abbe._.js.map