'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/cn';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'outlined' | 'glass';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  className,
  variant = 'default',
  padding = 'md',
  hover = false,
  onClick,
}) => {
  const baseClasses = [
    'rounded-xl transition-all duration-300',
    onClick && 'cursor-pointer',
  ];

  const variants = {
    default: [
      'bg-white border border-gray-200',
      hover && 'hover:border-gray-300 hover:shadow-md',
    ],
    elevated: [
      'bg-white shadow-lg border border-gray-100',
      hover && 'hover:shadow-xl hover:border-gray-200',
    ],
    outlined: [
      'bg-transparent border-2 border-primary-200',
      hover && 'hover:border-primary-300 hover:bg-primary-50/50',
    ],
    glass: [
      'bg-white/80 backdrop-blur-sm border border-white/20',
      hover && 'hover:bg-white/90 hover:border-white/30',
    ],
  };

  const paddings = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };

  const classes = cn(
    baseClasses,
    variants[variant],
    paddings[padding],
    className
  );

  const MotionComponent = onClick ? motion.div : motion.div;

  return (
    <MotionComponent
      className={classes}
      onClick={onClick}
      whileHover={hover ? { y: -2, scale: 1.02 } : undefined}
      whileTap={onClick ? { scale: 0.98 } : undefined}
      transition={{ duration: 0.2 }}
    >
      {children}
    </MotionComponent>
  );
};

// Card sub-components
const CardHeader: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <div className={cn('mb-4', className)}>
    {children}
  </div>
);

const CardTitle: React.FC<{
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}> = ({ children, className, as: Component = 'h3' }) => (
  <Component className={cn('text-xl font-semibold text-gray-900 mb-2', className)}>
    {children}
  </Component>
);

const CardDescription: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <p className={cn('text-gray-600 text-sm', className)}>
    {children}
  </p>
);

const CardContent: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <div className={cn('', className)}>
    {children}
  </div>
);

const CardFooter: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <div className={cn('mt-6 pt-4 border-t border-gray-100', className)}>
    {children}
  </div>
);

// Export all components
export default Card;
export { CardHeader, CardTitle, CardDescription, CardContent, CardFooter };
