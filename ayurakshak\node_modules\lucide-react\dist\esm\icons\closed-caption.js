/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 9.17a3 3 0 1 0 0 5.66", key: "h9wayk" }],
  ["path", { d: "M17 9.17a3 3 0 1 0 0 5.66", key: "1v6zke" }],
  ["rect", { x: "2", y: "5", width: "20", height: "14", rx: "2", key: "qneu4z" }]
];
const ClosedCaption = createLucideIcon("closed-caption", __iconNode);

export { __iconNode, ClosedCaption as default };
//# sourceMappingURL=closed-caption.js.map
