import { NextRequest, NextResponse } from 'next/server';
import { formatSuccessResponse, formatErrorResponse } from '@/utils/validation';

// Sample products data (in a real app, this would come from a database)
const PRODUCTS = [
  {
    id: 'ayur-001',
    name: 'Ayurvedic Immunity Booster',
    price: 299,
    currency: 'INR',
    description: 'Boost your natural immunity with our carefully crafted blend of traditional Ayurvedic herbs including Ashwagandha, Tulsi, and Amla.',
    images: ['/products/immunity-booster.jpg'],
    category: 'Immunity',
    ingredients: ['Ashwagandha', 'Tulsi', 'Amla', 'Giloy', 'Turmeric'],
    benefits: [
      'Strengthens immune system',
      'Reduces stress and fatigue',
      'Improves overall vitality',
      'Natural antioxidant properties'
    ],
    usage: 'Take 1-2 capsules daily with warm water after meals',
    features: ['100% Natural', 'No Side Effects', 'Clinically Tested', 'Vegetarian'],
    inStock: true,
    stockQuantity: 50,
    weight: '60 capsules',
    isActive: true,
  },
  {
    id: 'ayur-002',
    name: 'Herbal Digestive Tea',
    price: 199,
    currency: 'INR',
    description: 'A soothing blend of digestive herbs that helps improve digestion and reduces bloating naturally.',
    images: ['/products/digestive-tea.jpg'],
    category: 'Digestive Health',
    ingredients: ['Fennel', 'Ginger', 'Mint', 'Cumin', 'Coriander'],
    benefits: [
      'Improves digestion',
      'Reduces bloating and gas',
      'Soothes stomach discomfort',
      'Promotes healthy metabolism'
    ],
    usage: 'Steep 1 tea bag in hot water for 3-5 minutes. Drink after meals.',
    features: ['Caffeine Free', 'Natural Ingredients', 'Pleasant Taste', 'Daily Use'],
    inStock: true,
    stockQuantity: 75,
    weight: '25 tea bags',
    isActive: true,
  },
  {
    id: 'ayur-003',
    name: 'Natural Pain Relief Oil',
    price: 249,
    currency: 'INR',
    description: 'Traditional Ayurvedic oil blend for natural relief from joint and muscle pain.',
    images: ['/products/pain-relief-oil.jpg'],
    category: 'Pain Relief',
    ingredients: ['Eucalyptus', 'Wintergreen', 'Camphor', 'Sesame Oil', 'Mustard Oil'],
    benefits: [
      'Relieves joint and muscle pain',
      'Reduces inflammation',
      'Improves blood circulation',
      'Provides warming sensation'
    ],
    usage: 'Gently massage on affected area 2-3 times daily',
    features: ['Fast Acting', 'Deep Penetration', 'Non-Greasy', 'Pleasant Aroma'],
    inStock: true,
    stockQuantity: 30,
    weight: '100ml',
    isActive: true,
  },
  {
    id: 'ayur-004',
    name: 'Stress Relief Capsules',
    price: 399,
    currency: 'INR',
    description: 'Natural stress relief formula with Ashwagandha and other calming herbs to promote mental well-being.',
    images: ['/products/stress-relief.jpg'],
    category: 'Mental Wellness',
    ingredients: ['Ashwagandha', 'Brahmi', 'Jatamansi', 'Shankhpushpi', 'Tagar'],
    benefits: [
      'Reduces stress and anxiety',
      'Improves sleep quality',
      'Enhances mental clarity',
      'Balances mood naturally'
    ],
    usage: 'Take 1 capsule twice daily with milk or water',
    features: ['Clinically Proven', 'Non-Habit Forming', 'Safe for Long-term Use', 'Vegetarian'],
    inStock: true,
    stockQuantity: 40,
    weight: '60 capsules',
    isActive: true,
  },
  {
    id: 'ayur-005',
    name: 'Detox Herbal Powder',
    price: 349,
    currency: 'INR',
    description: 'Complete body detox powder made with traditional herbs to cleanse and rejuvenate your system.',
    images: ['/products/detox-powder.jpg'],
    category: 'Detox',
    ingredients: ['Triphala', 'Neem', 'Aloe Vera', 'Guduchi', 'Punarnava'],
    benefits: [
      'Cleanses digestive system',
      'Removes toxins naturally',
      'Improves skin health',
      'Boosts energy levels'
    ],
    usage: 'Mix 1 teaspoon with warm water, take on empty stomach',
    features: ['Complete Detox', 'Natural Cleansing', 'Improves Metabolism', 'Organic'],
    inStock: true,
    stockQuantity: 25,
    weight: '200g powder',
    isActive: true,
  },
  {
    id: 'ayur-006',
    name: 'Hair Growth Oil',
    price: 279,
    currency: 'INR',
    description: 'Nourishing hair oil with traditional herbs to promote healthy hair growth and prevent hair fall.',
    images: ['/products/hair-oil.jpg'],
    category: 'Hair Care',
    ingredients: ['Bhringraj', 'Amla', 'Fenugreek', 'Coconut Oil', 'Curry Leaves'],
    benefits: [
      'Promotes hair growth',
      'Reduces hair fall',
      'Nourishes scalp',
      'Adds natural shine'
    ],
    usage: 'Massage gently into scalp, leave for 2 hours or overnight, then wash',
    features: ['Chemical Free', 'Suitable for All Hair Types', 'Natural Fragrance', 'Deep Nourishment'],
    inStock: true,
    stockQuantity: 35,
    weight: '200ml',
    isActive: true,
  },
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const inStock = searchParams.get('inStock');
    const productId = searchParams.get('id');

    // If requesting a specific product
    if (productId) {
      const product = PRODUCTS.find(p => p.id === productId);
      if (!product) {
        return NextResponse.json(
          formatErrorResponse('Product not found'),
          { status: 404 }
        );
      }
      return NextResponse.json(
        formatSuccessResponse(product),
        { status: 200 }
      );
    }

    // Filter products based on query parameters
    let filteredProducts = PRODUCTS.filter(product => product.isActive);

    if (category) {
      filteredProducts = filteredProducts.filter(
        product => product.category.toLowerCase() === category.toLowerCase()
      );
    }

    if (search) {
      const searchTerm = search.toLowerCase();
      filteredProducts = filteredProducts.filter(
        product =>
          product.name.toLowerCase().includes(searchTerm) ||
          product.description.toLowerCase().includes(searchTerm) ||
          product.category.toLowerCase().includes(searchTerm) ||
          product.ingredients.some(ingredient => 
            ingredient.toLowerCase().includes(searchTerm)
          )
      );
    }

    if (inStock === 'true') {
      filteredProducts = filteredProducts.filter(product => product.inStock);
    }

    // Get unique categories for filtering
    const categories = [...new Set(PRODUCTS.map(product => product.category))];

    return NextResponse.json(
      formatSuccessResponse({
        products: filteredProducts,
        categories,
        total: filteredProducts.length,
      }),
      { status: 200 }
    );

  } catch (error) {
    console.error('Products fetch error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while fetching products.'),
      { status: 500 }
    );
  }
}
