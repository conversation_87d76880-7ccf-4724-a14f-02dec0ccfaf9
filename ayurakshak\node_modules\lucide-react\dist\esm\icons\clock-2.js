/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v6l4-2", key: "1r2kuh" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
];
const Clock2 = createLucideIcon("clock-2", __iconNode);

export { __iconNode, Clock2 as default };
//# sourceMappingURL=clock-2.js.map
