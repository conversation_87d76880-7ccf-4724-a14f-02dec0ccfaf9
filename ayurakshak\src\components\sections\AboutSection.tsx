'use client';

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import Card, { CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const AboutSection: React.FC = () => {
  const values = [
    {
      icon: '🌿',
      title: 'Traditional Wisdom',
      description: 'Preserving and practicing ancient Ayurvedic knowledge for modern healing',
    },
    {
      icon: '🤝',
      title: 'Community First',
      description: 'Building stronger, healthier communities through collaborative care',
    },
    {
      icon: '🌱',
      title: 'Sustainable Growth',
      description: 'Creating lasting change through environmentally conscious practices',
    },
    {
      icon: '💚',
      title: 'Compassionate Care',
      description: 'Providing healthcare with empathy, dignity, and respect for all',
    },
  ];

  const stats = [
    { number: '15,000+', label: 'Lives Impacted', icon: '👥' },
    { number: '250+', label: 'Villages Reached', icon: '🏘️' },
    { number: '180+', label: 'Health Camps', icon: '🏥' },
    { number: '2,500+', label: 'Women Empowered', icon: '👩' },
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-sage-50 to-mint-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            About <span className="text-primary-600">Ayurakshak</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Where ancient wisdom meets modern compassion to heal communities across India
          </p>
        </motion.div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              Healing Through Traditional Wisdom
            </h3>
            <div className="space-y-6 text-gray-600 leading-relaxed">
              <p>
                Ayurakshak was born from a simple yet powerful vision: to bridge the gap between 
                traditional Ayurvedic healing and modern healthcare accessibility. Founded by a 
                team of passionate healthcare professionals and social workers, we believe that 
                everyone deserves access to natural, holistic healthcare.
              </p>
              <p>
                Our journey began in rural villages where we witnessed the profound impact of 
                combining ancient Ayurvedic practices with community-centered care. Today, we've 
                grown into a movement that spans across India, touching lives through health camps, 
                women's empowerment programs, and sustainable livelihood initiatives.
              </p>
              <p>
                At Ayurakshak, we don't just treat symptoms – we nurture communities, empower 
                individuals, and create lasting change that ripples through generations. Every 
                program we run, every product we create, and every life we touch is guided by 
                our commitment to holistic wellness and social transformation.
              </p>
            </div>
            <div className="mt-8">
              <Button variant="primary" size="lg">
                Learn More About Our Mission
              </Button>
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="/about/ayurveda-healing.jpg"
                alt="Traditional Ayurvedic healing session"
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            </div>
            
            {/* Floating Stats Card */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border border-gray-100"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600 mb-1">8+</div>
                <div className="text-sm text-gray-600">Years of Service</div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Values Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-20"
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Our Core Values
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card variant="elevated" hover className="text-center h-full">
                  <CardContent className="p-6">
                    <div className="text-4xl mb-4">{value.icon}</div>
                    <h4 className="text-xl font-semibold text-gray-900 mb-3">
                      {value.title}
                    </h4>
                    <p className="text-gray-600 leading-relaxed">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Impact Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-primary-600 rounded-2xl p-8 lg:p-12 text-white"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">Our Impact So Far</h3>
            <p className="text-primary-100 text-lg">
              Every number represents a life touched, a community strengthened, and hope restored
            </p>
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-3xl mb-2">{stat.icon}</div>
                <div className="text-3xl lg:text-4xl font-bold mb-2">{stat.number}</div>
                <div className="text-primary-100">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutSection;
