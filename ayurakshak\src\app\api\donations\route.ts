import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Donation from '@/lib/models/Donation';
import { donationFormSchema } from '@/utils/validation';
import { formatSuccessResponse, formatErrorResponse, createRateLimiter } from '@/utils/validation';

// Rate limiter: 3 requests per 10 minutes per IP
const rateLimiter = createRateLimiter(10 * 60 * 1000, 3);

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    if (!rateLimiter(clientIP)) {
      return NextResponse.json(
        formatErrorResponse('Too many requests. Please try again later.'),
        { status: 429 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate input data
    const validationResult = donationFormSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        formatErrorResponse('Validation failed', validationResult.error.errors),
        { status: 400 }
      );
    }

    const donationData = validationResult.data;

    // Connect to database
    await connectDB();

    // Create new donation record
    const donation = new Donation({
      ...donationData,
      paymentStatus: 'pending',
      paymentMethod: 'online', // Default to online, can be updated based on payment gateway
    });

    await donation.save();

    // TODO: Integrate with payment gateway (Razorpay, Stripe, etc.)
    // TODO: Send confirmation email
    // TODO: Generate tax receipt for completed donations

    return NextResponse.json(
      formatSuccessResponse(
        { 
          id: donation._id,
          amount: donation.amount,
          purpose: donation.purpose,
          // Return payment gateway URL or order ID here
        },
        'Donation initiated successfully. Please complete the payment.'
      ),
      { status: 201 }
    );

  } catch (error) {
    console.error('Donation creation error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while processing your donation. Please try again later.'),
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const purpose = searchParams.get('purpose');
    const status = searchParams.get('status');
    const isPublic = searchParams.get('public') === 'true';

    await connectDB();

    // Build query
    const query: any = {};
    if (purpose) query.purpose = purpose;
    if (status) query.paymentStatus = status;
    if (isPublic) {
      query.paymentStatus = 'completed';
      query.isAnonymous = false;
    }

    // Get total count
    const total = await Donation.countDocuments(query);

    // Select fields based on public/admin access
    const selectFields = isPublic 
      ? 'donorName amount purpose createdAt'
      : 'donorName donorEmail amount purpose paymentStatus createdAt isAnonymous';

    // Get paginated results
    const donations = await Donation.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .select(selectFields);

    // Get statistics
    const stats = await Donation.aggregate([
      { $match: { paymentStatus: 'completed' } },
      {
        $group: {
          _id: '$purpose',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 },
        },
      },
    ]);

    const totalRaised = await Donation.aggregate([
      { $match: { paymentStatus: 'completed' } },
      { $group: { _id: null, total: { $sum: '$amount' } } },
    ]);

    return NextResponse.json(
      formatSuccessResponse({
        donations,
        stats: {
          byPurpose: stats,
          totalRaised: totalRaised[0]?.total || 0,
          totalDonors: await Donation.distinct('donorEmail', { paymentStatus: 'completed' }).then(emails => emails.length),
        },
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      }),
      { status: 200 }
    );

  } catch (error) {
    console.error('Donations fetch error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while fetching donations.'),
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // This endpoint is for payment gateway webhooks and admin updates
    const body = await request.json();
    const { id, paymentStatus, transactionId, paymentGatewayResponse } = body;

    if (!id || !paymentStatus) {
      return NextResponse.json(
        formatErrorResponse('Invalid request data'),
        { status: 400 }
      );
    }

    await connectDB();

    const updateData: any = { paymentStatus };
    if (transactionId) updateData.transactionId = transactionId;
    if (paymentGatewayResponse) updateData.paymentGatewayResponse = paymentGatewayResponse;

    const updatedDonation = await Donation.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );

    if (!updatedDonation) {
      return NextResponse.json(
        formatErrorResponse('Donation not found'),
        { status: 404 }
      );
    }

    // TODO: Send receipt email for completed donations
    // TODO: Update donor records
    // TODO: Trigger thank you email

    return NextResponse.json(
      formatSuccessResponse(updatedDonation, 'Donation updated successfully'),
      { status: 200 }
    );

  } catch (error) {
    console.error('Donation update error:', error);
    return NextResponse.json(
      formatErrorResponse('An error occurred while updating the donation.'),
      { status: 500 }
    );
  }
}
